PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/CoreOnly (10.22.0):
    - FirebaseCore (= 10.22.0)
  - Firebase/Messaging (10.22.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.22.0)
  - firebase_core (2.27.0):
    - Firebase/CoreOnly (= 10.22.0)
    - Flutter
  - firebase_messaging (14.7.19):
    - Firebase/Messaging (= 10.22.0)
    - firebase_core
    - Flutter
  - FirebaseCore (10.22.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.22.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - Flutter (1.0.0)
  - flutter_barcode_scanner (2.0.0):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_mailer (0.0.1):
    - Flutter
  - flutter_native_splash (0.0.1):
    - Flutter
  - flutter_pdfview (1.0.2):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - geolocator_apple (1.2.0):
    - Flutter
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_picker_ios (0.0.1):
    - Flutter
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - open_file_plus (1.0.0):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - ReachabilitySwift (5.2.4)
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SQLCipher (4.6.1):
    - SQLCipher/standard (= 4.6.1)
  - SQLCipher/common (4.6.1)
  - SQLCipher/standard (4.6.1):
    - SQLCipher/common
  - sqlcipher_flutter_libs (0.0.1):
    - Flutter
    - SQLCipher (~> 4.6.1)
  - SwiftyGif (5.4.5)
  - Toast (4.1.1)
  - uni_links (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_barcode_scanner (from `.symlinks/plugins/flutter_barcode_scanner/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_mailer (from `.symlinks/plugins/flutter_mailer/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - open_file_plus (from `.symlinks/plugins/open_file_plus/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqlcipher_flutter_libs (from `.symlinks/plugins/sqlcipher_flutter_libs/ios`)
  - uni_links (from `.symlinks/plugins/uni_links/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - ReachabilitySwift
    - SDWebImage
    - SQLCipher
    - SwiftyGif
    - Toast

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_barcode_scanner:
    :path: ".symlinks/plugins/flutter_barcode_scanner/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_mailer:
    :path: ".symlinks/plugins/flutter_mailer/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  open_file_plus:
    :path: ".symlinks/plugins/open_file_plus/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqlcipher_flutter_libs:
    :path: ".symlinks/plugins/sqlcipher_flutter_libs/ios"
  uni_links:
    :path: ".symlinks/plugins/uni_links/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  connectivity_plus: 481668c94744c30c53b8895afb39159d1e619bdf
  device_info_plus: 335f3ce08d2e174b9fdc3db3db0f4e3b1f66bd89
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: 07c75322ede1d47ec9bb4ac82b27c94d3598251a
  Firebase: 797fd7297b7e1be954432743a0b3f90038e45a71
  firebase_core: 2a80983e98480c7f02ff001358621c8d7f1ff8f0
  firebase_messaging: 879b396d2be7aa1eaf044d50d2407d24c971b934
  FirebaseCore: 0326ec9b05fbed8f8716cddbf0e36894a13837f7
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 9f71037fd9db3376a4caa54e5a3949d1027b4b6e
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_barcode_scanner: c5aa9f51c150a6242fa392386bd52b64bb27fcb5
  flutter_local_notifications: ef18f0537538fcd18e9106b3ddc91cc10b4e579a
  flutter_mailer: 3a8cd4f36c960fb04528d5471097270c19fec1c4
  flutter_native_splash: 9e672d3818957718ee006a491730c09deeecace9
  flutter_pdfview: 32bf27bda6fd85b9dd2c09628a824df5081246cf
  fluttertoast: eaafdac812d1d69931004e881c87a8643b1c9111
  geolocator_apple: 1560c3c875af2a412242c7a923e15d0d401966ff
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  image_picker_ios: 85f2b3c9fb98c09d63725c4d12ebd585b56ec35d
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  open_file_plus: b0a12f9db69dfdab3b4751761753d2ba229ae6c1
  package_info_plus: 566e1b7a2f3900e4b0020914ad3fc051dcc95596
  path_provider_foundation: 608fcb11be570ce83519b076ab6a1fffe2474f05
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  shared_preferences_foundation: 0b09b969fb36da5551c0bc4a2dbd9d0ff9387478
  SQLCipher: 77fbe633cd84db04b07876dd50766b4924b57d61
  sqlcipher_flutter_libs: 4d7cf8e35b46debcc8ab7903123e32c8dbb943f5
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  uni_links: ed8c961e47ed9ce42b6d91e1de8049e38a4b3152
  url_launcher_ios: a1c1f2bf89969cb13cce46d0333c7257d346e169
  video_player_avfoundation: 5685dd1957b7437a39376959f3502aac60b47aa5
  webview_flutter_wkwebview: 6e6160e04b1e85872253adc5322afe416d9cdddc

PODFILE CHECKSUM: 1959d098c91d8a792531a723c4a9d7e9f6a01e38

COCOAPODS: 1.16.2
