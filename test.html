<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Improved Maintenance Dashboard</title>
    <style>
        :root {
            --primary: #4355b9;
            --primary-light: #eceefe;
            --secondary: #38b673;
            --warning: #f6a623;
            --danger: #e74c3c;
            --gray-light: #f5f7fa;
            --gray: #e0e0e0;
            --gray-dark: #6c757d;
            --sidebar-width: 240px;
            --header-height: 60px;
            --rejected: #ffeded;
            --rejected-text: #e74c3c;
            --accepted: #e7f8ef;
            --accepted-text: #38b673;
            --completed: #ddf5e6;
            --completed-text: #1e8a4d;
            --open: #e6f0fa;
            --open-text: #3498db;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Se<PERSON>e <PERSON>', <PERSON><PERSON><PERSON>, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: #f8f9fa;
            color: #333;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            background-color: white;
            border-right: 1px solid var(--gray);
            padding: 20px 0;
            display: flex;
            flex-direction: column;
            transition: width 0.3s;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: #666;
            text-decoration: none;
            transition: background-color 0.2s;
        }

        .sidebar-item.active {
            background-color: var(--primary-light);
            color: var(--primary);
            border-left: 4px solid var(--primary);
        }

        .sidebar-item:hover:not(.active) {
            background-color: var(--gray-light);
        }

        .sidebar-icon {
            font-size: 20px;
            margin-right: 15px;
            width: 24px;
            text-align: center;
        }

        /* Main Content Styles */
        .main-content {
            flex: 1;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .header {
            height: var(--header-height);
            background-color: white;
            border-bottom: 1px solid var(--gray);
            display: flex;
            align-items: center;
            padding: 0 20px;
            justify-content: space-between;
        }

        .user-profile {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .user-avatar {
            width: 38px;
            height: 38px;
            border-radius: 50%;
            background-color: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }

        .profile-name {
            font-weight: 500;
        }

        .header-controls {
            display: flex;
            align-items: center;
        }

        .icon-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 5px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .icon-button:hover {
            background-color: var(--gray-light);
        }

        .notification-badge {
            position: relative;
        }

        .badge {
            position: absolute;
            top: 0;
            right: 0;
            background-color: var(--danger);
            color: white;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        /* Search Bar */
        .search-container {
            display: flex;
            padding: 15px 20px;
            position: relative;
        }

        .search-box {
            flex: 1;
            display: flex;
            align-items: center;
            background-color: white;
            border: 1px solid var(--gray);
            border-radius: 8px;
            padding: 10px 15px;
            transition: box-shadow 0.2s;
        }

        .search-box:focus-within {
            box-shadow: 0 0 0 2px rgba(67, 85, 185, 0.2);
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 15px;
            padding: 0 10px;
        }

        .search-icon {
            color: var(--gray-dark);
        }

        .advanced-search {
            background-color: var(--primary-light);
            color: var(--primary);
            border: none;
            border-radius: 8px;
            padding: 10px 15px;
            margin-left: 10px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .advanced-search:hover {
            background-color: #dbe0fb;
        }

        /* Dashboard Summary */
        .dashboard-summary {
            display: flex;
            padding: 20px;
            gap: 15px;
        }

        .summary-card {
            flex: 1;
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
            display: flex;
            flex-direction: column;
        }

        .summary-title {
            color: var(--gray-dark);
            font-size: 14px;
            margin-bottom: 8px;
        }

        .summary-value {
            font-size: 28px;
            font-weight: 600;
        }

        .summary-progress {
            margin-top: 10px;
            height: 6px;
            background-color: var(--gray-light);
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            border-radius: 3px;
        }

        .progress-completed {
            background-color: var(--secondary);
        }

        .progress-pending {
            background-color: var(--warning);
        }

        .progress-rejected {
            background-color: var(--danger);
        }

        .progress-open {
            background-color: var(--primary);
        }

        /* Calendar Navigation */
        .calendar-nav {
            padding: 15px 20px;
            background-color: white;
            border-bottom: 1px solid var(--gray);
        }

        .date-selector {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .current-date {
            font-size: 20px;
            font-weight: 600;
        }

        .view-options {
            display: flex;
            gap: 10px;
        }

        .view-option {
            padding: 8px 16px;
            border-radius: 20px;
            background-color: var(--gray-light);
            cursor: pointer;
            transition: all 0.2s;
        }

        .view-option.active {
            background-color: var(--primary);
            color: white;
        }

        .days-grid {
            display: flex;
            margin-top: 15px;
            gap: 10px;
        }

        .day-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .day-item:hover:not(.active) {
            background-color: var(--gray-light);
        }

        .day-item.active {
            background-color: var(--primary);
            color: white;
        }

        .day-name {
            font-size: 13px;
            margin-bottom: 5px;
        }

        .day-number {
            font-size: 18px;
            font-weight: 500;
        }

        /* Filters */
        .filters {
            display: flex;
            padding: 15px 20px;
            gap: 10px;
            flex-wrap: wrap;
            background-color: white;
            border-bottom: 1px solid var(--gray);
        }

        .filter-item {
            display: flex;
            align-items: center;
            background-color: var(--gray-light);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-item:hover {
            background-color: #e9ecef;
        }

        .filter-item.active {
            background-color: var(--primary-light);
            color: var(--primary);
        }

        .filter-icon {
            margin-right: 6px;
        }

        /* Tasks */
        .tasks-container {
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .task-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .task-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
        }

        .task-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
            border-bottom: 1px solid var(--gray);
        }

        .task-title {
            font-weight: 500;
            font-size: 16px;
        }

        .task-priority {
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .priority-high {
            background-color: #ffeceb;
            color: var(--danger);
        }

        .task-content {
            padding: 15px;
            display: flex;
            justify-content: space-between;
        }

        .task-details {
            flex: 1;
        }

        .task-meta {
            display: flex;
            margin-bottom: 10px;
            font-size: 14px;
            color: var(--gray-dark);
        }

        .task-time {
            display: flex;
            align-items: center;
            margin-right: 20px;
        }

        .task-assignee {
            display: flex;
            align-items: center;
        }

        .meta-icon {
            margin-right: 6px;
        }

        .task-categories {
            display: flex;
            gap: 5px;
            margin-top: 12px;
        }

        .category-pill {
            width: 28px;
            height: 28px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 12px;
        }

        .category-c {
            background-color: #4caf50;
        }

        .category-i {
            background-color: #ff9800;
        }

        .category-l {
            background-color: #9c27b0;
        }

        .category-t {
            background-color: #2196f3;
        }

        .task-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .task-status {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 13px;
            font-weight: 500;
        }

        .status-rejected {
            background-color: var(--rejected);
            color: var(--rejected-text);
        }

        .status-accepted {
            background-color: var(--accepted);
            color: var(--accepted-text);
        }

        .status-completed {
            background-color: var(--completed);
            color: var(--completed-text);
        }

        .status-open {
            background-color: var(--open);
            color: var(--open-text);
        }

        .task-progress {
            width: 100px;
            height: 6px;
            background-color: var(--gray-light);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 10px;
        }

        .action-button {
            padding: 8px 15px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border: none;
            transition: all 0.2s;
        }

        .btn-accept {
            background-color: var(--secondary);
            color: white;
        }

        .btn-accept:hover {
            background-color: #32a267;
        }

        .btn-assign {
            background-color: var(--primary);
            color: white;
        }

        .btn-assign:hover {
            background-color: #394ca0;
        }

        .btn-view {
            background-color: var(--gray-light);
            color: var(--gray-dark);
        }

        .btn-view:hover {
            background-color: var(--gray);
        }

        /* Modal */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal {
            background-color: white;
            border-radius: 10px;
            width: 500px;
            max-width: 90%;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--gray);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }

        .modal-close {
            cursor: pointer;
            font-size: 20px;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid var(--gray);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .tooltip {
            position: relative;
        }

        .tooltip .tooltip-text {
            visibility: hidden;
            width: 120px;
            background-color: #333;
            color: white;
            text-align: center;
            border-radius: 4px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
        }

        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }

        /* Task Legend */
        .task-legend {
            display: flex;
            justify-content: flex-end;
            padding: 0 20px 10px;
            gap: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: var(--gray-dark);
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
            margin-right: 6px;
        }

        .legend-c {
            background-color: #4caf50;
        }

        .legend-i {
            background-color: #ff9800;
        }

        .legend-l {
            background-color: #9c27b0;
        }

        .legend-t {
            background-color: #2196f3;
        }

        /* Task Details Popup */
        .quick-view {
            position: absolute;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
            width: 350px;
            max-width: 90vw;
            z-index: 100;
            padding: 15px;
        }

        .quick-view-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .quick-view-title {
            font-weight: 600;
        }

        .quick-view-close {
            cursor: pointer;
        }

        .quick-view-content {
            font-size: 14px;
        }

        .info-group {
            margin-bottom: 10px;
        }

        .info-label {
            font-weight: 500;
            color: var(--gray-dark);
            margin-bottom: 3px;
            font-size: 12px;
        }

        .info-value {
            font-size: 14px;
        }

        /* Responsiveness */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
            }

            .sidebar-text {
                display: none;
            }

            .sidebar-icon {
                margin-right: 0;
            }

            .sidebar-item {
                padding: 15px 0;
                justify-content: center;
            }

            .summary-value {
                font-size: 20px;
            }

            .days-grid {
                overflow-x: auto;
                scrollbar-width: none;
            }

            .days-grid::-webkit-scrollbar {
                display: none;
            }

            .day-item {
                min-width: 60px;
            }

            .task-actions {
                flex-direction: column;
            }
        }

        /* Tooltip helper */
        [data-tooltip] {
            position: relative;
            cursor: help;
        }

        [data-tooltip]::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background-color: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 10;
        }

        [data-tooltip]:hover::after {
            opacity: 1;
            visibility: visible;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <a href="#" class="sidebar-item active">
                <div class="sidebar-icon">📊</div>
                <div class="sidebar-text">Dashboard</div>
            </a>
            <a href="#" class="sidebar-item tooltip">
                <div class="sidebar-icon">📋</div>
                <div class="sidebar-text">Rounds</div>
                <span class="tooltip-text">Inspection Rounds</span>
            </a>
            <a href="#" class="sidebar-item tooltip">
                <div class="sidebar-icon">⚠️</div>
                <div class="sidebar-text">Fault</div>
                <span class="tooltip-text">Report Faults</span>
            </a>
            <a href="#" class="sidebar-item tooltip">
                <div class="sidebar-icon">🔧</div>
                <div class="sidebar-text">Job</div>
                <span class="tooltip-text">Maintenance Jobs</span>
            </a>
            <a href="#" class="sidebar-item tooltip">
                <div class="sidebar-icon">🏭</div>
                <div class="sidebar-text">Asset</div>
                <span class="tooltip-text">Asset Management</span>
            </a>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <div class="user-profile">
                    <div class="user-avatar">👤</div>
                    <div class="profile-name">Today</div>
                </div>
                <div class="header-controls">
                    <div class="icon-button">⚙️</div>
                    <div class="icon-button">➕</div>
                    <div class="icon-button notification-badge">
                        🔔
                        <span class="badge">3</span>
                    </div>
                    <div class="icon-button">⋮</div>
                </div>
            </div>

            <!-- Search -->
            <div class="search-container">
                <div class="search-box">
                    <span class="search-icon">🔍</span>
                    <input type="text" class="search-input" placeholder="Search tasks, assets, or assignees...">
                    <span class="search-icon">✖️</span>
                </div>
                <button class="advanced-search">Filters</button>
            </div>

            <!-- Dashboard Summary -->
            <div class="dashboard-summary">
                <div class="summary-card">
                    <div class="summary-title">Completed</div>
                    <div class="summary-value">24</div>
                    <div class="summary-progress">
                        <div class="progress-bar progress-completed" style="width: 42%;"></div>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-title">Pending</div>
                    <div class="summary-value">18</div>
                    <div class="summary-progress">
                        <div class="progress-bar progress-pending" style="width: 31%;"></div>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-title">Rejected</div>
                    <div class="summary-value">5</div>
                    <div class="summary-progress">
                        <div class="progress-bar progress-rejected" style="width: 9%;"></div>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-title">Open</div>
                    <div class="summary-value">10</div>
                    <div class="summary-progress">
                        <div class="progress-bar progress-open" style="width: 18%;"></div>
                    </div>
                </div>
            </div>

            <!-- Calendar Navigation -->
            <div class="calendar-nav">
                <div class="date-selector">
                    <div class="current-date">May 14, 2025</div>
                    <div class="view-options">
                        <div class="view-option">Day</div>
                        <div class="view-option active">Week</div>
                        <div class="view-option">Month</div>
                    </div>
                </div>
                <div class="days-grid">
                    <div class="day-item">
                        <div class="day-name">Tue</div>
                        <div class="day-number">13</div>
                    </div>
                    <div class="day-item active">
                        <div class="day-name">Wed</div>
                        <div class="day-number">14</div>
                    </div>
                    <div class="day-item">
                        <div class="day-name">Thu</div>
                        <div class="day-number">15</div>
                    </div>
                    <div class="day-item">
                        <div class="day-name">Fri</div>
                        <div class="day-number">16</div>
                    </div>
                    <div class="day-item">
                        <div class="day-name">Sat</div>
                        <div class="day-number">17</div>
                    </div>
                    <div class="day-item">
                        <div class="day-name">Sun</div>
                        <div class="day-number">18</div>
                    </div>
                    <div class="day-item">
                        <div class="day-name">Mon</div>
                        <div class="day-number">19</div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="filters">
                <div class="filter-item active">
                    <span class="filter-icon">🔍</span>All
                </div>
                <div class="filter-item">
                    <span class="filter-icon">✅</span>Completed
                </div>
                <div class="filter-item">
                    <span class="filter-icon">⏱️</span>Pending
                </div>
                <div class="filter-item">
                    <span class="filter-icon">❌</span>Rejected
                </div>
                <div class="filter-item">
                    <span class="filter-icon">📂</span>Open
                </div>
                <div class="filter-item">
                    <span class="filter-icon">🔥</span>High Priority
                </div>
                <div class="filter-item">
                    <span class="filter-icon">👤</span>My Tasks
                </div>
            </div>

            <!-- Task Legend -->
            <div class="task-legend">
                <div class="legend-item" data-tooltip="Cleaning">
                    <div class="legend-color legend-c"></div>C
                </div>
                <div class="legend-item" data-tooltip="Inspection">
                    <div class="legend-color legend-i"></div>I
                </div>
                <div class="legend-item" data-tooltip="Lubrication">
                    <div class="legend-color legend-l"></div>L
                </div>
                <div class="legend-item" data-tooltip="Testing">
                    <div class="legend-color legend-t"></div>T
                </div>
            </div>

            <!-- Tasks -->
            <div class="tasks-container">
                <!-- Task 1 -->
                <div class="task-card">
                    <div class="task-header">
                        <h3 class="task-title">Conveyor Roller Maintenance Plan</h3>
                        <span class="task-priority priority-high">High Priority</span>
                    </div>
                    <div class="task-content">
                        <div class="task-details">
                            <div class="task-meta">
                                <div class="task-time">
                                    <span class="meta-icon">🕒</span> 07:00 - 10:00
                                </div>
                                <div class="task-assignee">
                                    <span class="meta-icon">👤</span> Ranjit Sahu
                                </div>
                            </div>
                            <div class="task-categories">
                                <div class="category-pill category-c">C</div>
                                <div class="category-pill category-i">I</div>
                                <div class="category-pill category-l">L</div>
                                <div class="category-pill category-t">T</div>
                            </div>
                            <div class="task-progress">
                                <div class="progress-bar" style="width: 0%; background-color: #e74c3c;"></div>
                            </div>
                        </div>
                        <div class="task-actions">
                            <span class="task-status status-rejected">Rejected</span>
                            <button class="action-button btn-view">View Details</button>
                        </div>
                    </div>
                </div>

                <!-- Task 2 -->
                <div class="task-card">
                    <div class="task-header">
                        <h3 class="task-title">Conveyor Roller Maintenance Plan</h3>
                        <span class="task-priority priority-high">High Priority</span>
                    </div>
                    <div class="task-content">
                        <div class="task-details">
                            <div class="task-meta">
                                <div class="task-time">
                                    <span class="meta-icon">🕒</span> 11:00 - 14:00
                                </div>
                                <div class="task-assignee">
                                    <span class="meta-icon">👤</span> Ranjit Sahu
                                </div>
                            </div>
                            <div class="task-categories">
                                <div class="category-pill category-c">C</div>
                                <div class="category-pill category-i">I</div>
                                <div class="category-pill category-l">L</div>
                                <div class="category-pill category-t">T</div>
                            </div>
                            <div class="task-progress">
                                <div class="progress-bar" style="width: 92%; background-color: #38b673;"></div>
                            </div>
                        </div>
                        <div class="task-actions">
                            <span class="task-status status-accepted">Accepted</span>
                            <button class="action-button btn-view">View Details</button>
                        </div>
                    </div>
                </div>

                <!-- Task 3 -->