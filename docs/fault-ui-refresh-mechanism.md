# Fault UI Refresh Mechanism

## Overview
This document describes the automatic UI refresh mechanism implemented for fault cards in the list panel when fault status changes occur (completion, assignment, etc.).

## Problem Statement
When a fault completion process finishes, the fault list panel cards need to be updated automatically to reflect the new status. This applies to any value displayed on the list card - when it's updated successfully in the database, the card U<PERSON> needs to be refreshed.

## Solution Implementation

### Core Components

#### 1. Fault List Provider
- **Provider**: `faultHeaderListProvider`
- **Location**: `lib/providers/fault/fault_header_provider.dart`
- **Method**: `fetchFaultHeaderList(String plant)`
- **Purpose**: Fetches the latest fault data from the database

#### 2. Filtered Fault List Provider
- **Provider**: `filteredFaultHeaderListProvider`
- **Location**: `lib/pages/fault/fault_filter_provider.dart`
- **Method**: `filteredFaultHeaderList(...)`
- **Purpose**: Applies filters and search to the fault list

### Implementation Locations

#### ✅ Fault Detail Screen (fault_detail_screen.dart)
- **Location**: Lines 854-855 and 874-875 in `sendToServer()` method
- **Trigger**: After fault completion or job assignment
- **Implementation**: 
  ```dart
  final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
  await faultHeaderList.fetchFaultHeaderList(plant);
  ```

#### ✅ Fault Card (fault_card.dart)
- **Location**: Lines 885-886 in `sendToServer()` method
- **Trigger**: After fault completion or job assignment from card actions
- **Implementation**: Same as above

#### ✅ Modern Fault Detail View (modern_fault_detail_view.dart)
- **Location**: Lines 2311-2347 in `_sendToServer()` method
- **Trigger**: After fault completion or job assignment
- **Implementation**:
  ```dart
  // Refresh the individual fault header to update the detail view
  final faultHeaderData = ref.read(faultHeaderProvider.notifier);
  await faultHeaderData.getFaultHeader(faultId: faultHeader.fault_id.toString());

  // Refresh the fault list to update the UI with new status
  final plant = ref.read(plantProvider);
  final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
  await faultHeaderList.fetchFaultHeaderList(plant);

  // Also refresh filtered list if filters are applied
  final filteredFaultType = ref.read(filteredFaultHeaderListProvider.notifier);
  // ... filter logic
  ```

### Related Components

#### Job Creation and Assignment
- **job_creation_detail_screen.dart**: Lines 928-930 - Refreshes fault list after job creation
- **job_card.dart**: Lines 3418-3419 - Refreshes fault list after job completion
- **job_creation_card.dart**: Lines 1645-1650 - Updates fault status after job assignment

### Dual Refresh Pattern

The implementation uses a **dual refresh pattern** to ensure both the detail view and list view are updated:

1. **Individual Fault Header Refresh** - Updates the detail view
2. **Fault List Refresh** - Updates the list panel cards

### Refresh Pattern

The standard pattern for refreshing fault UI after status changes:

```dart
// 1. Refresh individual fault header (for detail view)
final faultHeaderData = ref.read(faultHeaderProvider.notifier);
await faultHeaderData.getFaultHeader(faultId: faultHeader.fault_id.toString());

// 2. Get current plant context
final plant = ref.read(plantProvider);

// 3. Refresh main fault list (for list panel)
final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
await faultHeaderList.fetchFaultHeaderList(plant);

// 4. Refresh filtered list if filters are active
final filteredFaultType = ref.read(filteredFaultHeaderListProvider.notifier);
final filterOfFaultCode = ref.read(filterOfFaultCodeProvider);
final filterOfPriorityCode = ref.read(filterOfFaultPriorityCodeProvider);
final statusTypeFilter = ref.read(statusTypeFaultFilterProvider);
final plantSection = ref.read(plantSectionProvider);
final search = ref.read(searchTextProvider);

// Apply filters if any are active
if ((search != '') ||
    filterOfFaultCode.isNotEmpty ||
    filterOfPriorityCode.isNotEmpty ||
    statusTypeFilter.isNotEmpty) {
  await filteredFaultType.filteredFaultHeaderList(
      faulttypeList: filterOfFaultCode,
      priorityList: filterOfPriorityCode,
      statusList: statusTypeFilter,
      type: (search != '') ? AppConstants.search : AppConstants.faultType,
      plantId: plant,
      plantSec: plantSection,
      search: search);
} else {
  final currentFaultList = ref.read(faultHeaderListProvider);
  await filteredFaultType.filteredFaultHeaderList(
      type: 'Initial',
      faultList: currentFaultList,
      plantId: plant,
      plantSec: plantSection);
}
```

### Status Change Triggers

The fault list refresh is triggered after these operations:
1. **Fault Completion** - Status changes to "Completed"
2. **Job Assignment** - Status changes to "Job Assigned"
3. **Job Completion** - Related fault status may change
4. **Sync Operations** - After server synchronization

### Benefits

1. **Real-time UI Updates**: Users see status changes immediately
2. **Consistent Experience**: Same behavior across all fault interaction points
3. **Filter Preservation**: Active filters are maintained after refresh
4. **Data Integrity**: UI always reflects the current database state

### Future Considerations

- Consider implementing a more centralized state management approach
- Evaluate using streams or listeners for automatic updates
- Optimize refresh frequency to avoid unnecessary database calls
- Consider partial updates for better performance

## Testing

To verify the implementation:
1. Open a fault in any view (detail screen, card, modern view)
2. Complete the fault or assign a job
3. Verify that the fault list immediately shows the updated status
4. Test with various filter combinations active
5. Verify behavior on both web and mobile platforms
