PODS:
  - connectivity_plus (0.0.1):
    - FlutterMacOS
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - Firebase/CoreOnly (10.22.0):
    - FirebaseCore (= 10.22.0)
  - Firebase/Messaging (10.22.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.22.0)
  - firebase_core (2.27.0):
    - Firebase/CoreOnly (~> 10.22.0)
    - FlutterMacOS
  - firebase_messaging (14.7.19):
    - Firebase/CoreOnly (~> 10.22.0)
    - Firebase/Messaging (~> 10.22.0)
    - firebase_core
    - FlutterMacOS
  - FirebaseCore (10.22.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.22.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - geolocator_apple (1.2.0):
    - FlutterMacOS
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - ReachabilitySwift (5.2.4)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SQLCipher (4.5.7):
    - SQLCipher/standard (= 4.5.7)
  - SQLCipher/common (4.5.7)
  - SQLCipher/standard (4.5.7):
    - SQLCipher/common
  - sqlcipher_flutter_libs (0.0.1):
    - FlutterMacOS
    - SQLCipher (~> 4.5.7)
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - firebase_core (from `Flutter/ephemeral/.symlinks/plugins/firebase_core/macos`)
  - firebase_messaging (from `Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - geolocator_apple (from `Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqlcipher_flutter_libs (from `Flutter/ephemeral/.symlinks/plugins/sqlcipher_flutter_libs/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - video_player_avfoundation (from `Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - ReachabilitySwift
    - SQLCipher

EXTERNAL SOURCES:
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  firebase_core:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_core/macos
  firebase_messaging:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  geolocator_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqlcipher_flutter_libs:
    :path: Flutter/ephemeral/.symlinks/plugins/sqlcipher_flutter_libs/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  video_player_avfoundation:
    :path: Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin

SPEC CHECKSUMS:
  connectivity_plus: 18d3c32514c886e046de60e9c13895109866c747
  device_info_plus: 5401765fde0b8d062a2f8eb65510fb17e77cf07f
  file_selector_macos: 54fdab7caa3ac3fc43c9fac4d7d8d231277f8cf2
  Firebase: 797fd7297b7e1be954432743a0b3f90038e45a71
  firebase_core: 94cef1d79e26747286a4808b2345c1ecb8501b11
  firebase_messaging: b243cd1026362f3f5ec5e73ffb0c4431519f0b57
  FirebaseCore: 0326ec9b05fbed8f8716cddbf0e36894a13837f7
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 9f71037fd9db3376a4caa54e5a3949d1027b4b6e
  flutter_local_notifications: 3805ca215b2fb7f397d78b66db91f6a747af52e4
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  geolocator_apple: 72a78ae3f3e4ec0db62117bd93e34523f5011d58
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  package_info_plus: 02d7a575e80f194102bef286361c6c326e4c29ce
  path_provider_foundation: 3784922295ac71e43754bd15e0653ccfd36a147c
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  shared_preferences_foundation: b4c3b4cddf1c21f02770737f147a3f5da9d39695
  SQLCipher: 5e6bfb47323635c8b657b1b27d25c5f1baf63bf5
  sqlcipher_flutter_libs: db7047866b877c027b050bb808b5480c68955c5e
  url_launcher_macos: 5f437abeda8c85500ceb03f5c1938a8c5a705399
  video_player_avfoundation: 02011213dab73ae3687df27ce441fbbcc82b5579

PODFILE CHECKSUM: 236401fc2c932af29a9fcf0e97baeeb2d750d367

COCOAPODS: 1.16.2
