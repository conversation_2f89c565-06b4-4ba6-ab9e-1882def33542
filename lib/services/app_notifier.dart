import 'dart:developer';

import 'package:unvired_sdk/unvired_sdk.dart';

class AppNotifier {
  notifySyncStatus(Function callback) {
    DartNotificationCenter.registerChannel(channel: EventNameSyncStatus);
    DartNotificationCenter.subscribe(
        channel: EventNameSyncStatus,
        observer: this,
        onNotification: (data) async {
          var sentItemsCount =
              data[EventSyncStatusFieldSentItemsCount]; //sent items count
          var inboxCount = data[EventSyncStatusFieldInboxCount]; //inbox count
          var outboxCount =
              data[EventSyncStatusFieldOutboxCount]; //outbox count

          var type = data[EventSyncStatusFieldType]; //sync status type
          callback(data);
          switch (type) {
            //sent
            case EventSyncStatusTypeSent:
              var beName = data[EventFieldData][EventFieldBeName]; //BE name
              var lid = data[EventFieldData][EventFieldBeLid]; // LID

              break;

            //inbox
            case EventSyncStatusTypeInbox:
              var processedHeaders =
                  data[EventFieldData]; // processed inbox headers
              break;

            case EventSyncStatusTypeReceived:
              var conversationId =
                  data[EventSyncStatusFieldConversationId]; // conversation id
              break;
          }

          if ((type == EventSyncStatusTypeInbox &&
              (inboxCount == 0 && sentItemsCount == 0))) {
          } else if (type == EventSyncStatusTypeSent) {
          } else {}
        });
  }

  notifyAttachmentStatus(Function callback) {
    DartNotificationCenter.registerChannel(channel: EventNameAttachmentStatus);
    DartNotificationCenter.subscribe(
        channel: EventNameAttachmentStatus,
        observer: this,
        onNotification: (data) async {
          callback(data);
          //Attachment error
          if (data[EventAttachmentStatusFieldStatus] ==
              EventAttachmentStatusError) {
            var beName = data[EventFieldData][EventFieldBeName]; //BE name
            var uid = data[EventFieldData][EventAttachmentStatusFieldUid]; //uid
            var errorCode =
                data[EventFieldError][EventFieldErrorCode]; //error code
            var errorMessage =
                data[EventFieldError][EventFieldErrorMessage]; //error message
          }

          //Attachment success
          if (data[EventAttachmentStatusFieldStatus] ==
              EventAttachmentStatusError) {
            var beName = data[EventFieldData][EventFieldBeName]; //BE name
            var uid = data[EventFieldData][EventAttachmentStatusFieldUid]; //uid
          }
        });
  }

  notifyAttachmentStatusUnsubscribe() {
    DartNotificationCenter.unsubscribe(observer: this);
    DartNotificationCenter.unregisterChannel(
        channel: EventNameAttachmentStatus);
  }

  unSubscribeNotifySyncStatus() {
    DartNotificationCenter.unsubscribe(
      observer: this,
      channel: EventNameSyncStatus,
    );
  }

  unSubscribeNotifyAttachmentStatus() {
    DartNotificationCenter.unsubscribe(
      observer: this,
      channel: EventNameAttachmentStatus,
    );
  }

  unSubscribeInfoMessage() {
    DartNotificationCenter.unsubscribe(
      observer: this,
      channel: EventNameInfoMessages,
    );
  }

  notifyConnectionStatus(Function callback) {
    DartNotificationCenter.registerChannel(channel: EventNameConnectionStatus);
    DartNotificationCenter.subscribe(
        channel: EventNameConnectionStatus,
        observer: this,
        onNotification: (data) async {
          callback({"event": "ConnectionStatus", "data": data});
        });
  }

  notifyInfoMessages(Function callback) {
    DartNotificationCenter.registerChannel(channel: EventNameInfoMessages);
    DartNotificationCenter.subscribe(
        channel: EventNameInfoMessages,
        observer: this,
        onNotification: (data) async {
          callback(data);
        });
  }

  notifySystemError(Function callback) {
    DartNotificationCenter.registerChannel(channel: EventNameSystemError);
    DartNotificationCenter.subscribe(
        channel: EventNameSystemError,
        observer: this,
        onNotification: (data) async {
          callback({"event": "SystemError", "data": data});
        });
  }
}
