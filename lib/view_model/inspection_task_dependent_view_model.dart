// // Step 1: State class
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:logger/Logger.dart';
//
// import '../be/INSPECTION_SECTION.dart';
// import '../be/INSPECTION_TASK.dart';
// import '../be/INSP_EXEC_HEADER.dart';
// import '../helpers/db_helper.dart';
//
// class InspectionTaskState {
//   final bool isLoading;
//   final List<INSPECTION_TASK> allTasks;
//   final List<INSPECTION_TASK> filteredTasks;
//
//   InspectionTaskState({
//     this.isLoading = false,
//     this.allTasks = const [],
//     this.filteredTasks = const [],
//   });
//
//   InspectionTaskState copyWith({
//     bool? isLoading,
//     List<INSPECTION_TASK>? allTasks,
//     List<INSPECTION_TASK>? filteredTasks,
//   }) {
//     return InspectionTaskState(
//       isLoading: isLoading ?? this.isLoading,
//       allTasks: allTasks ?? this.allTasks,
//       filteredTasks: filteredTasks ?? this.filteredTasks,
//     );
//   }
// }
//
// // Step 2: StateNotifier class
// class InspectionTaskNotifier extends StateNotifier<InspectionTaskState> {
//   InspectionTaskNotifier() : super(InspectionTaskState());
//
//   Future<void> fetchTasksBySection(INSPECTION_SECTION section) async {
//     try {
//       state = state.copyWith(isLoading: true);
//       final tasks = await DbHelper.getInspectionTaskPlanListHeader(
//         section.plan_id!.toString(),
//         section.section_id!.toString(),
//       );
//       state = state.copyWith(
//         isLoading: false,
//         allTasks: tasks,
//         filteredTasks: tasks,
//       );
//     } catch (e) {
//       Logger.logError(
//           'InspectionTaskNotifier', 'fetchTasksBySection', e.toString());
//       state = state.copyWith(isLoading: false);
//     }
//   }
//
//   Future<void> fetchTasksByPlanId(String planId) async {
//     try {
//       state = state.copyWith(isLoading: true);
//       final tasks =
//           await DbHelper.getInspectionTaskPlanListHeaderByPlanId(planId);
//       state = state.copyWith(
//         isLoading: false,
//         allTasks: tasks,
//         filteredTasks: tasks,
//       );
//     } catch (e) {
//       Logger.logError(
//           'InspectionTaskNotifier', 'fetchTasksByPlanId', e.toString());
//       state = state.copyWith(isLoading: false);
//     }
//   }
//
//   Future<void> fetchIncompleteTasks(INSP_EXEC_HEADER header) async {
//     try {
//       final tasks = await DbHelper.getIncompleteInspectionTasks(header);
//       state = state.copyWith(
//         allTasks: tasks,
//         filteredTasks: tasks,
//       );
//     } catch (e) {
//       Logger.logError(
//           'InspectionTaskNotifier', 'fetchIncompleteTasks', e.toString());
//     }
//   }
//
//   void filter(String searchString) {
//     final tasks = state.allTasks;
//     if (searchString.isEmpty) {
//       state = state.copyWith(filteredTasks: tasks);
//       return;
//     }
//
//     final pattern = searchString.contains('*')
//         ? RegExp(searchString.replaceAll('*', '.*').toLowerCase())
//         : null;
//
//     final keywords = searchString.toLowerCase().split(' ');
//
//     final filtered = tasks.where((task) {
//       final title = task.title?.toLowerCase() ?? '';
//       final desc =
//           task.description != null ? task.description!.toLowerCase() : '';
//       final asset = task.asset_no != null ? (task.asset_no!.toString()) : '';
//       final loc = task.location_id != null ? task.location_id!.toString() : '';
//       final id = task.task_id != null ? task.task_id!.toString() : '';
//
//       if (pattern != null) {
//         return pattern.hasMatch(title) ||
//             pattern.hasMatch(desc) ||
//             pattern.hasMatch(asset) ||
//             pattern.hasMatch(loc) ||
//             pattern.hasMatch(id);
//       } else {
//         return keywords.every((k) =>
//             title.contains(k) ||
//             desc.contains(k) ||
//             asset.contains(k) ||
//             loc.contains(k) ||
//             id.contains(k));
//       }
//     }).toList();
//
//     state = state.copyWith(filteredTasks: filtered);
//   }
//
//   void updateTask(INSPECTION_TASK updatedTask) {
//     final index = state.allTasks.indexWhere((e) => e.lid == updatedTask.lid);
//     if (index != -1) {
//       final updatedAll = [...state.allTasks];
//       updatedAll[index] = updatedTask;
//       state = state.copyWith(allTasks: updatedAll);
//       filter(''); // reset filter to reflect changes
//     }
//   }
//
//   Future<void> reset(String planId) async {
//     await fetchTasksByPlanId(planId);
//   }
// }
//
// // Step 3: Provider
// final inspectionTaskNotifier = StateNotifierProvider.family<
//     InspectionTaskNotifier, InspectionTaskState, INSPECTION_SECTION>(
//   (ref, section) {
//     final notifier = InspectionTaskNotifier();
//     notifier.fetchTasksBySection(section);
//     return notifier;
//   },
// );
