import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/INSPECTION_TASK.dart';
import 'package:rounds/be/INSP_EXEC_HEADER.dart';

import '../be/INSPECTION_PLAN_HEADER.dart';
import '../be/INSPECTION_SECTION.dart';
import '../be/INSP_EXEC_TASK.dart';
import '../helpers/db_helper.dart';

final taskResponseProvider = StateNotifierProvider.autoDispose
    .family<TaskVisibilityViewModel, Set<String>, List<INSPECTION_TASK>>(
  (ref, allTasks) => TaskVisibilityViewModel(allTasks),
);

class TaskVisibilityViewModel extends StateNotifier<Set<String>> {
  TaskVisibilityViewModel(this._allTasks) : super({}) {
    _initializeVisibleTasks();
  }

  final List<INSPECTION_TASK> _allTasks;

  final Map<String, dynamic> _taskResults = {};

  void _initializeVisibleTasks() {
    for (final task in _allTasks) {
      if (task.dependent != 'true') {
        state = {...state, task.task_id.toString()};
      }
    }
  }

  void updateTaskResult(String taskId, dynamic value) {
    _taskResults[taskId] = value;
    _evaluateVisibility();
  }

  void _evaluateVisibility() {
    final newVisibleTasks = <String>{};
    for (final task in _allTasks) {
      final taskId = task.task_id.toString();
      final isDependent = task.dependent == 'true';
      if (isDependent) {
        final dependsOn = task.dep_task_id;
        final expectedCode = task.dep_cond_code;
        final expectedValue = task.dep_cond_val;
        final actualValue = _taskResults[dependsOn];

        if (shouldShowTask(
          //   type:task.task_type.toString(),
          actual: actualValue,
          expected: expectedValue,
          operatorCode: expectedCode,
        )) {
          newVisibleTasks.add(taskId);
        }
/*        bool shouldDisplay = false;

        if (expectedCode == 'if') {
          shouldDisplay = actualValue == expectedValue;
        } else if (expectedCode == 'if not') {
          shouldDisplay = actualValue != expectedValue;
        } else {
          shouldDisplay = actualValue == expectedValue;
        }

        if (shouldDisplay) {
          newVisibleTasks.add(taskId);
        }
        */

/*        if (actualValue != null &&
            actualValue.toString() == expectedValue.toString()) {
          newVisibleTasks.add(taskId);
        }*/
      } else {
        newVisibleTasks.add(taskId);
      }
    }
    state = newVisibleTasks;
  }

  bool shouldShowTask({
    //  required String type,
    required String? actual,
    required String? expected,
    required String? operatorCode,
  }) {
    if (actual == null || expected == null || operatorCode == null) {
      return false;
    }

    final actualVal = actual;
    final expectedVal = expected;
    final op = operatorCode;

    late List<String> expectedValues;
    try {
      final parsed = jsonDecode(expected);
      if (parsed is List) {
        expectedValues = parsed.map((e) => e.toString()).toList();
      } else {
        expectedValues = [parsed.toString()];
      }
    } catch (_) {
      expectedValues = [expected];
    }

    switch (op) {
      case 'if':
        return expectedValues.contains(actualVal);
      case 'di':
        return !expectedValues.contains(actualVal);
      case 'e':
        return actualVal == expectedVal;

      case 'ne':
        return actualVal != expectedVal;

      case 'gt':
        return double.tryParse(actualVal) != null &&
            double.tryParse(expectedVal) != null &&
            double.parse(actualVal) > double.parse(expectedVal);

      case 'ng':
        return double.tryParse(actualVal) != null &&
            double.tryParse(expectedVal) != null &&
            double.parse(actualVal) <= double.parse(expectedVal);

      case 'lt':
        return double.tryParse(actualVal) != null &&
            double.tryParse(expectedVal) != null &&
            double.parse(actualVal) < double.parse(expectedVal);

      case 'nl':
        return double.tryParse(actualVal) != null &&
            double.tryParse(expectedVal) != null &&
            double.parse(actualVal) >= double.parse(expectedVal);

      case 'bt':
        final parts = expectedVal.split('-');
        if (parts.length == 2) {
          final min = double.tryParse(parts[0]);
          final max = double.tryParse(parts[1]);
          final actualNum = double.tryParse(actualVal);
          if (min != null && max != null && actualNum != null) {
            return actualNum >= min && actualNum <= max;
          }
        }
        return false;

      case 'nb':
        final parts = expectedVal.split('-');
        if (parts.length == 2) {
          final min = double.tryParse(parts[0]);
          final max = double.tryParse(parts[1]);
          final actualNum = double.tryParse(actualVal);
          if (min != null && max != null && actualNum != null) {
            return actualNum < min || actualNum > max;
          }
        }
        return false;

      default:

        // if(type != 'e' || type != 'o' || type != 'b'){
        //   return true;
        // }else{
        return actualVal == expectedVal;
      //}
      // fallback to equals
    }
  }
}

/*
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../be/INSPECTION_TASK.dart';

final taskResponseProvider = Provider<TaskResponseViewModel>((ref) {
  return TaskResponseViewModel();
});

class TaskResponseViewModel {
  final Map<int, dynamic> _taskResponses = {};

  void setResponse(int taskId, dynamic value) {
    _taskResponses[taskId] = value;
  }

  dynamic getResponse(int taskId) => _taskResponses[taskId];

  bool isTaskVisible(INSPECTION_TASK task, List<INSPECTION_TASK> allTasks) {
    if (task.dependent != 'true') return true;

    final depTaskId = task.dep_task_id;
    final depResponse = _taskResponses[depTaskId];
    if (depResponse == null) return false;

    final dependentCode = task.dep_cond_code?.trim();

    final depTask = allTasks.firstWhere(
      (t) => t.task_id == depTaskId,
      orElse: () =>
          INSPECTION_TASK(plan_id: null, task_id: null, section_id: null),
    );

    if (depTask.task_type == 'b') {
      final double? expectedValue = double.tryParse(dependentCode ?? '');
      return expectedValue != null && depResponse == expectedValue;
    }
    if (depTask.task_type== 'm') {
      final double? expectedValue = double.tryParse(dependentCode ?? '');
      return expectedValue != null && depResponse == expectedValue;
    }
    if (depTask.task_type == 'e') {
      final double? expectedValue = double.tryParse(dependentCode ?? '');
      return expectedValue != null && depResponse == expectedValue;
    }

    return depResponse.toString().toLowerCase() == dependentCode?.toLowerCase();
  }

  List<INSPECTION_TASK> getVisibleTasks(List<INSPECTION_TASK> allTasks) {
    List<INSPECTION_TASK> visible = [];

    for (final task in allTasks) {
      if (isTaskVisible(task, allTasks)) {
        visible.add(task);
      }
    }

    return visible;
  }

  Map<int, dynamic> get responses => _taskResponses;
}
*/

class InspectionTaskView {
  String? _search;
  INSPECTION_PLAN_HEADER? _inspectionPlanHeader;
  INSP_EXEC_HEADER? _inspectionExecHeader;
  List<INSPECTION_SECTION>? _inspectionSections;
  List<INSPECTION_TASK>? _inspectionTasks;
  bool _isToggle;

  InspectionTaskView({
    String? search,
    INSPECTION_PLAN_HEADER? inspectionPlanHeader,
    INSP_EXEC_HEADER? inspectionExecHeader,
    List<INSPECTION_SECTION>? inspectionSections,
    List<INSPECTION_TASK>? inspectionTasks,
    bool isToggle = false,
  })  : _search = search,
        _inspectionPlanHeader = inspectionPlanHeader,
        _inspectionExecHeader = inspectionExecHeader,
        _inspectionSections = inspectionSections,
        _inspectionTasks = inspectionTasks,
        _isToggle = isToggle;

  // Getters
  String? get search => _search;
  INSPECTION_PLAN_HEADER? get inspectionPlanHeader => _inspectionPlanHeader;
  INSP_EXEC_HEADER? get inspectionExecHeader => _inspectionExecHeader;
  List<INSPECTION_SECTION>? get inspectionSections => _inspectionSections;
  List<INSPECTION_TASK>? get inspectionTasks => _inspectionTasks;
  bool get isToggle => _isToggle;

  // Setters
  set search(String? value) => _search = value;
  set inspectionPlanHeader(INSPECTION_PLAN_HEADER? value) =>
      _inspectionPlanHeader = value;
  set inspectionExecHeader(INSP_EXEC_HEADER? value) =>
      _inspectionExecHeader = value;
  set inspectionSections(List<INSPECTION_SECTION>? value) =>
      _inspectionSections = value;
  set inspectionTasks(List<INSPECTION_TASK>? value) => _inspectionTasks = value;
  set isToggle(bool value) => _isToggle = value;
}

final inspectionTaskViewProvider = StateProvider<InspectionTaskView>((ref) {
  return InspectionTaskView(
    search: null,
    inspectionPlanHeader: null,
    inspectionExecHeader: null,
    inspectionSections: [],
    inspectionTasks: [],
    isToggle: false,
  );
});

class InspectionTaskViewNotifier extends StateNotifier<Set<String>> {
  InspectionTaskViewNotifier() : super({}) {
    void initInspectionViewprovider(
        {required INSPECTION_PLAN_HEADER? inspectionPlanHeader,
        required INSP_EXEC_HEADER? inspHeader}) {}

    Future<void> getInspExecuteTaskList() async {
      try {
        List<INSP_EXEC_TASK> executeHeaders =
            await DbHelper.getAllInspExecTaskList();
      } catch (e) {
        Logger.logError('CiltExecuteTaskListNotifier', 'getCiltExecuteTaskList',
            e.toString());
      }
    }
  }
}
