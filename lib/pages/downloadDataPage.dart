import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/widgets/dashboard_shimmer.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import '../helpers/pa_helper.dart';
import '../providers/assets/asset_provider.dart';
import '../providers/assets/floc_provider.dart';
import '../providers/assets/kpi_provider.dart';
import '../providers/attachments/attachment_provider.dart';
import '../providers/cilt/cilt_header_provider.dart';
import '../providers/cilt/cilt_plan_header_provider.dart';
import '../providers/fault/fault_header_provider.dart';
import '../providers/fault/fault_type_provider.dart';
import '../providers/inspection/inspection_header_provider.dart';
import '../providers/inspection/inspection_plan_header_provider.dart';
import '../providers/job_creation/job_header_provider.dart';
import '../providers/progress_provider.dart';
import '../providers/user_provider.dart';
import '../services/app_notifier.dart';
import 'dashboard/dashboard.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import 'inspection/inspection_list_screen.dart';

class DownloadDataPage extends ConsumerStatefulWidget {
  const DownloadDataPage({super.key});

  @override
  ConsumerState<DownloadDataPage> createState() => _DownloadDataPageState();
}

class _DownloadDataPageState extends ConsumerState<DownloadDataPage>
    with SingleTickerProviderStateMixin {
  late Future<void> _downloadFuture;
  bool _isLoading = true;
  String errorMessage = '';
  bool customizationDownloaded = false;
  AppNotifier _appNotifier = AppNotifier();
  late double screenWidth;
  late double screenHeight;
  double percentage = 0.0;

  late AnimationController _animationController;
  late Animation<Offset> _offsetAnimation;

  @override
  void initState() {
    // TODO: implement initState

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _offsetAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.0, -0.5), // Moves up
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    if (kIsWeb) {
      _downloadAllDatainSync();
    } else {
      _downloadAllDatainAsync();
    }
    _initNotifier();
    super.initState();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.width;
    if (kIsWeb) {
      return Scaffold(
        body: FutureBuilder<void>(
          future: _downloadFuture,
          builder: (context, snapshot) {
            bool isDone = snapshot.connectionState == ConnectionState.done;
            if (isDone) {
              _animationController.forward(); // Play upward animation
            }
            return AnimatedSwitcher(
              duration: const Duration(milliseconds: 600),
              transitionBuilder: (child, animation) =>
                  FadeTransition(opacity: animation, child: child),
              child: isDone
                  ? const Dashboard(key: ValueKey('dashboard'))
                  : AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return SlideTransition(
                            position: _offsetAnimation,
                            child: DashboardShimmer());
                      },
                    ),
            );
          },
        ),
      );
    } else {
      return const Dashboard();
    }
  }

  Future<void> _downloadAllDatainAsync() async {
    setState(() {
      _isLoading = true;
      errorMessage = "";
    });
    List<Future> futures = [
      PAHelper.getLocationInAsyncMode(context),
      PAHelper.getAssetInAsyncMode(context),
      PAHelper.getKpiInAsyncMode(context),
      PAHelper.getInspectionPlanInAsyncMode(context),
      PAHelper.getCiltPlanInAsyncMode(context),
      PAHelper.getInspectionInAsyncMode(context),
      PAHelper.getCiltInAsyncMode(context),
      PAHelper.getFaultInAsyncMode(context),
      PAHelper.getJobInAsyncMode(context),
    ];
    try {
      await Future.wait(futures);
    } catch (error, s) {
      setState(() {
        errorMessage =
            "${AppLocalizations.of(context)!.failed_to_downalod_data} $error";
      });
      UIHelper.showErrorDialogWithStackStrace(context,
          title: "Error While Downloading",
          stackTrace: s.toString(),
          description: errorMessage.toString());
    } finally {
      // setState(() {
      //   errorMessage = "";
      // });
    }
  }

  Future<void> _downloadAllDatainSync() async {
    setState(() {
      _isLoading = true;
      errorMessage = "";
    });

    List<Future> futures = [
      PAHelper.getLocationInSyncMode(context),
      PAHelper.getAssetInSyncMode(context),
      PAHelper.getKpiInSyncMode(context),
      PAHelper.getInspectionPlanInSyncMode(context),
      PAHelper.getCiltPlanInSyncMode(context),
      PAHelper.getInspectionInSyncMode(context),
      PAHelper.getCiltInSyncMode(context),
      PAHelper.getFaultInSyncMode(context),
      PAHelper.getJobInSyncMode(context),
    ];
    try {
      _downloadFuture = Future.wait(futures);
      await _downloadFuture;
      await _initDataFromDbForWeb();
    } catch (error, s) {
      setState(() {
        errorMessage =
            "${AppLocalizations.of(context)!.failed_to_downalod_data} $error";
      });

      UIHelper.showErrorDialogWithStackStrace(context,
          title: "Error While Downloading",
          stackTrace: s.toString(),
          description: errorMessage.toString());
    } finally {
      // setState(() {
      //   errorMessage = "";
      // });
    }
  }

  _initNotifier() {
    _appNotifier.notifyInfoMessages((data) async {
      if (data[EventSyncStatusFieldType] ==
          EventSyncStatusTypeInboxProcessingComplete) {
        if (mounted) {
          ref.read(progressProvider).setInboxCount();
          _initDataFromDb();
        }
      }
    });
    _appNotifier.notifySyncStatus((data) async {
      if (data[EventSyncStatusFieldType] ==
          EventSyncStatusTypeInboxProcessingComplete) {
        if (mounted) {
          ref.read(progressProvider).setInboxCount();
          _initDataFromDb();
        }
      }
    });
  }

  void _initDataFromDb() async {
    final plant = ref.read(plantProvider);
    final plantSection = ref.read(plantSectionProvider);
    final shift = ref.read(shiftProvider);
    await ref.read(progressProvider).setInboxCount();
    await ref.read(roleProvider.notifier).getRole();
    await ref
        .read(ciltHeaderListProvider.notifier)
        .fetchCiltListHeaders(plant, plantSection, shift, ref);
    await ref
        .read(ciltPlanListHeaderProvider.notifier)
        .fetchCiltPlanListHeaders();
    await ref
        .read(inspectionHeaderListProvider.notifier)
        .fetchInspectionListHeaders(plant, plantSection, shift, ref);
    await ref.read(assetHeaderProvider.notifier).getAssetHeaderList(plant);
    await ref.read(kpiHeaderProvider.notifier).getKPIHeaderList(plant);
    await ref.read(flocHeaderProvider.notifier).getLocHeaderList(plant);
    await ref
        .read(inspectionPlanListHeaderProvider.notifier)
        .fetchInspectionPlanListHeaders();

    await ref.read(locationListProvider.notifier).fetchLocationList(plant);
    await ref.read(assetListProvider.notifier).fetchAssetList(plant);
    await ref
        .read(locationCategoryListProvider.notifier)
        .fetchLocationCategoryList();
    await ref.read(assetCategoryListProvider.notifier).fetchAssetCategoryList();
    await ref.read(abcCategoryListProvider.notifier).fetchABCCategoryList();
    await ref
        .read(faultHeaderListProvider.notifier)
        .fetchFaultHeaderList(plant, initial: true);
    await ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant,initial: true);
    await ref.read(faultTypeListProvider.notifier).fetchFaultTypeList();
    await ref.read(faultModeHeaderListProvider.notifier).fetchFaultModeList();
    await ref.read(priorityListProvider.notifier).fetchPriorityList();
    await ref.read(jobTypeListProvider.notifier).jobTypeList();
    await ref.read(pickListHeaderListProvider.notifier).getPickListHeaderList();
    await ref.read(pickListCodeListProvider.notifier).getPickListCodeList();
    await ref.read(documentHeaderProvider.notifier).fetchDocumentHeaders();
    await ref
        .read(documentAttachmentProvider.notifier)
        .fetchDocumentAttachments();
    if(Platform.isAndroid){
      //ref.invalidate(inspectionDataLoaderProvider);
      Future.microtask(() => ref.invalidate(inspectionDataLoaderProvider));
    }

    _setHomeComponent();
  }

  Future<void> _initDataFromDbForWeb() async {
    final plant = ref.read(plantProvider);
    final plantSection = ref.read(plantSectionProvider);
    final shift = ref.read(shiftProvider);
    await ref.read(progressProvider).setInboxCount();
    await ref.read(roleProvider.notifier).getRole();
    await ref
        .read(ciltHeaderListProvider.notifier)
        .fetchCiltListHeaders(plant, plantSection, shift, ref);
    await ref
        .read(ciltPlanListHeaderProvider.notifier)
        .fetchCiltPlanListHeaders();
    await ref
        .read(inspectionHeaderListProvider.notifier)
        .fetchInspectionListHeaders(plant, plantSection, shift, ref);
    await ref.read(assetHeaderProvider.notifier).getAssetHeaderList(plant);
    await ref.read(kpiHeaderProvider.notifier).getKPIHeaderList(plant);
    await ref.read(flocHeaderProvider.notifier).getLocHeaderList(plant);
    await ref
        .read(inspectionPlanListHeaderProvider.notifier)
        .fetchInspectionPlanListHeaders();

    await ref.read(locationListProvider.notifier).fetchLocationList(plant);
    await ref.read(assetListProvider.notifier).fetchAssetList(plant);
    await ref
        .read(locationCategoryListProvider.notifier)
        .fetchLocationCategoryList();
    await ref.read(assetCategoryListProvider.notifier).fetchAssetCategoryList();
    await ref.read(abcCategoryListProvider.notifier).fetchABCCategoryList();
    await ref
        .read(faultHeaderListProvider.notifier)
        .fetchFaultHeaderList(plant);
    await ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
    await ref.read(faultTypeListProvider.notifier).fetchFaultTypeList();
    await ref.read(faultModeHeaderListProvider.notifier).fetchFaultModeList();
    await ref.read(priorityListProvider.notifier).fetchPriorityList();
    await ref.read(jobTypeListProvider.notifier).jobTypeList();
    await ref.read(pickListHeaderListProvider.notifier).getPickListHeaderList();
    await ref.read(pickListCodeListProvider.notifier).getPickListCodeList();
    await ref.read(documentHeaderProvider.notifier).fetchDocumentHeaders();
    await ref
        .read(documentAttachmentProvider.notifier)
        .fetchDocumentAttachments();
   // Future.microtask(() => ref.refresh(inspectionDataLoaderProvider));
   await ref.refresh(inspectionDataLoaderProvider.future);
   
    _setHomeComponent();
  }

  Future<void> _setHomeComponent() async {
    setState(() {
      _isLoading = false;
      errorMessage = "";
    });

    if (ref.watch(progressProvider).inboxCount == 0) {
      if (AuthenticationService().getMessageInterval() == 0) {
        AuthenticationService().setMessageInterval(15);
      }
    } else {
      int inboxCount = await SettingsHelper().getInboxCount();
      if (inboxCount > 0) {
        setState(() {
          _isLoading = true;
        });
      }
    }
  }
}
