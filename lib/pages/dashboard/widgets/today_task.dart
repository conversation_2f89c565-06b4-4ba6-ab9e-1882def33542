import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/CILT_EXEC_HEADER.dart';
import 'package:rounds/be/CILT_PLAN_HEADER.dart';
import 'package:rounds/be/INSPECTION_PLAN_HEADER.dart';
import 'package:rounds/be/JOB_HEADER.dart';
import 'package:rounds/pages/home/<USER>';
import 'package:rounds/pages/job_creation/widgets/job_creation_card.dart';
import 'package:rounds/providers/calendar_provider.dart';
import 'package:rounds/providers/cilt/cilt_header_provider.dart';
import 'package:rounds/providers/cilt/cilt_plan_header_provider.dart';
import 'package:rounds/providers/job_creation/job_header_provider.dart';
import 'package:rounds/utils/app_constants.dart';
import 'package:rounds/utils/utils.dart';
import 'package:shimmer/shimmer.dart';
import 'package:intl/intl.dart';

import '../../../be/INSP_EXEC_HEADER.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:collection/collection.dart';
import '../../../providers/inspection/inspection_header_provider.dart';
import '../../../providers/inspection/inspection_plan_header_provider.dart';
import '../../../utils/constants.dart';

class TodayTask extends ConsumerStatefulWidget {
  const TodayTask({super.key});

  @override
  ConsumerState<TodayTask> createState() => _TodayTaskState();
}

class _TodayTaskState extends ConsumerState<TodayTask> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final ciltHeaders = ref.watch(ciltHeaderListProvider);
    final ciltPlanListHeaders = ref.watch(ciltPlanListHeaderProvider);
    final inspectionHeaders = ref.watch(inspectionHeaderListProvider);
    final inspectionPlanListHeaders =
        ref.watch(inspectionPlanListHeaderProvider);
    final jobHeaderListHeaders = ref.watch(jobHeaderListProvider);
    List<CILT_EXEC_HEADER> ciltHeadersData = filterCiltHeaders(ciltHeaders);
    List<INSP_EXEC_HEADER> inspectionHeadersData =
        filterInspHeaders(inspectionHeaders);
    List<JOB_HEADER> jobHeaders = getJobs(jobHeaderListHeaders);
    int count = (ciltHeadersData.length +
            inspectionHeadersData.length +
            jobHeaders.length)
        .toInt();
    return (ciltHeaders.isNotEmpty ||
            inspectionHeaders.isNotEmpty ||
            jobHeaderListHeaders.isNotEmpty)
        ? (ciltHeadersData.isNotEmpty ||
                inspectionHeadersData.isNotEmpty ||
                jobHeaders.isNotEmpty)
            ? ListView.builder(
                physics: const AlwaysScrollableScrollPhysics(),
                itemCount: count,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      getCard(
                          index: index,
                          ciltPlanHeaders: ciltPlanListHeaders,
                          ciltHeaders: ciltHeadersData,
                          inspectionPlanHeaders: inspectionPlanListHeaders,
                          inspectionHeaders: inspectionHeadersData,
                          // faultHeaders: faultListHeaders,
                          jobHeaders: jobHeaders),
                    ],
                  );
                },
              )
            : Center(child: Text(AppLocalizations.of(context)!.no_tasks_found))
        : Center(child: _buildShimmerList());
  }

  List<JOB_HEADER> getJobs(jobHeaderListHeaders) {
    String currentDateStr = DateFormat("yyyyMMdd").format(DateTime.now());
    List<JOB_HEADER> filteredJobs = jobHeaderListHeaders.where((job) {
      return job.start_date.toString().compareTo(currentDateStr) >= 0;
    }).toList();
    filteredJobs.where((element) =>
        element.status == AppConstants.STATE_OPEN ||
        element.status == Constants.JOB_STATE_ORAS ||
        element.status == Constants.JOB_STATE_ACPT);
    return filteredJobs.take(2).toList();
  }

  List<CILT_EXEC_HEADER> filterHeadersByTime(List<CILT_EXEC_HEADER> headers) {
    final now = DateTime.now();
    final twoHoursLater = now.add(const Duration(hours: 2));

    List<CILT_EXEC_HEADER> filteredHeaders = [];

    for (var header in headers) {
      try {
        final startTime =
            DateFormat("HH:mm:ss").parse(convertTimeString(header.start_at));
        final headerDateTime = DateTime(now.year, now.month, now.day,
            startTime.hour, startTime.minute, startTime.second);
        if (headerDateTime.isAfter(now) &&
            headerDateTime.isBefore(twoHoursLater)) {
          filteredHeaders.add(header);
        }
      } catch (e) {
        Logger.logError('TodayTask', 'filterHeadersByTime', e.toString());
      }
    }

    return filteredHeaders;
  }

  List<INSP_EXEC_HEADER> filterInspHeadersByTime(
      List<INSP_EXEC_HEADER> headers) {
    final now = DateTime.now();
    final twoHoursLater = now.add(Duration(hours: 2));

    List<INSP_EXEC_HEADER> filteredHeaders = [];

    for (var header in headers) {
      try {
        final startTime =
            DateFormat("HH:mm:ss").parse(convertTimeString(header.start_at));
        final headerDateTime = DateTime(now.year, now.month, now.day,
            startTime.hour, startTime.minute, startTime.second);
        if (headerDateTime.isAfter(now) &&
            headerDateTime.isBefore(twoHoursLater)) {
          filteredHeaders.add(header);
        }
      } catch (e) {
        Logger.logError('TodayTask', 'filterInspHeadersByTime', e.toString());
      }
    }

    return filteredHeaders;
  }

  List<CILT_EXEC_HEADER> filterCiltHeaders(List<CILT_EXEC_HEADER> ciltHeader) {
    final date = ref.watch(currentDateProvider);
    List<CILT_EXEC_HEADER> headers = ciltHeader
        .where((element) => element.start_on == int.parse(date))
        .toList();
    List<CILT_EXEC_HEADER> fll = filterHeadersByTime(headers);
    fll.where((element) =>
        element.status == AppConstants.STATE_OPEN ||
        element.status == AppConstants.STATE_ACCEPTED ||
        element.status == AppConstants.STATE_UNASSIGNED ||
        element.status == AppConstants.STATE_ASSIGNED);
    return fll;
  }

  List<INSP_EXEC_HEADER> filterInspHeaders(List<INSP_EXEC_HEADER> inspHeader) {
    final date = ref.watch(currentDateProvider);
    List<INSP_EXEC_HEADER> headers = inspHeader
        .where((element) => element.start_on.toString() == date)
        .toList();
    List<INSP_EXEC_HEADER> fll = filterInspHeadersByTime(headers);
    fll.where((element) =>
        element.status == AppConstants.STATE_OPEN ||
        element.status == AppConstants.STATE_ACCEPTED ||
        element.status == AppConstants.STATE_ASSIGNED);
    return fll;
  }

  DateTime convertTimeToDateTime(String time) {
    String currentDate = DateTime.now().toIso8601String().split('T').first;
    String dateTimeString = '$currentDate $time';
    DateTime updatedTime = DateTime.parse(dateTimeString);

    return updatedTime;
  }

  Widget getCard({
    required int index,
    required List<CILT_PLAN_HEADER> ciltPlanHeaders,
    required List<CILT_EXEC_HEADER> ciltHeaders,
    required List<INSPECTION_PLAN_HEADER> inspectionPlanHeaders,
    required List<INSP_EXEC_HEADER> inspectionHeaders,
    required List<JOB_HEADER> jobHeaders,
  }) {
    bool hasCiltHeaders = ciltHeaders.isNotEmpty;
    bool hasInspectionHeaders = inspectionHeaders.isNotEmpty;
    bool hasJobHeaders = jobHeaders.isNotEmpty;
    if (hasCiltHeaders) {
      if (index < ciltHeaders.length) {
        final ciltExec = ciltHeaders[index];
        CILT_PLAN_HEADER? filteredCiltPlan;
        for (var data in ciltPlanHeaders) {
          if (data.plan_id == ciltExec.plan_id) {
            filteredCiltPlan = ciltPlanHeaders.firstWhereOrNull(
                (element) => element.plan_id == ciltExec.plan_id);
          }
        }

        if (filteredCiltPlan != null) {
          return Padding(
            padding: const EdgeInsets.only(top: 8),
            child: JobCard(
                index: index,
                onTap: onTap,
                ciltPlanHeader: filteredCiltPlan,
                ciltHeader: ciltExec),
          );
        } else {
          return Padding(
            padding: const EdgeInsets.only(top: 8),
            child: JobCard(
                index: index, onTap: onTap, ciltPlanHeader: filteredCiltPlan),
          );
        }
      }
      if (index >= ciltHeaders.length && hasInspectionHeaders) {
        final inspectionIndex = index - ciltHeaders.length;
        if (inspectionIndex < inspectionPlanHeaders.length) {
          final inspection = inspectionPlanHeaders[inspectionIndex];

          INSP_EXEC_HEADER? filteredInspectionHeader =
              inspectionHeaders.firstWhereOrNull(
                  (element) => element.plan_id == inspection.plan_id);

          if (filteredInspectionHeader != null) {
            return Padding(
              padding: const EdgeInsets.only(top: 8),
              child: JobCard(
                  index: index,
                  onTap: onTap,
                  inspectionPlanHeader: inspection,
                  inspectionHeader: filteredInspectionHeader),
            );
          }
          //  else {
          //   return Padding(
          //     padding: const EdgeInsets.only(top: 8),
          //     child: JobCard(
          //         index: index, onTap: onTap, inspectionPlanHeader: inspection),
          //   );
          // }
        }
      }
    }

    if (!hasCiltHeaders && hasInspectionHeaders) {
      if (index < inspectionPlanHeaders.length) {
        final inspection = inspectionPlanHeaders[index];

        INSP_EXEC_HEADER? filteredInspectionHeader =
            inspectionHeaders.firstWhereOrNull(
                (element) => element.plan_id == inspection.plan_id);

        if (filteredInspectionHeader != null) {
          return Padding(
            padding: const EdgeInsets.only(top: 8),
            child: JobCard(
                index: index,
                onTap: onTap,
                inspectionPlanHeader: inspection,
                inspectionHeader: filteredInspectionHeader),
          );
        } 
        // else {
        //   return Padding(
        //     padding: const EdgeInsets.only(top: 8),
        //     child: JobCard(
        //         index: index, onTap: onTap, inspectionPlanHeader: inspection),
        //   );
        // }
      }
    }

    if ((!hasCiltHeaders && !hasInspectionHeaders) || hasJobHeaders) {
      if (index < jobHeaders.length) {
        final jobIndex = index -
            (hasCiltHeaders ? ciltHeaders.length : 0) -
            (hasInspectionHeaders ? inspectionPlanHeaders.length : 0);
        final job = jobHeaders[jobIndex];

        JOB_HEADER? filteredJobHeader = jobHeaders
            .firstWhereOrNull((element) => element.job_id == job.job_id);

        if (filteredJobHeader != null) {
          return Padding(
              padding: const EdgeInsets.only(top: 8),
              child: JobCreationCard(
                index: index,
                item: job,
                onTap: onTap,
              ));
        } else {
          return Padding(
              padding: const EdgeInsets.only(top: 8),
              child: JobCreationCard(
                index: index,
                item: job,
                onTap: onTap,
              ));
        }
      }
    }

    return Container();
  }

  onTap(value) {}

  Widget _buildShimmerList() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: 10,
        itemBuilder: (context, index) {
          return Padding(
            padding:
                const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
            child: Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: double.infinity,
                        height: 16,
                        color: Colors.white,
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: 150,
                        height: 12,
                        color: Colors.white,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
