import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:rounds/helpers/pa_helper.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/pages/inspection/inspection_screen.dart';
import 'package:rounds/pages/profile/profile_screen.dart';
import 'package:rounds/providers/calendar_provider.dart';
import 'package:rounds/providers/card_color_provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:rounds/providers/cilt/cilt_header_provider.dart';
import 'package:rounds/providers/cilt/cilt_plan_header_provider.dart';
import 'package:rounds/providers/inspection/inspection_plan_header_provider.dart';

import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:unvired_settings/main.dart';

import '../../../be/APP_SETTING_HEADER.dart';
import '../../../helpers/db_helper.dart';
import '../../../providers/fault/fault_header_provider.dart';
import '../../../providers/inspection/inspection_header_provider.dart';
import '../../../providers/user_provider.dart';

class TopHeader extends ConsumerStatefulWidget {
  final Widget? titleWidget;
  final Widget? headerTrailingIcons;
  final VoidCallback? onAddButton;
  final VoidCallback? onFilterButton;
  final bool isAddIconRequired;
  final bool isFilterIconRequired;
  final String? screen;

  const TopHeader(
      {super.key,
      this.titleWidget,
      this.headerTrailingIcons,
      this.isAddIconRequired = false,
      this.isFilterIconRequired = false,
      this.screen,
      this.onFilterButton,
      this.onAddButton});

  @override
  ConsumerState<TopHeader> createState() => _TopHeaderState();
}

class _TopHeaderState extends ConsumerState<TopHeader>
    with SingleTickerProviderStateMixin {
  bool _isLoading = false;

  bool isSearchVisible = false;

  late AnimationController _animationController;
  late Animation<Offset> _drawerAnimation;

  late SharedPreferences preference;

  @override
  void initState() {
    getUser();
    super.initState();
    initSharedPref();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _drawerAnimation =
        Tween<Offset>(begin: const Offset(1.0, 0.0), end: Offset.zero)
            .animate(_animationController);
  }

  void getUser() async {
    await ref.read(userProvider.notifier).getUser();
    if(kIsWeb){
      String? imageString = await DbHelper()
          .getAttachmentFromIndexDbByUid(ref.read(userProvider)!.user_id!);
      if (imageString != null && imageString.isNotEmpty) {
        ref
            .read(profileImageProvider.notifier)
            .getImage(base64Decode(imageString));
      }
    }
    }

  @override
  Widget build(BuildContext context) {
    final userHeader = ref.watch(userProvider);
    return Consumer(
      builder: (context, ref, child) {
        String selectedDate = ref.watch(selectedDateProvider);

        DateTime today = stripTime(DateTime.now());
        String formattedDate =
            '${selectedDate.substring(0, 4)}-${selectedDate.substring(4, 6)}-${selectedDate.substring(6, 8)}';

        DateTime convertedDate = DateFormat("yyyy-MM-dd").parse(formattedDate);

        DateTime selectedDateStripped = stripTime(convertedDate);

        int difference = selectedDateStripped.difference(today).inDays;

        String displayText;
        if (difference == 0) {
          displayText = AppLocalizations.of(context)!.today;
        } else if (difference == 1) {
          displayText = AppLocalizations.of(context)!.tomorrow;
        } else if (difference == -1) {
          displayText = AppLocalizations.of(context)!.yesterday;
        } else if (difference > 1) {
          displayText =
              "$difference ${AppLocalizations.of(context)!.days_after}";
        } else {
          displayText =
              "${difference.abs()} ${AppLocalizations.of(context)!.days_ago}";
        }
        displayText = displayText;

        return SingleChildScrollView(
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  InkWell(
                    onTap: () async {
                      if (userHeader != null) {
                        List<APP_SETTING_HEADER> list =
                            await DbHelper.getAppSettingHeader();
                        if (list.isNotEmpty) {
                          for (var item in list) {
                            if (item.prop_name == 'plantId') {
                              ref
                                  .read(plantProvider.notifier)
                                  .setPlant(item.prop_value.toString());
                            } else if (item.prop_name == 'sectionId') {
                              List<dynamic> decodedList =
                                  jsonDecode(item.prop_value.toString());
                              List<String> sectionIds =
                                  decodedList.map((e) => e.toString()).toList();
                              ref
                                  .read(plantSectionProvider.notifier)
                                  .setPlantSection(sectionIds);
                            } else if (item.prop_name == 'shiftCode') {
                              ref
                                  .read(shiftProvider.notifier)
                                  .setShift(item.prop_value.toString());
                            }
                          }
                        }

                        if (ScreenType.desktop ==
                            UIHelper().getScreenType(context)) {
                          await showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (rootDialogContext) => Dialog(
                                  backgroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20.0),
                                  ),
                                  child: ConstrainedBox(
                                    constraints: const BoxConstraints(
                                      maxWidth:
                                          420, // Slightly more than a mobile screen width
                                      maxHeight:
                                          800, // Not more than the size of a mobile phone
                                    ),
                                    child: Padding(
                                        padding: const EdgeInsets.all(20.0),
                                        child: ProfileScreen(
                                            userHeader: userHeader)),
                                  )));
                        } else {
                          Navigator.push(context,
                              MaterialPageRoute(builder: (context) {
                            return ProfileScreen(userHeader: userHeader);
                          }));
                        }
                      }
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppColors.grey,
                          width: 1.0,
                        ),
                      ),
                      child: Hero(
                        tag: "profileImage",
                        child: CircleAvatar(
                            radius: 20,
                            backgroundColor: AppColors.white,
                            backgroundImage: userHeader != null
                                ? (ref.watch(profileImageProvider) != null
                                    ? MemoryImage(ref
                                        .watch(profileImageProvider.notifier)
                                        .state!)
                                    : const AssetImage(
                                            'assets/images/empty_profile.png')
                                        as ImageProvider)
                                : const AssetImage(
                                    'assets/images/empty_profile.png')),
                      ),
                    ),
                  ),
                  const SizedBox(width: 5),
                  widget.titleWidget ??
                      Text(
                        displayText,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                  const Spacer(),
                  widget.headerTrailingIcons ?? _getTrailerIcons(context),
                ],
              ),
              if (_isLoading)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: LinearProgressIndicator(
                    backgroundColor: Colors.grey[300],
                    valueColor:
                        AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  _getTrailerIcons(BuildContext context) {
    return Consumer(
        builder: (BuildContext context, WidgetRef ref, Widget? child) {
      return Row(
        children: [
          widget.isAddIconRequired
              ? IconButton(
                  style: IconButton.styleFrom(padding: EdgeInsets.zero),
                  onPressed: widget.onAddButton,
                  splashColor: Colors.grey,
                  icon: const Icon(
                    Icons.add,
                    size: 24,
                  ),
                )
              : const SizedBox(),
          widget.isFilterIconRequired
              ? IconButton(
                  style: IconButton.styleFrom(padding: EdgeInsets.zero),
                  onPressed: widget.onFilterButton,
                  splashColor: Colors.grey,
                  icon: Image.asset(
                    'assets/icon/filter.png',
                    width: 24,
                    height: 24,
                    fit: BoxFit.contain,
                  ),
                )
              : const SizedBox(),
          SizedBox(
            width: 24,
            height: 24,
            child: _getPopupMenu(),
          )
        ],
      );
    });
  }

  ///Redo

  final List<Map<String, dynamic>> menuItemss = [
    {'label': 'Refresh', 'icon': Icons.refresh},
  ];

  Widget _getPopupMenu() {
    return Consumer(
        builder: (BuildContext context, WidgetRef ref, Widget? child) {
      return PopupMenuButton<Map<String, dynamic>>(
        iconSize: 20,
        padding: EdgeInsets.zero,
        icon: const Icon(Icons.more_vert),
        onSelected: (item) {
          if (item['label'] == 'Maps') {
            ref.read(homeCardColorProvider).getScaffoldColor(AppColors.white);
            ref.read(homeCardColorProvider).getCardColor(AppColors.white);
            ref
                .read(homeCardColorProvider)
                .getCardBorderColor(AppColors.cardBorderGrey);
          } else if (item['label'] == 'Report') {
            ref.read(homeCardColorProvider).getScaffoldColor(AppColors.white);
            ref.read(homeCardColorProvider).getCardColor(AppColors.white);
            ref.read(homeCardColorProvider).getCardBorderColor(AppColors.grey);
          } else if (item['label'] == 'Skip Round') {
            ref.read(homeCardColorProvider).getScaffoldColor(AppColors.white);
            ref.read(homeCardColorProvider).getCardColor(AppColors.white);
            ref
                .read(homeCardColorProvider)
                .getCardBorderColor(AppColors.darkgrey);
          } else if (item['label'] == 'Asset View') {
            ref.read(homeCardColorProvider).getScaffoldColor(AppColors.white);
            ref.read(homeCardColorProvider).getCardColor(AppColors.lighterGrey);
          } else if (item['label'] == 'Theme1') {
            ref
                .read(homeCardColorProvider)
                .getScaffoldColor(AppColors.backgroundGrey);
            ref.read(homeCardColorProvider).getCardColor(AppColors.white);
          } else if (item['label'] == 'Theme2') {
            ref
                .read(homeCardColorProvider)
                .getScaffoldColor(AppColors.cardBorderGrey);
            ref.read(homeCardColorProvider).getCardColor(AppColors.white);
          } else if (item['label'] == 'Refresh') {
            getRefreshdata(context);
          } else {
            ref
                .read(homeCardColorProvider)
                .getScaffoldColor(AppColors.lighterGrey);
            ref.read(homeCardColorProvider).getCardColor(AppColors.white);
          }
        },
        itemBuilder: (context) => menuItemss.map((item) {
          return PopupMenuItem<Map<String, dynamic>>(
            value: item,
            child: Row(
              children: [
                Icon(item['icon'], color: AppColors.black),
                const SizedBox(width: 10),
                Text(item['label']),
              ],
            ),
          );
        }).toList(),
      );
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  initSharedPref() async {
    preference = await SharedPreferences.getInstance();
  }

  DateTime stripTime(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  getRefreshdata(BuildContext context) async {
    if (!kIsWeb) {
      List<Future> futures = [
        PAHelper.getInspectionInAsyncMode(context),
        PAHelper.getCiltPlanInAsyncMode(context),
        PAHelper.getInspectionInAsyncMode(context),
        PAHelper.getCiltInAsyncMode(context),
        PAHelper.getFaultInAsyncMode(context),
        PAHelper.getJobInAsyncMode(context),
      ];
      
      SyncEngine().receive();
      UIHelper().progressDialog(
          context: context, message: AppLocalizations.of(context)!.refreshing);
      await Future.wait(futures).then((value) async {
        await updateTheState();
        Navigator.of(context, rootNavigator: true).pop();
      });
    } else {
      List<Future> futures = [
        PAHelper.getInspectionPlanInSyncMode(context),
        PAHelper.getCiltPlanInSyncMode(context),
        PAHelper.getInspectionInSyncMode(context),
        PAHelper.getCiltInSyncMode(context),
        PAHelper.getFaultInSyncMode(context),
        PAHelper.getJobInSyncMode(context),
      ];

      UIHelper().progressDialog(
          context: context, message: AppLocalizations.of(context)!.refreshing);
      await Future.wait(futures).then((value) async {
        await updateTheState();
        Navigator.of(context, rootNavigator: true).pop();
      });
    }
  }

  Future<void> updateTheState() async {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final shift = ref.read(shiftProvider.notifier).state;
    await ref
        .read(ciltHeaderListProvider.notifier)
        .fetchCiltListHeaders(plant, plantSection, shift, ref);
    await ref
        .read(ciltPlanListHeaderProvider.notifier)
        .fetchCiltPlanListHeaders();
    await ref
        .read(inspectionHeaderListProvider.notifier)
        .fetchInspectionListHeaders(plant, plantSection, shift, ref);
    await ref
        .read(inspectionPlanListHeaderProvider.notifier)
        .fetchInspectionPlanListHeaders();
    ref.read(filteredCiltProvider.notifier).filter(
          plant,
          plantSection,
          shift,
          ref.read(roundsSearchProvider.notifier).state,
          ref,
        );
  }
}
