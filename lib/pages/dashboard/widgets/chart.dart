/*
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class Occupancy<PERSON><PERSON> extends StatelessWidget {
  const OccupancyChart({super.key, required this.title});
  final String title;

  @override
  Widget build(BuildContext context) {
    double chartSize =
        MediaQuery.of(context).size.width * 0.25;
    return SizedBox(
      width: chartSize,
      height: chartSize,
      child: Stack(
        children: [
          PieChart(
            PieChartData(
              sections: [
                PieChartSectionData(
                  value: 150,
                  color: Colors.lightBlueAccent,
                  radius: chartSize * 0.2, // Dynamic radius based on chart size
                  title: '70%', // Title for this section
                  titleStyle: TextStyle(
                    fontSize: chartSize * 0.07, // Dynamic font size
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  titlePositionPercentageOffset:
                      0.55, // Center the text in the section
                ),
                PieChartSectionData(
                  value: 41,
                  color: Colors.purpleAccent,
                  radius: chartSize * 0.2, // Dynamic radius
                  title: '30%', // Title for this section
                  titleStyle: TextStyle(
                    fontSize: chartSize * 0.07, // Dynamic font size
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  titlePositionPercentageOffset:
                      0.55, // Center the text in the section
                ),
                PieChartSectionData(
                  value: 20,
                  color: Colors.greenAccent,
                  radius: chartSize * 0.2, // Dynamic radius
                  title: '10%', // Title for this section
                  titleStyle: TextStyle(
                    fontSize: chartSize * 0.07, // Dynamic font size
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  titlePositionPercentageOffset:
                      0.55, // Center the text in the section
                ),
              ],
              centerSpaceRadius:
                  chartSize * 0.35, // Dynamic center space radius
              sectionsSpace: 3,
            ),
          ),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                //  Text("Rounds"),
                Text(
                  '100', // The overall count
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: chartSize * 0.1, // Dynamic font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
*/
