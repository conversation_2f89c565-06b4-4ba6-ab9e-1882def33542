/*
import 'package:flutter/material.dart';

class HorizontalBarData {
  final String title;
  final int count;

  HorizontalBarData({required this.title, required this.count});
}

class HorizontalBarPainter extends CustomPainter {
  final double animationValue;
  final List<HorizontalBarData> data;
  final double barHeight;

  HorizontalBarPainter({
    required this.animationValue,
    required this.data,
    required this.barHeight,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double maxBarWidth =
        size.width - 80; // Allocate space for text and padding

    for (int i = 0; i < data.length; i++) {
      final paint = Paint()
        ..color = Colors.primaries[i % Colors.primaries.length];

      // Calculate the bar width based on the percentage
      final barWidth =
          (data[i].count / getMaxValue()) * maxBarWidth * animationValue;

      // Ensure the bar width does not exceed maxBarWidth
      final actualBarWidth = barWidth > maxBarWidth ? maxBarWidth : barWidth;

      // Draw the bar
      final rect =
          Rect.fromLTWH(0, i * (barHeight + 10), actualBarWidth, barHeight);
      canvas.drawRect(rect, paint);

      // Draw the title text
      final textPainter = TextPainter(
        text: TextSpan(
          text: "${data[i].count}",
          style: TextStyle(color: Colors.black, fontSize: 10),
        ),
        textDirection: TextDirection.ltr,
      )..layout(maxWidth: maxBarWidth);

      // Position the text to the right of the bar
      final textOffset = Offset(
        actualBarWidth + 10,
        i * (barHeight + 10) + (barHeight - textPainter.height) / 2,
      );
      textPainter.paint(canvas, textOffset);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }

  double getMaxValue() {
    return data
        .map((item) => item.count.toDouble())
        .reduce((a, b) => a > b ? a : b);
  }
}
*/
