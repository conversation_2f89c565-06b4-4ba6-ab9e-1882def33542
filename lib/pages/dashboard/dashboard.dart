import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/pages/home/<USER>';
import 'package:rounds/pages/inspection/inspection_screen.dart';
import 'package:rounds/pages/login/login.dart';
import 'package:rounds/pages/login/login_state/login_state.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/utils.dart';
import 'package:rounds/widgets/conectivity_card.dart';
import 'package:screenshot/screenshot.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import '../../models/intractive_Item_Model.dart';
import '../../providers/assets/asset_provider.dart';
import '../../providers/assets/floc_provider.dart';
import '../../providers/assets/kpi_provider.dart';
import '../../providers/cilt/cilt_header_provider.dart';
import '../../providers/fault/fault_header_provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../providers/inspection/inspection_header_provider.dart';
import '../assets/assets.dart';
import '../fault/fault_screen.dart';
import '../job_creation/job_creation_screen.dart';

final bottomNavIndexProvider = StateProvider<int>((ref) => 0);

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double minHeight;
  final double maxHeight;

  _SliverAppBarDelegate({
    required this.child,
    required this.minHeight,
    required this.maxHeight,
  });

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight > minHeight ? maxHeight : minHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    final double effectiveHeight = maxHeight - shrinkOffset;
    return SizedBox.expand(
      child: Container(
        height: effectiveHeight,
        color: Colors.white,
        child: child,
      ),
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}

class Dashboard extends ConsumerStatefulWidget {
  const Dashboard({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _DashboardState();
}

class _DashboardState extends ConsumerState<Dashboard>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  bool isEurekaOn = false;
  late AnimationController _animationController;
  late Animation<Offset> _offsetAnimation;
  bool _isExpanded = false;
  ScreenshotController screenshotController = ScreenshotController();
  Uint8List? data;
  Offset _fabPosition = const Offset(20, 20);
  String assistantSvgIconPath = 'assets/svg/assistant_svg_icon.svg';
  Color assistantIconColor = Colors.white;

  Future<void> _onItemTapped(int index) async {
    final plant = ref.watch(plantProvider);
    final plantSection = ref.watch(plantSectionProvider);
    final shift = ref.watch(shiftProvider);
    ref.read(bottomNavIndexProvider.notifier).state = index;
    if (!kIsWeb && UIHelper().getScreenType(context) == ScreenType.mobile) {
      ref.read(searchTextProvider.notifier).state = '';
      ref.read(roundsSearchProvider.notifier).state = '';
      ref.read(searchTextJobProvider.notifier).state = '';
      ref.read(faultSearchProvider.notifier).state = '';
      // ref.read()
    }
    await ref
        .read(filteredCiltProvider.notifier)
        .filter(plant, plantSection, shift, "", ref);

    await ref
        .read(filterInspectionHeaderListProvider.notifier)
        .filter(plant, plantSection, shift, "", ref);

    await ref.read(flocHeaderProvider.notifier).getLocHeaderList(plant);
    await ref.read(assetHeaderProvider.notifier).getAssetHeaderList(plant);
    await ref.read(kpiHeaderProvider.notifier).getKPIHeaderList(plant);
    roundDetailViewNotifier.value = InteractiveItemModel(
      type: "",
      data: {"type": "", "index": null},
    );
    faultDetailViewNotifier.value = InteractiveItemModel(
      type: "",
      data: {"type": "", "index": null},
    );
    jobDetailViewNotifier.value = InteractiveItemModel(
      type: "",
      data: {"type": "", "index": null},
    );
    assetDetailViewNotifier.value = InteractiveItemModel(
      type: "",
      data: {"type": "", "index": null},
    );
  }

  final List<Widget> _screens = [
    const Home(),
    const InspectionScreen(),
    const FaultScreen(),
    const JobCreationScreen(),
    const AssetScreen()
  ];

  @override
  void initState() {
    //_initializeAiMlPackage();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _offsetAnimation = Tween<Offset>(
      begin: const Offset(0, 5), // Start from below the screen
      end: Offset.zero, // End at the top of the screen
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
      reverseCurve: Curves.easeInOut,
    ));
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final screenSize = MediaQuery.of(context).size;
        const fabSize = 80.0;
        final bottomNavHeight =
            UIHelper().getScreenType(context) != ScreenType.desktop
                ? kBottomNavigationBarHeight + 20
                : 5;

        setState(() {
          _fabPosition = Offset(
            screenSize.width - fabSize,
            screenSize.height - bottomNavHeight - fabSize,
          );
        });
      }
    });

    WidgetsBinding.instance.addObserver(this);
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        systemNavigationBarColor: AppColors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
        statusBarColor: AppColors.white,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    debugPrint("🧹 Dashboard disposed");
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        debugPrint("▶️ App Resumed (foreground)");
        break;
      case AppLifecycleState.inactive:
        debugPrint("⏸️ App Inactive");
        break;
      case AppLifecycleState.paused:
        debugPrint("⏬ App Paused (background)");
        break;
      case AppLifecycleState.detached:
        debugPrint("🚪 App Detached");
        break;
      case AppLifecycleState.hidden:
        debugPrint("👻 App Hidden (e.g., iOS multitasking)");
        break;
    }
  }
  //
  // void _initializeAiMlPackage() async {
  //   await initializeAiMlSupport();
  // }

  @override
  Widget build(BuildContext context) {
    final selectedIndex = ref.watch(bottomNavIndexProvider);
    return Scaffold(
      body: SafeArea(
        child: getDashBoard(selectedIndex),
      ),
    );
  }

  Widget getDashBoard(int selectedIndex) {
    return LayoutBuilder(builder: (context, constraints) {
      final screenSize = UIHelper().getScreenType(context);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          final currentScreenSize = MediaQuery.of(context).size;
          const fabSize = 80.0;
          final bottomNavHeight = screenSize != ScreenType.desktop
              ? kBottomNavigationBarHeight + 20
              : 5;

          // Check if the FAB position is uninitialized or out of bounds
          if (_fabPosition.dx > currentScreenSize.width - fabSize ||
              _fabPosition.dy >
                  currentScreenSize.height - bottomNavHeight - fabSize) {
            setState(() {
              _fabPosition = Offset(
                currentScreenSize.width - fabSize,
                currentScreenSize.height - bottomNavHeight - fabSize,
              );
            });
          }
        }
      });

      return Screenshot(
        controller: screenshotController,
        child: Stack(
          children: [
            Positioned.fill(
              child: Builder(
                builder: (_) {
                  switch (screenSize) {
                    case ScreenType.mobile:
                      return getMobileDashBoard(selectedIndex);
                    case ScreenType.tablet:
                      return getTabDashBoard(selectedIndex);
                    case ScreenType.desktop:
                      return getWebDashBoard(selectedIndex);
                  }
                },
              ),
            ),
            // Eureka AI/ML Support Package
            // if (isEurekaOn)
            //   Align(
            //     alignment: Alignment.center,
            //     child: SlideTransition(
            //       position: _offsetAnimation,
            //       child: AiMlSupportPackage(
            //         path: data,
            //         appName: "Rounds",
            //         onMinimize: () {
            //           setState(() {
            //             isEurekaOn = false;
            //             _animationController.reverse(); // Reverse the animation
            //             _isExpanded = false;
            //           });
            //         },
            //       ),
            //     ),
            //   ),
            // Draggable Floating Action Button with Consumer
            // if (!isEurekaOn)
            //   Consumer(
            //     builder: (context, ref, child) {
            //       final isFabVisible = ref.watch(fabVisibilityProvider);
            //       return isFabVisible
            //           ? Positioned(
            //               left: _fabPosition.dx,
            //               top: _fabPosition.dy,
            //               child: Draggable(
            //                 feedback: FloatingActionButton(
            //                   heroTag: 'draggable_fab_feedback',
            //                   onPressed: () async {
            //                     await _captureScreenshot();
            //                   },
            //                   backgroundColor: Colors.black,
            //                   child: SvgPicture.asset(
            //                     assistantSvgIconPath,
            //                     height: 40,
            //                     width: 40,
            //                     theme: SvgTheme(
            //                       currentColor: assistantIconColor,
            //                     ),
            //                   ),
            //                 ),
            //                 childWhenDragging: const SizedBox.shrink(),
            //                 onDragEnd: (details) {
            //                   setState(() {
            //                     final screenSize = MediaQuery.of(context).size;
            //                     const fabSize = 80.0;
            //                     final bottomNavHeight =
            //                         UIHelper().getScreenType(context) !=
            //                                 ScreenType.desktop
            //                             ? kBottomNavigationBarHeight + 20
            //                             : 5;

            //                     final maxY = screenSize.height -
            //                         bottomNavHeight -
            //                         fabSize;

            //                     _fabPosition = Offset(
            //                       details.offset.dx
            //                           .clamp(0.0, screenSize.width - fabSize),
            //                       details.offset.dy.clamp(0.0, maxY),
            //                     );
            //                   });
            //                 },
            //                 child: FloatingActionButton(
            //                   heroTag: 'draggable_fab',
            //                   onPressed: () async {
            //                     await _captureScreenshot();
            //                   },
            //                   backgroundColor: Colors.black,
            //                   child: SvgPicture.asset(
            //                     assistantSvgIconPath,
            //                     height: 40,
            //                     width: 40,
            //                     theme: SvgTheme(
            //                       currentColor: assistantIconColor,
            //                     ),
            //                     // color: assistantIconColor,
            //                   ),
            //                 ),
            //               ),
            //             )
            //           : const SizedBox.shrink();
            //     },
            //   ),
          ],
        ),
      );

      // switch (screenSize) {
      //   case ScreenType.mobile:
      //     return getMobileDashBoard(selectedIndex);
      //   case ScreenType.tablet:
      //     return getTabDashBoard(selectedIndex);
      //   case ScreenType.desktop:
      //     return getWebDashBoard(selectedIndex);
      // }
    });
  }

  Widget getMobileDashBoard(int selectedIndex) {
    return Scaffold(
      backgroundColor: AppColors.lighterGrey,
      appBar: const ConnectivityCard(),
      body: IndexedStack(index: selectedIndex, children: _screens),
      bottomNavigationBar: Container(
        margin: const EdgeInsets.symmetric(horizontal: 0),
        decoration: BoxDecoration(
          color: AppColors.backgroundGrey,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20.0),
            topRight: Radius.circular(20.0),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.5),
              spreadRadius: 5,
              blurRadius: 7,
              offset: const Offset(0, 3), // changes position of shadow
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20.0),
            topRight: Radius.circular(20.0),
          ),
          child: BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            currentIndex: selectedIndex,
            onTap: _onItemTapped,
            selectedItemColor: AppColors.black,
            unselectedItemColor: Colors.grey,
            selectedFontSize: 10.0,
            unselectedFontSize: 10.0,
            elevation: 10.0,
            backgroundColor: AppColors.white,
            items: buildBottomNavigationBarItems(selectedIndex),
          ),
        ),
      ),
    );
  }

  Widget getTabDashBoard(int selectedIndex) {
    return Scaffold(
      backgroundColor: AppColors.lighterGrey,
      appBar: const ConnectivityCard(),
      body: _screens[selectedIndex],
      bottomNavigationBar: Container(
        margin: const EdgeInsets.symmetric(horizontal: 0),
        decoration: BoxDecoration(
          color: AppColors.backgroundGrey,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20.0),
            topRight: Radius.circular(20.0),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.5),
              spreadRadius: 5,
              blurRadius: 7,
              offset: const Offset(0, 3), // changes position of shadow
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20.0),
            topRight: Radius.circular(20.0),
          ),
          child: BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            currentIndex: selectedIndex,
            onTap: _onItemTapped,
            selectedItemColor: AppColors.black,
            unselectedItemColor: Colors.grey,
            selectedFontSize: 10.0,
            unselectedFontSize: 10.0,
            elevation: 10.0,
            backgroundColor: AppColors.white,
            items: buildBottomNavigationBarItems(selectedIndex),
          ),
        ),
      ),
    );
  }

  Widget getWebDashBoard(int selectedIndex) {
    final bottomItems = buildBottomNavigationBarItems(selectedIndex);

    return Scaffold(
      backgroundColor: AppColors.lighterGrey,
      appBar: const ConnectivityCard(),
      body: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.backgroundGrey,
            ),
            child: SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: MediaQuery.of(context).size.height,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    children: [
                      Expanded(
                        child: NavigationRail(
                          selectedIndex: selectedIndex,
                          onDestinationSelected: _onItemTapped,
                          backgroundColor: AppColors.backgroundGrey,
                          labelType: NavigationRailLabelType.all,
                          destinations: bottomItems.map((item) {
                            return NavigationRailDestination(
                              icon: item.icon,
                              selectedIcon: item.activeIcon ?? item.icon,
                              label: Text(item.label ?? ''),
                            );
                          }).toList(),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: Column(
                          children: [
                            IconButton(
                              splashRadius: 20,
                              focusColor:
                                  AppColors.primaryColor.withOpacity(0.5),
                              highlightColor:
                                  AppColors.primaryColor.withOpacity(0.5),
                              splashColor:
                                  AppColors.primaryColor.withOpacity(0.5),
                              icon: const Icon(Icons.logout),
                              tooltip: 'Logout',
                              onPressed: onLogOut,
                            ),
                            const Text('Logout'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Expanded(child: _screens[selectedIndex]),
        ],
      ),
    );
  }

  //Logout
  void onLogOut() {
    UIHelper.showConfirmationDialogWithYesOrNo(context,
        description: AppLocalizations.of(context)!.alert_msg_to_clear_data,
        yes: () {
      Navigator.pop(context);
      clearData();
    }, no: () {
      Navigator.pop(context);
    });
  }

  clearData() async {
    try {
      await SettingsHelper().clearData();
    } catch (e) {
      Logger.logError('ProfileScreen', 'clearData', e.toString());
    }
    final prefs = await SharedPreferences.getInstance();
    // Preserve userName and userEmail before clearing
    final userName = prefs.getString('userName');
    final userEmail = prefs.getString('userEmail');
    await prefs.clear();
    if (kIsWeb) {
      if (userName != null) {
        await prefs.setString('userName', userName);
      }
      if (userEmail != null) {
        await prefs.setString('userEmail', userEmail);
      }
    }
    ref.read(loginStateProvider.notifier).setLoginState(LoginState.domain);
    Future.delayed(Duration(milliseconds: 300), () {
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (Route<dynamic> route) => false,
      );
    });
  }

  List<BottomNavigationBarItem> buildBottomNavigationBarItems(
      int selectedIndex) {
    final items = [
      {'icon': Icons.home, 'label': 'Dashboard'},
      {'icon': Icons.assignment, 'label': 'Rounds'},
      {'icon': Icons.error_outline, 'label': 'Fault'},
      {'icon': 'assets/icon/setting.svg', 'label': 'Job'},
      {'icon': Icons.inventory, 'label': 'Asset'},
    ];

    return items.asMap().entries.map((entry) {
      int idx = entry.key;
      var item = entry.value;
      return BottomNavigationBarItem(
        icon: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          decoration: BoxDecoration(
            color: selectedIndex == idx
                ? Colors.white.withOpacity(0.2)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.all(8),
          child: buildIconWidget(item['icon']),
        ),
        activeIcon: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          decoration: BoxDecoration(
            color: selectedIndex == idx
                ? Colors.white.withOpacity(0.2)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.all(8),
          child: buildIconWidgett(item['icon']),
        ),
        label: item['label'] as String,
      );
    }).toList();
  }

  Widget buildIconWidget(dynamic icon) {
    if (icon is IconData) {
      return Icon(icon);
    } else if (icon is String) {
      return SvgPicture.asset(
        icon,
        height: 20,
        width: 20,
      );
    }
    return const SizedBox.shrink();
  }

  Widget buildIconWidgett(dynamic icon) {
    if (icon is IconData) {
      return Icon(
        icon,
      );
    } else if (icon is String) {
      return SvgPicture.asset(
        icon,
        height: 20,
        width: 20,
        color: ThemeData().colorScheme.primary,
      );
    }
    return const SizedBox.shrink();
  }
}
