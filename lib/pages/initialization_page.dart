import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/pages/dashboard/dashboard.dart';
import 'package:rounds/pages/user-plant-screen.dart';
import 'package:rounds/providers/fault/fault_header_provider.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:path/path.dart' as path;

class InitializationPage extends ConsumerStatefulWidget {
  const InitializationPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _InitializationPageState();
}

class _InitializationPageState extends ConsumerState<InitializationPage> {
  @override
  void initState() {
    getData();
    super.initState();
    Future.delayed(Duration.zero).then((value) {
      _checkUserPreference().then((value) {
        return value;
      });
    });
  }

  getData() async {
    try {
      if (!kIsWeb) {
        await SettingsHelper().setFetchInterval(15);
      }
    } catch (e) {
      Logger.logError("InitializationPage", "setFetchInterval", e.toString());
    }
  }

  Future<bool> _checkUserPreference() async {
    final prefs = await SharedPreferences.getInstance();
    final isUserPreferenceSet = prefs.getBool('isUserPreferenceSet') ?? false;
    final plant = ref.read(plantProvider.notifier);
    final plantSection = ref.read(plantSectionProvider.notifier);
    final shift = ref.read(shiftProvider.notifier);

    if (isUserPreferenceSet) {
      final selectedPlant = prefs.getString("Plant") ?? '';
      final selectedPlantSection = prefs.getStringList("PlantSection") ?? [];
      final selectedShift = prefs.getString("Shift") ?? '';
      plant.state = selectedPlant;
      plantSection.state = selectedPlantSection;
      shift.state = selectedShift;
    }

    return isUserPreferenceSet;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FutureBuilder(
        future: _checkUserPreference(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.connectionState == ConnectionState.done) {
            if (snapshot.hasData) {
              if (snapshot.data == true) {
                return const Dashboard();
              } else {
                return const UserPlantScreen();
              }
            } else {
              return Center(
                  child: Text(AppLocalizations.of(context)!
                      .error_while_checking_user_preference));
            }
          } else {
            return Container();
          }
        },
      ),
    );
  }
}
