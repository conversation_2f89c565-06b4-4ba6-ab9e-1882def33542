import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:rounds/be/CILT_SECTION.dart';
import 'package:rounds/be/CILT_TASK.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:rounds/pages/cilt/widgets/cilt_task_card.dart';

import 'package:rounds/providers/cilt/cilt_header_provider.dart';
import 'package:rounds/providers/cilt/cilt_plan_header_provider.dart';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../widgets/notaskfoundwidget.dart';

class CiltTaskTaskListScreen extends ConsumerWidget {
  final CILT_SECTION section;
  final int sectionList;
  final int secIndex;
  final ScrollController scrollController;

  CiltTaskTaskListScreen({required this.section, required this.sectionList, required this.secIndex, required this.scrollController, super.key});
  List<DOCUMENT_HEADER> attachments = [];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ciltPlanTaskHeader =
        ref.watch(ciltPlanTaskListHeaderProvider(section));
    final filteredTasks = ref.watch(ciltTaskNotifier);
    final bool isSearch = ref.watch(ciltTaskNotifier.notifier).isSearch;
    final bool isToggled = ref.watch(ciltToggleStateProvider);
    final ciltHeader = ref.watch(ciltHeaderProvider);
    final clitPlan = ref.watch(ciltPlanHeaderProvider);
    //ScrollController myscrollController = ScrollController();
    List<CILT_TASK>? tasksToDisplay = (isSearch || isToggled)
        ? filteredTasks
            .where((element) => element.section_id == section.section_id)
            .toList()
        : ciltPlanTaskHeader.data;
    if (tasksToDisplay != null) {
      tasksToDisplay
          .sort((a, b) => a.seq_no.toString().compareTo(b.seq_no.toString()));
    }
    return ciltPlanTaskHeader.isLoading
        ? const CircularProgressIndicator()
        : ciltPlanTaskHeader.error != null
            ? Center(
                child: Text(
                    '${AppLocalizations.of(context)!.error}: ${ciltPlanTaskHeader.error}'))
            : (isSearch && filteredTasks.isEmpty)
                ? const NoTasksFoundWidget()
                : Column(
                  children: List.generate(
                     tasksToDisplay!.length,
                      (index){
                        final task = tasksToDisplay[index];
                        return Padding(
                          padding: const EdgeInsets.only(top: 10),
                          child: ref
                                      .watch(ciltExecuteTaskListProvider.notifier)
                                      .getciltExecTaskHeaderByTask(
                                          task, ciltHeader) ==
                                  null
                              ? NoTasksFoundWidget()
                              : CiltTaskCard(
                                totaltaskcount: sectionList,
                                sectionIndex: secIndex,
                                scrollController: scrollController,
                                key: Key(section.section_id.toString()),
                                 // key: Key('${section.section_id.toString()}_${task.task_id}'),
                                  task: task,
                                  ciltExecHeader: ciltHeader,
                                  index: index,
                                  isDuratonRequried:
                                      clitPlan.exec_time?.toLowerCase() ==
                                              'true' ??
                                          false,
                                  execTaskHeader: ref
                                      .watch(ciltExecuteTaskListProvider.notifier)
                                      .getciltExecTaskHeaderByTask(
                                          task, ciltHeader)),
                        );
                      },
                    ),
                );
  }
}


/**
 * GRID AND LIST VIEW
 * LayoutBuilder(
                    builder: (context, constraints) {
                      // Define a breakpoint for desktop screens (e.g., width >= 900)
                      bool isDesktop = constraints.maxWidth >= 900;
                      if (isDesktop) {
                        return MasonryGridView.count(
                          crossAxisCount:
                              (constraints.maxWidth ~/ 350).clamp(1, 2),
                          mainAxisSpacing: 16,
                          crossAxisSpacing: 16,
                          shrinkWrap: true,
                          physics: const ScrollPhysics(),
                          // gridDelegate:
                          //     SliverGridDelegateWithFixedCrossAxisCount(
                          //   crossAxisCount:
                          //       (constraints.maxWidth ~/ 350).clamp(1, 2),
                          //   crossAxisSpacing: 16,
                          //   mainAxisSpacing: 16,
                          // ),
                          itemCount: tasksToDisplay!.length,
                          itemBuilder: (context, index) {
                            final task = tasksToDisplay[index];
                            final execTaskHeader = ref
                                .watch(ciltExecuteTaskListProvider.notifier)
                                .getciltExecTaskHeaderByTask(task, ciltHeader);
                            if (execTaskHeader == null) {
                              return const NoTasksFoundWidget();
                            }
                            return CiltTaskCard(
                              key: Key('${section.section_id}_${task.task_id}'),
                              task: task,
                              ciltExecHeader: ciltHeader,
                              index: index,
                              isDuratonRequried:
                                  clitPlan.exec_time?.toLowerCase() == 'true' ??
                                      false,
                              execTaskHeader: execTaskHeader,
                            );
                          },
                        );
                      } else {
                        return ListView.builder(
                          shrinkWrap: true,
                          physics: const ScrollPhysics(),
                          itemCount: tasksToDisplay!.length,
                          itemBuilder: (context, index) {
                            final task = tasksToDisplay[index];
                            final execTaskHeader = ref
                                .watch(ciltExecuteTaskListProvider.notifier)
                                .getciltExecTaskHeaderByTask(task, ciltHeader);
                            if (execTaskHeader == null) {
                              return const NoTasksFoundWidget();
                            }
                            return Padding(
                              padding: const EdgeInsets.only(top: 10),
                              child: CiltTaskCard(
                                key: Key(
                                    '${section.section_id}_${task.task_id}'),
                                task: task,
                                ciltExecHeader: ciltHeader,
                                index: index,
                                isDuratonRequried:
                                    clitPlan.exec_time?.toLowerCase() ==
                                            'true' ??
                                        false,
                                execTaskHeader: execTaskHeader,
                              ),
                            );
                          },
                        );
                      }
                    },
                  );
 
 */