import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/be/ASSET_HEADER.dart';
import 'package:rounds/be/CILT_EXEC_ACTION.dart';
import 'package:rounds/be/CILT_EXEC_SEC.dart';
import 'package:rounds/be/CILT_SECTION.dart';
import 'package:rounds/be/CILT_TASK.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:rounds/be/LOCATION_HEADER.dart';
import 'package:rounds/helpers/pa_helper.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/pages/inspection/inspection_screen.dart';
import 'package:rounds/providers/assets/asset_provider.dart';
import 'package:rounds/providers/assets/floc_provider.dart';
import 'package:rounds/providers/attachments/attachment_provider.dart';
import 'package:rounds/providers/cilt/cilt_plan_header_provider.dart';
import 'package:rounds/providers/fault/fault_header_provider.dart';
import 'package:rounds/providers/fault/fault_type_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/app_constants.dart';
import 'package:rounds/utils/utils.dart';
import 'package:rounds/widgets/skip_button.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../../be/CILT_EXEC_HEADER.dart';
import '../../../be/CILT_EXEC_TASK.dart';
import '../../../helpers/db_helper.dart';
import '../../../providers/cilt/cilt_header_provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../providers/user_provider.dart';
import 'cilt_section_task_list_screen.dart';
import 'package:intl/intl.dart';

class CiltTaskSectionDetailCard extends ConsumerStatefulWidget {
  const CiltTaskSectionDetailCard({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _CiltTaskSectionDetailCardState();
}

class _CiltTaskSectionDetailCardState
    extends ConsumerState<CiltTaskSectionDetailCard> {
  TextEditingController delayReason = TextEditingController();
  TextEditingController reasonController = TextEditingController();

  @override
  void dispose() {
    // TODO: implement dispose
    delayReason.dispose();
    reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ciltPlanSectionHeader = ref.watch(ciltPlanSectionListHeaderProvider);
    final ciltHeader = ref.watch(ciltHeaderProvider);
    final ciltExecSecList = ref.watch(ciltExecSectionListHeaderProvider);
    final skipReasonList = ref.watch(skipReasonListProvider.notifier).state;
    dynamic progressResult = _calulateProgress(ciltHeader);
    ScrollController _scrollController = ScrollController();


    return ciltPlanSectionHeader.isNotEmpty
        ? ListView.builder(
            controller: _scrollController,
            shrinkWrap: true,
            physics: const ScrollPhysics(),
            itemCount: ciltPlanSectionHeader.length,
            itemBuilder: (context, index) {
              final section = ciltPlanSectionHeader[index];
              CILT_EXEC_SEC? execSecHeader = ciltExecSecList.firstWhereOrNull(
                  (element) => element.section_id == section.section_id);
              //bool reachedend = (index==ciltPlanSectionHeader.length-1) ? true :false;
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 20, bottom: 0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: RichText(
                              overflow: TextOverflow.ellipsis,
                              text: TextSpan(
                                  text: section.title.toString(),
                                  style: TextStyle(
                                    fontSize: 18,
                                    color: AppColors.titleTextColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  children: [
                                    TextSpan(
                                        text:
                                            _getSectionAdditionalInfo(section),
                                        style: TextStyle(
                                            fontSize: 18,
                                            color: AppColors.titleTextColor,
                                            fontWeight: FontWeight.normal))
                                  ])),
                        ),
                        // Text(
                        //   section.title.toString(),
                        //   style: TextStyle(
                        //     fontSize: 18,
                        //     color: AppColors.titleTextColor,
                        //     fontWeight: FontWeight.bold,
                        //   ),
                        // ),
                        isExecutionVisible()
                            ? Column(
                                children: [
                                  Visibility(
                                    visible: ref
                                        .watch(ciltExecuteTaskListProvider
                                            .notifier)
                                        .isSectionCompleted(
                                            ciltTask:
                                                ref.watch(ciltTaskNotifier),
                                            section_id: section.section_id ?? 0,
                                            ciltHeader:
                                                ref.watch(ciltHeaderProvider)),
                                    child: (ref.watch(userProvider)?.user_id !=
                                                ciltHeader.assigned_to ||
                                            ciltHeader.status == "REJECTED")
                                        ? const SizedBox.shrink()
                                        : SkipButton(
                                            isActive: ref
                                                .watch(
                                                    ciltExecuteTaskListProvider
                                                        .notifier)
                                                .isSectionCompleted(
                                                  ciltTask: ref
                                                      .watch(ciltTaskNotifier),
                                                  section_id:
                                                      section.section_id ?? 0,
                                                  ciltHeader: ref.watch(
                                                      ciltHeaderProvider),
                                                ),
                                            onPressed: () {
                                              if (ciltHeader.status !=
                                                  AppConstants
                                                      .STATE_TASK_COMP) {
                                                performSkip(
                                                    context,
                                                    section,
                                                    ref,
                                                    ciltHeader,
                                                    execSecHeader);
                                              }
                                            },
                                            width: 70,
                                            height: 30,
                                          ),
                                  ),
                                  if (execSecHeader?.reason != null &&
                                      execSecHeader?.reason != "")
                                    Text(skipReasonList
                                        .where((element) =>
                                            element.reason ==
                                            execSecHeader!.reason!)
                                        .toList()[0]
                                        .description!)
                                ],
                              )
                            : SizedBox()
                      ],
                    ),
                  ),
                  CiltTaskTaskListScreen(section: section, sectionList: ciltPlanSectionHeader.length, secIndex: index, scrollController: _scrollController,)
                ],
              );
            },
          )
        : progressResult["totalTasks"] == progressResult["completedTasks"] &&
                ref.watch(ciltToggleStateProvider)
            ? Center(
                child: Text(
                "${AppLocalizations.of(context)!.successfully_completed_all_tasks} \n ${AppLocalizations.of(context)!.great_job}",
                textAlign: TextAlign.center,
              ))
            : Center(child: Text(AppLocalizations.of(context)!.no_tasks_found));
  }

  void performSkip(BuildContext context, CILT_SECTION section, WidgetRef ref,
      CILT_EXEC_HEADER ciltHeader, CILT_EXEC_SEC? execSecHeader) {
    ref.read(skipReasonProvider.notifier).clearSkipReason();
    onSkipYes(context, section, ref, ciltHeader, execSecHeader);
  }

  onSkipYes(BuildContext context, CILT_SECTION section, WidgetRef ref,
      CILT_EXEC_HEADER ciltHeader, CILT_EXEC_SEC? execSecHeader) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return true;
          },
          child: StatefulBuilder(
            builder: (context, setState) {
              return AlertDialog(
                contentPadding: const EdgeInsets.all(5),
                content: ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxWidth: 400,
                  ),
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                AppLocalizations.of(context)!.select_reason,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold, fontSize: 14),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          getReasonDropdown(context, ref, setState),
                          const SizedBox(height: 10),
                          TextField(
                            style: const TextStyle(fontSize: 14),
                            controller: reasonController,
                            onChanged: (value) {
                              setState(() {
                                reasonController.text = value;
                              });
                            },
                            maxLines: 3,
                            decoration: InputDecoration(
                              hintText: AppLocalizations.of(context)!
                                  .additionalComment,
                              border: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              focusedBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              enabledBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              contentPadding: const EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 10),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: reasonController.text.isEmpty ||
                            selectedSkipReason.isEmpty
                        ? null
                        : () {
                            if (reasonController.text == '' ||
                                reasonController.text == null) {
                              UIHelper.showInfoDialogWithtitleAndDescription(
                                  context,
                                  title: AppLocalizations.of(context)!.warning,
                                  description: AppLocalizations.of(context)!
                                      .please_add_additional_message);
                            } else {
                              onSaveReason(reasonController.text, ciltHeader,
                                  section, execSecHeader);
                            }
                          },
                    child: Text(
                      AppLocalizations.of(context)!.save,
                      style: TextStyle(
                        color: reasonController.text.isEmpty ||
                                selectedSkipReason.isEmpty
                            ? AppColors.grey
                            : AppColors.primaryColor,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      selectedSkipReason = '';
                      reasonController.clear();
                      Navigator.pop(context);
                    },
                    child: Text(
                      AppLocalizations.of(context)!.cancel,
                      style: TextStyle(
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  String selectedSkipReason = '';
  Widget getReasonDropdown(
      BuildContext context, WidgetRef ref, StateSetter setState) {
    final skipReasonList = ref
        .watch(skipReasonListProvider)
        .where((element) => (element.category! & 2) != 0)
        .toList();

    final skipReason = ref.watch(skipReasonProvider.notifier);

    final dropdownItems = skipReasonList.map((option) {
      return DropdownMenuItem<String>(
        value: option.description,
        child: Padding(
          padding: const EdgeInsets.only(left: 5.0),
          child: Text(option.description!, style: UIHelper.valueStyle()),
        ),
      );
    }).toList();

    if (dropdownItems.where((item) => item.value == '').isEmpty) {
      dropdownItems.insert(
        0,
        DropdownMenuItem<String>(
          value: '',
          child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(AppLocalizations.of(context)!.select,
                style: UIHelper.valueStyle()),
          ),
        ),
      );
    }
    return Container(
      width: MediaQuery.of(context).size.width,
      decoration: UIHelper.fieldDecoration(),
      child: DropdownButton<String>(
        elevation: 0,
        isExpanded: true,
        underline: const SizedBox(),
        value: selectedSkipReason.isNotEmpty ? selectedSkipReason : '',
        items: dropdownItems,
        onChanged: (newValue) {
          setState(() {
            selectedSkipReason = newValue!;
            skipReason.getSkipReason(selectedSkipReason);
          });
        },
      ),
    );
  }

  void onSaveReason(String reason, CILT_EXEC_HEADER ciltHeader,
      CILT_SECTION section, CILT_EXEC_SEC? execSecHeader) async {
    final ciltPlanHeader = ref.watch(ciltPlanHeaderProvider);
    final skipReason = ref.watch(skipReasonProvider.notifier).state;
    // ciltHeader.reason = skipReason.category.toString();

    await DbHelper.updateCiltExecSection(
        section, reason, skipReason.reason.toString(), ciltHeader);

    List<CILT_TASK> list = await DbHelper.getCiltTaskPlanListHeader(
        section.plan_id.toString(), section.section_id.toString());

    for (CILT_TASK task in list) {
      await DbHelper().taskUpdate(
          task: task,
          status: AppConstants.STATE_TASK_COMP,
          ciltHeader: ciltHeader,
          skipreason: reason,
          p_mode: "M",
          reasonCode: skipReason.reason.toString());

      ref.read(ciltTasksProvider.notifier).getCiltTasks(ciltHeader.plan_id!);

      ref.read(ciltExecuteProvider.notifier).getCiltExecute(ciltHeader);
      final ciltCompleted = ref.watch(ciltCompletedProvider.notifier);
      final ciltExecHeader = ref.watch(ciltExecuteTaskListProvider.notifier);
      await ciltCompleted.ciltCompleted(ciltHeader);
      await ciltExecHeader.getCiltExecuteTaskList();

      ref.read(ciltExecuteTaskListProvider.notifier).getCiltExecuteTaskList();
      ref
          .read(ciltTaskNotifier.notifier)
          .filter(ref.read(ciltPlanHeaderProvider).plan_id.toString(), "", ref);
      ref.read(ciltExecuteTaskListProvider.notifier).getCiltExecuteTaskList();
      ref.read(ciltTasksProvider.notifier).getCiltTasks(task.plan_id!);
      ref.read(ciltExecuteProvider.notifier).getCiltExecute(ciltHeader);
    }

    if (ref.read(ciltToggleStateProvider)) {
      ref.read(ciltTaskNotifier.notifier).fetchIncompleteTasks(
          (ciltPlanHeader.plan_id.toString() ?? ""),
          ref.read(ciltHeaderProvider),
          ref);
    }

    //ref.invalidate(ciltPlanTaskListHeaderProvider);
    //Future.microtask(() => ref.invalidate(ciltPlanTaskListHeaderProvider));

    delayReason.clear();
    reasonController.clear();
    ref.read(skipReasonProvider.notifier).clearSkipReason();
    selectedSkipReason = '';

    if (mounted) {
      Navigator.pop(context, true);
    }

    _getProgressValue(ciltHeader);
  }

  _getProgressValue(CILT_EXEC_HEADER? ciltHeader) {
    if (ciltHeader != null) {
      ref.watch(ciltTasksProvider.notifier).getCiltTasks(ciltHeader.plan_id!);
      ref.watch(ciltExecuteProvider.notifier).getCiltExecute(ciltHeader);
      final numberOfTasks =
          ref.watch(ciltTasksProvider)[ciltHeader.plan_id] ?? [];
      final numberOfCompletedTasks =
          ref.watch(ciltExecuteProvider)[ciltHeader.cilt_id] ?? [];
      int totalTasks = numberOfTasks
          .where((element) =>
              element.plan_id.toString() == ciltHeader.plan_id.toString())
          .length;
      int completedTasks = numberOfCompletedTasks.length;
      numberOfCompletedTasks
          .where((element) =>
              element.cilt_id.toString() == ciltHeader.cilt_id.toString())
          .length;
      if (totalTasks == completedTasks) {
        Future.delayed(Duration.zero, () {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) {
              return Dialog(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxWidth: 400,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Center(
                                child: Text(
                                  "${AppLocalizations.of(context)!.congratulations} 🎉",
                                  style: UIHelper.titleStyle16(),
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                              child: Icon(
                                Icons.cancel_outlined,
                                color: AppColors.black,
                                size: 20,
                              ),
                            ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 16.0,
                          ),
                          child: Text(
                            AppLocalizations.of(context)!.all_tasks_completed,
                            textAlign: TextAlign.center,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryColor,
                            minimumSize: const Size(double.infinity, 40),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                          onPressed: () async {
                            final current = DateTime.now();

                            DateTime endTime = Utils.getTimeStamp(
                                ciltHeader.start_on.toString(),
                                ciltHeader.end_at.toString());

                            if (current.isAfter(endTime)) {
                              showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (context) {
                                  return AlertDialog(
                                    title: Text(AppLocalizations.of(context)!
                                        .please_provide_reason_for_submission_delay),
                                    content: TextField(
                                      decoration: InputDecoration(
                                        hintText: AppLocalizations.of(context)!
                                            .reason_for_submission_delay,
                                        border: OutlineInputBorder(
                                          borderSide:
                                              BorderSide(color: AppColors.grey),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderSide:
                                              BorderSide(color: AppColors.grey),
                                        ),
                                        enabledBorder: OutlineInputBorder(
                                          borderSide:
                                              BorderSide(color: AppColors.grey),
                                        ),
                                        contentPadding:
                                            const EdgeInsets.symmetric(
                                          vertical: 10,
                                          horizontal: 10,
                                        ),
                                      ),
                                      controller: delayReason,
                                    ),
                                    actions: [
                                      TextButton(
                                        onPressed: () async {
                                          dynamic progressValue =
                                              _calculateProgress(ciltHeader);
                                          if (progressValue != null) {
                                            Navigator.of(context)
                                                .pop(); // Close the reason dialog
                                            Navigator.of(context)
                                                .pop(); // Close the confirmation dialog

                                            if (progressValue["totalTasks"] ==
                                                progressValue[
                                                    "completedTasks"]) {
                                              await getSubmitWithCloseAction(
                                                  ciltHeader);
                                            } else {
                                              await getSubmitWithoutCloseAction(
                                                  ciltHeader);
                                            }

                                            // Show success message
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              SnackBar(
                                                content: Text(AppLocalizations
                                                        .of(context)!
                                                    .submitted_successfully),
                                                duration: Duration(seconds: 2),
                                                behavior:
                                                    SnackBarBehavior.floating,
                                              ),
                                            );
                                          }
                                        },
                                        child: Text(
                                            AppLocalizations.of(context)!
                                                .submit),
                                      ),
                                      TextButton(
                                        onPressed: () {
                                          delayReason.clear();
                                          Navigator.of(context)
                                              .pop(); // Just close the reason dialog
                                        },
                                        child: Text(
                                            AppLocalizations.of(context)!
                                                .cancel),
                                      ),
                                    ],
                                  );
                                },
                              );
                            } else {
                              dynamic progressValue =
                                  _calculateProgress(ciltHeader);
                              if (progressValue != null) {
                                Navigator.of(context, rootNavigator: true)
                                    .pop();
                                if (progressValue["totalTasks"] ==
                                    progressValue["completedTasks"]) {
                                  await getSubmitWithCloseAction(ciltHeader);
                                } else {
                                  await getSubmitWithoutCloseAction(ciltHeader);
                                }

                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(AppLocalizations.of(context)!
                                        .submitted_successfully),
                                    duration: const Duration(seconds: 2),
                                    behavior: SnackBarBehavior.floating,
                                  ),
                                );
                              }
                            }
                          },
                          child: Text(
                            AppLocalizations.of(context)!.submit,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(height: 4),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        });
      }
      if (totalTasks == 0) {
        return {
          'percentage': 0.0,
          'completedTasks': 0,
          'totalTasks': 0,
        };
        /*return 0.0;*/
      }
      double percentage =
          double.parse((completedTasks / totalTasks).toStringAsFixed(2));
      return {
        'percentage': percentage,
        'completedTasks': completedTasks,
        'totalTasks': totalTasks,
      };
      /*return percentage/100;*/
    } else {
      return {
        'percentage': 0.0,
        'completedTasks': 0,
        'totalTasks': 0,
      };
    }
  }

  Map<String, dynamic> _calulateProgress(CILT_EXEC_HEADER? ciltHeader) {
    if (ciltHeader != null) {
      final numberOfTasks =
          ref.watch(ciltTasksProvider)[ciltHeader.plan_id] ?? [];
      final numberOfCompletedTasks =
          ref.watch(ciltExecuteProvider)[ciltHeader.cilt_id] ?? [];
      int totalTasks = numberOfTasks
          .where((element) =>
              element.plan_id.toString() == ciltHeader.plan_id.toString())
          .length;
      int completedTasks = numberOfCompletedTasks.length;
      numberOfCompletedTasks
          .where((element) =>
              element.cilt_id.toString() == ciltHeader.cilt_id.toString())
          .length;
      if (totalTasks == 0) {
        return {
          'percentage': 0.0,
          'completedTasks': 0,
          'totalTasks': 0,
        };
      }
      double percentage =
          double.parse((completedTasks / totalTasks).toStringAsFixed(2));
      return {
        'percentage': percentage,
        'completedTasks': completedTasks,
        'totalTasks': totalTasks,
      };
    } else {
      return {
        'percentage': 0.0,
        'completedTasks': 0,
        'totalTasks': 0,
      };
    }
  }

  _calculateProgress(CILT_EXEC_HEADER? ciltHeader) {
    if (ciltHeader != null) {
      ref.watch(ciltTasksProvider.notifier).getCiltTasks(ciltHeader.plan_id!);
      ref.watch(ciltExecuteProvider.notifier).getCiltExecute(ciltHeader);
      final numberOfTasks =
          ref.watch(ciltTasksProvider)[ciltHeader.plan_id] ?? [];
      final numberOfCompletedTasks =
          ref.watch(ciltExecuteProvider)[ciltHeader.cilt_id] ?? [];
      int totalTasks = numberOfTasks
          .where((element) =>
              element.plan_id.toString() == ciltHeader.plan_id.toString())
          .length;
      int completedTasks = numberOfCompletedTasks.length;
      numberOfCompletedTasks
          .where((element) =>
              element.cilt_id.toString() == ciltHeader.cilt_id.toString())
          .length;

      if (totalTasks == 0) {
        return {
          'percentage': 0.0,
          'completedTasks': 0,
          'totalTasks': 0,
        };
        /*return 0.0;*/
      }
      double percentage =
          double.parse((completedTasks / totalTasks).toStringAsFixed(2));
      return {
        'percentage': percentage,
        'completedTasks': completedTasks,
        'totalTasks': totalTasks,
      };
      /*return percentage/100;*/
    } else {
      return {
        'percentage': 0.0,
        'completedTasks': 0,
        'totalTasks': 0,
      };
    }
  }

  Future<void> getSubmitWithCloseAction(CILT_EXEC_HEADER ciltHeader) async {
    if (delayReason.text.isNotEmpty) {
      ciltHeader.delay_comments = delayReason.text;
    }
    await AppDatabaseManager().update(
        DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, ciltHeader.toJson()));
    final numberOfCompletedTasks = ref
            .watch(ciltExecuteProvider)[ciltHeader.cilt_id] ??
        [].where((element) => element.cilt_id == ciltHeader.cilt_id).toList();

    CILT_EXEC_ACTION action = CILT_EXEC_ACTION(
        cilt_id: ciltHeader.cilt_id, user_action: AppConstants.STATE_COMPLETED);
    action.fid = ciltHeader.lid;

    var result = await AppDatabaseManager().execute(
        "select * from CILT_EXEC_ACTION where CILT_ID = '${ciltHeader.cilt_id.toString()}'");
    if (result.length > 0) {
      await AppDatabaseManager()
          .update(DBInputEntity(CILT_EXEC_ACTION.TABLE_NAME, action.toJson()));
    } else {
      await AppDatabaseManager()
          .insert(DBInputEntity(CILT_EXEC_ACTION.TABLE_NAME, action.toJson()));
    }

    List<CILT_EXEC_TASK> cilt_exec_tasks = ref
        .read(ciltExecuteTaskListProvider.notifier)
        .findAllCiltExecOfCilt(ciltHeader);

    List<DOCUMENT_HEADER> headerData = [];

    for (var task in cilt_exec_tasks) {
      final documentHeaders = ref
          .watch(ciltTaskExecDocumentHeaderProvider.notifier)
          .getDocumentHeadersForTask(task);
      if (documentHeaders != null) {
        headerData.addAll(documentHeaders);
      }
    }

    List<DOCUMENT_HEADER> documents = headerData
        .where((element) => element.objectStatus == ObjectStatus.add)
        .toList();

    if (kIsWeb) {
      UIHelper().progressDialog(
          context: context, message: "Submiting Cilt Execution");

      for (DOCUMENT_HEADER document in documents) {
        var doc = await DbHelper()
            .getAttachmentFromIndexDbByUid(document.doc_id ?? "");
        await SyncEngine().uploadAttachmentSync(
            doc ?? "", document.file_name ?? "", document.doc_id ?? "");
      }
      await PAHelper.addDocumentInSyncMode(context, documents);
    } else {
      await PAHelper.addDocumentInAsyncMode(context, documents);
    }

    /*
    for (var task in numberOfCompletedTasks) {
      await DbHelper.updateCiltExecTask(task);
    }
*/

    if (!kIsWeb) {
      await PAHelper.modifyCiltExecInAsyncMode(context, ciltHeader);
    } else {
      await PAHelper.modifyCiltExecInSyncMode(context, ciltHeader);
      Navigator.of(context, rootNavigator: true).pop();
    }
    delayReason.clear();
    selectedSkipReason = '';
    reasonController.clear();

    if (mounted) {
      if (UIHelper().getScreenType(context) != ScreenType.desktop) {
        Navigator.pop(context, ciltHeader);
      } else {
        setState(() {});
        var ciltPlanHeader = ref.read(ciltPlanHeaderProvider);

        ref
            .read(ciltTaskNotifier.notifier)
            .filter(ciltPlanHeader.plan_id.toString() ?? "", "", ref);
      }
      final plant = ref.read(plantProvider);
      final plantSection = ref.read(plantSectionProvider);
      final shift = ref.read(shiftProvider);

      await ref
          .read(ciltHeaderListProvider.notifier)
          .fetchCiltListHeaders(plant, plantSection, shift, ref);

      await ref
          .read(ciltExecuteTaskListProvider.notifier)
          .getCiltExecuteTaskList();

      final searchProvider = ref.read(roundsSearchProvider.notifier).state;

      ref
          .read(filteredCiltProvider.notifier)
          .filter(plant, plantSection, shift, searchProvider, ref);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.submitted_successfully),
          duration: Duration(seconds: 2),
        ),
      );

   if(!kIsWeb){
         CILT_EXEC_HEADER? newExecHeader = await DbHelper.getCiltExecHeader(ciltHeader);
   if(newExecHeader != null){
     await ref.read(ciltHeaderProvider.notifier).selectedCiltHeaders(ciltHeader);
     print('Refreshing cilt submit ${ref.read(ciltHeaderProvider.notifier).state.syncStatus}');
   }
   }
    }
   
  }

  Future<void> getSubmitWithoutCloseAction(CILT_EXEC_HEADER ciltHeader) async {
    ciltHeader.p_mode = AppConstants.modified;
    final numberOfCompletedTasks = ref
            .watch(ciltExecuteProvider)[ciltHeader.cilt_id] ??
        [].where((element) => element.cilt_id == ciltHeader.cilt_id).toList();

    if (delayReason.text.isNotEmpty) {
      ciltHeader.delay_comments = delayReason.text;
    }

    await AppDatabaseManager().update(
        DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, ciltHeader.toJson()));

    List<CILT_EXEC_TASK> cilt_exec_tasks = ref
        .read(ciltExecuteTaskListProvider.notifier)
        .findAllCiltExecOfCilt(ciltHeader);

    List<DOCUMENT_HEADER> headerData = [];

    for (var task in cilt_exec_tasks) {
      final documentHeaders = ref
          .watch(ciltTaskExecDocumentHeaderProvider.notifier)
          .getDocumentHeadersForTask(task);
      if (documentHeaders != null) {
        headerData.addAll(documentHeaders);
      }
    }

    List<DOCUMENT_HEADER> documents = headerData
        .where((element) => element.objectStatus == ObjectStatus.add)
        .toList();

    if (kIsWeb) {
      UIHelper().progressDialog(
          context: context, message: "Submiting Cilt Execution");
      for (DOCUMENT_HEADER document in documents) {
        var doc = await DbHelper()
            .getAttachmentFromIndexDbByUid(document.doc_id ?? "");
        await SyncEngine().uploadAttachmentSync(
            doc ?? "", document.file_name ?? "", document.doc_id ?? "");
      }
      await PAHelper.addDocumentInSyncMode(context, documents);
    } else {
      await PAHelper.addDocumentInAsyncMode(context, documents);
    }

    if (!kIsWeb) {
      await PAHelper.modifyCiltExecInAsyncMode(context, ciltHeader);
    } else {
      await PAHelper.modifyCiltExecInSyncMode(context, ciltHeader);
      Navigator.of(context, rootNavigator: true).pop();
    }

    delayReason.clear();
    selectedSkipReason = '';
    reasonController.clear();

    ///TODO : Ranjit
/*    for (var task in numberOfCompletedTasks) {
      await DbHelper.updateCiltExecTask(task);
    }*/

    if (mounted) {
      if (UIHelper().getScreenType(context) != ScreenType.desktop) {
        Navigator.pop(context, ciltHeader);
      } else {
        setState(() {});
        var ciltPlanHeader = ref.read(ciltPlanHeaderProvider);
        ref.read(ciltExecuteTaskListProvider.notifier).getCiltExecuteTaskList();
        ref
            .read(ciltTaskNotifier.notifier)
            .filter(ciltPlanHeader.plan_id.toString() ?? "", "", ref);
      }
      final plant = ref.read(plantProvider);
      final plantSection = ref.read(plantSectionProvider);
      final shift = ref.read(shiftProvider);

      await ref
          .read(ciltHeaderListProvider.notifier)
          .fetchCiltListHeaders(plant, plantSection, shift, ref);

      final searchProvider = ref.read(roundsSearchProvider.notifier).state;

      ref
          .read(filteredCiltProvider.notifier)
          .filter(plant, plantSection, shift, searchProvider, ref);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.submitted_successfully),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  bool isExecutionVisible() {
    final role = ref.watch(roleProvider);
    DateTime now = DateTime.now();
    String startAT = ref.read(ciltHeaderProvider).start_at.toString();
    String startOn = ref.read(ciltHeaderProvider).start_on.toString();
    DateTime executionDateTime = Utils.getTimeStamp(startOn, startAT);
    if (role != null) {
      if ((UIHelper.isExecute(role.cilt!) && executionDateTime.isBefore(now))) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  String _getSectionAdditionalInfo(CILT_SECTION section) {
    // Scenario 4: No technical objects
    if (section.location_id == null && section.asset_no == null) {
      return "";
    }

    // Scenario 3: Both location and asset exist - show only asset info
    if (section.location_id != null && section.asset_no != null) {
      ASSET_HEADER? asset_header = ref
          .read(assetHeaderProvider.notifier)
          .findAssetHeaderById(section.asset_no ?? -1);
      return UIHelper.formatIdAndDescription(
          section.asset_no.toString(), asset_header?.description,
          wrap: WrapType.id);
    }

    // Scenario 2: Only asset exists
    if (section.asset_no != null) {
      ASSET_HEADER? asset_header = ref
          .read(assetHeaderProvider.notifier)
          .findAssetHeaderById(section.asset_no ?? -1);
      return UIHelper.formatIdAndDescription(
          section.asset_no.toString(), asset_header?.description,
          wrap: WrapType.id);
    }

    // Scenario 1: Only location exists
    if (section.location_id != null) {
      LOCATION_HEADER? location_header = ref
          .read(flocHeaderProvider.notifier)
          .findLocationHeadById(section.location_id ?? "");
      return UIHelper.formatIdAndDescription(
          section.location_id, location_header?.description,
          wrap: WrapType.id);
    }

    return "";
  }
}
