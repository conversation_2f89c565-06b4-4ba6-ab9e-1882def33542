import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:logger/Logger.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:rounds/be/CILT_EXEC_ACTION.dart';
import 'package:rounds/be/CILT_EXEC_TASK.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';

import 'package:rounds/helpers/db_helper.dart';
import 'package:rounds/helpers/pa_helper.dart';

import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/pages/cilt/round_plan_detail.dart';
import 'package:rounds/pages/widgets/error_longtext_view.dart';
import 'package:rounds/providers/assets/asset_provider.dart';
import 'package:rounds/providers/attachments/attachment_provider.dart';
import 'package:rounds/providers/fault/fault_header_provider.dart';
import 'package:intl/intl.dart';
import 'package:rounds/utils/app_extensions.dart';
import 'package:rounds/utils/utils.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';

import 'package:rounds/widgets/toggle_switch.dart';

import 'package:unvired_sdk/unvired_sdk.dart';

import '../../../be/CILT_EXEC_HEADER.dart';
import '../../../be/CILT_PLAN_HEADER.dart';
import '../../../be/CILT_TASK.dart';
import '../../../providers/cilt/cilt_header_provider.dart';
import '../../../providers/cilt/cilt_plan_header_provider.dart';
import '../../../providers/fault/fault_type_provider.dart';
import '../../../providers/user_provider.dart';
import '../../../utils/app_colors.dart';
import '../../../utils/app_constants.dart';
import '../../utils/constants.dart';
import '../inspection/inspection_screen.dart';
import 'cilt_task_detail_screen.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class CiltDetailScreen extends ConsumerStatefulWidget {
  final CILT_PLAN_HEADER? ciltPlanHeader;
  final CILT_EXEC_HEADER? ciltHeader;

  const CiltDetailScreen(
      {super.key, required this.ciltPlanHeader, this.ciltHeader});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _CiltDetailScreenState();
}

class _CiltDetailScreenState extends ConsumerState<CiltDetailScreen>
    with SingleTickerProviderStateMixin {
  List<Map<String, dynamic>> menuItems = [];
  var selectedSkipReason = "";
  TextEditingController searchController = TextEditingController();
  TextEditingController delayReason = TextEditingController();
  TextEditingController reason = TextEditingController();

  Map<String, dynamic>? selectedItems;
  String? selectedItem;

  bool isRunning = false;
  Stream<int>? timerStream;
  StreamSubscription<int>? timerSubscription;
  String hoursStr = '00';
  String minutesStr = '00';
  String secondsStr = '00';
  bool showSearch = false;
  FocusNode searchFocusNode = FocusNode();

  late AnimationController _animationController;

  Future<List<InfoMessageData?>>? fetchInfoMessage;

  @override
  void initState() {
    menuItems = [
      {'label': 'Skip Round', 'icon': Icons.fast_forward_outlined},
      {'label': 'Reject', 'icon': Icons.cancel},
      {'label': 'Info', 'icon': Icons.info},
    ];
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    fetchInfoMessage =
        InfoMessageHelper().getInfoMessageByBeLid(widget.ciltHeader!.lid);
    Future.delayed(Duration.zero).then((value) {
      ref.read(ciltToggleStateProvider.notifier).setToggleState(false);
      ref.read(ciltExecuteTaskListProvider.notifier).getCiltExecuteTaskList();
      ref
          .read(ciltTaskNotifier.notifier)
          .filter(widget.ciltPlanHeader?.plan_id.toString() ?? "", "", ref);
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Column(
          children: [
            showSearch
                ? Container(
                    height: 60,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 10),
                    child: TextField(
                      focusNode: searchFocusNode,
                      controller: searchController,
                      onChanged: (value) {
                        ref.read(ciltTaskNotifier.notifier).filter(
                            widget.ciltPlanHeader!.plan_id!.toString(),
                            value,
                            ref);
                      },
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(
                          vertical: 5.0,
                          horizontal: 10.0,
                        ),
                        hintText: AppLocalizations.of(context)!.search,
                        border: OutlineInputBorder(
                          borderSide: BorderSide(color: AppColors.black),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        suffixIcon: IconButton(
                          icon: const Icon(Icons.cancel),
                          onPressed: () {
                            setState(() {
                              ref.read(ciltTaskNotifier.notifier).filter(
                                  widget.ciltPlanHeader!.plan_id!.toString(),
                                  "",
                                  ref);
                              showSearch = false;
                              searchController.clear();
                            });
                            _animationController.reverse();
                          },
                        ),
                      ),
                    ),
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 10),
                        child: _getSearchWidget(),
                      ),
                    ],
                  ),
            widget.ciltHeader?.delay_comments != null &&
                    widget.ciltHeader!.delay_comments!.isNotEmpty &&
                    widget.ciltHeader!.delay_comments != ''
                ? Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: MobileErrorAndLongTextview(
                        type: TextDisplayType.warning,
                        text: widget.ciltHeader!.delay_comments!.toString()))
                : const SizedBox(),
            widget.ciltHeader?.delay_comments != null &&
                    widget.ciltHeader!.delay_comments!.isNotEmpty &&
                    widget.ciltHeader!.delay_comments != ''
                ? 10.0.spaceY
                : const SizedBox(),
            widget.ciltHeader?.infoMsgCat == "WARNING" ||
                    widget.ciltHeader?.infoMsgCat == "FAILURE"
                ? Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: FutureBuilder<List<InfoMessageData?>>(
                        future: fetchInfoMessage,
                        builder: (context, snapshot) {
                          if (snapshot.hasData) {
                            return (snapshot.data ?? []).isNotEmpty
                                ? MobileErrorAndLongTextview(
                                    type: widget.ciltHeader?.infoMsgCat ==
                                            "FAILURE"
                                        ? TextDisplayType.error
                                        : TextDisplayType.warning,
                                    text: snapshot.data!
                                        .map((e) => e!.message)
                                        .join("\n"))
                                : const SizedBox.shrink();
                          } else {
                            return const SizedBox();
                          }
                        }),
                  )
                : const SizedBox(),
            widget.ciltHeader?.infoMsgCat == "WARNING" ||
                    widget.ciltHeader?.infoMsgCat == "FAILURE"
                ? 10.0.spaceY
                : const SizedBox(),
            Padding(
              padding: UIHelper().getScreenType(context) == ScreenType.desktop
                  ? const EdgeInsets.only(left: 8, right: 18)
                  : UIHelper.horizontalPaddingOf18(),
              child: RoundSegmentedSwitch(
                height: 25,
                firstLabel: AppLocalizations.of(context)!.all,
                secondLabel: AppLocalizations.of(context)!.open,
                value: ref.watch(ciltToggleStateProvider),
                onChanged: (value) async {
                  if (value) {
                    await ref
                        .read(ciltTaskNotifier.notifier)
                        .fetchIncompleteTasks(
                            (widget.ciltPlanHeader?.plan_id.toString() ?? ""),
                            ref.read(ciltHeaderProvider),
                            ref);
                  } else {
                    /*        await ref.read(ciltTaskNotifier.notifier).fetchIncompleteTasks(
                        (widget.ciltPlanHeader?.plan_id.toString() ?? ""),
                        ref.read(ciltHeaderProvider),
                        ref);*/ //All
                    await ref
                        .read(ciltTaskNotifier.notifier)
                        .reset(widget.ciltPlanHeader!.plan_id.toString(), ref);
                  }
                  ref
                      .read(ciltToggleStateProvider.notifier)
                      .setToggleState(value);
                },
              ),
            ),
            Expanded(
                child: Padding(
              padding: UIHelper().getScreenType(context) == ScreenType.desktop
                  ? const EdgeInsets.only(left: 8, right: 18)
                  : UIHelper.horizontalPaddingOf18(),
              child: const CiltTaskSectionDetailCard(),
            )),
          ],
        ),
      ),
    );
  }

  Widget _getSearchWidget() {
    final percentage = _getProgressValue(widget.ciltHeader);
    final taskList = ref.watch(ciltExecuteTaskListProvider.notifier).state;
    final modifiedList = taskList
        .where((element) =>
            element.p_mode == AppConstants.modified &&
            element.cilt_id == widget.ciltHeader!.cilt_id)
        .toList();
    return Row(
      children: [
        Visibility(
          visible: UIHelper().getScreenType(context) != ScreenType.desktop,
          child: IconButton(
              onPressed: () {
                Navigator.pop(context);
              },
              icon: Icon(
                Icons.arrow_back_ios,
                color: AppColors.titleTextColor,
                size: 20,
              )),
        ),
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Visibility(
                visible: getConditionsAccess(),
                child: getConditionsAccessMsg() == ""
                    ? SizedBox()
                    : MobileErrorAndLongTextview(
                        type: TextDisplayType.msg,
                        text: getConditionsAccessMsg()),
              ),
              getConditionsAccess() ? SizedBox(height: 5) : SizedBox(),
              Text("${widget.ciltPlanHeader!.title}" ?? "",
                  overflow: TextOverflow.ellipsis,
                  style: UIHelper.titleStyle14()),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                    child: LinearPercentIndicator(
                      padding: EdgeInsets.zero,
                      progressColor: Colors.green,
                      lineHeight: 5,
                      backgroundColor: HexColor('#EEEEEE'),
                      percent: percentage["percentage"],
                      center: const Text(''), // animationDuration: 1000,
                      barRadius: const Radius.circular(8.0),
                    ),
                  ),
                  10.0.spaceX,
                  Text(
                      '${percentage["completedTasks"]}/${percentage["totalTasks"]}',
                      style: const TextStyle(fontSize: 12)),
                ],
              ),
            ],
          ),
        ),
        // const Spacer(),
        Expanded(
          flex: 1,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (!showSearch)
                IconButton(
                  icon: const Icon(Icons.search),
                  onPressed: () {
                    setState(() {
                      showSearch = true;
                      searchFocusNode.requestFocus();
                    });
                  },
                ),
              if ((widget.ciltHeader!.status != AppConstants.STATE_COMPLETED &&
                      widget.ciltHeader?.syncStatus.index != 2) ||
                  percentage["completedTasks"] != percentage["totalTasks"])
                isExecutionVisible()
                    ? (widget.ciltHeader?.status == AppConstants.STATE_COMPLETED &&
                            percentage["completedTasks"] ==
                                percentage["totalTasks"])
                        ? SizedBox()
                        : SizedBox(
                            height: 30,
                            width: (!kIsWeb &&
                                    UIHelper().getScreenType(context) ==
                                        ScreenType.mobile)
                                ? 24
                                : 80,
                            child: ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                    padding: EdgeInsets.zero,
                                    backgroundColor: ((modifiedList.isNotEmpty &&
                                                percentage["completedTasks"] !=
                                                    percentage["totalTasks"]) ||
                                            widget.ciltHeader!.syncStatus.index ==
                                                1)
                                        ? AppColors.primaryColor
                                        : Colors.grey,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    )),
                                onPressed: () async {
                                  if (kIsWeb) {
                                    if (!(await URLService
                                        .isInternetConnected())) {
                                      if (mounted) {
                                        UIHelper.showErrorDialog(
                                          context,
                                          description:
                                              AppLocalizations.of(context)!
                                                  .noInternetConnectivityString,
                                        );
                                      }
                                    } else {
                                      if (widget.ciltHeader?.status !=
                                              AppConstants.STATE_COMPLETED &&
                                          (modifiedList.isNotEmpty ||
                                              percentage["completedTasks"] ==
                                                  percentage["totalTasks"])) {
                                        if (mounted) {
                                          showDialog(
                                            context: context,
                                            barrierDismissible: false,
                                            builder: (context) {
                                              return AlertDialog(
                                                title: Text(AppLocalizations.of(
                                                        context)!
                                                    .are_you_sure_you_want_to_submit_round),
                                                actions: [
                                                  TextButton(
                                                    onPressed: () async {
                                                      final current =
                                                          DateTime.now();
                                                      DateTime endTime =
                                                          Utils.getTimeStamp(
                                                              widget.ciltHeader!
                                                                  .start_on
                                                                  .toString(),
                                                              widget.ciltHeader!
                                                                  .start_at
                                                                  .toString());

                                                      if (current
                                                          .isAfter(endTime)) {
                                                        Navigator.pop(context);
                                                        showDialog(
                                                          context: context,
                                                          barrierDismissible:
                                                              false,
                                                          builder: (context) {
                                                            return AlertDialog(
                                                              title: Text(
                                                                  AppLocalizations.of(
                                                                          context)!
                                                                      .please_provide_reason_for_submission_delay),
                                                              content:
                                                                  TextField(
                                                                decoration:
                                                                    InputDecoration(
                                                                  hintText: AppLocalizations.of(
                                                                          context)!
                                                                      .reason_for_submission_delay,
                                                                  border: OutlineInputBorder(
                                                                      borderSide:
                                                                          BorderSide(
                                                                              color: AppColors.grey)),
                                                                  focusedBorder:
                                                                      OutlineInputBorder(
                                                                          borderSide:
                                                                              BorderSide(color: AppColors.grey)),
                                                                  enabledBorder:
                                                                      OutlineInputBorder(
                                                                          borderSide:
                                                                              BorderSide(color: AppColors.grey)),
                                                                  contentPadding: const EdgeInsets
                                                                      .symmetric(
                                                                      vertical:
                                                                          10,
                                                                      horizontal:
                                                                          10),
                                                                ),
                                                                controller:
                                                                    delayReason,
                                                                onChanged:
                                                                    (value) {},
                                                              ),
                                                              actions: [
                                                                TextButton(
                                                                  onPressed:
                                                                      () async {
                                                                    dynamic
                                                                        result =
                                                                        _getProgressValue(
                                                                            widget.ciltHeader);
                                                                    if (result !=
                                                                        null) {
                                                                      Navigator.of(
                                                                              context)
                                                                          .pop();

                                                                      if (result[
                                                                              "totalTasks"] ==
                                                                          result[
                                                                              "completedTasks"]) {
                                                                        await getSubmitWithCloseAction(
                                                                            widget.ciltHeader!);
                                                                      } else {
                                                                        await getSubmitWithoutCloseAction(
                                                                            widget.ciltHeader!);
                                                                      }
                                                                    }
                                                                  },
                                                                  child: Text(
                                                                      AppLocalizations.of(
                                                                              context)!
                                                                          .submit),
                                                                ),
                                                                TextButton(
                                                                  onPressed:
                                                                      () {
                                                                    Navigator.of(
                                                                            context)
                                                                        .pop(); // Just close the reason dialog
                                                                  },
                                                                  child: Text(
                                                                      AppLocalizations.of(
                                                                              context)!
                                                                          .cancel),
                                                                ),
                                                              ],
                                                            );
                                                          },
                                                        );
                                                      } else {
                                                        // No delay, process directly
                                                        dynamic result =
                                                            _getProgressValue(
                                                                widget
                                                                    .ciltHeader);
                                                        if (result != null) {
                                                          Navigator.of(context)
                                                              .pop(); // Close the confirmation dialog

                                                          if (result[
                                                                  "totalTasks"] ==
                                                              result[
                                                                  "completedTasks"]) {
                                                            await getSubmitWithCloseAction(
                                                                widget
                                                                    .ciltHeader!);
                                                          } else {
                                                            await getSubmitWithoutCloseAction(
                                                                    widget
                                                                        .ciltHeader!)
                                                                .then((value) =>
                                                                    {});
                                                          }
                                                        }
                                                      }
                                                    },
                                                    child: Text(
                                                        AppLocalizations.of(
                                                                context)!
                                                            .submit),
                                                  ),
                                                  TextButton(
                                                    onPressed: () {
                                                      Navigator.pop(context);
                                                    },
                                                    child: Text(
                                                        AppLocalizations.of(
                                                                context)!
                                                            .cancel),
                                                  ),
                                                ],
                                              );
                                            },
                                          );
                                        }
                                      }
                                    }
                                  } else {
                                    if (widget.ciltHeader?.status !=
                                            AppConstants.STATE_COMPLETED &&
                                        (modifiedList.isNotEmpty ||
                                            percentage["completedTasks"] ==
                                                percentage["totalTasks"])) {
                                      showDialog(
                                        context: context,
                                        barrierDismissible: false,
                                        builder: (context) {
                                          return AlertDialog(
                                            title: Text(AppLocalizations.of(
                                                    context)!
                                                .are_you_sure_you_want_to_submit_round),
                                            actions: [
                                              TextButton(
                                                onPressed: () async {
                                                  final current =
                                                      DateTime.now();
                                                  DateTime endTime =
                                                      Utils.getTimeStamp(
                                                          widget.ciltHeader!
                                                              .start_on
                                                              .toString(),
                                                          widget.ciltHeader!
                                                              .start_at
                                                              .toString());

                                                  if (current
                                                      .isAfter(endTime)) {
                                                    Navigator.pop(context);
                                                    showDialog(
                                                      context: context,
                                                      barrierDismissible: false,
                                                      builder: (context) {
                                                        return AlertDialog(
                                                          title: Text(
                                                              AppLocalizations.of(
                                                                      context)!
                                                                  .please_provide_reason_for_submission_delay),
                                                          content: TextField(
                                                            decoration:
                                                                InputDecoration(
                                                              hintText: AppLocalizations
                                                                      .of(context)!
                                                                  .reason_for_submission_delay,
                                                              border: OutlineInputBorder(
                                                                  borderSide:
                                                                      BorderSide(
                                                                          color:
                                                                              AppColors.grey)),
                                                              focusedBorder: OutlineInputBorder(
                                                                  borderSide:
                                                                      BorderSide(
                                                                          color:
                                                                              AppColors.grey)),
                                                              enabledBorder: OutlineInputBorder(
                                                                  borderSide:
                                                                      BorderSide(
                                                                          color:
                                                                              AppColors.grey)),
                                                              contentPadding:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                      vertical:
                                                                          10,
                                                                      horizontal:
                                                                          10),
                                                            ),
                                                            controller:
                                                                delayReason,
                                                            onChanged:
                                                                (value) {},
                                                          ),
                                                          actions: [
                                                            TextButton(
                                                              onPressed:
                                                                  () async {
                                                                dynamic result =
                                                                    _getProgressValue(
                                                                        widget
                                                                            .ciltHeader);
                                                                if (result !=
                                                                    null) {
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop();

                                                                  if (result[
                                                                          "totalTasks"] ==
                                                                      result[
                                                                          "completedTasks"]) {
                                                                    await getSubmitWithCloseAction(
                                                                        widget
                                                                            .ciltHeader!);
                                                                  } else {
                                                                    await getSubmitWithoutCloseAction(
                                                                        widget
                                                                            .ciltHeader!);
                                                                  }
                                                                }
                                                              },
                                                              child: Text(
                                                                  AppLocalizations.of(
                                                                          context)!
                                                                      .submit),
                                                            ),
                                                            TextButton(
                                                              onPressed: () {
                                                                Navigator.of(
                                                                        context)
                                                                    .pop(); // Just close the reason dialog
                                                              },
                                                              child: Text(
                                                                  AppLocalizations.of(
                                                                          context)!
                                                                      .cancel),
                                                            ),
                                                          ],
                                                        );
                                                      },
                                                    );
                                                  } else {
                                                    // No delay, process directly
                                                    dynamic result =
                                                        _getProgressValue(
                                                            widget.ciltHeader);
                                                    if (result != null) {
                                                      Navigator.of(context)
                                                          .pop(); // Close the confirmation dialog

                                                      if (result[
                                                              "totalTasks"] ==
                                                          result[
                                                              "completedTasks"]) {
                                                        await getSubmitWithCloseAction(
                                                            widget.ciltHeader!);
                                                      } else {
                                                        await getSubmitWithoutCloseAction(
                                                                widget
                                                                    .ciltHeader!)
                                                            .then(
                                                                (value) => {});
                                                      }
                                                    }
                                                  }
                                                },
                                                child: Text(AppLocalizations.of(
                                                        context)!
                                                    .submit),
                                              ),
                                              TextButton(
                                                onPressed: () {
                                                  Navigator.pop(context);
                                                },
                                                child: Text(AppLocalizations.of(
                                                        context)!
                                                    .cancel),
                                              ),
                                            ],
                                          );
                                        },
                                      );
                                    }
                                  }
                                },
                                child: (!kIsWeb &&
                                        UIHelper().getScreenType(context) == ScreenType.mobile)
                                    ? Icon(Icons.save)
                                    : Text(AppLocalizations.of(context)!.submit)))
                    : const SizedBox.shrink(),
              // isExecutionVisible() ? _getPopupMenu() : SizedBox(),
              _getPopupMenu(),
            ],
          ),
        )
      ],
    );
  }

  Widget _getPopupMenu() {
    final taskList = ref.watch(ciltExecuteTaskListProvider);
    menuItems = [
      {'label': 'Skip Round', 'icon': Icons.fast_forward_outlined},
      {'label': 'Reject', 'icon': Icons.cancel},
      {'label': 'Info', 'icon': Icons.info},
    ];
    final modifiedList = taskList
        .where((element) =>
            element.objectStatus.index == 0 &&
            element.cilt_id == widget.ciltHeader!.cilt_id &&
            (element.status == AppConstants.STATE_TASK_COMP ||
                element.status == AppConstants.STATE_COMPLETED))
        .toList();
    var percentage = _getProgressValue(widget.ciltHeader);

    if (percentage["percentage"] >= 1.0 || !isExecutionVisible()) {
      menuItems.removeWhere((element) => element["label"] == "Skip Round");
    }
    if (ref.read(userProvider)!.user_id != widget.ciltHeader!.assigned_to ||
        modifiedList.isNotEmpty) {
      menuItems.removeWhere((element) => element["label"] == "Reject");
    }

    return PopupMenuButton<Map<String, dynamic>>(
      padding: EdgeInsets.zero,
      icon: const Icon(Icons.more_vert, size: 30),
      onSelected: (item) {
        onItemSelected(item['label']);
      },
      itemBuilder: (context) => menuItems.map((item) {
        return PopupMenuItem<Map<String, dynamic>>(
          value: item,
          child: Row(
            children: [
              Icon(item['icon'], color: AppColors.black),
              const SizedBox(width: 10),
              Text(item['label']),
            ],
          ),
        );
      }).toList(),
    );
  }

  onItemSelected(String item) {
    switch (item) {
      case 'Maps':
        break;
      case 'Report':
        break;
      case 'Skip Round':
        onSkipRound(context, ref, widget.ciltHeader!);
        break;
      case 'Reject':
        onReject();
        // onSkipRound(context, ref, widget.ciltHeader!);
        break;
      case 'Asset View':
        break;
      case 'Info':
        if (widget.ciltPlanHeader!.asset_no != null) {
          ref
              .read(singleAssetProvider.notifier)
              .getAssetHeader(widget.ciltPlanHeader!.asset_no!);
        }
        if (ScreenType.desktop == UIHelper().getScreenType(context)) {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (rootDialogContext) => Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxWidth: 420, // Slightly more than a mobile screen width
                      maxHeight:
                          800, // Not more than the size of a mobile phone
                    ),
                    child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: RoundPlanDetailScreen(
                            ciltPlanHeader: widget.ciltPlanHeader!)),
                  )));
        } else {
          Navigator.of(context).push(MaterialPageRoute(
            builder: (context) =>
                RoundPlanDetailScreen(ciltPlanHeader: widget.ciltPlanHeader!),
          ));
        }

        break;
    }
  }

  Map<String, dynamic> _getProgressValue(CILT_EXEC_HEADER? ciltHeader) {
    if (ciltHeader != null) {
      final numberOfTasks =
          ref.watch(ciltTasksProvider)[ciltHeader.plan_id] ?? [];
      final numberOfCompletedTasks =
          ref.watch(ciltExecuteProvider)[ciltHeader.cilt_id] ?? [];
      int totalTasks = numberOfTasks
          .where((element) =>
              element.plan_id.toString() == ciltHeader.plan_id.toString())
          .length;
      int completedTasks = numberOfCompletedTasks.length;
      numberOfCompletedTasks
          .where((element) =>
              element.cilt_id.toString() == ciltHeader.cilt_id.toString())
          .length;
      if (totalTasks == 0) {
        return {
          'percentage': 0.0,
          'completedTasks': 0,
          'totalTasks': 0,
        };
      }
      double percentage =
          double.parse((completedTasks / totalTasks).toStringAsFixed(2));
      return {
        'percentage': percentage,
        'completedTasks': completedTasks,
        'totalTasks': totalTasks,
      };
    } else {
      return {
        'percentage': 0.0,
        'completedTasks': 0,
        'totalTasks': 0,
      };
    }
  }

  onSkipRound(
      BuildContext context, WidgetRef ref, CILT_EXEC_HEADER ciltHeader) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return true;
          },
          child: StatefulBuilder(
            builder: (context, setState) {
              return AlertDialog(
                contentPadding: const EdgeInsets.all(5),
                content: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 400),
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                AppLocalizations.of(context)!.select_reason,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold, fontSize: 14),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          getReasonDropdown(context, ref, setState),
                          const SizedBox(height: 10),
                          TextField(
                            style: const TextStyle(fontSize: 14),
                            minLines: 3,
                            controller: reason,
                            onChanged: (value) {
                              setState(() {
                                reason.text = value;
                              });
                            },
                            maxLines: null,
                            decoration: InputDecoration(
                              hintText: AppLocalizations.of(context)!
                                  .additionalComment,
                              border: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              focusedBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              enabledBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              contentPadding: const EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 10),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: reason.text.isEmpty || selectedSkipReason.isEmpty
                        ? null
                        : () async {
                            if (reason.text == '' || reason.text == null) {
                              Navigator.pop(context);
                              UIHelper.showInfoDialogWithtitleAndDescription(
                                  context,
                                  title: AppLocalizations.of(context)!.warning,
                                  description: AppLocalizations.of(context)!
                                      .please_add_additional_message);
                            } else {
                              Navigator.pop(context);
                              await onSaveReason(reason.text, ciltHeader);
                            }
                          },
                    child: Text(
                      AppLocalizations.of(context)!.save,
                      style: TextStyle(
                        color: reason.text.isEmpty || selectedSkipReason.isEmpty
                            ? AppColors.grey
                            : AppColors.primaryColor,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      selectedSkipReason = '';
                      reason.clear();
                      Navigator.pop(context);
                    },
                    child: Text(
                      AppLocalizations.of(context)!.cancel,
                      style: TextStyle(
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  onReject() {
    UIHelper.showConfirmationDialogWithYesOrNo(
      context,
      description: AppLocalizations.of(context)!.do_you_want_to_reject_cilt,
      yes: () async {
        await rejectCitPlan(widget.ciltHeader!);
        roundDetailViewNotifier.value = null;
      },
      no: () {
        Navigator.of(context, rootNavigator: true).pop();
      },
    );
  }

  Future<void> rejectCitPlan(CILT_EXEC_HEADER ciltExecHeader) async {
    final plant = ref.watch(plantProvider.notifier).state;
    final plantSection = ref.watch(plantSectionProvider.notifier).state;
    final shift = ref.read(shiftProvider.notifier).state;

    CILT_EXEC_ACTION rejectAction = CILT_EXEC_ACTION(
        cilt_id: ciltExecHeader.cilt_id, user_action: Constants.ACTION_REJECT);
    rejectAction.fid = ciltExecHeader.lid;
    await DbHelper().acceptCiltAction(rejectAction);

    await DbHelper.updateCilt(ciltExecHeader);

    var result = await PAHelper.rejectCLITInSyncMode(context, ciltExecHeader);

    final searchkey = ref.read(roundsSearchProvider);
    // if(!kIsWeb && UIHelper().getScreenType(context) == ScreenType.mobile){
    //   ref.read(roundsSearchProvider.notifier).state = '';
    //   await ref.read(filteredCiltProvider.notifier).filter(plant, plantSection, "", searchkey, ref);
    // } else{
    await ref
        .read(filteredCiltProvider.notifier)
        .filter(plant, plantSection, shift, searchkey, ref);
    // }

    // await ref
    //     .read(ciltHeaderListProvider.notifier)
    //     .fetchCiltListHeaders(plant, plantSection, shift, ref);
    await ref
        .read(ciltPlanListHeaderProvider.notifier)
        .fetchCiltPlanListHeaders();
    Navigator.of(context, rootNavigator: true).pop();
    if (!kIsWeb && UIHelper().getScreenType(context) == ScreenType.mobile) {
      Navigator.of(context, rootNavigator: true).pop();
      // Navigator.of(context, rootNavigator: true).pop();
    }
    if (result.body['InfoMessage'] != null) {
      String? message = result.body['InfoMessage'][0]['message'];
      if (message != null) {
        UIHelper.showErrorDialog(
          context,
          title: "Error",
          description: message,
        );
      }
    }
  }

  Widget getReasonDropdown(
      BuildContext context, WidgetRef ref, StateSetter setState) {
    final skipReasonList = ref
        .watch(skipReasonListProvider)
        .where((element) => (element.category! & 4) != 0)
        .toList();
    final skipReason = ref.watch(skipReasonProvider.notifier);
    final dropdownItems = skipReasonList.map((option) {
      return DropdownMenuItem<String>(
        value: option.description,
        child: Padding(
          padding: const EdgeInsets.only(left: 5.0),
          child: Text(option.description!, style: UIHelper.valueStyle()),
        ),
      );
    }).toList();

    if (dropdownItems.where((item) => item.value == '').isEmpty) {
      dropdownItems.insert(
        0,
        DropdownMenuItem<String>(
          value: '',
          child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(AppLocalizations.of(context)!.select,
                style: UIHelper.valueStyle()),
          ),
        ),
      );
    }

    return Container(
      decoration: UIHelper.fieldDecoration(),
      child: DropdownButton<String>(
        elevation: 0,
        isExpanded: true,
        underline: const SizedBox(),
        value: selectedSkipReason.isEmpty ? "" : selectedSkipReason,
        items: dropdownItems,
        onChanged: (newValue) {
          setState(() {
            selectedSkipReason = newValue!;
            skipReason.getSkipReason(selectedSkipReason);
          });
        },
      ),
    );
  }

  Future<void> onSaveReason(String reason, CILT_EXEC_HEADER ciltHeader) async {
    try {
      final skipReason = ref.watch(skipReasonProvider.notifier).state;
      ciltHeader.reason = skipReason.reason.toString();
      ciltHeader.skip_comments = reason;
      ciltHeader.p_mode = AppConstants.modified;

      await AppDatabaseManager().update(
          DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, ciltHeader.toJson()));

      List<CILT_TASK> list = await DbHelper.getCiltTaskPlanListHeaderByPlanId(
          ciltHeader.plan_id.toString());

      for (CILT_TASK task in list) {
        await DbHelper().taskUpdate(
            task: task,
            status: AppConstants.STATE_TASK_COMP,
            ciltHeader: ciltHeader,
            skipreason: reason,
            reasonCode: skipReason.reason.toString(),
            p_mode: AppConstants.modified);
        await ref.read(ciltTasksProvider.notifier).getCiltTasks(task.plan_id!);
        await ref.read(ciltExecuteProvider.notifier).getCiltExecute(ciltHeader);
      }

      await ref
          .read(ciltExecuteTaskListProvider.notifier)
          .getCiltExecuteTaskList();

      await ref
          .read(ciltTaskNotifier.notifier)
          .filter(widget.ciltPlanHeader?.plan_id.toString() ?? "", "", ref);

      CILT_EXEC_ACTION action = CILT_EXEC_ACTION(
          cilt_id: ciltHeader.cilt_id,
          user_action: AppConstants.STATE_COMPLETED);

      action.fid = ciltHeader.lid;

      await AppDatabaseManager().execute(
          "select * from CILT_EXEC_ACTION where CILT_ID = '${ciltHeader.cilt_id.toString()}'");
      // Future.delayed(Duration.zero, () {
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return Dialog(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 400),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Center(
                              child: Text(
                                "${AppLocalizations.of(context)!.congratulations} 🎉",
                                style: UIHelper.titleStyle16(),
                              ),
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              Navigator.of(context).pop();
                            },
                            child: Icon(
                              Icons.cancel_outlined,
                              color: AppColors.black,
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 16.0,
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.all_tasks_completed,
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryColor,
                          minimumSize: const Size(double.infinity, 40),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                        onPressed: () async {
                          final current = DateTime.now();
                          DateTime endTime = DateTime(
                            current.year,
                            current.month,
                            current.day,
                            int.parse(
                                ciltHeader.end_at.toString().substring(0, 2)),
                            int.parse(
                                ciltHeader.end_at.toString().substring(2, 4)),
                          );

                          if (current.isAfter(endTime)) {
                            // Show reason for delay dialog
                            Navigator.pop(context);
                            showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (context) {
                                return AlertDialog(
                                  title: Text(AppLocalizations.of(context)!
                                      .please_provide_reason_for_submission_delay),
                                  content: TextField(
                                    decoration: InputDecoration(
                                      hintText: AppLocalizations.of(context)!
                                          .reason_for_submission_delay,
                                      border: OutlineInputBorder(
                                        borderSide:
                                            BorderSide(color: AppColors.grey),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide:
                                            BorderSide(color: AppColors.grey),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderSide:
                                            BorderSide(color: AppColors.grey),
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                        vertical: 10,
                                        horizontal: 10,
                                      ),
                                    ),
                                    controller: delayReason,
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () async {
                                        Navigator.pop(context);
                                        await getSubmitWithCloseAction(
                                            ciltHeader);
                                      },
                                      child: Text(
                                          AppLocalizations.of(context)!.submit),
                                    ),
                                    TextButton(
                                      onPressed: () {
                                        delayReason.clear();
                                        Navigator.of(context).pop();
                                        // Just close the reason dialog
                                      },
                                      child: Text(
                                          AppLocalizations.of(context)!.cancel),
                                    ),
                                  ],
                                );
                              },
                            );
                          } else {
                            Navigator.pop(context);
                            await getSubmitWithCloseAction(ciltHeader);
                          }
                        },
                        child: Text(
                          AppLocalizations.of(context)!.submit,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      }
    } catch (e) {
      Logger.logError('CiltDetailScreen', 'onSaveReason', e.toString());
    }
  }

  Future<void> getSubmitWithCloseAction(CILT_EXEC_HEADER ciltHeader) async {
    if (delayReason.text.isNotEmpty) {
      ciltHeader.delay_comments = delayReason.text;
    }
    await AppDatabaseManager().update(
        DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, ciltHeader.toJson()));
    final numberOfCompletedTasks = ref
            .watch(ciltExecuteProvider)[ciltHeader.cilt_id] ??
        [].where((element) => element.cilt_id == ciltHeader.cilt_id).toList();
    List<Future<void>> updateTasks = [];
    for (var task in numberOfCompletedTasks) {
      await DbHelper.updateCiltExecTask(task);
      CILT_EXEC_ACTION action = CILT_EXEC_ACTION(
          cilt_id: ciltHeader.cilt_id,
          user_action: AppConstants.STATE_COMPLETED);
      action.fid = ciltHeader.lid;

      var result = await AppDatabaseManager().execute(
          "select * from CILT_EXEC_ACTION where CILT_ID = '${ciltHeader.cilt_id.toString()}'");
      if (result.isNotEmpty) {
        updateTasks.add(AppDatabaseManager().update(
            DBInputEntity(CILT_EXEC_ACTION.TABLE_NAME, action.toJson())));
      } else {
        updateTasks.add(AppDatabaseManager().insert(
            DBInputEntity(CILT_EXEC_ACTION.TABLE_NAME, action.toJson())));
      }
    }
    await Future.wait(updateTasks);
    List<CILT_EXEC_TASK> cilt_exec_tasks = ref
        .read(ciltExecuteTaskListProvider.notifier)
        .findAllCiltExecOfCilt(ciltHeader);

    List<DOCUMENT_HEADER> headerData = [];

    for (var task in cilt_exec_tasks) {
      final documentHeaders = ref
          .watch(ciltTaskExecDocumentHeaderProvider.notifier)
          .getDocumentHeadersForTask(task);
      if (documentHeaders != null) {
        headerData.addAll(documentHeaders);
      }
    }

    List<DOCUMENT_HEADER> documents = headerData
        .where((element) => element.objectStatus == ObjectStatus.add)
        .toList();

    if (kIsWeb) {
      if (mounted) {
        UIHelper().progressDialog(
            context: context, message: "Submiting Cilt Execution");
      }
      for (DOCUMENT_HEADER document in documents) {
        var doc = await DbHelper()
            .getAttachmentFromIndexDbByUid(document.doc_id ?? "");
        await SyncEngine().uploadAttachmentSync(
            doc ?? "", document.file_name ?? "", document.doc_id ?? "");
      }
      await PAHelper.addDocumentInSyncMode(context, documents);
    } else {
      await PAHelper.addDocumentInAsyncMode(context, documents);
    }
    // }
    if (!kIsWeb) {
      await PAHelper.modifyCiltExecInAsyncMode(context, ciltHeader);
    } else {
      await PAHelper.modifyCiltExecInSyncMode(context, ciltHeader);
      Navigator.of(context, rootNavigator: true).pop();
      // Navigator.of(context).pop();
    }
    delayReason.clear();
    selectedSkipReason = '';
    reason.clear();

    ///TODO : Ranjit
    /*    for (var task in numberOfCompletedTasks) {
      task.p_mode = "";
      await DbHelper.updateCiltExecTask(task);
      ref.read(ciltExecuteTaskListProvider.notifier).updateCiltExexTask(task);
    }*/

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocalizations.of(context)!.submitted_successfully),
        duration: const Duration(seconds: 2),
      ),
    );

    if (mounted) {
      final plant = ref.read(plantProvider);
      final plantSection = ref.read(plantSectionProvider);
      final shift = ref.read(shiftProvider);

      final searchkey = ref.read(roundsSearchProvider);
      if (!kIsWeb && UIHelper().getScreenType(context) == ScreenType.mobile) {
        ref.read(roundsSearchProvider.notifier).state = '';
        await ref
            .read(filteredCiltProvider.notifier)
            .filter(plant, plantSection, "", searchkey, ref);
      } else {
        await ref
            .read(filteredCiltProvider.notifier)
            .filter(plant, plantSection, shift, searchkey, ref);
      }
      // await ref
      //     .read(ciltHeaderListProvider.notifier)
      //     .fetchCiltListHeaders(plant, plantSection, shift, ref);

      if (ScreenType.desktop != UIHelper().getScreenType(context)) {
        Navigator.pop(context, ciltHeader);
      } else {
        await ref
            .read(ciltExecuteTaskListProvider.notifier)
            .getCiltExecuteTaskList();

        await ref
            .read(ciltTaskNotifier.notifier)
            .filter(widget.ciltPlanHeader?.plan_id.toString() ?? "", "", ref);
      }

      final searchProvider = ref.read(roundsSearchProvider.notifier).state;

      ref
          .read(filteredCiltProvider.notifier)
          .filter(plant, plantSection, shift, searchProvider, ref);

      if (!kIsWeb) {
        CILT_EXEC_HEADER? newExecHeader =
            await DbHelper.getCiltExecHeader(ciltHeader);
        if (newExecHeader != null) {
          await ref
              .read(ciltHeaderProvider.notifier)
              .selectedCiltHeaders(ciltHeader);
        }

        if ((newExecHeader!.syncStatus.index == 2)) {
          List<CILT_EXEC_TASK> ciltExecTasks = ref
              .read(ciltExecuteTaskListProvider.notifier)
              .findAllCiltExecOfCilt(ciltHeader);

          for (var task in ciltExecTasks) {
            if (task.p_mode == AppConstants.modified) {
              await DbHelper.updateCiltExecTask(task);
            }
          }
          await ref
              .read(ciltExecuteTaskListProvider.notifier)
              .getCiltExecuteTaskList();

          if (mounted) {
            setState(() {});
          }
        }
      }
    }
  }

  Future<void> getSubmitWithoutCloseAction(CILT_EXEC_HEADER ciltHeader) async {
    final plant = ref.read(plantProvider);
    final plantSection = ref.read(plantSectionProvider);
    final shift = ref.read(shiftProvider);
    ciltHeader.p_mode = AppConstants.modified;
    final numberOfCompletedTasks = ref
            .watch(ciltExecuteProvider)[ciltHeader.cilt_id] ??
        [].where((element) => element.cilt_id == ciltHeader.cilt_id).toList();

    if (delayReason.text.isNotEmpty) {
      ciltHeader.delay_comments = delayReason.text;
    }

    await AppDatabaseManager().update(
        DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, ciltHeader.toJson()));

    List<CILT_EXEC_TASK> ciltExecTasks = ref
        .read(ciltExecuteTaskListProvider.notifier)
        .findAllCiltExecOfCilt(ciltHeader);

    List<DOCUMENT_HEADER> headerData = [];

    for (var task in ciltExecTasks) {
      final documentHeaders = ref
          .watch(ciltTaskExecDocumentHeaderProvider.notifier)
          .getDocumentHeadersForTask(task);
      if (documentHeaders != null) {
        headerData.addAll(documentHeaders);
      }
    }

    List<DOCUMENT_HEADER> documents = headerData
        .where((element) => element.objectStatus == ObjectStatus.add)
        .toList();

    if (kIsWeb) {
      UIHelper().progressDialog(
          context: context, message: "Submiting Cilt Execution");
      for (DOCUMENT_HEADER document in documents) {
        var doc = await DbHelper()
            .getAttachmentFromIndexDbByUid(document.doc_id ?? "");
        await SyncEngine().uploadAttachmentSync(
            doc ?? "", document.file_name ?? "", document.doc_id ?? "");
      }
      await PAHelper.addDocumentInSyncMode(context, documents);
    } else {
      await PAHelper.addDocumentInAsyncMode(context, documents);
    }

    if (!kIsWeb) {
      {
        await PAHelper.modifyCiltExecInAsyncMode(context, ciltHeader);
      }
    } else {
      await PAHelper.modifyCiltExecInSyncMode(context, ciltHeader);
      Navigator.of(context).pop();
    }
    delayReason.clear();
    selectedSkipReason = '';
    reason.clear();

    ///TODO : Ranjit
/*
    for (var task in numberOfCompletedTasks) {
      await DbHelper.updateCiltExecTask(task);
    }
*/
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocalizations.of(context)!.submitted_successfully),
        duration: const Duration(seconds: 2),
      ),
    );

    if (mounted) {
      if (ScreenType.desktop != UIHelper().getScreenType(context)) {
        Navigator.pop(context, ciltHeader);
      } else {
        setState(() {});
        await ref
            .read(ciltExecuteTaskListProvider.notifier)
            .getCiltExecuteTaskList();
        await ref
            .read(ciltTaskNotifier.notifier)
            .filter(widget.ciltPlanHeader?.plan_id.toString() ?? "", "", ref);
        /*  setState(() {});*/
      }

      final searchProvider = ref.read(roundsSearchProvider.notifier).state;

      ref
          .read(filteredCiltProvider.notifier)
          .filter(plant, plantSection, shift, searchProvider, ref);
    }
    ref.read(ciltToggleStateProvider.notifier).setToggleState(false);

    if (!kIsWeb) {
      CILT_EXEC_HEADER? newExecHeader =
          await DbHelper.getCiltExecHeader(ciltHeader);
      if (newExecHeader != null) {
        await ref
            .read(ciltHeaderProvider.notifier)
            .selectedCiltHeaders(ciltHeader);
    }

      if ((newExecHeader!.syncStatus.index == 2)) {
        List<CILT_EXEC_TASK> ciltExecTasks = ref
            .read(ciltExecuteTaskListProvider.notifier)
            .findAllCiltExecOfCilt(ciltHeader);

        for (var task in ciltExecTasks) {
          if (task.p_mode == AppConstants.modified) {
            await DbHelper.updateCiltExecTask(task);
          }
        }
        await ref
            .read(ciltExecuteTaskListProvider.notifier)
            .getCiltExecuteTaskList();

        if (mounted) {
          setState(() {});
        }
      }
    }
  }

  bool isExecutionVisible() {
    final role = ref.watch(roleProvider);
    final todayDate = DateFormat('yyyyMMdd').format(DateTime.now());
    DateTime now = DateTime.now();
    String formattedTime = DateFormat('HHmmss').format(now);
    String startAT = widget.ciltHeader!.start_at.toString();
    DateTime executionDateTime =
        Utils.getTimeStamp(widget.ciltHeader!.start_on.toString(), startAT);

    if (role != null) {
      if (UIHelper.isExecute(role.cilt!) &&
          (ref.watch(userProvider)?.user_id == widget.ciltHeader?.assigned_to &&
              widget.ciltHeader?.status != "REJECTED") &&
          executionDateTime.isBefore(now)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool getConditionsAccess() {
    final user_header = ref.watch(userProvider);
    if (widget.ciltPlanHeader != null) {
      if (widget.ciltHeader!.status == AppConstants.STATE_ACCEPTED ||
          widget.ciltHeader!.status == AppConstants.STATE_COMPLETED) {
        DateTime now = DateTime.now();
        DateTime ciltDateAndTime =
            getTimeStamps(); // returns combined start_on + start_at

        String startAT = widget.ciltHeader!.start_at.toString();
        String startON = widget.ciltHeader!.start_on.toString();

        final int startYear = int.parse(startON.substring(0, 4));
        final int startMonth = int.parse(startON.substring(4, 6));
        final int startDay = int.parse(startON.substring(6, 8));
        final DateTime onlyStartDate =
            DateTime(startYear, startMonth, startDay);

        if (now.year == onlyStartDate.year &&
            now.month == onlyStartDate.month &&
            now.day == onlyStartDate.day &&
            now.isBefore(ciltDateAndTime) &&
            (widget.ciltHeader!.status != AppConstants.STATE_COMPLETED)) {
          return true;
        } else {
          return false;
        }
      } else {
        if (widget.ciltHeader!.assigned_to == null ||
            ((widget.ciltHeader?.status == AppConstants.STATE_ASSIGNED) &&
                user_header?.user_id == widget.ciltHeader!.assigned_to)) {
          return false;
        } else if (user_header?.user_id == widget.ciltHeader!.assigned_to) {
          if (widget.ciltHeader!.status != AppConstants.STATE_REJECTED) {
            return false;
          } else {
            if (user_header?.user_id == widget.ciltHeader!.assigned_to) {
              return false;
            } else {
              return true;
            }
          }
        } else {
          return true;
        }
      }
    } else {
      return false;
    }
  }

  String formatStartTimeAsClock(String startAT) {
    if (startAT.length != 6) return "Invalid Time";

    String hours = startAT.substring(0, 2);
    String minutes = startAT.substring(2, 4);
    String seconds = startAT.substring(4, 6);

    return "$hours:$minutes:$seconds"; // Format as HH:mm:ss
  }

  DateTime getTimeStamps() {
    final String startAT =
        widget.ciltHeader!.start_at.toString(); // e.g., "140000"
    final String startON =
        widget.ciltHeader!.start_on.toString(); // e.g., "20250323"

    final year = int.parse(startON.substring(0, 4));
    final month = int.parse(startON.substring(4, 6));
    final day = int.parse(startON.substring(6, 8));

    int time = int.parse(startAT);

    int hours = time ~/ 10000;
    int minutes = (time % 10000) ~/ 100;
    int seconds = time % 100; // Extract seconds by taking the remainder

    final DateTime combinedDateTime =
        DateTime(year, month, day, hours, minutes, seconds);
    return combinedDateTime;
  }

  String getConditionsAccessMsg() {
    final user_header = ref.watch(userProvider);
    if (widget.ciltPlanHeader != null) {
      if (widget.ciltHeader!.status == AppConstants.STATE_ACCEPTED ||
          widget.ciltHeader!.status == AppConstants.STATE_COMPLETED) {
        DateTime now = DateTime.now();
        DateTime ciltDateAndTime =
            getTimeStamps(); // returns combined start_on + start_at

        String startAT = widget.ciltHeader!.start_at.toString();
        String startON = widget.ciltHeader!.start_on.toString();

        final int startYear = int.parse(startON.substring(0, 4));
        final int startMonth = int.parse(startON.substring(4, 6));
        final int startDay = int.parse(startON.substring(6, 8));
        final DateTime onlyStartDate =
            DateTime(startYear, startMonth, startDay);

        if (now.year == onlyStartDate.year &&
            now.month == onlyStartDate.month &&
            now.day == onlyStartDate.day &&
            now.isBefore(ciltDateAndTime) &&
            (widget.ciltHeader!.status != AppConstants.STATE_COMPLETED)) {
          return "${AppLocalizations.of(context)!.cannot_start_task_before} ${formatStartTimeAsClock(startAT)}";
        } else {
          return "";
        }
      } else {
        if (widget.ciltHeader!.assigned_to == null ||
            ((widget.ciltHeader?.status == AppConstants.STATE_ASSIGNED) &&
                user_header?.user_id == widget.ciltHeader!.assigned_to)) {
          return "";
        } else if (user_header?.user_id == widget.ciltHeader!.assigned_to) {
          if (widget.ciltHeader!.status != AppConstants.STATE_REJECTED) {
            return "";
          } else {
            if (user_header?.user_id == widget.ciltHeader!.assigned_to) {
              return "";
            } else {
              return "${AppLocalizations.of(context)!.cannot_perform_action_as_assigned_to_other}";
            }
          }
        } else {
          return "${AppLocalizations.of(context)!.cannot_perform_action_as_assigned_to_other}";
        }
      }
    } else {
      return "";
    }
  }
}

class MySliverDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  MySliverDelegate({required this.child});

  @override
  double get maxExtent => kToolbarHeight;
  @override
  double get minExtent => kToolbarHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(
      child: Container(
          decoration: const BoxDecoration(color: Colors.white), child: child),
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
