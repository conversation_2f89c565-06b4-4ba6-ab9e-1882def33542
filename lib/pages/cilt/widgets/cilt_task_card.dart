import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logger/Logger.dart';
import 'package:open_file_plus/open_file_plus.dart';
import 'package:rounds/be/ASSET_HEADER.dart';
import 'package:rounds/be/CILT_EXEC_HEADER.dart';
import 'package:rounds/be/CILT_EXEC_TASK.dart';
import 'package:rounds/be/CILT_TASK.dart';
import 'package:rounds/be/CILT_TASK_DOC.dart';
import 'package:rounds/be/FAULT_HEADER.dart';
import 'package:rounds/be/LOCATION_HEADER.dart';
import 'package:rounds/be/SYSTEM_CONDITION_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/pages/fault/fault_detail_screen.dart';
import 'package:rounds/pages/fault/tabs/edit_fault_field_provider.dart';
import 'package:rounds/pages/inspection/inspection_screen.dart';
import 'package:rounds/providers/assets/asset_provider.dart';
import 'package:rounds/providers/assets/floc_provider.dart';
import 'package:rounds/providers/cilt/cilt_task_provider.dart';
import 'package:rounds/providers/system_condition_provider.dart';
import 'package:rounds/providers/user_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/debouncer.dart';
import 'package:rounds/utils/utils.dart';
import 'package:rounds/widgets/cilt_indicator.dart';
import 'package:rounds/widgets/image_viewer_screen.dart';
import 'package:rounds/widgets/round_attachment_picker.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:uuid/uuid.dart';
import '../../../../be/CILT_EXEC_ACTION.dart';
import '../../../../be/CILT_EXEC_DOC.dart';
import '../../../../be/DOCUMENT_ATTACHMENT.dart';
import '../../../../be/DOCUMENT_HEADER.dart';
import '../../../../be/SKIP_REASON_HEADER.dart';
import '../../../../helpers/pa_helper.dart';
import '../../../../providers/attachments/attachment_provider.dart';
import '../../../../providers/cilt/cilt_header_provider.dart';
import '../../../../providers/cilt/cilt_plan_header_provider.dart';
import '../../../../providers/fault/fault_header_provider.dart';
import '../../../../providers/fault/fault_type_provider.dart';
import 'package:intl/intl.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:path/path.dart' as path;

import '../../../../utils/app_constants.dart';
import '../../../be/CILT_SECTION.dart';

final expandedTileProvider = StateProvider<int>((ref) => -1);
final expandedAttachmentProvider = StateProvider<int>((ref) => -1);
final expandedCommentProvider = StateProvider<int>((ref) => -1);

class CiltTaskCard extends ConsumerStatefulWidget {
  final CILT_TASK task;
  final int index;
  final CILT_EXEC_TASK? execTaskHeader;
  final CILT_EXEC_HEADER? ciltExecHeader;
  final bool isDuratonRequried;
  final int totaltaskcount;
  final int sectionIndex;
  final ScrollController scrollController;

  const CiltTaskCard({
    super.key,
    required this.execTaskHeader,
    required this.task,
    required this.ciltExecHeader,
    required this.index,
    required this.totaltaskcount,
    required this.scrollController,
    required this.sectionIndex,
    this.isDuratonRequried = false,
  });

  @override
  _CiltTaskCardState createState() => _CiltTaskCardState();
}

class _CiltTaskCardState extends ConsumerState<CiltTaskCard> {
  late TextEditingController controller;
  late TextEditingController inputcontroller;
  late TextEditingController timercontroller;
  late TextEditingController dateAndTimercontroller;
  late TextEditingController dateRangecontroller;
  late TextEditingController locationcontroller;
  TextEditingController delayReason = TextEditingController();
  TextEditingController commentController = TextEditingController();
  TextEditingController reasonController = TextEditingController();
  List<DOCUMENT_HEADER> addedAttachments = [];
  int selectedImageIndex = -1;
  bool isUploading = false;
  bool isHovered = false;
  int hoveredIndices = -1;
  double uploadProgress = 0.0;
  bool? canEdit = false;
  List<SKIP_REASON_HEADER> skipReasonList = [];
  final _debouncer = Debouncer(milliseconds: 500);

  late SYSTEM_CONDITION_HEADER systemcondition;

  @override
  void initState() {
    super.initState();
    controller = TextEditingController();
    inputcontroller = TextEditingController();
    timercontroller = TextEditingController();
    dateAndTimercontroller = TextEditingController();
    dateRangecontroller = TextEditingController();
    locationcontroller = TextEditingController();
    systemcondition = ref
        .read(systemConditionProvider.notifier)
        .getSystemConditionHeader(widget.task.sys_cond ?? "");
  }

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
    skipReasonList = ref.watch(skipReasonListProvider.notifier).state;
    commentController =
        TextEditingController(text: "${widget.execTaskHeader?.comments}" ?? "");
  }

  @override
  void dispose() {
    controller.dispose();
    inputcontroller.dispose();
    timercontroller.dispose();
    commentController.dispose();
    dateAndTimercontroller.dispose();
    dateRangecontroller.dispose();
    delayReason.dispose();
    reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final expandedIndex = ref.watch(expandedTileProvider);
    final ciltHeader = ref.watch(ciltHeaderProvider.notifier).state;
    final isExpanded = expandedIndex ==
        "${widget.index} ${widget.task.task_id.toString()}".hashCode;

    commentController.text = widget.execTaskHeader?.comments ?? "";
    return GestureDetector(
      onTap: () {
        ref
            .read(documentAttachmentProvider.notifier)
            .fetchDocumentAttachments();
        ref
            .read(ciltTaskExecDocumentHeaderProvider.notifier)
            .fetchTaskDocumentHeaders(
                widget.execTaskHeader!, ciltHeader, widget.task);

        // ref.read(expandedTileProvider.notifier).state = isExpanded
        //     ? -1
        //     : "${widget.index} ${widget.key.toString()}".hashCode;

        ref.read(expandedTileProvider.notifier).state = isExpanded
            ? -1
            : "${widget.index} ${widget.task.task_id.toString()}".hashCode;
        if (kIsWeb) {
          final expandedAttachmentIndex = ref.watch(expandedAttachmentProvider);
          final isAttachmentExpanded = expandedAttachmentIndex == widget.index;
          ref.read(expandedAttachmentProvider.notifier).state =
              isAttachmentExpanded ? -1 : widget.index;
          ref.read(expandedCommentProvider.notifier).state = -1;
          ref
              .read(ciltTaskDocumentHeaders.notifier)
              .fetchTaskDocumentHeaders(widget.execTaskHeader!, widget.task);
        }
        if (showAttachmentDot(widget.execTaskHeader!)) {
          ref.read(expandedAttachmentProvider.notifier).state = widget.index;
          ref.read(expandedCommentProvider.notifier).state = -1;
        } else if (showNoteDot(widget.execTaskHeader!)) {
          ref.read(expandedAttachmentProvider.notifier).state = -1;
          ref.read(expandedCommentProvider.notifier).state = widget.index;
        }

        if (widget.sectionIndex == widget.totaltaskcount - 1) {
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            await Future.delayed(const Duration(milliseconds: 400));
            if (widget.scrollController.hasClients) {
              widget.scrollController.animateTo(
                widget.scrollController.position.maxScrollExtent,
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeOut,
              );
            }
          });
        }
      },
      child: Container(
        decoration: UIHelper.cardDecoration(),
        child: Padding(
          padding: UIHelper.columnFieldPadding(),
          child: Column(
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _getTitleRowWidget(),
                  (widget.task.location_id != null &&
                          widget.task.location_id != '')
                      ? Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: Row(
                            children: [
                              Align(
                                alignment: Alignment.topLeft,
                                child: Text(
                                  '${AppLocalizations.of(context)!.location_id} : ',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: AppColors.titleTextColor,
                                    fontWeight: FontWeight.w600,
                                    letterSpacing: 0.1,
                                  ),
                                ),
                              ),
                              const SizedBox(
                                height: 3,
                              ),
                              Text(
                                widget.task.location_id.toString(),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppColors.titleTextColor,
                                  letterSpacing: 0.1,
                                ),
                              ),
                            ],
                          ),
                        )
                      : SizedBox(),
                  (widget.task.asset_no != null && widget.task.asset_no != '')
                      ? Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: Row(
                            children: [
                              Align(
                                alignment: Alignment.topLeft,
                                child: Text(
                                  '${AppLocalizations.of(context)!.asset} : ',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: AppColors.titleTextColor,
                                    fontWeight: FontWeight.w600,
                                    letterSpacing: 0.1,
                                  ),
                                ),
                              ),
                              const SizedBox(
                                height: 3,
                              ),
                              Text(
                                widget.task.asset_no.toString(),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppColors.titleTextColor,
                                  fontWeight: FontWeight.w300,
                                  letterSpacing: 0.1,
                                ),
                              ),
                            ],
                          ),
                        )
                      : SizedBox(),
                  _getDescriptionRowWidget(),
                  isExecutionVisible()
                      ? _getSkipOrCompleteButtons()
                      : widget.execTaskHeader?.status ==
                              AppConstants.STATE_TASK_COMP
                          ? _getSkipOrCompleteButtons()
                          : SizedBox(),
                  widget.execTaskHeader?.status ==
                              AppConstants.STATE_TASK_COMP &&
                          widget.execTaskHeader?.reason != null &&
                          (widget.execTaskHeader?.reason ?? "").isNotEmpty
                      ? Center(
                          child: Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              getReasonDescriptionValue(),
                            ),
                          ),
                        )
                      : const SizedBox(),
                ],
              ),
              UIHelper.sizedBox10(),
              AnimatedCrossFade(
                firstChild: const SizedBox(
                  height: 0,
                  width: double.infinity,
                ),
                secondChild: _buildExpandedContent(context),
                crossFadeState: isExpanded
                    ? CrossFadeState.showSecond
                    : CrossFadeState.showFirst,
                duration: const Duration(milliseconds: 300),
                sizeCurve: Curves.easeInOut,
              )
            ],
          ),
        ),
      ),
    );
  }

  String getReasonDescriptionValue() {
    String? reasonReason;
    if (widget.execTaskHeader?.reason != null &&
        widget.execTaskHeader!.reason!.isNotEmpty) {
      reasonReason = widget.execTaskHeader!.reason!;
    }
    var filteredList = skipReasonList
        .where((element) => element.reason == reasonReason)
        .toList();

    String description = filteredList.isNotEmpty
        ? filteredList[0].description!
        : AppLocalizations.of(context)!.no_reason_found;
    return description;
  }

  Widget _buildExpandedContent(BuildContext context) {
    final role = ref.watch(roleProvider);
    final expandedAttachmentIndex = ref.watch(expandedAttachmentProvider);
    final expandedCommentIndex = ref.watch(expandedCommentProvider);

    final isAttachmentExpanded = expandedAttachmentIndex == widget.index;
    final isCommentExpanded = expandedCommentIndex == widget.index;

    return Column(
      children: [
        Container(
          margin: const EdgeInsets.only(bottom: 3),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              getExpandedIcon(
                  type: AppConstants.attachments,
                  child: Center(
                    child: Transform.rotate(
                      angle: 45 * (3.14159 / 180),
                      child: Icon(
                        Icons.attach_file,
                        size: 25,
                        color: AppColors.black,
                      ),
                    ),
                  ),
                  showDot: showAttachmentDot(widget.execTaskHeader!)),
              getExpandedIcon(
                  type: AppConstants.notes,
                  child: Center(
                    child: Image.asset(
                      'assets/icon/task_icons/notes.png',
                      width: 20,
                      height: 20,
                    ),
                  ),
                  showDot: showNoteDot(widget.execTaskHeader!)),
              role != null
                  ? (UIHelper.isCreate(role.fault!)
                      ? getExpandedIcon(
                          type: AppConstants.faults,
                          child: Center(
                            child: Icon(
                              Icons.error_outline,
                              color: AppColors.titleTextColor,
                              size: 25,
                            ),
                          ),
                          showDot:
                              showDot(widget.execTaskHeader?.cilt_task_id!))
                      : const SizedBox.shrink())
                  : const SizedBox.shrink(),
            ],
          ),
        ),
        if (isAttachmentExpanded)
          attachmentWidget(context, widget.execTaskHeader!),
        if (isCommentExpanded) _buildCommentInput(),
      ],
    );
  }

  bool showDot(int? task_id) {
    bool data = false;
    final ciltExecHeader =
        ref.watch(ciltExecuteTaskListProvider.notifier).state;
    final ciltHeader = ref.watch(ciltHeaderProvider.notifier).state;
    final faultListHeader = ref.watch(faultHeaderListProvider);
    if (ciltHeader.plan_id == widget.task.plan_id) {
      for (var datas in ciltExecHeader) {
        if (ciltHeader.cilt_id == datas.cilt_id) {
          CILT_EXEC_TASK exec = datas;
          if (exec.cilt_task_id == task_id) {
            for (var element in faultListHeader) {
              if (exec.cilt_task_id == element.cilt_task_id) {
                if (element.cilt_task_id != null) {
                  data = true;
                  break;
                }
              }
            }
          }
        }
      }
    }
    return data;
  }

  bool showNoteDot(CILT_EXEC_TASK task) {
    bool data = false;
    final ciltExecHeader =
        ref.watch(ciltExecuteTaskListProvider.notifier).state;
    final ciltHeader = ref.watch(ciltHeaderProvider.notifier).state;
    if (ciltHeader.cilt_id == widget.execTaskHeader?.cilt_id &&
        task.cilt_task_id == widget.execTaskHeader?.cilt_task_id) {
      for (var datas in ciltExecHeader) {
        if (task.section_id == datas.section_id) {
          CILT_EXEC_TASK exec = datas;
          if (exec.cilt_task_id == task.cilt_task_id) {
            if (exec.comments != null && exec.comments != "") {
              data = true;
              break;
            }
          }
        }
      }
    }
    return data;
  }

  bool showAttachmentDot(CILT_EXEC_TASK ciltExecTaskHeader) {
    bool data = false;
    final documentHeaders = ref
        .watch(ciltTaskExecDocumentHeaderProvider.notifier)
        .getDocumentHeadersForTask(ciltExecTaskHeader);

    final documentAttachments = ref.watch(documentAttachmentProvider);

    List<DOCUMENT_ATTACHMENT> attachmentList = documentAttachments.where(
      (attachment) {
        return documentHeaders
                ?.where((docHeader) => docHeader.lid == attachment.fid)
                .toList()
                .length ==
            1;
      },
    ).toList();
    if (attachmentList.isNotEmpty) {
      data = true;
    }
    return data;
  }

  getFaultData(String taskId) async {
    final ciltHeader = ref.watch(ciltHeaderProvider.notifier).state;
    final faultListHeader = ref.watch(faultHeaderListProvider.notifier).state;
    final faultHeader = ref.read(faultHeaderProvider.notifier);
    if (ciltHeader.plan_id == widget.task.plan_id) {
      CILT_EXEC_TASK? exec = await DbHelper.getCiltExecTaskByCiltHeaderCiltId(
          ciltHeader.cilt_id.toString(), widget.task.task_id.toString());
      if (exec != null) {
        if (exec.cilt_task_id.toString() == taskId.toString()) {
          bool isMatched = false;
          for (var element in faultListHeader) {
            if (exec.cilt_task_id == element.cilt_task_id) {
              if (element.cilt_task_id != null) {
                await faultHeader.getFaultHeader(
                    taskNo: element.cilt_task_id.toString());
                isMatched = true;
                break;
              }
            }
          }

          if (!isMatched) {
            await faultHeader.getFaultHeader(taskNo: null);
          }
        }
      }
    }
  }

  Widget getExpandedIcon({
    required Widget child,
    required String type,
    bool showDot = false,
  }) {
    final expandedAttachmentIndex = ref.watch(expandedAttachmentProvider);
    final isAttachmentExpanded = expandedAttachmentIndex == widget.index;
    final expandedCommentIndex = ref.watch(expandedCommentProvider);
    return Expanded(
      child: IconButton(
        onPressed: () {
          if (type == AppConstants.attachments) {
            ref.read(expandedAttachmentProvider.notifier).state =
                isAttachmentExpanded ? -1 : widget.index;
            ref.read(expandedCommentProvider.notifier).state = -1;
            ref
                .read(ciltTaskDocumentHeaders.notifier)
                .fetchTaskDocumentHeaders(widget.execTaskHeader!, widget.task);
          }
          if (type == AppConstants.notes) {
            ref.read(expandedCommentProvider.notifier).state =
                (expandedCommentIndex == widget.index) ? -1 : widget.index;
            ref.read(expandedAttachmentProvider.notifier).state = -1;
          }
          if (isExecutionVisible(checkFor: AppConstants.faults)) {
            if (type == AppConstants.faults) {
              navigateToFaultScreen();
            }
          }
        },
        icon: Container(
          height: 40,
          width: 45,
          child: Stack(
            alignment: Alignment.topRight,
            children: [
              child,
              if (showDot)
                Positioned(
                  bottom: 15,
                  left: 35,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _getTitleRowWidget() {
    return Row(
      children: [
        SizedBox(
            height: 20,
            width: 90,
            child: CiltIndicator(
                boxSize: 18,
                colors: CiltColor.fromCiltCode(widget.task.cilt_code!))),
        const Spacer(),
        widget.isDuratonRequried
            ? Align(
                alignment: Alignment.topRight,
                child: Row(
                  children: [
                    // Padding(
                    //   padding: const EdgeInsets.symmetric(horizontal: 8),
                    //   child: SvgPicture.asset(
                    //     "assets/icons/machine_status.svg",
                    //     height: 30,
                    //     width: 30,
                    //     color: widget.task.sys_cond == "R"
                    //         ? Colors.green
                    //         : Colors.red,
                    //   ),
                    // ),

                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text(
                        systemcondition.description ?? "",
                        style: TextStyle(
                            color:
                                HexColor(systemcondition.color ?? "#FFFFFF")),
                      ),
                    ),

                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(width: 1, color: AppColors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.all(5.0),
                      child: Text(
                        getDuration(widget.task.duration.toString()),
                        style:
                            const TextStyle(color: AppColors.greySubtitleText),
                      ),
                    ),
                  ],
                ))
            : const SizedBox.shrink(),
      ],
    );
  }

  getDuration(String time) {
    double appendedTime = double.parse(time);
    return "${appendedTime.floor()} ${AppLocalizations.of(context)!.m}";
  }

  Widget _getDescriptionRowWidget() {
    return Column(
      children: [
        // _getLocationAndAssetDetails(),
        _buildTextField(AppLocalizations.of(context)!.description,
            widget.task.activity.toString()),
        _buildTextField(AppLocalizations.of(context)!.machinePart,
            widget.task.machine_part.toString()),
        _buildTextField(AppLocalizations.of(context)!.standard,
            widget.task.standard.toString()),
        widget.task.method != null
            ? _buildTextField(AppLocalizations.of(context)!.method,
                widget.task.method.toString())
            : const SizedBox.shrink(),
        widget.task.tool != null
            ? _buildTextField(
                AppLocalizations.of(context)!.tool, widget.task.tool.toString())
            : const SizedBox.shrink(),
      ],
    );
  }

  Widget _getLocationAndAssetDetails() {
    // Scenario 4: No technical objects
    if (widget.task.location_id == null && widget.task.asset_no == null) {
      return const SizedBox.shrink();
    }

    // Scenario 3: Both location and asset exist - show only asset info
    if (widget.task.location_id != null && widget.task.asset_no != null) {
      ASSET_HEADER? asset_header = ref
          .read(assetHeaderProvider.notifier)
          .findAssetHeaderById(widget.task.asset_no ?? -1);
      return _buildTextField(
          "Asset Not",
          UIHelper.formatIdAndDescription(
              widget.task.asset_no.toString(), asset_header?.description));
    }

    // Scenario 2: Only asset exists
    if (widget.task.asset_no != null) {
      ASSET_HEADER? asset_header = ref
          .read(assetHeaderProvider.notifier)
          .findAssetHeaderById(widget.task.asset_no ?? -1);
      return _buildTextField(
          "Asset No",
          UIHelper.formatIdAndDescription(
              asset_header?.asset_no.toString(), asset_header?.description));
    }

    // Scenario 1: Only location exists
    if (widget.task.location_id != null) {
      LOCATION_HEADER? location_header = ref
          .read(flocHeaderProvider.notifier)
          .findLocationHeadById(widget.task.location_id ?? "");
      return _buildTextField(
          "Location Id",
          UIHelper.formatIdAndDescription(
              location_header?.location_id, location_header?.description));
    }

    return const SizedBox.shrink();
  }

  Widget _buildTextField(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.titleTextColor,
                fontWeight: FontWeight.w600,
                letterSpacing: 0.1,
              ),
            ),
          ),
          const SizedBox(
            height: 3,
          ),
          Text(value,
              style: TextStyle(
                fontSize: 12,
                color: AppColors.titleTextColor,
                letterSpacing: 0.1,
              ))
        ],
      ),
    );
  }

  _getSkipOrCompleteButtons() {
    return Align(
      alignment: Alignment.centerRight,
      child: Row(
        children: [
          widget.execTaskHeader?.status == AppConstants.STATE_TASK_COMP &&
                  (widget.execTaskHeader?.reason == null ||
                      widget.execTaskHeader?.reason == "")
              ? widget.execTaskHeader?.p_mode == null ||
                      widget.execTaskHeader?.p_mode == ""
                  ? Container()
                  : SizedBox(
                      width: 60,
                      height: 45,
                      child: ElevatedButton(
                        onPressed: () {
                          if (widget.execTaskHeader?.p_mode == "M") {
                            onUndo();
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15)),
                          backgroundColor: AppColors.offWhiteColor,
                        ),
                        child: const Icon(
                          Icons.undo,
                          color: AppColors.redAccentColor,
                        ),
                      ),
                    )
              : Expanded(
                  child: SizedBox(
                    height: 45,
                    child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15)),
                          backgroundColor: AppColors.offWhiteColor,
                        ),
                        onPressed: widget.execTaskHeader?.status ==
                                AppConstants.STATE_TASK_COMP
                            ? () {}
                            : () {
                                //check
                                onSkipButton();
                              },
                        child: Text(
                          widget.execTaskHeader?.status ==
                                  AppConstants.STATE_TASK_COMP
                              ? AppLocalizations.of(context)!.skipped
                              : AppLocalizations.of(context)!.skip,
                          style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: AppColors.redAccentColor,
                              fontSize: 18),
                        )),
                  ),
                ),
          const SizedBox(width: 10),
          widget.execTaskHeader?.status == AppConstants.STATE_TASK_COMP &&
                  widget.execTaskHeader?.reason != null &&
                  widget.execTaskHeader?.reason != ""
              ? Container()
              : Expanded(
                  child: SizedBox(
                    height: 45,
                    child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15)),
                          backgroundColor:
                              AppColors.offWhiteColor, // Set your desired color
                        ),
                        onPressed: widget.execTaskHeader?.status ==
                                AppConstants.STATE_TASK_COMP
                            ? () {}
                            : () {
                                //check
                                onCompleteButton();
                              },
                        child: Text(
                            widget.execTaskHeader?.status ==
                                    AppConstants.STATE_TASK_COMP
                                ? AppLocalizations.of(context)!.completed
                                : AppLocalizations.of(context)!.complete,
                            style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: AppColors.greenSentColor,
                                fontSize: 18))),
                  ),
                ),
        ],
      ),
    );
  }

  onSkipButton() async {
    final ciltHeader = ref.watch(ciltHeaderProvider);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return true;
          },
          child: StatefulBuilder(
            builder: (context, setState) {
              return Dialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.0),
                ),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxWidth: 400, // Width constrained to a smartphone size
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              AppLocalizations.of(context)!.select_reason,
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 14),
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        getReasonDropdown(context, ref, setState),
                        const SizedBox(height: 10),
                        TextField(
                          style: const TextStyle(fontSize: 14),
                          controller: reasonController,
                          onChanged: (value) {
                            setState(() {
                              reasonController.text = value;
                            });
                          },
                          maxLines: 3,
                          decoration: InputDecoration(
                            hintText:
                                AppLocalizations.of(context)!.additionalComment,
                            border: OutlineInputBorder(
                                borderSide: BorderSide(color: AppColors.grey)),
                            focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(color: AppColors.grey)),
                            enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(color: AppColors.grey)),
                            contentPadding: const EdgeInsets.symmetric(
                                vertical: 10, horizontal: 10),
                          ),
                        ),
                        const SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton(
                              onPressed: reasonController.text.isEmpty ||
                                      selectedSkipReason.isEmpty
                                  ? null
                                  : () {
                                      if (reasonController.text == '' ||
                                          reasonController.text == null) {
                                        UIHelper.showInfoDialogWithtitleAndDescription(
                                            context,
                                            title: AppLocalizations.of(context)!
                                                .warning,
                                            description: AppLocalizations.of(
                                                    context)!
                                                .please_add_additional_message);
                                      } else {
                                        /*  Navigator.pop(context);*/
                                        onSaveReason(
                                            reasonController.text, ciltHeader);
                                      }
                                    },
                              child: Text(
                                AppLocalizations.of(context)!.save,
                                style: TextStyle(
                                  color: reasonController.text.isEmpty ||
                                          selectedSkipReason.isEmpty
                                      ? AppColors.grey
                                      : AppColors.primaryColor,
                                ),
                              ),
                            ),
                            TextButton(
                              onPressed: () {
                                selectedSkipReason = '';
                                reasonController.clear();
                                Navigator.pop(context);
                              },
                              child: Text(
                                AppLocalizations.of(context)!.cancel,
                                style: TextStyle(
                                  color: AppColors.primaryColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  onCompleteButton() async {
    final ciltHeader = ref.watch(ciltHeaderProvider);
    final ciltCompleted = ref.watch(ciltCompletedProvider.notifier);
    final ciltExecHeader = ref.watch(ciltExecuteTaskListProvider.notifier);
    final ciltPlanHeader = ref.watch(ciltPlanHeaderProvider);
    await DbHelper().taskUpdate(
        task: widget.task,
        status: AppConstants.STATE_TASK_COMP,
        ciltHeader: ciltHeader,
        reasonCode: '',
        skipreason: '',
        p_mode: "M");

    await ref
        .read(ciltTasksProvider.notifier)
        .getCiltTasks(ciltHeader.plan_id!);

    await ref.read(ciltExecuteProvider.notifier).getCiltExecute(ciltHeader);

    await ciltCompleted.ciltCompleted(ciltHeader);
    await ciltExecHeader.getCiltExecuteTaskList();
    await ref
        .read(ciltExecuteTaskListProvider.notifier)
        .getCiltExecuteTaskList();
    widget.execTaskHeader?.status = AppConstants.STATE_TASK_COMP;
    widget.execTaskHeader?.p_mode = "M";

    // ref
    //     .read(ciltTaskNotifier.notifier)
    //     .filter(ref.read(ciltPlanHeaderProvider).plan_id.toString(), "", ref);

    var progresssData = _getProgressValue(ciltHeader);

    if (progresssData["totalTasks"] == progresssData["completedTasks"]) {
      await _showTaskCompletionDialog(ciltHeader, context);
    } else {
      ref
          .read(ciltExecuteTaskListProvider.notifier)
          .updateCiltExexTask(widget.execTaskHeader!);
      if (ref.read(ciltToggleStateProvider)) {
        await ref.read(ciltTaskNotifier.notifier).fetchIncompleteTasks(
            (ciltPlanHeader.plan_id.toString() ?? ""),
            ref.read(ciltHeaderProvider),
            ref);
      }
    }
  }

  Future<void> navigateToFaultScreen() async {
    clearFaultStates();
    final user = ref.watch(userProvider);
    final ciltPlanHeader = ref.watch(ciltPlanHeaderProvider);
    final ciltTask = ref.watch(ciltTaskProvider.notifier);
    final faultTypeHeader = ref.read(faultTypeListProvider.notifier);
    final faultInsertHeader = ref.read(insertFaultHeaderProvider.notifier);
    final faultHeader = ref.read(faultHeaderProvider.notifier);
    final faultAction = ref.watch(getFaultActionProvider.notifier);
    final faultDocument = ref.watch(getFaultDocumentProvider.notifier);
    await faultTypeHeader.fetchFaultTypeList();
    final faultTypeHeaderList =
        ref.read(faultHeaderListByPlanIdProvider.notifier);
    final editProvider = ref.read(editFaultFieldProvider.notifier);
    await faultTypeHeaderList.fetchFaultHeaderListByPlanId(
        plantId: ciltPlanHeader.plant_id.toString());
    ciltTask.getCiltTask(widget.task);
    await getFaultData(widget.execTaskHeader!.cilt_task_id.toString());
    if (faultHeader.state.cilt_task_id != null) {
      faultHeader.state.p_mode = AppConstants.modified;
      faultHeader.state.reported_by = ref.read(userProvider)?.user_id;
      DateTime now = DateTime.now();
      String faultNoticedOnDate = DateFormat('dd MMM yyyy').format(now);
      DateTime dateTime = DateFormat("dd MMM yyyy").parse(faultNoticedOnDate);
      DateTime adjustedDate = DateTime(
        dateTime.year,
        dateTime.month,
        dateTime.day,
      );
      String formattedDate = DateFormat("yyyyMMdd").format(adjustedDate);
      int dateAsInt = int.parse(formattedDate);
      faultHeader.state.reported_on = dateAsInt;
      await DbHelper.updateFault(faultHeader.state);
      await faultHeader.getFaultHeader(
          faultId: faultHeader.state.fault_id.toString());
      editProvider.getEditFaultFieldEnable(false);
      if (mounted) {
        ref.read(faultDescriptionProvider.notifier).resetFaultDescription();
        ref.read(faultLongTextProvider.notifier).resetFaultLongText();
        ref.read(faultTypeProvider.notifier).clearFaultType();
        ref.read(priorityProvider.notifier).clearPriority();
        ref.read(faultModeHeaderProvider.notifier).clearFaultMode();
        await faultDocument
            .getFaultDocuments(faultHeader.state.fault_id.toString());

        if (UIHelper().getScreenType(context) == ScreenType.desktop) {
          await showDialog(
              barrierDismissible: false,
              context: context,
              builder: (context) => Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxWidth: 600,
                      maxHeight: 800,
                    ),
                    child: const Padding(
                      padding: EdgeInsets.all(20.0),
                      child: FaultDetailScreen(
                        type: AppConstants.ciltFault,
                      ),
                    ),
                  )));
        } else {
          Navigator.push(context, MaterialPageRoute(builder: (context) {
            return const FaultDetailScreen(
              type: AppConstants.ciltFault,
            );
          }));
        }
      }
    } else {
      if (isExecutionVisible()) {
        clearNewFaultStates();
        int? ciltTaskId;
        final ciltExecHeader = ref.watch(ciltHeaderProvider.notifier).state;
        final ciltFaultHeader =
            ref.watch(getCiltHeaderByFaultHeaderProvider.notifier);
        CILT_EXEC_TASK? exec = await DbHelper.getCiltExecTaskByCiltHeaderCiltId(
            ciltExecHeader.cilt_id.toString(), widget.task.task_id.toString());
        if (exec != null) {
          ciltTaskId = exec.cilt_task_id;
        }
        DateTime now = DateTime.now();
        String faultNoticedOnDate = DateFormat('dd MMM yyyy').format(now);
        DateTime dateTime = DateFormat("dd MMM yyyy").parse(faultNoticedOnDate);
        DateTime adjustedDate = DateTime(
          dateTime.year,
          dateTime.month,
          dateTime.day,
        );
        String formattedDate = DateFormat("yyyyMMdd").format(adjustedDate);
        int dateAsInt = int.parse(formattedDate);

        String? locationId;
        int? assetNo;
        CILT_SECTION? section;
        final ciltPlanSectionHeader =
            ref.watch(ciltPlanSectionListHeaderProvider);
        section = ciltPlanSectionHeader.firstWhere((element) =>
            (element.section_id == widget.task.section_id &&
                element.plan_id == widget.task.plan_id));

        if (widget.task.location_id != null && widget.task.location_id != '') {
          locationId = widget.task.location_id;
        } else if (widget.task.asset_no != null && widget.task.asset_no != '') {
          assetNo = widget.task.asset_no;
          ASSET_HEADER header = await DbHelper.getAssetByAssetno(assetNo!);
          locationId = (await DbHelper.getLocationUsingParentLocFromAsset(
                  header.parent_loc_id.toString()))!
              .location_id;
        } else if (section.location_id != null && section.location_id != '') {
          locationId = section.location_id;
        } else if (section.asset_no != null && section.asset_no != '') {
          assetNo = section.asset_no;
          ASSET_HEADER header = await DbHelper.getAssetByAssetno(assetNo!);
          locationId = (await DbHelper.getLocationUsingParentLocFromAsset(
                  header.parent_loc_id.toString()))!
              .location_id;
        } else if (ciltPlanHeader.location_id != null &&
            ciltPlanHeader.location_id != '') {
          locationId = ciltPlanHeader.location_id;
        } else if (ciltPlanHeader.asset_no != null &&
            ciltPlanHeader.asset_no != '') {
          assetNo = ciltPlanHeader.asset_no;
          ASSET_HEADER header = await DbHelper.getAssetByAssetno(assetNo!);
          locationId = (await DbHelper.getLocationUsingParentLocFromAsset(
                  header.parent_loc_id.toString()))!
              .location_id;
        }

        FAULT_HEADER faultHeaderData = FAULT_HEADER(
          fault_id: UIHelper.generateRandomId(),
          asset_no: assetNo,
          plant_id: ciltPlanHeader.plant_id,
          location_id: locationId,
          p_mode: AppConstants.add,
          cilt_task_id: ciltTaskId,
          reported_on: dateAsInt,
          reported_by: user!.user_id,
        );
        await faultInsertHeader.insertFaultHeader(faultHeaderData);
        await faultHeader.getFaultHeader(
            faultId: faultHeaderData.fault_id.toString());
        await faultAction.getFaultAction(faultHeaderData.fault_id.toString());
        await faultDocument
            .getFaultDocuments(faultHeaderData.fault_id.toString());
        await ciltFaultHeader.getCiltPlanHeaderByFaultHeader(faultHeaderData);
        editProvider.getEditFaultFieldEnable(true);
        docAttachments.clear();
        if (mounted) {
          ref.read(faultDescriptionProvider.notifier).resetFaultDescription();
          ref.read(faultLongTextProvider.notifier).resetFaultLongText();
          ref.read(faultTypeProvider.notifier).clearFaultType();
          ref.read(priorityProvider.notifier).clearPriority();
          ref.read(faultModeHeaderProvider.notifier).clearFaultMode();
          if (UIHelper().getScreenType(context) == ScreenType.desktop) {
            await showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => Dialog(
                    backgroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(
                        maxWidth:
                            600, // Slightly more than a mobile screen width
                        maxHeight:
                            800, // Not more than the size of a mobile phone
                      ),
                      child: const Padding(
                          padding: EdgeInsets.all(20.0),
                          child: FaultDetailScreen(
                            type: AppConstants.ciltFault,
                          )),
                    )));
          } else {
            Navigator.push(context, MaterialPageRoute(builder: (context) {
              return const FaultDetailScreen(
                type: AppConstants.ciltFault,
              );
            }));
          }
        }
      } else {
        if (mounted) {
          UIHelper.showSnackBar(context,
              message: "No Fault created against this task");
        }
      }
    }
  }

  String selectedSkipReason = '';
  Widget getReasonDropdown(
      BuildContext context, WidgetRef ref, StateSetter setState) {
    final skipReasonList = ref
        .watch(skipReasonListProvider)
        .where((element) => (element.category! & 1) != 0)
        .toList();
    final skipReason = ref.watch(skipReasonProvider.notifier);

    final dropdownItems = skipReasonList.map((option) {
      return DropdownMenuItem<String>(
        value: option.description,
        child: Padding(
          padding: const EdgeInsets.only(left: 5.0),
          child: Text(option.description!, style: UIHelper.valueStyle()),
        ),
      );
    }).toList();

    if (dropdownItems.where((item) => item.value == '').isEmpty) {
      dropdownItems.insert(
        0,
        DropdownMenuItem<String>(
          value: '',
          child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(AppLocalizations.of(context)!.select,
                style: UIHelper.valueStyle()),
          ),
        ),
      );
    }
    return Container(
      width: MediaQuery.of(context).size.width,
      decoration: UIHelper.fieldDecoration(),
      child: DropdownButton<String>(
        elevation: 0,
        isExpanded: true,
        underline: const SizedBox(),
        value: selectedSkipReason.isNotEmpty ? selectedSkipReason : '',
        items: dropdownItems,
        onChanged: (newValue) {
          setState(() {
            selectedSkipReason = newValue!;
            skipReason.getSkipReason(selectedSkipReason);
          });
        },
      ),
    );
  }

  void onSaveReason(String reason, CILT_EXEC_HEADER ciltHeader) async {
    final ciltPlanHeader = ref.watch(ciltPlanHeaderProvider);
    final ciltHeader = ref.watch(ciltHeaderProvider);
    final ciltCompleted = ref.watch(ciltCompletedProvider.notifier);
    final ciltExecHeader = ref.watch(ciltExecuteTaskListProvider.notifier);
    final skipReason = ref.watch(skipReasonProvider.notifier).state;

    await DbHelper().taskUpdate(
        task: widget.task,
        status: AppConstants.STATE_TASK_COMP,
        ciltHeader: ciltHeader,
        reasonCode: skipReason.reason.toString(),
        skipreason: reason,
        p_mode: "M");

    ref.read(ciltTasksProvider.notifier).getCiltTasks(ciltHeader.plan_id!);
    ref.read(ciltExecuteProvider.notifier).getCiltExecute(ciltHeader);
    await ciltCompleted.ciltCompleted(ciltHeader);
    await ciltExecHeader.getCiltExecuteTaskList();
    ref.read(ciltExecuteTaskListProvider.notifier).getCiltExecuteTaskList();

    widget.execTaskHeader?.status = AppConstants.STATE_TASK_COMP;
    widget.execTaskHeader?.reason = skipReason.reason.toString();
    widget.execTaskHeader?.skip_comments = reason;
    widget.execTaskHeader?.comments = commentController.text;
    widget.execTaskHeader?.p_mode = "M";

    ref.read(ciltTaskNotifier.notifier).updateTask(widget.task);
    var progresssData = _getProgressValue(ciltHeader);

    delayReason.clear();
    reasonController.clear();
    ref.read(skipReasonProvider.notifier).clearSkipReason();
    selectedSkipReason = '';

    if (mounted) {
      Navigator.pop(context);
    }

    if (progresssData["totalTasks"] == progresssData["completedTasks"]) {
      Future.delayed(Duration.zero, () async {
        await _showTaskCompletionDialog(ciltHeader, context).then((value) {});
      });
    } else {
      ref
          .watch(ciltExecuteTaskListProvider.notifier)
          .updateCiltExexTask(widget.execTaskHeader!);
      if (ref.read(ciltToggleStateProvider)) {
        ref.read(ciltTaskNotifier.notifier).fetchIncompleteTasks(
            (ciltPlanHeader.plan_id.toString() ?? ""),
            ref.read(ciltHeaderProvider),
            ref);
      }
    }
  }

  static String? generate32BitDocId() {
    var uuid = const Uuid();
    return uuid.v4();
  }

  void onUndo() async {
    final ciltHeader = ref.watch(ciltHeaderProvider);
    await DbHelper().taskUpdate(
        task: widget.task,
        status: AppConstants.STATE_TASK_OPEN,
        ciltHeader: ciltHeader,
        reasonCode: '',
        skipreason: '',
        p_mode: null);
    ref.read(ciltTasksProvider.notifier).getCiltTasks(ciltHeader.plan_id!);
    ref.read(ciltExecuteProvider.notifier).getCiltExecute(ciltHeader);
    widget.execTaskHeader?.status = "";
    widget.execTaskHeader?.p_mode = null;
    ref
        .read(ciltExecuteTaskListProvider.notifier)
        .updateCiltExexTask(widget.execTaskHeader!);
    ref.read(ciltTaskNotifier.notifier).updateTask(widget.task);
    setState(() {
      widget.execTaskHeader?.status = "";
      widget.execTaskHeader?.p_mode = null;
    });
  }

  onAttachmentPicked(FilePickerResult result) async {
    final ciltHeader = ref.read(ciltHeaderProvider.notifier).state;
    if (kIsWeb) {
      onAttachmentPickedWeb(result);
    } else {
      await onAttchmentPickedMobile(result, ciltHeader);
    }
  }

  Future<void> onAttachmentPickedWeb(FilePickerResult result) async {
    final ciltHeader = ref.read(ciltHeaderProvider.notifier).state;

    for (var data in result.files) {
      Uint8List? fileBytes = data.bytes;
      if (fileBytes == null) continue; // No bytes, skip

      String fileName = data.name;
      String fileDocType = fileName.split('.').last.toUpperCase();
      String base64String = '';

      base64String = base64Encode(fileBytes);

      String? docId = generate32BitDocId();

      DOCUMENT_HEADER documentHeader = DOCUMENT_HEADER(
        doc_id: docId,
        doc_type: fileDocType,
        file_name: fileName,
        title: fileName,
        mime_type: 'application/${fileDocType.toLowerCase()}',
      );

      if (kIsWeb) {
        DbHelper().saveAttachmentinIndexDbByUid(docId ?? "", base64String);
      }

      List list1 = await AppDatabaseManager().select(
          DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, {})
            ..setWhereClause("${DOCUMENT_HEADER.FIELD_DOC_ID} = '$docId'"));

      if (list1.isEmpty) {
        await DbHelper.insertDocumentsHeader(documentHeader);
      } else {
        await DbHelper.updateDocumentsHeader(documentHeader);
      }

      DOCUMENT_ATTACHMENT documentAttachment = DOCUMENT_ATTACHMENT(uid: '');
      documentAttachment.fid = documentHeader.lid;
      documentAttachment.uid = docId;
      documentAttachment.local_path = ''; // no local path on web
      documentAttachment.file_name = fileName;
      documentAttachment.mime_type = 'application/${fileDocType.toLowerCase()}';
      documentAttachment.external_url = "";
      documentAttachment.url_requires_auth = "";
      documentAttachment.attachment_status = AttachmentStatusSavedForUpload;

      List list2 =
          await DbHelper.getDocumentAttachmentsByFid(documentHeader.lid);

      if (list2.isEmpty) {
        await DbHelper.insertDocumentAttachment(documentAttachment);
      } else {
        await DbHelper.updateDocumentAttachment(documentAttachment);
      }

      CILT_EXEC_DOC document = CILT_EXEC_DOC(
        cilt_id: widget.execTaskHeader?.cilt_id,
        doc_id: documentAttachment.uid,
        cilt_task_id: widget.execTaskHeader?.cilt_task_id,
      );

      var data1 = await AppDatabaseManager().select(
          DBInputEntity(CILT_EXEC_DOC.TABLE_NAME, {})
            ..setWhereClause(
                "${CILT_EXEC_DOC.FIELD_DOC_ID} = '${documentAttachment.uid}'"));

      try {
        if (data1.isNotEmpty) {
          await DbHelper.updateCiltExeDoc(document);
        } else {
          document.fid = ciltHeader.lid;
          await DbHelper.insertCiltExeDoc(document);
        }

        await DbHelper.updateCilt(ciltHeader);

        setState(() {
          ref
              .read(documentAttachmentProvider.notifier)
              .fetchDocumentAttachments();
          ref
              .read(ciltTaskExecDocumentHeaderProvider.notifier)
              .fetchTaskDocumentHeaders(
                  widget.execTaskHeader!, ciltHeader, widget.task);
        });
      } catch (e) {
        Logger.logError("CiltTaskCard", "PickFile", e.toString());
      }
    }
  }

  Future<void> onAttchmentPickedMobile(
      FilePickerResult result, CILT_EXEC_HEADER ciltHeader) async {
    if (result != null && result.files.single.path != null) {
      for (var data in result.files) {
        File fileData = File(data.path!);
        String? docId = generate32BitDocId();
        String fileName = path.basename(fileData.path);
        String filePath = fileData.path;
        String fileDocType = fileData.path.split('.').last.toUpperCase();
        String base64String = '';
        if (filePath.isNotEmpty) {
          final extension = filePath.split('.').last.toLowerCase();
          if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
              .contains(extension)) {
            base64String = base64Encode(fileData.readAsBytesSync());
          } else if (['mp4', 'mov', 'avi', 'mkv', 'flv', 'wmv', 'webm']
              .contains(extension)) {
            ByteData byteData =
                await rootBundle.load('assets/icon/task_icons/video.png');
            Uint8List bytes = byteData.buffer.asUint8List();
            base64String = base64Encode(bytes);
          } else if (extension == 'pdf') {
            ByteData byteData =
                await rootBundle.load('assets/icon/task_icons/pdff.png');
            Uint8List bytes = byteData.buffer.asUint8List();
            base64String = base64Encode(bytes);
          }
        }
        DOCUMENT_HEADER documentHeader = DOCUMENT_HEADER(
            doc_id: docId,
            doc_type: fileDocType,
            file_name: fileName,
            title: fileName,
            mime_type: 'application/${fileDocType.toLowerCase()}',
            thumbnail: base64String);
        List list1 = await AppDatabaseManager().select(
            DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, {})
              ..setWhereClause("${DOCUMENT_HEADER.FIELD_DOC_ID} = '$docId'"));

        if (list1.isEmpty) {
          await DbHelper.insertDocumentsHeader(documentHeader);
        } else {
          await DbHelper.updateDocumentsHeader(documentHeader);
        }
        DOCUMENT_ATTACHMENT documentAttachment = DOCUMENT_ATTACHMENT(uid: '');
        documentAttachment.fid = documentHeader.lid;
        documentAttachment.uid = docId;
        documentAttachment.local_path = filePath;
        documentAttachment.file_name = fileName;
        documentAttachment.mime_type =
            'application/${fileDocType.toLowerCase()}';
        documentAttachment.external_url = "";
        documentAttachment.url_requires_auth = "";
        documentAttachment.attachment_status = AttachmentStatusSavedForUpload;

        List list2 =
            await DbHelper.getDocumentAttachmentsByFid(documentHeader.lid);

        if (list2.isEmpty) {
          await DbHelper.insertDocumentAttachment(documentAttachment);
        } else {
          await DbHelper.updateDocumentAttachment(documentAttachment);
        }

        CILT_EXEC_DOC document = CILT_EXEC_DOC(
          cilt_id: widget.execTaskHeader?.cilt_id,
          doc_id: documentAttachment.uid,
          cilt_task_id: widget.execTaskHeader?.cilt_task_id,
        );

        var data1 = await AppDatabaseManager().select(DBInputEntity(
            CILT_EXEC_DOC.TABLE_NAME, {})
          ..setWhereClause(
              "${CILT_EXEC_DOC.FIELD_DOC_ID} = '${documentAttachment.uid}'"));
        try {
          if (data1.isNotEmpty) {
            await DbHelper.updateCiltExeDoc(document);
          } else {
            document.fid = ciltHeader.lid;
            await DbHelper.insertCiltExeDoc(document);
          }

          await DbHelper.updateCilt(ciltHeader);

          setState(() {
            ref
                .read(documentAttachmentProvider.notifier)
                .fetchDocumentAttachments();
            ref
                .read(ciltTaskExecDocumentHeaderProvider.notifier)
                .fetchTaskDocumentHeaders(
                    widget.execTaskHeader!, ciltHeader, widget.task);
          });

          uploadFile(ref, documentAttachment.lid);
        } catch (e) {
          Logger.logError("CiltTaskCard", "PickFile", e.toString());
        }
      }
    }
  }

  Future<void> uploadFile(WidgetRef ref, String lid) async {
    final uploadProgressNotifier =
        ref.read(attachMentUploadProgressProvider.notifier);
    uploadProgressNotifier.startUpload(lid);

    for (double progress = 0.0; progress <= 1.0; progress += 0.1) {
      await Future.delayed(Duration(milliseconds: 500));
      uploadProgressNotifier.updateProgress(lid, progress);
    }

    uploadProgressNotifier.finishUpload(lid);
  }

  isAllowedToModify() {
    final ciltHeader = ref.watch(ciltHeaderProvider);

    return (ciltHeader.status != AppConstants.STATE_COMPLETED &&
            widget.execTaskHeader?.status != AppConstants.STATE_TASK_COMP)
        ? true
        : (widget.execTaskHeader?.p_mode == "M" &&
                ciltHeader.syncStatus.index != 2)
            ? true
            : false;
  }

  Widget attachmentWidget(
      BuildContext context, CILT_EXEC_TASK ciltExecTaskHeader) {
    final documentHeaders = ref
        .watch(ciltTaskExecDocumentHeaderProvider.notifier)
        .getDocumentHeadersForTask(ciltExecTaskHeader);

    final documentAttachments = ref.watch(documentAttachmentProvider);

    List<DOCUMENT_ATTACHMENT> attachmentList = documentAttachments.where(
      (attachment) {
        return documentHeaders
                ?.where((docHeader) => docHeader.lid == attachment.fid)
                .toList()
                .length ==
            1;
      },
    ).toList();
    if (attachmentList.isNotEmpty) {
      if (mounted) {
        setState(() {});
      }
    }

    return RoundsAttachmentPicker(
        isAddButtonRequired: isExecutionVisible() ? isAllowedToModify() : false,
        onAttachmentPicked: onAttachmentPicked,
        viewType: RoundsAttachmentPickerViewType.list,
        attachments: attachmentList
            .map((e) => DocumentAttachmentContainer(
                  height: 80,
                  width: 80,
                  isFromPlanner: e.objectStatus == ObjectStatus.global,
                  attachment: e,
                  uploadProgress:
                      ref.watch(attachMentUploadProgressProvider)[e.lid] ?? 1.0,
                  isUploading:
                      (ref.watch(attachMentUploadProgressProvider)[e.lid] ??
                              1.0) <
                          1.0,
                  onDelete: (value) async {
                    UIHelper.showEamDialog(context,
                        title: AppLocalizations.of(context)!.delete_attachment,
                        description: AppLocalizations.of(context)!
                            .do_you_want_delete_attachment,
                        positiveActionLabel: AppLocalizations.of(context)!.yes,
                        negativeActionLabel: AppLocalizations.of(context)!
                            .cancel, onPositiveClickListener: () async {
                      await ref
                          .read(documentAttachmentProvider.notifier)
                          .deleteDocumentAttachments(e);

                      Navigator.of(context, rootNavigator: true).pop();
                    });
                  },
                ))
            .toList());
  }

  Widget _buildCommentInput() {
    final ciltExecHeader = ref.watch(ciltHeaderProvider.notifier).state;
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.all(10.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextField(
                style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black),
                controller: commentController,
                enabled: isAllowedToModify(),
                onChanged: (value) async {
                  _debouncer.run(() async {
                    await DbHelper().taskUpdate(
                        task: widget.task,
                        // status: null,
                        ciltHeader: ciltExecHeader,
                        // reasonCode: '',
                        // skipreason: '',
                        p_mode: "M",
                        comment: value);
                    ref
                        .read(ciltExecuteTaskListProvider.notifier)
                        .updateCiltExexTask(widget.execTaskHeader!);

                    ref
                        .read(ciltExecuteTaskListProvider.notifier)
                        .getCiltExecuteTaskList();
                    setState(() {
                      widget.execTaskHeader?.p_mode = AppConstants.modified;
                      widget.execTaskHeader?.comments = commentController.text;
                    });
                  });
                },
                maxLines: 4,
                decoration: InputDecoration(
                  hintText: AppLocalizations.of(context)!.enter_comment,
                  border: OutlineInputBorder(
                      borderSide: BorderSide(color: AppColors.grey)),
                  focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: AppColors.grey)),
                  enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: AppColors.grey)),
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  _getProgressValue(CILT_EXEC_HEADER? ciltHeader) {
    if (ciltHeader != null) {
      ref.watch(ciltTasksProvider.notifier).getCiltTasks(ciltHeader.plan_id!);
      ref.watch(ciltExecuteProvider.notifier).getCiltExecute(ciltHeader);
      final numberOfTasks =
          ref.watch(ciltTasksProvider)[ciltHeader.plan_id] ?? [];
      final numberOfCompletedTasks =
          ref.watch(ciltExecuteProvider)[ciltHeader.cilt_id] ?? [];
      int totalTasks = numberOfTasks
          .where((element) =>
              element.plan_id.toString() == ciltHeader.plan_id.toString())
          .length;
      int completedTasks = numberOfCompletedTasks.length;
      numberOfCompletedTasks
          .where((element) =>
              element.cilt_id.toString() == ciltHeader.cilt_id.toString())
          .length;

      if (totalTasks == 0) {
        return {
          'percentage': 0.0,
          'completedTasks': 0,
          'totalTasks': 0,
        };
        /*return 0.0;*/
      }
      double percentage =
          double.parse((completedTasks / totalTasks).toStringAsFixed(2));
      return {
        'percentage': percentage,
        'completedTasks': completedTasks,
        'totalTasks': totalTasks,
      };
      /*return percentage/100;*/
    } else {
      return {
        'percentage': 0.0,
        'completedTasks': 0,
        'totalTasks': 0,
      };
    }
  }

  Future<void> _showTaskCompletionDialog(
      CILT_EXEC_HEADER ciltHeader, BuildContext context) async {
    return await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return Dialog(
          child: ConstrainedBox(
            constraints: const BoxConstraints(
              maxWidth: 400,
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Center(
                          child: Text(
                            "${AppLocalizations.of(context)!.congratulations} 🎉",
                            style: UIHelper.titleStyle16(),
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Icon(
                          Icons.cancel_outlined,
                          color: AppColors.black,
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(
                      vertical: 16.0,
                    ),
                    child: Text(
                      AppLocalizations.of(context)!
                          .all_tasks_have_successfully_completed,
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      minimumSize: const Size(double.infinity, 40),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                    onPressed: () async {
                      final current = DateTime.now();

                      DateTime endTime = Utils.getTimeStamp(
                          ciltHeader.start_on.toString(),
                          ciltHeader.end_at.toString());

                      if (current.isAfter(endTime)) {
                        showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (context) {
                            return AlertDialog(
                              title: Text(AppLocalizations.of(context)!
                                  .please_provide_reason_for_submission_delay),
                              content: TextField(
                                decoration: InputDecoration(
                                  hintText: AppLocalizations.of(context)!
                                      .reason_for_submission_delay,
                                  border: OutlineInputBorder(
                                    borderSide:
                                        BorderSide(color: AppColors.grey),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderSide:
                                        BorderSide(color: AppColors.grey),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide:
                                        BorderSide(color: AppColors.grey),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                    vertical: 10,
                                    horizontal: 10,
                                  ),
                                ),
                                controller: delayReason,
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () async {
                                    dynamic progressValue =
                                        _calculateProgress(ciltHeader);
                                    if (progressValue != null) {
                                      Navigator.of(context)
                                          .pop(); // Close the reason dialog

                                      Navigator.of(context)
                                          .pop(); // Close the confirmation dialog

                                      if (progressValue["totalTasks"] ==
                                          progressValue["completedTasks"]) {
                                        await getSubmitWithCloseAction(
                                            ciltHeader);
                                      } else {
                                        await getSubmitWithoutCloseAction(
                                            ciltHeader, context);
                                      }

                                      // Show success message
                                      /* ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content: Text(
                                              AppLocalizations.of(context)!
                                                  .submitted_successfully),
                                          duration: Duration(seconds: 2),
                                          behavior: SnackBarBehavior.floating,
                                        ),
                                      );*/
                                    }
                                  },
                                  child: Text(
                                      AppLocalizations.of(context)!.submit),
                                ),
                                TextButton(
                                  onPressed: () {
                                    delayReason.clear();
                                    Navigator.of(context)
                                        .pop(); // Just close the reason dialog
                                  },
                                  child: Text(
                                      AppLocalizations.of(context)!.cancel),
                                ),
                              ],
                            );
                          },
                        );
                      } else {
                        // No delay, process directly
                        dynamic progressValue = _calculateProgress(ciltHeader);
                        if (progressValue != null) {
                          // Navigator.of(context).pop(); // Close the reasonDialog
                          Navigator.of(context)
                              .pop(); // Close the confirmation dialog
                          if (progressValue["totalTasks"] ==
                              progressValue["completedTasks"]) {
                            await getSubmitWithCloseAction(ciltHeader);
                          } else {
                            await getSubmitWithoutCloseAction(
                                ciltHeader, context);
                          }
                        }
                      }
                    },
                    child: Text(
                      AppLocalizations.of(context)!.submit,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  _calculateProgress(CILT_EXEC_HEADER? ciltHeader) {
    if (ciltHeader != null) {
      ref.watch(ciltTasksProvider.notifier).getCiltTasks(ciltHeader.plan_id!);
      ref.watch(ciltExecuteProvider.notifier).getCiltExecute(ciltHeader);
      final numberOfTasks =
          ref.watch(ciltTasksProvider)[ciltHeader.plan_id] ?? [];
      final numberOfCompletedTasks =
          ref.watch(ciltExecuteProvider)[ciltHeader.cilt_id] ?? [];
      int totalTasks = numberOfTasks
          .where((element) =>
              element.plan_id.toString() == ciltHeader.plan_id.toString())
          .length;
      int completedTasks = numberOfCompletedTasks.length;
      numberOfCompletedTasks
          .where((element) =>
              element.cilt_id.toString() == ciltHeader.cilt_id.toString())
          .length;

      if (totalTasks == 0) {
        return {
          'percentage': 0.0,
          'completedTasks': 0,
          'totalTasks': 0,
        };
        /*return 0.0;*/
      }
      double percentage =
          double.parse((completedTasks / totalTasks).toStringAsFixed(2));
      return {
        'percentage': percentage,
        'completedTasks': completedTasks,
        'totalTasks': totalTasks,
      };
      /*return percentage/100;*/
    } else {
      return {
        'percentage': 0.0,
        'completedTasks': 0,
        'totalTasks': 0,
      };
    }
  }

  Future<void> getSubmitWithCloseAction(CILT_EXEC_HEADER ciltHeader) async {
    final navigator = Navigator.of(context, rootNavigator: true);
    final BuildContext buildContext = context;
    var ciltPlanHeader = ref.read(ciltPlanHeaderProvider);

    if (delayReason.text.isNotEmpty) {
      ciltHeader.delay_comments = delayReason.text;
    }

    await AppDatabaseManager().update(
      DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, ciltHeader.toJson()),
    );

    final numberOfCompletedTasks = ref
            .watch(ciltExecuteProvider)[ciltHeader.cilt_id] ??
        [].where((element) => element.cilt_id == ciltHeader.cilt_id).toList();

    CILT_EXEC_ACTION action = CILT_EXEC_ACTION(
      cilt_id: ciltHeader.cilt_id,
      user_action: AppConstants.STATE_COMPLETED,
    );

    action.fid = ciltHeader.lid;

    var result = await AppDatabaseManager().execute(
      "SELECT * from CILT_EXEC_ACTION WHERE CILT_ID = '${ciltHeader.cilt_id}'",
    );
    if (result.isNotEmpty) {
      await AppDatabaseManager().update(
        DBInputEntity(CILT_EXEC_ACTION.TABLE_NAME, action.toJson()),
      );
    } else {
      await AppDatabaseManager().insert(
        DBInputEntity(CILT_EXEC_ACTION.TABLE_NAME, action.toJson()),
      );
    }

    List<CILT_EXEC_TASK> cilt_exec_tasks = ref
        .read(ciltExecuteTaskListProvider.notifier)
        .findAllCiltExecOfCilt(ciltHeader);

    List<DOCUMENT_HEADER> headerData = [];
    for (var task in cilt_exec_tasks) {
      final documentHeaders = ref
          .watch(ciltTaskExecDocumentHeaderProvider.notifier)
          .getDocumentHeadersForTask(task);
      if (documentHeaders != null) {
        headerData.addAll(documentHeaders);
      }
    }

    List<DOCUMENT_HEADER> documents = headerData
        .where((element) => element.objectStatus == ObjectStatus.add)
        .toList();

    if (kIsWeb) {
      UIHelper().progressDialog(
          context: context, message: "Submiting Cilt Execution");

      for (DOCUMENT_HEADER document in documents) {
        var doc = await DbHelper()
            .getAttachmentFromIndexDbByUid(document.doc_id ?? "");
        await SyncEngine().uploadAttachmentSync(
          doc ?? "",
          document.file_name ?? "",
          document.doc_id ?? "",
        );
      }

      await PAHelper.addDocumentInSyncMode(buildContext, documents);
    } else {
      await PAHelper.addDocumentInAsyncMode(buildContext, documents);
    }

    if (!kIsWeb) {
      await PAHelper.modifyCiltExecInAsyncMode(buildContext, ciltHeader);
    } else {
      Result? result =
          await PAHelper.modifyCiltExecInSyncMode(buildContext, ciltHeader);
      // Navigator.of(context, rootNavigator: true).pop();
      navigator.pop();
    }

    delayReason.clear();
    selectedSkipReason = '';
    reasonController.clear();

    if (UIHelper().getScreenType(buildContext) != ScreenType.desktop) {
      Navigator.pop(buildContext, ciltHeader);
    } else {
      // setState(() {});
      if (mounted) {
        await ref
            .read(ciltTaskNotifier.notifier)
            .filter(ciltPlanHeader.plan_id.toString(), "", ref);
      }
    }
    ScaffoldMessenger.of(buildContext).showSnackBar(
      SnackBar(
        content:
            Text(AppLocalizations.of(buildContext)!.submitted_successfully),
        duration: const Duration(seconds: 2),
      ),
    );
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final shift = ref.read(shiftProvider.notifier).state;

    // await ref
    //     .read(ciltPlanListHeaderProvider.notifier)
    //     .fetchCiltPlanListHeaders();

    await ref
        .read(ciltHeaderListProvider.notifier)
        .fetchCiltListHeaders(plant, plantSection, shift, ref);

    ref
        .read(ciltExecuteTaskListProvider.notifier)
        .updateCiltExexTask(widget.execTaskHeader!);

    if (ref.read(ciltToggleStateProvider)) {
      await ref.read(ciltTaskNotifier.notifier).fetchIncompleteTasks(
          (ciltPlanHeader.plan_id.toString() ?? ""),
          ref.read(ciltHeaderProvider),
          ref);
    }

    await ref
        .read(ciltExecuteTaskListProvider.notifier)
        .getCiltExecuteTaskList();

    final searchProvider = ref.read(roundsSearchProvider.notifier).state;

    ref
        .read(filteredCiltProvider.notifier)
        .filter(plant, plantSection, shift, searchProvider, ref);
    if (!kIsWeb) {
      CILT_EXEC_HEADER? newExecHeader =
          await DbHelper.getCiltExecHeader(ciltHeader);
      if (newExecHeader != null) {
        await ref
            .read(ciltHeaderProvider.notifier)
            .selectedCiltHeaders(ciltHeader);
      }

      if ((newExecHeader!.syncStatus.index == 2)) {
        List<CILT_EXEC_TASK> ciltExecTasks = ref
            .read(ciltExecuteTaskListProvider.notifier)
            .findAllCiltExecOfCilt(ciltHeader);

        for (var task in ciltExecTasks) {
          if (task.p_mode == AppConstants.modified) {
            await DbHelper.updateCiltExecTask(task);
          }
        }

        await ref
            .read(ciltExecuteTaskListProvider.notifier)
            .getCiltExecuteTaskList();

        await ref.read(ciltExecuteProvider.notifier).getCiltExecute(ciltHeader);
      }
    }
  }

  Future<void> getSubmitWithoutCloseAction(
      CILT_EXEC_HEADER ciltHeader, BuildContext context) async {
    var ciltPlanHeader = ref.read(ciltPlanHeaderProvider);
    ciltHeader.p_mode = AppConstants.modified;
    final numberOfCompletedTasks = ref
            .watch(ciltExecuteProvider)[ciltHeader.cilt_id] ??
        [].where((element) => element.cilt_id == ciltHeader.cilt_id).toList();

    if (delayReason.text.isNotEmpty) {
      ciltHeader.delay_comments = delayReason.text;
    }

    await AppDatabaseManager().update(
      DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, ciltHeader.toJson()),
    );

    List<CILT_EXEC_TASK> cilt_exec_tasks = ref
        .read(ciltExecuteTaskListProvider.notifier)
        .findAllCiltExecOfCilt(ciltHeader);

    List<DOCUMENT_HEADER> headerData = [];
    for (var task in cilt_exec_tasks) {
      final documentHeaders = ref
          .watch(ciltTaskExecDocumentHeaderProvider.notifier)
          .getDocumentHeadersForTask(task);
      if (documentHeaders != null) {
        headerData.addAll(documentHeaders);
      }
    }

    List<DOCUMENT_HEADER> documents = headerData
        .where((element) => element.objectStatus == ObjectStatus.add)
        .toList();

    if (kIsWeb) {
      UIHelper().progressDialog(
          context: context, message: "Submiting Cilt Execution");

      for (DOCUMENT_HEADER document in documents) {
        var doc = await DbHelper()
            .getAttachmentFromIndexDbByUid(document.doc_id ?? "");
        await SyncEngine().uploadAttachmentSync(
          doc ?? "",
          document.file_name ?? "",
          document.doc_id ?? "",
        );
      }

      if (mounted) {
        await PAHelper.addDocumentInSyncMode(context, documents);
      }
    } else {
      if (mounted) {
        await PAHelper.addDocumentInAsyncMode(context, documents);
      }
    }

    if (!kIsWeb) {
      await PAHelper.modifyCiltExecInAsyncMode(context, ciltHeader);
    } else {
      Result? result =
          await PAHelper.modifyCiltExecInSyncMode(context, ciltHeader);
      Navigator.of(context, rootNavigator: true).pop(); // ✅ Fixed pop
    }

    delayReason.clear();
    selectedSkipReason = '';
    reasonController.clear();

    if (mounted) {
      if (UIHelper().getScreenType(context) != ScreenType.desktop) {
        Navigator.pop(context, ciltHeader);
      } else {
        setState(() {});
        await ref
            .read(ciltExecuteTaskListProvider.notifier)
            .getCiltExecuteTaskList();
        await ref
            .read(ciltTaskNotifier.notifier)
            .filter(ciltPlanHeader.plan_id.toString(), "", ref);
      }
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.submitted_successfully),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );

      final plant = ref.read(plantProvider.notifier).state;
      final plantSection = ref.read(plantSectionProvider.notifier).state;
      final shift = ref.read(shiftProvider.notifier).state;
      await ref
          .read(ciltPlanListHeaderProvider.notifier)
          .fetchCiltPlanListHeaders();
      await ref
          .read(ciltHeaderListProvider.notifier)
          .fetchCiltListHeaders(plant, plantSection, shift, ref);
      await ref
          .read(ciltExecuteTaskListProvider.notifier)
          .getCiltExecuteTaskList();
      await ref
          .read(ciltTaskNotifier.notifier)
          .filter(ciltPlanHeader.plan_id.toString(), "", ref);

      if (!kIsWeb) {
        CILT_EXEC_HEADER? newExecHeader =
            await DbHelper.getCiltExecHeader(ciltHeader);
        if (newExecHeader != null) {
          await ref
              .read(ciltHeaderProvider.notifier)
              .selectedCiltHeaders(ciltHeader);
        }
      }
    }
  }

  bool isExecutionVisible({String? checkFor}) {
    final todayDate = DateFormat('yyyyMMdd').format(DateTime.now());
    DateTime now = DateTime.now();

    String startAT = widget.ciltExecHeader!.start_at.toString();
    DateTime executionDateTime =
        Utils.getTimeStamp(widget.ciltExecHeader!.start_on.toString(), startAT);
    final role = ref.watch(roleProvider);

    bool isAssignedUser =
        (ref.read(userProvider)?.user_id == widget.ciltExecHeader?.assigned_to);

    if (role != null &&
        (checkFor == AppConstants.faults || isAssignedUser) &&
        (widget.ciltExecHeader?.status != "REJECTED") &&
        (executionDateTime.isBefore(now))) {
      if (UIHelper.isExecute(role.cilt!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  clearNewFaultStates() {
    final faultHeader = ref.read(faultHeaderProvider.notifier);
    final location = ref.watch(locationProvider.notifier);
    final assetLocList = ref.watch(assetLocListProvider.notifier);
    final asset = ref.watch(assetProvider.notifier);
    final dueOn = ref.watch(faultDueOnProvider.notifier);
    final description = ref.watch(faultDescriptionProvider.notifier);
    final faultMode = ref.watch(faultModeHeaderProvider.notifier);
    final priority = ref.watch(priorityProvider.notifier);
    final faultType = ref.watch(faultTypeProvider.notifier);
    final longText = ref.watch(faultLongTextProvider.notifier);
    final reportedBy = ref.watch(faultReportedByProvider.notifier);
    location.clearLocation();
    assetLocList.clearAssetLocList();
    asset.clearAsset();
    dueOn.clearDueOn();
    description.clearFaultDescription();
    faultMode.clearFaultMode();
    priority.clearPriority();
    faultType.clearFaultType();
    longText.clearFaultLongText();
    reportedBy.clearFaultReportedBy();
    faultHeader.clearFault();
  }

  clearFaultStates() {
    final location = ref.watch(locationProvider.notifier);
    final assetLocList = ref.watch(assetLocListProvider.notifier);
    final asset = ref.watch(assetProvider.notifier);
    final dueOn = ref.watch(faultDueOnProvider.notifier);
    final description = ref.watch(faultDescriptionProvider.notifier);
    final faultMode = ref.watch(faultModeHeaderProvider.notifier);
    final priority = ref.watch(priorityProvider.notifier);
    final faultType = ref.watch(faultTypeProvider.notifier);
    final longText = ref.watch(faultLongTextProvider.notifier);
    final reportedBy = ref.watch(faultReportedByProvider.notifier);
    location.clearLocation();
    assetLocList.clearAssetLocList();
    asset.clearAsset();
    dueOn.clearDueOn();
    description.clearFaultDescription();
    faultMode.clearFaultMode();
    priority.clearPriority();
    faultType.clearFaultType();
    longText.clearFaultLongText();
    reportedBy.clearFaultReportedBy();
  }
}
