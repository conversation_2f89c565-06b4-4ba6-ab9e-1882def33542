import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';

class DisplayAttachmentPage extends StatelessWidget {
  final DOCUMENT_HEADER document_header;

  const DisplayAttachmentPage({Key? key, required this.document_header}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(

      body: SafeArea(
        child: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: Column(
            children: [
              getAppbar(context),
              Expanded(
                child: Center(
                  child: GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Center(
                      child: Image.memory(
                        base64Decode(document_header.thumbnail!.replaceAll(RegExp(r'\s+'), '')),

                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  getAppbar(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
        height:50,
      child: Row(
        children: [
          IconButton(onPressed: (){
            Navigator.pop(context);
            }, icon: const Icon(Icons.arrow_back_ios)),
          SizedBox(
            height: 50,
            width: double.infinity,
            child: Center(child: Text(document_header.file_name!),),
          ),
        ],
      ),
    );
  }
}