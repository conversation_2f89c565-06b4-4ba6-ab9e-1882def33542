import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class NoRecordWidget extends StatelessWidget {
  final String? label;
  final bool showImage;

  const NoRecordWidget({Key? key, this.label, this.showImage = true})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (showImage) ...[
          Expanded(
              flex: 3,
              child: Center(child: Image.asset("assets/images/logo.png")))
        ],
        Expanded(
          flex: 1,
          child: Center(
            child: Text(
              label ?? AppLocalizations.of(context)!.no_records_found,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.grey,
                fontWeight: FontWeight.bold,
                fontSize: 30,
              ),
            ),
          ),
        )
      ],
    );
  }
}
