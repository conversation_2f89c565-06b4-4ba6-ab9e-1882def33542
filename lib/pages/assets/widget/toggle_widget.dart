import 'package:flutter/material.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:rounds/icons/app_icons.dart';

class EamToggleWidget extends StatefulWidget {
  final EamIcon firstIcon;
  final EamIcon secondIcon;
  final EamIcon? thirdIcon;
  final ValueChanged<int> onToggleCallback;
  final int externalSelectedIndex;

  final Color background = HexColor('#F6F6F7');
  final Color highlightedBackground = Colors.white;
  final Color normal = HexColor('#777676');
  final Color highlighted = Colors.black;

  EamToggleWidget({
    required this.firstIcon,
    required this.secondIcon,
    required this.thirdIcon,
    required this.onToggleCallback,
    this.externalSelectedIndex = -1, // Default to no selection
  });

  @override
  State<EamToggleWidget> createState() => _EamToggleWidgetState();
}

class _EamToggleWidgetState extends State<EamToggleWidget> {
  int _selectedIndex = 0; // Default to no selection

  @override
  void didUpdateWidget(EamToggleWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.externalSelectedIndex != oldWidget.externalSelectedIndex) {
      setState(() {
        _selectedIndex = widget.externalSelectedIndex;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 172,
      height: 48,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        color: widget.background,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildToggleIcon(index: 0, assetPath: 'assets/icons/Floc.png'),
          const SizedBox(width: 2),
          _buildToggleIcon(index: 1, assetPath: 'assets/icons/asset.png'),
          const SizedBox(width: 2),
          if (widget.thirdIcon != null)
            _buildToggleIcon(index: 2, assetPath: 'assets/icons/kpi.png'),
        ],
      ),
    );
  }

  Widget _buildToggleIcon({required int index, required String assetPath}) {
    final isSelected = _selectedIndex == index;

    return InkWell(
      onTap: () {
        setState(() {
          _selectedIndex = index;
          widget.onToggleCallback(_selectedIndex);
        });
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius:
              isSelected ? BorderRadius.circular(6) : BorderRadius.circular(0),
          color: isSelected ? widget.highlightedBackground : widget.background,
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.5),
                    blurRadius: 0.5,
                    offset: const Offset(0.0, 0.75),
                  ),
                ]
              : [],
        ),
        alignment: Alignment.center,
        margin: const EdgeInsets.only(left: 4, right: 4),
        width: 48,
        height: 42,
        child: ColorFiltered(
          colorFilter: isSelected
              ? const ColorFilter.mode(Colors.transparent, BlendMode.multiply)
              : const ColorFilter.matrix(
                  <double>[
                    0.2126, 0.7152, 0.0722, 0, 0, // Red coefficient
                    0.2126, 0.7152, 0.0722, 0, 0, // Green coefficient
                    0.2126, 0.7152, 0.0722, 0, 0, // Blue coefficient
                    0, 0, 0, 1, 0, // Alpha
                  ],
                ),
          child: Image.asset(
            assetPath,
            width: 30,
            height: 30,
          ),
        ),
      ),
    );
  }
}
