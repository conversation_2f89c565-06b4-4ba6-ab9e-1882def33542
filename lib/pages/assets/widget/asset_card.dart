import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:rounds/be/ASSET_HEADER.dart';
import 'package:rounds/be/FAULT_HEADER.dart';
import 'package:intl/intl.dart';
import 'package:rounds/be/KPI_HEADER.dart';
import 'package:rounds/be/LOCATION_CATEGORY_HEADER.dart';
import 'package:rounds/be/LOCATION_HEADER.dart';
import 'package:rounds/be/ROLE_HEADER.dart';
import 'package:rounds/models/intractive_Item_Model.dart';
import 'package:rounds/pages/assets/detail_screens/asset_detail_screen.dart';
import 'package:rounds/utils/utils.dart';
import '../../../be/ASSET_CATEGORY_HEADER.dart';
import '../../../helpers/ui_helper.dart';

import '../../../providers/fault/fault_header_provider.dart';
import '../../../providers/fault/fault_type_provider.dart';
import '../../../providers/user_provider.dart';
import '../../../utils/app_colors.dart';
import '../../../utils/app_constants.dart';
import '../../fault/fault_detail_screen.dart';
import '../../fault/tabs/edit_fault_field_provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AssetCard extends ConsumerStatefulWidget {
  final int itemIndex;
  final Color color;
  final int assetTypeIndex;
  final dynamic? asset;
  const AssetCard(
      {super.key,
      required this.itemIndex,
      this.color = Colors.white,
      required this.assetTypeIndex,
      required this.asset,
      required this.onTap});
  final Function(InteractiveItemModel) onTap;

  @override
  ConsumerState<AssetCard> createState() => _AssetCardState();
}

class _AssetCardState extends ConsumerState<AssetCard> {
  String title = "";
  String description = "";
  String category = "";

  @override
  void initState() {
    // TODO: implement initStat
    super.initState();
   /* Future.delayed(Duration.zero).then((value) {
      if (UIHelper().getScreenType(context) == ScreenType.desktop) {
        if (widget.itemIndex == 0) {
          onTapCard();
        }
      }
    });*/
  }

  @override
  Widget build(BuildContext context) {
    final role = ref.watch(roleProvider);
    if (widget.asset is ASSET_HEADER) {
      title = widget.asset.asset_no.toString();
      description = widget.asset.description.toString();
      category = (widget.asset.category ?? "").toString();
    } else if (widget.asset is KPI_HEADER) {
      title = widget.asset.kpi_id.toString();
      description = widget.asset.description.toString();
      category = widget.asset.kpi_type.toString();
    } else if (widget.asset is LOCATION_HEADER) {
      title = widget.asset.location_id.toString();
      description = widget.asset.description.toString();
      category = (widget.asset.category ?? "").toString();
    }
    return getAssetCard(role);
  }

  Widget getAssetCard(ROLE_HEADER? role) {
    if (ScreenType.desktop != UIHelper().getScreenType(context)) {
      {
        return Slidable(
            key: ValueKey((widget.asset is ASSET_HEADER)
                ? widget.asset!.asset_no
                : (widget.asset is KPI_HEADER)
                    ? widget.asset!.kpi_id
                    : (widget.asset is LOCATION_HEADER)
                        ? widget.asset!.location_id
                        : ""),
            endActionPane: ActionPane(
              motion: const ScrollMotion(),
              children: role != null
                  ? UIHelper.isCreate(role.fault!)
                      ? [
                          SlidableAction(
                            onPressed: (context) {
                              clearFaultStates();
                              navigateToFaultScreen();
                            },
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            icon: Icons.arrow_forward_ios,
                            label: AppLocalizations.of(context)!.create_fault,
                          ),
                        ]
                      : [
                          SlidableAction(
                            onPressed: (context) {},
                            label: '',
                          ),
                        ]
                  : [
                      SlidableAction(
                        onPressed: (context) {},
                        label: '',
                      ),
                    ],
            ),
            child: getCard());
      }
    } else {
      return getCard();
    }
  }

  onTapCard() {
    if (UIHelper().getScreenType(context) != ScreenType.desktop) {
      Navigator.of(context).push(MaterialPageRoute(
          builder: (context) => AssetDetailScreen(assetHeader: widget.asset)));
    } else {
      widget.onTap(InteractiveItemModel(
          type: widget.asset.runtimeType.toString(),
          data: {
            "assetHeader": widget.asset,
            "index": widget.itemIndex,
            "assetTypeIndex": widget.assetTypeIndex
          }));
    }
  }

  Future<void> navigateToFaultScreen() async {
    final faultTypeHeader = ref.read(faultTypeListProvider.notifier);
    final faultInsertHeader = ref.read(insertFaultHeaderProvider.notifier);
    final faultHeader = ref.read(faultHeaderProvider.notifier);
    await faultTypeHeader.fetchFaultTypeList();
    final editProvider = ref.read(editFaultFieldProvider.notifier);
    final faultAction = ref.watch(getFaultActionProvider.notifier);
    final faultDocument = ref.watch(getFaultDocumentProvider.notifier);
    final assetLocList = ref.read(assetLocListProvider.notifier);
    final user = ref.watch(userProvider);
    final plant = ref.watch(plantProvider.notifier).state;
    DateTime now = DateTime.now();
    String faultNoticedOnDate = DateFormat('dd MMM yyyy').format(now);
    DateTime dateTime = DateFormat("dd MMM yyyy").parse(faultNoticedOnDate);
    DateTime adjustedDate = DateTime(
      dateTime.year,
      dateTime.month,
      dateTime.day,
    );
    String formattedDate = DateFormat("yyyyMMdd").format(adjustedDate);
    int dateAsInt = int.parse(formattedDate);

    FAULT_HEADER faultHeaderData = FAULT_HEADER(
        fault_id: UIHelper.generateRandomId(),
        reported_by: user!.user_id,
        p_mode: AppConstants.add,
        reported_on: dateAsInt);
    faultHeaderData.location_id = widget.asset is ASSET_HEADER
        ? widget.asset!.parent_loc_id
        : widget.asset.location_id;
    faultHeaderData.asset_no =
        widget.asset is LOCATION_HEADER ? null : widget.asset!.asset_no;
    if (widget.asset is ASSET_HEADER) {
      if (widget.asset!.asset_no != null) {
        await assetLocList
            .fetchAssetLocList(faultHeaderData.location_id.toString());
      }
    }
    if (plant.isNotEmpty) {
      faultHeaderData.plant_id = plant;
    }
    await faultInsertHeader.insertFaultHeader(faultHeaderData);
    await faultHeader.getFaultHeader(
        faultId: faultHeaderData.fault_id.toString());
    await faultAction.getFaultAction(faultHeaderData.fault_id.toString());
    await faultDocument.getFaultDocuments(faultHeaderData.fault_id.toString());

    editProvider.getEditFaultFieldEnable(true);
    if (mounted) {
      if (ScreenType.desktop != UIHelper().getScreenType(context)) {
        await Navigator.push(context, MaterialPageRoute(builder: (context) {
          return const FaultDetailScreen(
            type: AppConstants.addFault,
          );
        }));
      } else {
        await showDialog(
            context: context,
            barrierDismissible: false,
            builder: (rootDialogContext) => Dialog(
                backgroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0),
                ),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.9,
                    maxHeight: MediaQuery.of(context).size.height * 0.9,
                  ),
                  child: const Padding(
                    padding: EdgeInsets.all(20.0),
                    child: FaultDetailScreen(type: AppConstants.addFault),
                  ),
                )));
      }
/*      await Navigator.push(context, MaterialPageRoute(builder: (context) {
        return FaultDetailScreen(
          type: 'Add Fault',
        );
      }));*/
    }
  }

  clearFaultStates() {
    final faultHeader = ref.read(faultHeaderProvider.notifier);
    final location = ref.watch(locationProvider.notifier);
    final assetLocList = ref.watch(assetLocListProvider.notifier);
    final asset = ref.watch(assetProvider.notifier);
    final dueOn = ref.watch(faultDueOnProvider.notifier);
    final description = ref.watch(faultDescriptionProvider.notifier);
    final faultMode = ref.watch(faultModeHeaderProvider.notifier);
    final priority = ref.watch(priorityProvider.notifier);
    final faultType = ref.watch(faultTypeProvider.notifier);
    final longText = ref.watch(faultLongTextProvider.notifier);
    final reportedBy = ref.watch(faultReportedByProvider.notifier);
    location.clearLocation();
    assetLocList.clearAssetLocList();
    asset.clearAsset();
    dueOn.clearDueOn();
    description.clearFaultDescription();
    faultMode.clearFaultMode();
    priority.clearPriority();
    faultType.clearFaultType();
    longText.clearFaultLongText();
    reportedBy.clearFaultReportedBy();
    faultHeader.clearFault();
  }

  String getCategory(dynamic asset) {
    String category = "";
    if (asset is ASSET_HEADER) {
      category = getAssetCategoryName(asset.category.toString());
    } else if (asset is LOCATION_HEADER) {
      category = getLocationName(asset.category.toString());
    } else if (asset is KPI_HEADER) {
      category = getKpiCategoryName(asset.kpi_type.toString());
    } else {
      category = "";
    }
    return category;
  }

  String getAssetCategoryName(String category) {
    final assetCategoryList = ref.read(assetCategoryListProvider);
    String asset = "";
    ASSET_CATEGORY_HEADER matchedCategory = assetCategoryList.firstWhere(
        (item) => item.category_code == category,
        orElse: () => ASSET_CATEGORY_HEADER(category_code: ''));
    asset = matchedCategory.description ?? "";
    return asset;
  }

  String getLocationName(String category) {
    final locCategoryList = ref.read(locationCategoryListProvider);
    String loc = "";
    LOCATION_CATEGORY_HEADER matchedCategory = locCategoryList.firstWhere(
      (item) => item.category_code == category,
      orElse: () => LOCATION_CATEGORY_HEADER(category_code: ''),
    );
    loc = matchedCategory.description ?? "";
    return loc;
  }

  getKpiCategoryName(String category) {
    String type = '';
    switch (category) {
      case "1":
        type = "Qualitative";
        break;
      case "2":
        type = "Quantitative";
        break;
      case "3":
        type = "Up Counter";
        break;
      case "4":
        type = "Down Counter";
        break;
    }
    return type;
  }

  getChipColor(asset) {
    if (asset is ASSET_HEADER) {
      return HexColor("#23A9A3");
    } else if (asset is KPI_HEADER) {
      return HexColor("#FFB000");
    } else if (asset is LOCATION_HEADER) {
      return HexColor("#1046AE");
    } else {
      return AppColors.white;
    }
  }

  Widget getCard() {
    return InkWell(
      onTap: onTapCard,
      child: Card(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          child: Container(
            padding: const EdgeInsets.only(top: 10, bottom: 10),
            decoration: UIHelper.cardDecoration(cardColor: widget.color),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 18.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 13,
                          color: AppColors.titleTextColor,
                          fontWeight: FontWeight.w700,
                          letterSpacing: 0.1,
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Text(
                        description,
                        style: TextStyle(
                          color: AppColors.secondaryTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                getCategory(widget.asset) != ""
                    ? Align(
                        alignment: Alignment.centerRight,
                        child: Padding(
                          padding: const EdgeInsets.only(right: 0.0),
                          child: Container(
                            alignment: Alignment.centerRight,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 4),
                            decoration: BoxDecoration(
                              color: getChipColor(widget.asset),
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(12),
                                bottomLeft: Radius.circular(12),
                              ),
                            ),
                            child: Text(
                              getCategory(widget.asset),
                              style: TextStyle(
                                color: AppColors.white,
                                fontSize: 11,
                                letterSpacing: 0.5,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      )
                    : SizedBox(),
              ],
            ),
          )),
    );
  }
}
