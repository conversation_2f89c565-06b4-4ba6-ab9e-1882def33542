import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/models/intractive_Item_Model.dart';

import 'package:rounds/pages/assets/detail_screens/asset_detail_screen.dart';

import 'package:rounds/pages/home/<USER>';
import 'package:rounds/pages/widgets/search_bar.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:rounds/utils/utils.dart';

import '../../icons/app_icons.dart';
import '../../models/menu_action_item.dart';
import '../../providers/assets/asset_provider.dart';
import '../../providers/assets/floc_provider.dart';
import '../../providers/assets/kpi_provider.dart';
import '../../providers/fault/fault_header_provider.dart';
import '../fault/fault_filter_provider.dart';
import '../widgets/menu_action_button.dart';
import 'assets_list.dart';
import 'hierarchy_page.dart';

ValueNotifier<InteractiveItemModel?> assetDetailViewNotifier =
    ValueNotifier<InteractiveItemModel?>(null);

class AssetScreen extends ConsumerStatefulWidget {
  const AssetScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AssetScreenState();
}

class _AssetScreenState extends ConsumerState<AssetScreen>
    with SingleTickerProviderStateMixin {
  int selectedIndex = 0;

  final ScrollController scrollController = ScrollController();
  final ScrollController nestedScrollController = ScrollController();
  TextEditingController searchController = TextEditingController();

  late TabController tabController;

  @override
  void initState() {
    super.initState();
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    tabController = TabController(length: 4, vsync: this);
    tabController.addListener(() {
      searchController.clear();
      if (scrollController.hasClients) {
        scrollController.animateTo(
          0.0,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
        nestedScrollController.animateTo(
          0.0,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
      if (tabController.indexIsChanging != false) {

        selectedIndex = tabController.index;

        switch (selectedIndex) {
          case 0:
            ref.read(flocHeaderProvider.notifier).getLocHeaderList(plant);
            break;
          case 1:
            ref.read(assetHeaderProvider.notifier).getAssetHeaderList(plant);
            break;
          case 2:
            ref.read(kpiHeaderProvider.notifier).getKPIHeaderList(plant);
            break;
          default:
        }
        setState(() {});
      }
      assetDetailViewNotifier.value = InteractiveItemModel(
        type: "",
        data: {"type": "", "index": null},
      );
    });
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  List<Tab> tabs = [
    Tab(
      icon: FittedBox(
        child: Image.asset(
          'assets/icons/Floc.png',
          width: 30,
          height: 30,
        ),
      ),
    ),
    Tab(
      icon: FittedBox(
        child: Image.asset(
          'assets/icons/asset.png',
          width: 30,
          height: 30,
        ),
      ),
    ),
    Tab(
      icon: FittedBox(
        child: Image.asset(
          'assets/icons/kpi.png',
          width: 30,
          height: 30,
        ),
      ),
    ),
    Tab(
      icon: FittedBox(
        child: EamIcon(iconName: EamIcon.hierarchy)
            .copyWith(
              height: 30,
              width: 30,
              color: Colors.grey,
            )
            .icon(),
      ),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final plant = ref.watch(plantProvider.notifier).state;
    final plantSection = ref.watch(plantSectionProvider.notifier).state;
    final filteredFaultType =
        ref.watch(filteredFaultHeaderListProvider.notifier);
    return DefaultTabController(
      length: 4,
      initialIndex: 0,
      child: SafeArea(
        child: Scaffold(
          backgroundColor: Colors.white,
          body: LayoutBuilder(
            builder: (context, constraints) {
              var screenType = UIHelper().getScreenType(context);
              switch (screenType) {
                case ScreenType.mobile:
                  return getBody(plant, plantSection);
                case ScreenType.tablet:
                  return getBody(plant, plantSection);
                case ScreenType.desktop:
                  return Row(
                    children: [
                      ConstrainedBox(
                          constraints: const BoxConstraints(
                            minWidth: 410,
                            maxWidth: 410,
                          ),
                          child: getBody(plant, plantSection)),
                      const VerticalDivider(
                        thickness: 3,
                      ),
                      Expanded(
                          child: ValueListenableBuilder(
                        valueListenable: assetDetailViewNotifier,
                        builder: (context, value, child) {
                          if (value == null || value.type == "") {
                            return Container(
                              color: Colors.white,
                            );
                          } else if (value.type == "hierarchy") {
                            return HierarchyPage(
                              onItemTap: (item) {
                                assetDetailViewNotifier.value = item;
                              },
                            );
                          } else {
                            return AssetDetailScreen(
                                assetHeader: value.getValue("assetHeader"));
                          }
                        },
                      ))
                    ],
                  );
              }
            },
          ),
        ),
      ),
    );
  }

  Widget getBody(String plant, List<String> plantSection) {
    return NestedScrollView(
      controller: nestedScrollController,
      headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
        return <Widget>[
          SliverPersistentHeader(
            pinned: true,
            delegate: SliverAppBarDelegate(
              minHeight: 163.0,
              maxHeight: 163.0,
              child: Container(
                color: AppColors.white,
                padding: UIHelper().getScreenType(context) == ScreenType.desktop
                    ? const EdgeInsets.symmetric(horizontal: 10)
                    : const EdgeInsets.symmetric(horizontal: 18),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    Row(
                      children: [
                        Text(
                          tabController.index == 0
                              ? "${AppLocalizations.of(context)!.floc}(${ref.watch(flocHeaderProvider).length})"
                              : tabController.index == 1
                                  ? "${AppLocalizations.of(context)!.asset}(${ref.watch(assetHeaderProvider).length})"
                                  : tabController.index == 2
                                      ? "${AppLocalizations.of(context)!.kpi}(${ref.watch(kpiHeaderProvider).length})"
                                      : AppLocalizations.of(context)!
                                          .location_hierarchy,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        // Hierarchy button

                        /*                     MenuActionButton(
                          options: [
                            MenuActionItem(
                              value: 'search',
                              name: AppLocalizations.of(context)!.searchString,
                            ),
                            MenuActionItem(
                              value: 'refresh',
                              name: AppLocalizations.of(context)!.refreshString,
                            ),
                          ],
                          onOptionItemSelected: (item) async {
                            switch (item.value) {
                              case 'search':
                              case 'refresh':
                                // Handle refresh
                                break;
                              default:

                              // Handle options
                            }
                          },
                        ),*/
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Expanded(
                      child: CustomSearchBar(
                        onChanged: (v) {
                          if (kIsWeb) {
                            assetDetailViewNotifier.value =
                                InteractiveItemModel(
                              type: "",
                              data: {"type": "", "index": null},
                            );
                          }
                          switch (selectedIndex) {
                            case 0:
                              ref
                                  .read(flocHeaderProvider.notifier)
                                  .filter(v, plant);
                              break;
                            case 1:
                              ref
                                  .read(assetHeaderProvider.notifier)
                                  .filter(v, plant);
                              break;
                            case 2:
                              ref
                                  .read(kpiHeaderProvider.notifier)
                                  .filter(v, plant);
                              break;
                            default:
                          }
                          if (scrollController.hasClients) {
                            scrollController.animateTo(
                              0.0,
                              duration: Duration(milliseconds: 300),
                              curve: Curves.easeOut,
                            );
                            nestedScrollController.animateTo(
                              0.0,
                              duration: Duration(milliseconds: 300),
                              curve: Curves.easeOut,
                            );
                          }
                          setState(() {});
                        },
                        controller: searchController,
                        onCancel: () {
                          setState(() {
                            searchController.clear();
                          });
                          if (scrollController.hasClients) {
                            scrollController.animateTo(
                              0.0,
                              duration: Duration(milliseconds: 300),
                              curve: Curves.easeOut,
                            );
                            nestedScrollController.animateTo(
                              0.0,
                              duration: Duration(milliseconds: 300),
                              curve: Curves.easeOut,
                            );
                          }
                          switch (selectedIndex) {
                            case 0:
                              ref
                                  .read(flocHeaderProvider.notifier)
                                  .getLocHeaderList(plant);
                              break;
                            case 1:
                              ref
                                  .read(assetHeaderProvider.notifier)
                                  .getAssetHeaderList(plant);
                              break;
                            case 2:
                              ref
                                  .read(kpiHeaderProvider.notifier)
                                  .getKPIHeaderList(plant);
                              break;
                            default:
                          }
                          assetDetailViewNotifier.value = InteractiveItemModel(
                            type: "",
                            data: {"type": "", "index": null},
                          );
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 10, bottom: 5),
                      child: TabBar(controller: tabController, tabs: tabs),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ];
      },
      body: Container(
        color: AppColors.white,
        child: Padding(
          padding: UIHelper().getScreenType(context) == ScreenType.desktop
              ? const EdgeInsets.symmetric(horizontal: 0)
              : const EdgeInsets.symmetric(horizontal: 10),
          child: TabBarView(
            controller: tabController,
            children: <Widget>[
              AssetListPage(
                selectedIndex: 0,
                scrollController: scrollController,
                onItemTap: (value) {
                  assetDetailViewNotifier.value = value;
                },
              ),
              AssetListPage(
                scrollController: scrollController,
                selectedIndex: 1,
                onItemTap: (value) {
                  assetDetailViewNotifier.value = value;
                },
              ),
              AssetListPage(
                scrollController: scrollController,
                selectedIndex: 2,
                onItemTap: (value) {
                  assetDetailViewNotifier.value = value;
                },
              ),
              HierarchyPage(
                onItemTap: (value) {
                  assetDetailViewNotifier.value = value;
                },
              )
              // ActivityTab(),
            ],
          ),
        ),
      ),
    );
  }

  getBodyChild() {
    switch (selectedIndex) {
      case 0:
      case 2:
      case 1:
        return AssetListPage(
          scrollController: scrollController,
          selectedIndex: selectedIndex,
          onItemTap: (value) {
            assetDetailViewNotifier.value = value;
          },
        );
      case 3:
        return HierarchyPage(
          onItemTap: (value) {
            assetDetailViewNotifier.value = value;
          },
        );
    }
  }
}
/*
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/models/intractive_Item_Model.dart';

import 'package:rounds/pages/assets/detail_screens/asset_detail_screen.dart';

import 'package:rounds/pages/home/<USER>';
import 'package:rounds/pages/widgets/search_bar.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:rounds/utils/utils.dart';

import '../../icons/app_icons.dart';
import '../../models/menu_action_item.dart';
import '../../providers/assets/asset_provider.dart';
import '../../providers/assets/floc_provider.dart';
import '../../providers/assets/kpi_provider.dart';
import '../../providers/fault/fault_header_provider.dart';
import '../fault/fault_filter_provider.dart';
import '../widgets/menu_action_button.dart';
import 'assets_list.dart';
import 'hierarchy_page.dart';

class AssetScreen extends ConsumerStatefulWidget {
  const AssetScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AssetScreenState();
}

class _AssetScreenState extends ConsumerState<AssetScreen>
    with SingleTickerProviderStateMixin {
  int selectedIndex = 0;

  final ScrollController scrollController = ScrollController();
  TextEditingController searchController = TextEditingController();
  ValueNotifier<InteractiveItemModel?> assetDetailViewNotifier =
      ValueNotifier<InteractiveItemModel?>(null);

  late TabController tabController;

  @override
  void initState() {
    // TODO: implement initState
    tabController = TabController(length: 3, vsync: this);
    super.initState();
  }

  List<Tab> tabs = [
    Tab(
      icon: FittedBox(
        child: Image.asset(
          'assets/icons/Floc.png',
          width: 30,
          height: 30,
        ),
      ),
    ),
    Tab(
      icon: FittedBox(
        child: Image.asset(
          'assets/icons/asset.png',
          width: 30,
          height: 30,
        ),
      ),
    ),
    Tab(
      icon: FittedBox(
        child: Image.asset(
          'assets/icons/kpi.png',
          width: 30,
          height: 30,
        ),
      ),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final plant = ref.watch(plantProvider.notifier).state;
    final plantSection = ref.watch(plantSectionProvider.notifier).state;
    final filteredFaultType =
        ref.watch(filteredFaultHeaderListProvider.notifier);
    return DefaultTabController(
      length: 3,
      initialIndex: 0,
      child: SafeArea(
        child: Scaffold(
          backgroundColor: Colors.white,
          body: LayoutBuilder(
            builder: (context, constraints) {
              var screenType = UIHelper().getScreenType(context);
              switch (screenType) {
                case ScreenType.mobile:
                  return getBody(plant, plantSection);
                case ScreenType.tablet:
                  return getBody(plant, plantSection);
                case ScreenType.desktop:
                  return Row(
                    children: [
                      ConstrainedBox(
                          constraints: const BoxConstraints(
                            minWidth: 410,
                            maxWidth: 410,
                          ),
                          child: getBody(plant, plantSection)),
                      const VerticalDivider(
                        thickness: 3,
                      ),
                      Expanded(
                          child: ValueListenableBuilder(
                        valueListenable: assetDetailViewNotifier,
                        builder: (context, value, child) {
                          if (value == null) {
                            return Container(
                              color: Colors.white,
                            );
                          } else {
                            return AssetDetailScreen(
                                assetHeader: value.getValue("assetHeader"));
                          }
                        },
                      ))
                    ],
                  );
              }
            },
          ),
        ),
      ),
    );
  }

  // Widget getBody(String plant, List<String> plantSection) {
  //   return NestedScrollView(
  //     controller: scrollController,
  //     headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
  //       return <Widget>[
  //         SliverPersistentHeader(
  //           pinned: true,
  //           delegate: SliverAppBarDelegate(
  //             minHeight: 163.0,
  //             maxHeight: 163.0,
  //             child: Container(
  //               color: AppColors.white,
  //               padding: UIHelper().getScreenType(context) == ScreenType.desktop
  //                   ? const EdgeInsets.symmetric(horizontal: 10)
  //                   : const EdgeInsets.symmetric(horizontal: 18),
  //               child: Column(
  //                 crossAxisAlignment: CrossAxisAlignment.start,
  //                 children: [
  //                   const SizedBox(
  //                     height: 10,
  //                   ),
  //                   Row(
  //                     children: [
  //                       Text(
  //                         selectedIndex == 0
  //                             ? "${AppLocalizations.of(context)!.floc}(${ref.watch(flocHeaderProvider).length})"
  //                             : selectedIndex == 1
  //                             ? "${AppLocalizations.of(context)!.asset}(${ref.watch(assetHeaderProvider).length})"
  //                             : selectedIndex == 2
  //                             ? "${AppLocalizations.of(context)!.kpi}(${ref.watch(kpiHeaderProvider).length})"
  //                             : AppLocalizations.of(context)!
  //                             .location_hierarchy,
  //                         style: const TextStyle(
  //                           fontSize: 20,
  //                           fontWeight: FontWeight.bold,
  //                         ),
  //                       ),
  //                       const Spacer(),
  //                       // Hierarchy button
  //                       InkWell(
  //                         onTap: () {
  //                           setState(() {
  //                             selectedIndex = 3;
  //                             scrollController.animateTo(
  //                               0.0,
  //                               duration: const Duration(milliseconds: 300),
  //                               curve: Curves.easeInOut,
  //                             );
  //                           });
  //                         },
  //                         child: Container(
  //                           alignment: Alignment.center,
  //                           margin: const EdgeInsets.only(left: 4),
  //                           width: 48,
  //                           height: 42,
  //                           child: EamIcon(iconName: EamIcon.hierarchy)
  //                               .copyWith(
  //                             height: 30,
  //                             width: 30,
  //                             color: selectedIndex == 3
  //                                 ? Colors.black
  //                                 : Colors.grey,
  //                           )
  //                               .icon(),
  //                         ),
  //                       ),
  //                       MenuActionButton(
  //                         options: [
  //                           MenuActionItem(
  //                             value: 'search',
  //                             name: AppLocalizations.of(context)!.searchString,
  //                           ),
  //                           MenuActionItem(
  //                             value: 'refresh',
  //                             name: AppLocalizations.of(context)!.refreshString,
  //                           ),
  //                         ],
  //                         onOptionItemSelected: (item) async {
  //                           switch (item.value) {
  //                             case 'search':
  //                             case 'refresh':
  //                             // Handle refresh
  //                               break;
  //                             default:
  //
  //                             // Handle options
  //                           }
  //                         },
  //                       ),
  //                     ],
  //                   ),
  //                   const SizedBox(
  //                     height: 10,
  //                   ),
  //                   Expanded(
  //                     child: CustomSearchBar(
  //                       onChanged: (v) {
  //                         switch (selectedIndex) {
  //                           case 0:
  //                             ref
  //                                 .read(flocHeaderProvider.notifier)
  //                                 .filter(v, plant, plantSection);
  //                             break;
  //                           case 1:
  //                             ref
  //                                 .read(assetHeaderProvider.notifier)
  //                                 .filter(v, plant, plantSection);
  //                             break;
  //                           case 2:
  //                             ref
  //                                 .read(kpiHeaderProvider.notifier)
  //                                 .filter(v, plant);
  //                             break;
  //                           default:
  //                         }
  //                         setState(() {});
  //                       },
  //                       controller: searchController,
  //                       onCancel: () {
  //                         setState(() {
  //                           searchController.clear();
  //                         });
  //                         switch (selectedIndex) {
  //                           case 0:
  //                             ref
  //                                 .read(flocHeaderProvider.notifier)
  //                                 .getLocHeaderList(plant, plantSection);
  //                             break;
  //                           case 1:
  //                             ref
  //                                 .read(assetHeaderProvider.notifier)
  //                                 .getAssetHeaderList(plant, plantSection);
  //                             break;
  //                           case 2:
  //                             ref
  //                                 .read(kpiHeaderProvider.notifier)
  //                                 .getKPIHeaderList(plant);
  //                             break;
  //                           default:
  //                         }
  //                       },
  //                     ),
  //                   ),
  //                   Padding(
  //                     padding: const EdgeInsets.only(top: 10, bottom: 5),
  //                     child: TabBar(controller: tabController, tabs: tabs),
  //                   ),
  //                 ],
  //               ),
  //             ),
  //           ),
  //         ),
  //       ];
  //     },
  //     body: Container(
  //       color: AppColors.white,
  //       child: Padding(
  //         padding: UIHelper().getScreenType(context) == ScreenType.desktop
  //             ? const EdgeInsets.symmetric(horizontal: 0)
  //             : const EdgeInsets.symmetric(horizontal: 10),
  //         child: TabBarView(
  //           controller: tabController,
  //           children: <Widget>[
  //             AssetListPage(
  //               selectedIndex: 0,
  //               onItemTap: (value) {
  //                 assetDetailViewNotifier.value = value;
  //               },
  //             ),
  //             AssetListPage(
  //               selectedIndex: 1,
  //               onItemTap: (value) {
  //                 assetDetailViewNotifier.value = value;
  //               },
  //             ),
  //             AssetListPage(
  //               selectedIndex: 2,
  //               onItemTap: (value) {
  //                 assetDetailViewNotifier.value = value;
  //               },
  //             ),
  //             // ActivityTab(),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }
  Widget getBody(String plant, List<String> plantSection) {
    return NestedScrollView(
      controller: scrollController,
      headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
        return <Widget>[
          SliverPersistentHeader(
            pinned: true,
            delegate: SliverAppBarDelegate(
              minHeight: 163.0,
              maxHeight: 163.0,
              child: Container(
                color: AppColors.white,
                padding: UIHelper().getScreenType(context) == ScreenType.desktop
                    ? const EdgeInsets.symmetric(horizontal: 10)
                    : const EdgeInsets.symmetric(horizontal: 18),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        Text(
                          selectedIndex == 0
                              ? "${AppLocalizations.of(context)!.floc}(${ref.watch(flocHeaderProvider).length})"
                              : selectedIndex == 1
                                  ? "${AppLocalizations.of(context)!.asset}(${ref.watch(assetHeaderProvider).length})"
                                  : selectedIndex == 2
                                      ? "${AppLocalizations.of(context)!.kpi}(${ref.watch(kpiHeaderProvider).length})"
                                      : AppLocalizations.of(context)!
                                          .location_hierarchy,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        // Hierarchy Button
                        InkWell(
                          onTap: () {
                            setState(() {
                              selectedIndex = 3;
                              scrollController.animateTo(
                                0.0,
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              );
                            });
                          },
                          child: Container(
                            alignment: Alignment.center,
                            margin: const EdgeInsets.only(left: 4),
                            width: 48,
                            height: 42,
                            child: EamIcon(iconName: EamIcon.hierarchy)
                                .copyWith(
                                  height: 30,
                                  width: 30,
                                  color: selectedIndex == 3
                                      ? Colors.black
                                      : Colors.grey,
                                )
                                .icon(),
                          ),
                        ),
                        MenuActionButton(
                          options: [
                            MenuActionItem(
                              value: 'search',
                              name: AppLocalizations.of(context)!.searchString,
                            ),
                            MenuActionItem(
                              value: 'refresh',
                              name: AppLocalizations.of(context)!.refreshString,
                            ),
                          ],
                          onOptionItemSelected: (item) async {
                            switch (item.value) {
                              case 'search':
                              case 'refresh':
                                break;
                              default:
                            }
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    Expanded(
                      child: CustomSearchBar(
                        onChanged: (v) {
                          switch (selectedIndex) {
                            case 0:
                              ref
                                  .read(flocHeaderProvider.notifier)
                                  .filter(v, plant, plantSection);
                              break;
                            case 1:
                              ref
                                  .read(assetHeaderProvider.notifier)
                                  .filter(v, plant, plantSection);
                              break;
                            case 2:
                              ref
                                  .read(kpiHeaderProvider.notifier)
                                  .filter(v, plant);
                              break;
                          }
                          setState(() {});
                        },
                        controller: searchController,
                        onCancel: () {
                          setState(() {
                            searchController.clear();
                          });
                          switch (selectedIndex) {
                            case 0:
                              ref
                                  .read(flocHeaderProvider.notifier)
                                  .getLocHeaderList(plant, plantSection);
                              break;
                            case 1:
                              ref
                                  .read(assetHeaderProvider.notifier)
                                  .getAssetHeaderList(plant, plantSection);
                              break;
                            case 2:
                              ref
                                  .read(kpiHeaderProvider.notifier)
                                  .getKPIHeaderList(plant);
                              break;
                          }
                        },
                      ),
                    ),
                    if (selectedIndex != 3)
                      Padding(
                        padding: const EdgeInsets.only(top: 10, bottom: 5),
                        child: TabBar(controller: tabController, tabs: tabs),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ];
      },
      body: Container(
        color: AppColors.white,
        child: Padding(
          padding: UIHelper().getScreenType(context) == ScreenType.desktop
              ? const EdgeInsets.symmetric(horizontal: 0)
              : const EdgeInsets.symmetric(horizontal: 10),
          child:
              // selectedIndex == 3
              //     ? HierarchyPage(
              //         onItemTap: (value) {
              //           assetDetailViewNotifier.value = value;
              //         },
              //       )
              //     :
              TabBarView(
            controller: tabController,
            children: <Widget>[
              selectedIndex == 3
                  ? HierarchyPage(
                      onItemTap: (value) {
                        assetDetailViewNotifier.value = value;
                      },
                    )
                  : AssetListPage(
                      selectedIndex: 0,
                      onItemTap: (value) {
                        assetDetailViewNotifier.value = value;
                      },
                    ),
              selectedIndex == 3
                  ? HierarchyPage(
                      onItemTap: (value) {
                        assetDetailViewNotifier.value = value;
                      },
                    )
                  : AssetListPage(
                      selectedIndex: 1,
                      onItemTap: (value) {
                        assetDetailViewNotifier.value = value;
                      },
                    ),
              selectedIndex == 3
                  ? HierarchyPage(
                      onItemTap: (value) {
                        assetDetailViewNotifier.value = value;
                      },
                    )
                  : AssetListPage(
                      selectedIndex: 2,
                      onItemTap: (value) {
                        assetDetailViewNotifier.value = value;
                      },
                    ),
            ],
          ),
        ),
      ),
    );
  }

  getBodyChild() {
    switch (selectedIndex) {
      case 0:
      case 2:
      case 1:
        return AssetListPage(
          selectedIndex: selectedIndex,
          onItemTap: (value) {
            assetDetailViewNotifier.value = value;
          },
        );
      case 3:
        return HierarchyPage(
          onItemTap: (value) {
            assetDetailViewNotifier.value = value;
          },
        );
    }
  }
}
*/
