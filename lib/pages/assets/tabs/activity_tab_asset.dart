import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../../be/FAULT_HEADER.dart';
import '../../../helpers/ui_helper.dart';
import 'package:intl/intl.dart';

import '../../../providers/fault/fault_header_provider.dart';
import '../../../utils/app_colors.dart';
import '../widget/display_attachment_page.dart';

class ActivityTab extends ConsumerStatefulWidget {
  ActivityTab({super.key});

  @override
  _ActivityTabState createState() => _ActivityTabState();
}

class _ActivityTabState extends ConsumerState<ActivityTab> {
  TextEditingController inputController = TextEditingController();
  String readOnlyText = "";

  @override
  void initState() {
    super.initState();
    inputController = TextEditingController();
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    if (faultHeader.details != null) {
      final currentDateTime =
          DateFormat('dd-MM-yyyy hh:mm a').format(DateTime.now());
      readOnlyText = '$currentDateTime \n ${faultHeader.details} \n';
    } else {
      final currentDateTime =
          DateFormat('dd-MM-yyyy hh:mm a').format(DateTime.now());
      readOnlyText = '$currentDateTime \n';
    }
  }

  @override
  Widget build(BuildContext context) {

    return SafeArea(
      child: SingleChildScrollView(
        child: Padding(
          padding: UIHelper.columnFieldPadding(),
         child:           Container(
           decoration: UIHelper.cardDecoration(),

         ),

        ),
      ),
    );
  }
}


