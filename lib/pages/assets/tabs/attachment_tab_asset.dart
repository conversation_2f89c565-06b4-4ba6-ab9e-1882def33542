import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/ASSET_HEADER.dart';
import 'package:rounds/be/DOCUMENT_ATTACHMENT.dart';
import 'package:rounds/be/LOCATION_HEADER.dart';
import 'package:rounds/widgets/image_viewer_screen.dart';
import '../../../be/DOCUMENT_HEADER.dart';
import '../../../helpers/db_helper.dart';
import '../../../helpers/ui_helper.dart';
import '../../../providers/attachments/attachment_provider.dart';

import '../../../widgets/pdf_viewer_screen.dart';
import '../../../widgets/video_player_screen.dart';
import 'dart:convert';

class AttachmentTab extends ConsumerStatefulWidget {
  dynamic assetHeader;
  AttachmentTab({super.key, required this.assetHeader});

  @override
  _AttachmentTabState createState() => _AttachmentTabState();
}

class _AttachmentTabState extends ConsumerState<AttachmentTab> {
  bool isExpandedForAttachment = false;
  List<DOCUMENT_HEADER> documentHeaders = [];
  List<DOCUMENT_ATTACHMENT> doc_attachments = [];
  List<DOCUMENT_ATTACHMENT> doc_attachmentsTodisplay = [];
  List<DOCUMENT_HEADER> documentHeadersDisplay = [];

  @override
  Widget build(BuildContext context) {
    getFiles();
    return SafeArea(
      child: SingleChildScrollView(
        child: Padding(
          padding: UIHelper.columnFieldOnlhorizontalPadding(),
          child: Column(
            children: [
              UIHelper.sizedBox8(),
              getAttachments(),
            ],
          ),
        ),
      ),
    );
  }

  Widget getAttachments() {
    return doc_attachmentsTodisplay.isNotEmpty
        ? Padding(
            padding: UIHelper.columnFieldPadding(),
            child: Column(
              children: [
                Container(
                  decoration: UIHelper.cardDecoration(),
                  child: Padding(
                    padding: UIHelper.allPaddingOf10(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: MediaQuery.of(context).size.width,
                          height: 240,
                          child: GridView.builder(
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 3,
                              crossAxisSpacing: 8.0,
                              mainAxisSpacing: 8.0,
                              childAspectRatio: 1.0,
                            ),
                            itemCount: doc_attachmentsTodisplay.length,
                            itemBuilder: (context, index) {
                              final header = doc_attachmentsTodisplay[index];
                              String? fileName = header.file_name;
                              String? fileExtension =
                                  fileName?.split('.').last.toLowerCase();

                              if (fileName == null || fileExtension == null) {
                                return const Placeholder(); // Handle null cases
                              }
                              String cleanedString = documentHeaders[index]
                                  .thumbnail!
                                  .replaceAll(RegExp(r'\s+'), '');
                              Uint8List? decodedBytes;
                              try {
                                decodedBytes = base64Decode(
                                    cleanedString); // Replace with your base64 logic
                              } catch (e) {
                                Logger.logInfo("AttachmentTabAsset",
                                    'getAttachments', e.toString());
                              }

                              return InkWell(
                                  onTap: () async {
                                    if (['jpg', 'jpeg', 'png', 'gif']
                                        .contains(fileExtension)) {
                                      _openImage(documentHeaders[index]);
                                    } else if (['mp4', 'avi', 'mov', 'mkv']
                                        .contains(fileExtension)) {
                                      _openVideo(File(fileName));
                                    } else {
                                      _openFile(documentHeaders[index]);
                                    }
                                  },
                                  child: Card(
                                    elevation: 5,
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(10)),
                                    child: Container(
                                      decoration: BoxDecoration(
                                          image: getFileDisplayWidget(
                                              fileExtension, header)),
                                      child: Stack(
                                        children: [
                                          Center(
                                            child: Container(
                                              decoration:
                                                  UIHelper.cardDecoration(
                                                      cardColor:
                                                          Colors.transparent),
                                              child: Container(),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ) // Default placeholder
                                  );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          )
        : Container();
  }

  DecorationImage getFileDisplayWidget(
      String fileExtension, DOCUMENT_ATTACHMENT attachment) {
    File file = File(attachment.local_path!);

    if (['jpg', 'jpeg', 'png', 'gif'].contains(fileExtension)) {
      return DecorationImage(
        image: FileImage(
          file,
        ),
        fit: BoxFit.cover,
      );
    }

    if (['mp4', 'avi', 'mov', 'mkv'].contains(fileExtension)) {
      return const DecorationImage(
        image: AssetImage(
          'assets/icon/task_icons/video.png',
        ),
      );
    }

    return const DecorationImage(
        image: AssetImage(
      'assets/icon/task_icons/pdff.png',
    ));
  }

  void _openFile(DOCUMENT_HEADER header) async {
    try {
      DOCUMENT_ATTACHMENT? docAttachment;
      List<DOCUMENT_ATTACHMENT> attachments =
          await DbHelper.getDocumentAttachments();
      for (var data in attachments) {
        if (data.fid == header.lid) {
          docAttachment = data;
          break;
        }
      }

      if (docAttachment == null || docAttachment.local_path == null) {
        Logger.logInfo(
            "AttachmentTabAsset", 'openFile', 'No valid attachment found.');
        return;
      }

      String file = docAttachment.local_path!;
      if (file.isNotEmpty) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PdfViewerScreen(filePath: file),
          ),
        );
      }
    } catch (e) {
      Logger.logInfo(
          "AttachmentTabAsset", 'openFile', 'Error: ${e.toString()}');
    }
  }

  void _openVideo(File file) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoPlayerScreen(file: file),
      ),
    );
  }

  void _openImage(DOCUMENT_HEADER header) async {
    DOCUMENT_ATTACHMENT? docAttachment;
    List<DOCUMENT_ATTACHMENT> attachments =
        await DbHelper.getDocumentAttachments();
    for (var data in attachments) {
      if (data.fid == header.lid) {
        docAttachment = data;
      }
    }
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ImageViewerScreen(attachment: docAttachment!),
      ),
    );
  }

  getFiles() async {
    ref.watch(documentHeaderProvider.notifier).fetchDocumentHeaders();
    ref.watch(documentAttachmentProvider.notifier).fetchDocumentAttachments();
    documentHeaders = await ref.watch(documentHeaderProvider);
    doc_attachments = await ref.watch(documentAttachmentProvider);
    List documents = [];
    if (widget.assetHeader is ASSET_HEADER) {
      documents = await DbHelper.getAssetDocument(widget.assetHeader);
    } else if (widget.assetHeader is LOCATION_HEADER) {
      documents = await DbHelper.getFlocDocument(widget.assetHeader);
    }
    documentHeadersDisplay.clear();
    documentHeaders.forEach((element) {
      documents.forEach((element1) {
        if (element.doc_id == element1.doc_id) {
          documentHeadersDisplay.add(element);
        }
      });
    });

    doc_attachments.forEach((element) {
      documents.forEach((element1) {
        if (element.fid == element1.doc_id) {
          doc_attachmentsTodisplay.add(element);
        }
      });
    });
    if (doc_attachmentsTodisplay.isNotEmpty) {
      setState(() {});
    }
  }
}
