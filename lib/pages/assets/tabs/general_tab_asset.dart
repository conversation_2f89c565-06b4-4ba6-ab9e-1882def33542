import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/be/ASSET_CATEGORY_HEADER.dart';
import 'package:rounds/be/ASSET_HEADER.dart';
import 'package:rounds/be/KPI_HEADER.dart';
import 'package:rounds/providers/fault/fault_header_provider.dart';
import 'package:rounds/utils/utils.dart';

import '../../../be/ABCINDICATOR_HEADER.dart';
import '../../../be/LOCATION_CATEGORY_HEADER.dart';
import '../../../helpers/ui_helper.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../../utils/app_colors.dart';

class GeneralTab extends ConsumerStatefulWidget {
  dynamic assetHeader;

  GeneralTab({required this.assetHeader, super.key});

  @override
  _GeneralTabState createState() => _GeneralTabState();
}

class _GeneralTabState extends ConsumerState<GeneralTab> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 18),
            widget.assetHeader is ASSET_HEADER
                ? _buildAssetBody()
                : widget.assetHeader is KPI_HEADER
                    ? _buildKpiBody()
                    : _buildLocationBody(),
          ],
        ),
      ),
    );
  }

  Widget _buildAssetBody() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10.0),
        child: Container(
          decoration: UIHelper.cardDecoration(),
          width: MediaQuery.of(context).size.width,
          child: Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
            margin: EdgeInsets.zero,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ScreenType.desktop == UIHelper().getScreenType(context)
                      ? _buildAssetGeneralDataWeb()
                      : _buildAssetGeneralData(),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.0),
                    child: Divider(
                      color: Colors.grey,
                    ),
                  ),
                  ScreenType.desktop == UIHelper().getScreenType(context)
                      ? _buildAssetLocationDataWeb()
                      : _buildAssetLocationData(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAssetGeneralData() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader("General Data"),
        _buildLabelAndValue("Category", widget.assetHeader.category),
        _buildLabelAndValue("Asset Number", widget.assetHeader.asset_no),
        _buildLabelAndValue("Description", widget.assetHeader.description),
        _buildLabelAndValue(
            "Functional Location", widget.assetHeader.parent_loc_id),
        _buildLabelAndValue(
            "Superior Asset", widget.assetHeader.parent_asset_no),
        _buildLabelAndValue(
            "Tech Identification", widget.assetHeader.technical_id),
        _buildLabelAndValue("ABC Indicator",
            '${widget.assetHeader.abc_indicator} - ${getABCIndicator(widget.assetHeader.abc_indicator)}'),
        _buildLabelAndValue("Manufacturer", widget.assetHeader.manufacturer),
      ],
    );
  }

  Widget _buildAssetGeneralDataWeb() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader("General Data"),
        Row(
          children: [
            Expanded(
                child: _buildLabelAndValue("Category",
                    '${widget.assetHeader.category} - ${getAssetCategoryName(widget.assetHeader.category)}')),
            Expanded(
                child: _buildLabelAndValue(
                    "Asset Number", widget.assetHeader.asset_no)),
          ],
        ),
        Row(
          children: [
            Expanded(
              child: _buildLabelAndValue(
                  "Superior Asset", widget.assetHeader.parent_asset_no),
            ),
            Expanded(
              child: _buildLabelAndValue(
                  "Manufacturer", widget.assetHeader.manufacturer),
            ),
          ],
        ),
        Row(
          children: [
            Expanded(
              child: _buildLabelAndValue(
                  "Tech Identification", widget.assetHeader.technical_id),
            ),
            Expanded(
              child: _buildLabelAndValue(
                  "ABC Indicator",
                  widget.assetHeader.abc_indicator != null
                      ? '${widget.assetHeader.abc_indicator} - ${getABCIndicator(widget.assetHeader.abc_indicator)}'
                      : 'N/A'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAssetLocationData() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader("Location"),
        _buildLabelAndValue("Plant", widget.assetHeader.plant_id),
        _buildLabelAndValue("Plant Section", widget.assetHeader.plant_sec_id),
        _buildLabelAndValue("Latitude", widget.assetHeader.latitude),
        _buildLabelAndValue("Longitude ", widget.assetHeader.longitude),
      ],
    );
  }

  Widget _buildAssetLocationDataWeb() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader("Location"),
        Row(
          children: [
            Expanded(
                child:
                    _buildLabelAndValue("Plant", widget.assetHeader.plant_id)),
            Expanded(
                child: Row(
              children: [
                _buildLabelAndValue(
                    "Plant Section", widget.assetHeader.plant_sec_id),
              ],
            )),
          ],
        ),
        Row(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Geo-Location",
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.titleTextColor,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.1,
                    ),
                  ),
                  const SizedBox(height: 4),
                  InkWell(
                      onTap: () {},
                      child: Image.asset(
                        "assets/icons/route.png",
                        height: 40,
                        width: 40,
                      )),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLabelAndValue(String label, dynamic value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.titleTextColor,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.1,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value != null
                ? value.toString()
                : AppLocalizations.of(context)!.not_applicable,
            style: UIHelper.valueStyle14(),
          ),
        ],
      ),
    );
  }

  Widget _buildKpiBody() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10.0),
        child: Container(
          decoration: UIHelper.cardDecoration(),
          width: MediaQuery.of(context).size.width,
          child: Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
            margin: EdgeInsets.zero,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ScreenType.desktop == UIHelper().getScreenType(context)
                      ? _buildKpiGeneralDataWeb()
                      : _buildKpiGeneralData(),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.0),
                    child: Divider(
                      color: Colors.grey,
                    ),
                  ),
                  ScreenType.desktop == UIHelper().getScreenType(context)
                      ? _buildKpiLocationDataWeb()
                      : _buildKpiLocationData(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildKpiGeneralData() {
    String type = '';
    switch (widget.assetHeader.kpi_type) {
      case "1":
        type = "Qualitative";
        break;
      case "2":
        type = "Quantitative";
        break;
      case "3":
        type = "Up Counter";
        break;
      case "4":
        type = "Down Counter";
        break;
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader("General Data"),
        _buildLabelAndValue("KPI Type", type),
        _buildLabelAndValue("Number", widget.assetHeader.kpi_id),
        _buildLabelAndValue("Position", widget.assetHeader.position),
        _buildLabelAndValue("Description", widget.assetHeader.description),
        _buildLabelAndValue("Asset/Location",
            widget.assetHeader.asset_no ?? widget.assetHeader.location_id),
      ],
    );
  }

  Widget _buildKpiGeneralDataWeb() {
    String type = '';
    switch (widget.assetHeader.kpi_type) {
      case "1":
        type = "Qualitative";
        break;
      case "2":
        type = "Quantitative";
        break;
      case "3":
        type = "Up Counter";
        break;
      case "4":
        type = "Down Counter";
        break;
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader("General Data"),
        Row(
          children: [
            Expanded(child: _buildLabelAndValue("KPI Type", type)),
            Expanded(
                child: _buildLabelAndValue(
                    "Asset/Location",
                    widget.assetHeader.asset_no ??
                        widget.assetHeader.location_id)),
          ],
        ),
/*        Row(
          children: [
            Expanded(
                child:
                    _buildLabelAndValue("Number", widget.assetHeader.kpi_id)),
            Expanded(
                child: _buildLabelAndValue(
                    "Position", widget.assetHeader.position)),
          ],
        ),*/
      ],
    );
  }

  Widget _buildKpiLocationData() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader("Location"),
        _buildLabelAndValue("Plant", widget.assetHeader.plant_id),
        _buildLabelAndValue("Plant Section", widget.assetHeader.external_id),
      ],
    );
  }

  Widget _buildKpiLocationDataWeb() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader("Location"),
        Row(
          children: [
            Expanded(
                child:
                    _buildLabelAndValue("Plant", widget.assetHeader.plant_id)),
            Expanded(
              child: Row(
                children: [
                  _buildLabelAndValue(
                      "Plant Section", widget.assetHeader.external_id),
                ],
              ),
            ),
          ],
        ),
        Row(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Geo-Location",
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.titleTextColor,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.1,
                    ),
                  ),
                  const SizedBox(height: 4),
                  InkWell(
                      onTap: () {},
                      child: Image.asset(
                        "assets/icons/route.png",
                        height: 40,
                        width: 40,
                      )),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildHeader(String header) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        header,
        style: UIHelper.headerStyle(),
      ),
    );
  }

  _buildLocationBody() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10.0),
        child: Container(
          decoration: UIHelper.cardDecoration(),
          width: MediaQuery.of(context).size.width,
          child: Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
            margin: EdgeInsets.zero,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ScreenType.desktop == UIHelper().getScreenType(context)
                      ? _buildFlocGeneralDataWeb()
                      : _buildFlocGeneralData(),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.0),
                    child: Divider(
                      color: Colors.grey,
                    ),
                  ),
                  ScreenType.desktop == UIHelper().getScreenType(context)
                      ? _buildFlocLocationDataWeb()
                      : _buildFlocLocationData(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  _buildFlocGeneralData() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader("General Data"),
        _buildLabelAndValue("Category", widget.assetHeader.category),
        _buildLabelAndValue("Location Name", widget.assetHeader.location_id),
        _buildLabelAndValue("Description", widget.assetHeader.description),
        _buildLabelAndValue(
            "Parent Location", widget.assetHeader.parent_loc_id),
        _buildLabelAndValue(
            "Tech Identification", widget.assetHeader.technical_id),
        _buildLabelAndValue(
            "ABC Indicator",
            widget.assetHeader.abc_indicator != null
                ? '${widget.assetHeader.abc_indicator} - ${getABCIndicator(widget.assetHeader.abc_indicator)}'
                : 'N/A'),
        _buildLabelAndValue("Manufacturer", widget.assetHeader.manufacturer),
      ],
    );
  }

  _buildFlocGeneralDataWeb() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader("General Data"),
        Row(
          children: [
            Expanded(
                child: _buildLabelAndValue("Category",
                    '${widget.assetHeader.category} - ${getLocationName(widget.assetHeader.category)}')),
            Expanded(
              child: _buildLabelAndValue(
                  "Parent Location", widget.assetHeader.parent_loc_id),
            ),
          ],
        ),
        Row(
          children: [
            Expanded(
              child: _buildLabelAndValue(
                  "Tech Identification", widget.assetHeader.technical_id),
            ),
            Expanded(
              child: _buildLabelAndValue(
                  "ABC Indicator",
                  widget.assetHeader.abc_indicator != null
                      ? '${widget.assetHeader.abc_indicator} - ${getABCIndicator(widget.assetHeader.abc_indicator)}'
                      : 'N/A'),
            ),
          ],
        ),
        _buildLabelAndValue("Manufacturer", widget.assetHeader.manufacturer),
      ],
    );
  }

  _buildFlocLocationData() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader("Location"),
        _buildLabelAndValue("Plant", widget.assetHeader.plant_id),
        _buildLabelAndValue("Plant Section", widget.assetHeader.plant_sec_id),
        _buildLabelAndValue("Latitude", widget.assetHeader.latitude),
        _buildLabelAndValue("Longitude ", widget.assetHeader.longitude),
      ],
    );
  }

  _buildFlocLocationDataWeb() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader("Location"),
        Row(
          children: [
            Expanded(
                child:
                    _buildLabelAndValue("Plant", widget.assetHeader.plant_id)),
            Expanded(
                child: Row(
              children: [
                _buildLabelAndValue(
                    "Plant Section", widget.assetHeader.plant_sec_id),
              ],
            )),
          ],
        ),
        Row(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Geo-Location",
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.titleTextColor,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.1,
                    ),
                  ),
                  const SizedBox(height: 4),
                  InkWell(
                      onTap: () {},
                      child: Image.asset(
                        "assets/icons/route.png",
                        height: 40,
                        width: 40,
                      )),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  String getAssetCategoryName(String category) {
    final assetCategoryList = ref.read(assetCategoryListProvider);
    String asset = "";
    ASSET_CATEGORY_HEADER matchedCategory = assetCategoryList.firstWhere(
        (item) => item.category_code == category,
        orElse: () => ASSET_CATEGORY_HEADER(category_code: ''));
    asset = matchedCategory.description ?? "";
    return asset;
  }

  String getLocationName(String category) {
    final locCategoryList = ref.read(locationCategoryListProvider);
    String loc = "";
    LOCATION_CATEGORY_HEADER matchedCategory = locCategoryList.firstWhere(
      (item) => item.category_code == category,
      orElse: () => LOCATION_CATEGORY_HEADER(category_code: ''),
    );
    loc = matchedCategory.description ?? "";
    return loc;
  }

  String getABCIndicator(String abcIndicator) {
    final assetCategoryList = ref.read(abcCategoryListProvider);
    String abcIndicatorData = "";
    ABCINDICATOR_HEADER matchedCategory = assetCategoryList.firstWhere(
        (item) => item.abc_indicator == abcIndicator,
        orElse: () => ABCINDICATOR_HEADER(abc_indicator: ''));
    abcIndicatorData = matchedCategory.description ?? "";
    return abcIndicatorData;
  }

/*  getLocationName(String category) {
    final locCategoryList = ref.read(locationCategoryListProvider);
    return locCategoryList
        .firstWhere((item) => item.category_code == category)
        .description;
  }*/
}
