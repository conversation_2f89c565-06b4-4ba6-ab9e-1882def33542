import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/models/intractive_Item_Model.dart';
import 'package:rounds/providers/assets/floc_provider.dart';
import 'package:rounds/providers/assets/kpi_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/utils.dart';

import '../../providers/assets/asset_provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'widget/asset_card.dart';

class AssetListPage extends ConsumerStatefulWidget {
  final int selectedIndex;
  final Function(InteractiveItemModel) onItemTap;
  final ScrollController scrollController;

  const AssetListPage(
      {super.key, required this.selectedIndex,  required this.scrollController, required this.onItemTap});

  @override
  ConsumerState<AssetListPage> createState() => _AssetListPageState();
}

class _AssetListPageState extends ConsumerState<AssetListPage> {
  late List<dynamic> filteredAssetList;
  late ValueNotifier<InteractiveItemModel?> assetDetailViewNotifier;

  @override
  void initState() {
    super.initState();
    assetDetailViewNotifier = ValueNotifier<InteractiveItemModel?>(null);
    filteredAssetList = [];
  }

  @override
  Widget build(BuildContext context) {
    final assetHeaderState = widget.selectedIndex == 0
        ? ref.watch(flocHeaderProvider)
        : widget.selectedIndex == 1
            ? ref.watch(assetHeaderProvider)
            : ref.watch(kpiHeaderProvider);
    filteredAssetList = assetHeaderState;

    return assetHeaderState.isEmpty
        ? Center(child: Text(AppLocalizations.of(context)!.no_tech_objects_found))
        : SlidableAutoCloseBehavior(
            child: ListView.builder(
              controller: widget.scrollController,
              itemCount: filteredAssetList.isNotEmpty
                  ? filteredAssetList.length
                  : assetHeaderState.length,
              itemBuilder: (context, i) {
                final asset = filteredAssetList.isNotEmpty
                    ? filteredAssetList[i]
                    : assetHeaderState[i];
                return Padding(
                  padding: const EdgeInsets.only(top: 5, left: 5, right: 5),
                  child: Row(
                    children: [
                      ValueListenableBuilder(
                        valueListenable: assetDetailViewNotifier,
                        builder: (context, value, child) {
                          return Expanded(
                            child: AssetCard(
                              color: (UIHelper().getScreenType(context) ==
                                          ScreenType.desktop &&
                                      value?.getValue("index") == i &&
                                      widget.selectedIndex ==
                                          value?.getValue("assetTypeIndex"))
                                  ? AppColors.primaryColor
                                      .withOpacity(0.2)
                                      .withRed(10)
                                  : Colors.white,
                              itemIndex: i,
                              assetTypeIndex: widget.selectedIndex,
                              asset: asset,
                              onTap: (value) {
                                assetDetailViewNotifier.value = value;

                                widget.onItemTap(value);
                              },
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                );
              },
            ),
          );
  }
}
