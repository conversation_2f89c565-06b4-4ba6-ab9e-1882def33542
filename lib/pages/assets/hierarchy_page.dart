import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/be/ASSET_HEADER.dart';
import 'package:rounds/be/KPI_HEADER.dart';
import 'package:rounds/be/LOCATION_HEADER.dart';
import 'package:rounds/models/intractive_Item_Model.dart';
import 'package:rounds/pages/assets/detail_screens/asset_detail_screen.dart';
import 'package:rounds/providers/assets/asset_provider.dart';
import 'package:rounds/providers/assets/kpi_provider.dart';

import '../../helpers/ui_helper.dart';
import '../../providers/assets/floc_provider.dart';
import '../../utils/app_colors.dart';
import '../../utils/utils.dart';

class HierarchyPage extends ConsumerStatefulWidget {
  const HierarchyPage({super.key, required this.onItemTap});
  final Function(InteractiveItemModel) onItemTap;

  @override
  ConsumerState createState() => _HierarchyPageState();
}

class _HierarchyPageState extends ConsumerState<HierarchyPage> {
  List<LOCATION_HEADER>? flocsList = [];
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future.delayed(Duration.zero).then((value) {
      if (UIHelper().getScreenType(context) == ScreenType.desktop) {
        widget.onItemTap(InteractiveItemModel(type: "", data: {}));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final flocHeaderState = ref.watch(flocHeaderProvider);
    final assetHeaderState = ref.watch(assetHeaderProvider);
    final kpiHeaderState = ref.watch(kpiHeaderProvider);
    flocsList = flocHeaderState
            .where((floc) =>
                floc.parent_loc_id == '' || floc.parent_loc_id == null)
            .toList() ??
        [];

    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: ListView.builder(
          itemCount: flocsList!.length,
          itemBuilder: (context, index) {
            LOCATION_HEADER location_header = flocsList![index];
            List<KPI_HEADER> kpiLIst = kpiHeaderState
                .where((kpi) => kpi.location_id == location_header.location_id)
                .toList();
            List<ASSET_HEADER> assetList = assetHeaderState
                .where((asset) =>
                    asset.parent_loc_id == location_header.location_id)
                .toList();
            List<LOCATION_HEADER> flocList = flocHeaderState
                .where((asset) =>
                    asset.parent_loc_id == location_header.location_id)
                .toList();
            return Container(
              color: AppColors.white,
              child: InkWell(
                onDoubleTap: () {
                  navigatetoDetailsScreen(location_header, context);
                },
                child: ExpansionTile(
                  backgroundColor: Colors.white,
                  onExpansionChanged: (value) {
                    widget.onItemTap(InteractiveItemModel(
                        type: "LOCATION_HEADER ",
                        data: {"assetHeader": location_header}));
                  },
                  trailing:
                      kpiLIst.isEmpty && assetList.isEmpty && flocList.isEmpty
                          ? SizedBox.shrink()
                          : null,
                  title: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      color: AppColors.white,
                      child: Row(
                        children: [
                          Image.asset('assets/icons/Floc.png',
                              height: 15, width: 15),
                          const SizedBox(width: 10),
                          Expanded(
                              child: Text(
                            '${location_header.location_id!} (${location_header.description})',
                            overflow: TextOverflow.ellipsis,
                          )), // Prevent overflow
                        ],
                      ),
                    ),
                  ),
                  children: [
                    ...kpiLIst
                        .map((kpi) => KPITile(
                              kpi: kpi,
                              onTap: widget.onItemTap,
                            ))
                        .toList(),
                    ...assetList
                        .map((asset) => AssetTile(
                              asset: asset,
                              onTap: widget.onItemTap,
                            ))
                        .toList(),
                    ...flocList
                        .map((loc) => LocationTile(
                              location: loc,
                              onTap: widget.onItemTap,
                            ))
                        .toList(),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class LocationTile extends ConsumerStatefulWidget {
  final LOCATION_HEADER location;
  const LocationTile({super.key, required this.location, required this.onTap});
  final Function(InteractiveItemModel) onTap;

  @override
  ConsumerState createState() => _LocationTileState();
}

class _LocationTileState extends ConsumerState<LocationTile> {
  @override
  Widget build(BuildContext context) {
    final flocHeaderState = ref.watch(flocHeaderProvider);
    final assetHeaderState = ref.watch(assetHeaderProvider);
    final kpiHeaderState = ref.watch(kpiHeaderProvider);
    List<KPI_HEADER> kpiLIst = kpiHeaderState
        .where((kpi) => kpi.location_id == widget.location.location_id)
        .toList();
    List<ASSET_HEADER> assetList = assetHeaderState
        .where((asset) =>
            asset.parent_loc_id == widget.location.location_id &&
            (asset.parent_asset_no == 0 || asset.parent_asset_no == null))
        .toList();
    List<LOCATION_HEADER> flocList = flocHeaderState
        .where((asset) => asset.parent_loc_id == widget.location.location_id)
        .toList();

    return Padding(
      padding: const EdgeInsets.only(left: 20.0),
      child: InkWell(
        onDoubleTap: () {
          widget.onTap(InteractiveItemModel(
              type: "LOCATION_HEADER", data: {"assetHeader": widget.location}));
          navigatetoDetailsScreen(widget.location, context);
        },
        child: ExpansionTile(
          onExpansionChanged: (value) {
            widget.onTap(InteractiveItemModel(
                type: "LOCATION_HEADER",
                data: {"assetHeader": widget.location}));
          },
          trailing: kpiLIst.isEmpty && assetList.isEmpty && flocList.isEmpty
              ? SizedBox.shrink()
              : null,
          title: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              width: MediaQuery.of(context).size.width,
              child: Row(
                children: [
                  Image.asset('assets/icons/Floc.png', height: 15, width: 15),
                  const SizedBox(width: 10),
                  Expanded(
                      child: Text(
                    '${widget.location.location_id!} (${widget.location.description!})',
                    overflow: TextOverflow.ellipsis,
                  )), // Prevent overflow
                ],
              ),
            ),
          ),
          children: [
            ...kpiLIst
                .map((kpi) => KPITile(
                      kpi: kpi,
                      onTap: widget.onTap,
                    ))
                .toList(),
            ...assetList
                .map((asset) => AssetTile(
                      asset: asset,
                      onTap: widget.onTap,
                    ))
                .toList(),
            ...flocList
                .map((loc) => LocationTile(
                      location: loc,
                      onTap: widget.onTap,
                    ))
                .toList(),
          ],
        ),
      ),
    );
  }
}

class AssetTile extends ConsumerStatefulWidget {
  final ASSET_HEADER asset;
  const AssetTile({super.key, required this.asset, required this.onTap});
  final Function(InteractiveItemModel) onTap;

  @override
  ConsumerState createState() => _AssetTileState();
}

class _AssetTileState extends ConsumerState<AssetTile> {
  @override
  Widget build(BuildContext context) {
    final assetProviderState = ref.watch(assetHeaderProvider);
    final kpiProviderState = ref.watch(kpiHeaderProvider);
    List<KPI_HEADER> kpiLIst = kpiProviderState
        .where((kpi) => kpi.asset_no == widget.asset.asset_no)
        .toList();
    List<ASSET_HEADER> assetList = assetProviderState
        .where((asset) => asset.parent_asset_no == widget.asset.asset_no)
        .toList();
    return Padding(
      padding: const EdgeInsets.only(left: 20.0),
      child: InkWell(
        onDoubleTap: () {
          widget.onTap(InteractiveItemModel(
              type: "KPI_HEADER", data: {"assetHeader": widget.asset}));
          navigatetoDetailsScreen(widget.asset, context);
        },
        child: ExpansionTile(
            onExpansionChanged: (value) {
              widget.onTap(InteractiveItemModel(
                  type: "KPI_HEADER", data: {"assetHeader": widget.asset}));
            },
            trailing:
                kpiLIst.isEmpty && assetList.isEmpty ? SizedBox.shrink() : null,
            title: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Row(
                  children: [
                    Image.asset(
                      'assets/icons/asset.png',
                      height: 15,
                      width: 15,
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Expanded(
                        child: Text(
                      '${widget.asset.asset_no!} (${widget.asset.description!})',
                      overflow: TextOverflow.ellipsis,
                    )),
                  ],
                ),
              ),
            ),
            children: [
              ...kpiLIst
                  .map((kpi) => KPITile(
                        kpi: kpi,
                        onTap: widget.onTap,
                      ))
                  .toList(),
              ...assetList
                  .map((asset) => AssetTile(
                        asset: asset,
                        onTap: widget.onTap,
                      ))
                  .toList(),
            ]),
      ),
    );
  }
}

class KPITile extends StatelessWidget {
  final KPI_HEADER kpi;
  final Function(InteractiveItemModel) onTap;
  const KPITile({Key? key, required this.kpi, required this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 37.0),
      child: InkWell(
        onDoubleTap: () {
          onTap(InteractiveItemModel(
              type: "KPI_HEADER", data: {"assetHeader": kpi}));
          navigatetoDetailsScreen(kpi, context);
        },
        onTap: () {
          onTap(InteractiveItemModel(
              type: "KPI_HEADER", data: {"assetHeader": kpi}));
        },
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Container(
            height: 50,
            width: MediaQuery.of(context).size.width,
            child: Row(
              children: [
                Image.asset('assets/icons/kpi.png', height: 15, width: 15),
                const SizedBox(width: 10),
                Expanded(
                  // Ensure text doesn't overflow
                  child: Text(
                    "${kpi.kpi_id!.toString()}(${kpi.description!})",
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

navigatetoDetailsScreen(dynamic location, BuildContext context) {
  SchedulerBinding.instance.scheduleFrameCallback((timeStamp) {
    Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => AssetDetailScreen(assetHeader: location)));
  });
}
