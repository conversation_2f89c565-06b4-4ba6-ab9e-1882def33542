import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/be/ASSET_HEADER.dart';
import 'package:rounds/be/KPI_HEADER.dart';
import 'package:rounds/be/LOCATION_HEADER.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/pages/assets/tabs/activity_tab_asset.dart';
import 'package:rounds/pages/assets/tabs/attachment_tab_asset.dart';
import 'package:rounds/pages/assets/tabs/general_tab_asset.dart';
import 'package:rounds/utils/utils.dart';
import '../../../be/FAULT_HEADER.dart';
import '../../../models/menu_action_item.dart';

import '../../../providers/fault/fault_header_provider.dart';
import '../../../providers/fault/fault_type_provider.dart';
import '../../../providers/user_provider.dart';
import '../../../utils/app_colors.dart';
import '../../../utils/app_constants.dart';
import '../../fault/fault_detail_screen.dart';
import '../../fault/tabs/edit_fault_field_provider.dart';
import '../../widgets/menu_action_button.dart';
import 'package:intl/intl.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AssetDetailScreen extends ConsumerStatefulWidget {
  final dynamic assetHeader;

  const AssetDetailScreen({
    super.key,
    required this.assetHeader,
  });

  @override
  ConsumerState<AssetDetailScreen> createState() => _AssetDetailScreenState();
}

class _AssetDetailScreenState extends ConsumerState<AssetDetailScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final role = ref.watch(roleProvider);
    late String title = '';
    if (widget.assetHeader is ASSET_HEADER) {
      title = widget.assetHeader.asset_no.toString();
    } else if (widget.assetHeader is KPI_HEADER) {
      title = widget.assetHeader.kpi_id.toString();
    } else if (widget.assetHeader is LOCATION_HEADER) {
      title = widget.assetHeader.location_id.toString();
    }
    return DefaultTabController(
      length: (widget.assetHeader is KPI_HEADER) ? 2 : 3,
      initialIndex: 0,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          elevation: 0,
          backgroundColor: AppColors.white,
          leadingWidth:
              UIHelper().getScreenType(context) != ScreenType.desktop ? 40 : 0,
          leading: UIHelper().getScreenType(context) != ScreenType.desktop
              ? IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: Icon(
                    Icons.arrow_back_ios,
                    color: AppColors.titleTextColor,
                    size: 20,
                  ))
              : SizedBox(),
          title: Text(
            title,
            overflow: TextOverflow.ellipsis,
            style: UIHelper.titleStyle16(),
          ),
          actions: [
            role != null
                ? UIHelper.isCreate(role.fault!)
                    ? InkWell(
                        onTap: () {
                          clearFaultStates();
                          navigateToFaultScreen();
                        },
                        child: Icon(
                          Icons.error_outline,
                          color: AppColors.titleTextColor,
                          size: 20,
                        ),
                      )
                    : const SizedBox()
                : const SizedBox(),
            role != null
                ? UIHelper.isCreate(role.fault!)
                    ? const SizedBox(width: 10)
                    : SizedBox()
                : SizedBox(),
            /*   Padding(
              padding: const EdgeInsets.all(8.0),
              child: MenuActionButton(
                  options: widget.assetHeader is KPI_HEADER
                      ? [
                          MenuActionItem(
                            value: 'refresh',
                            name: AppLocalizations.of(context)!.refreshString,
                          ),
                          MenuActionItem(
                            value: 'document',
                            name: AppLocalizations.of(context)!.getDocument,
                          ),
                        ]
                      : [
                          MenuActionItem(
                            value: 'refresh',
                            name: AppLocalizations.of(context)!.refreshString,
                          ),
                          MenuActionItem(
                            value: 'attachments',
                            name: AppLocalizations.of(context)!.getAttachment,
                          ),
                          MenuActionItem(
                            value: 'history',
                            name: AppLocalizations.of(context)!.getHistory,
                          ),
                        ],
                  onOptionItemSelected: (item) async {
                    switch (item.value) {
                      case 'refresh':

                        break;
                      case 'history':
                        break;
                      case 'attachments':
                        break;
                    }
                  }),
            ),*/
          ],
        ),
        body: SafeArea(
          child: Padding(
            padding: UIHelper().getScreenType(context) != ScreenType.desktop
                ? UIHelper.columnFieldOnlhorizontalPadding6()
                : EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: UIHelper.columnFieldOnlhorizontalPadding6(),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.assetHeader.description.toString(),
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.titleTextColor,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.1,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),
                TabBar(
                  tabs: (widget.assetHeader is KPI_HEADER)
                      ? <Widget>[
                          Tab(
                            icon: FittedBox(
                              child: Text(AppLocalizations.of(context)!.general,
                                  style: const TextStyle(
                                      color: AppColors.blackTitleText)),
                            ),
                          ),
                          /*       Tab(
                            icon: FittedBox(
                              child: Text(
                                (widget.assetHeader is KPI_HEADER)
                                    ? AppLocalizations.of(context)!
                                        .measuring_document
                                    : AppLocalizations.of(context)!.activity,
                                style: const TextStyle(
                                    color: AppColors.blackTitleText),
                              ),
                            ),
                          ),*/
                        ]
                      : <Widget>[
                          Tab(
                            icon: FittedBox(
                              child: Text(
                                AppLocalizations.of(context)!.general,
                                style: const TextStyle(
                                    color: AppColors.blackTitleText),
                              ),
                            ),
                          ),
                          Tab(
                            icon: FittedBox(
                              child: Text(
                                AppLocalizations.of(context)!.attachments,
                                style: const TextStyle(
                                    color: AppColors.blackTitleText),
                              ),
                            ),
                          ),
                          /*        Tab(
                            icon: FittedBox(
                              child: Text(
                                (widget.assetHeader is KPI_HEADER)
                                    ? AppLocalizations.of(context)!
                                        .measuring_document
                                    : AppLocalizations.of(context)!.history,
                                style: const TextStyle(
                                    color: AppColors.blackTitleText),
                              ),
                            ),
                          ),*/
                        ],
                ),
                Expanded(
                  child: TabBarView(
                    children: widget.assetHeader is KPI_HEADER
                        ? <Widget>[
                            GeneralTab(
                              assetHeader: widget.assetHeader,
                            ),
                            // ActivityTab(),
                          ]
                        : <Widget>[
                            GeneralTab(
                              assetHeader: widget.assetHeader,
                            ),
                            AttachmentTab(
                              assetHeader: widget.assetHeader,
                            ),
                            // ActivityTab(),
                          ],
                  ),
                ),
                // Expanded(child: _buildBody()),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    final role = ref.watch(roleProvider);
    late String title = '';
    if (widget.assetHeader is ASSET_HEADER) {
      title = widget.assetHeader.asset_no.toString();
    } else if (widget.assetHeader is KPI_HEADER) {
      title = widget.assetHeader.kpi_id.toString();
    } else if (widget.assetHeader is LOCATION_HEADER) {
      title = widget.assetHeader.location_id.toString();
    }
    return SizedBox(
      height: kToolbarHeight,
      child: Padding(
        padding: UIHelper().getScreenType(context) == ScreenType.desktop
            ? const EdgeInsets.only(right: 10.0)
            : const EdgeInsets.all(10.0),
        child: Row(
          children: [
            Visibility(
              visible: UIHelper().getScreenType(context) != ScreenType.desktop,
              child: IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: const Icon(Icons.arrow_back_ios, color: Colors.black)),
            ),
            Text(
              title,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const Spacer(),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                role != null
                    ? UIHelper.isCreate(role.fault!)
                        ? InkWell(
                            onTap: () {
                              clearFaultStates();
                              navigateToFaultScreen();
                            },
                            child: Icon(
                              Icons.error_outline,
                              color: AppColors.titleTextColor,
                              size: 20,
                            ),
                          )
                        : SizedBox()
                    : SizedBox(),
                role != null
                    ? UIHelper.isCreate(role.fault!)
                        ? const SizedBox(width: 18)
                        : SizedBox()
                    : SizedBox(),
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: MenuActionButton(
                      options: widget.assetHeader is KPI_HEADER
                          ? [
                              MenuActionItem(
                                value: 'refresh',
                                name:
                                    AppLocalizations.of(context)!.refreshString,
                              ),
                              MenuActionItem(
                                value: 'document',
                                name: AppLocalizations.of(context)!.getDocument,
                              ),
                            ]
                          : [
                              MenuActionItem(
                                value: 'refresh',
                                name:
                                    AppLocalizations.of(context)!.refreshString,
                              ),
                              MenuActionItem(
                                value: 'attachments',
                                name:
                                    AppLocalizations.of(context)!.getAttachment,
                              ),
                              MenuActionItem(
                                value: 'history',
                                name: AppLocalizations.of(context)!.getHistory,
                              ),
                            ],
                      onOptionItemSelected: (item) async {
                        switch (item.value) {
                          case 'refresh':
                            // Refresh code here
                            break;
                          case 'history':
                            // Get History PA call
                            break;
                          case 'attachments':
                            // Get Attachments PA call
                            break;
                        }
                      }),
                )
                // InkWell(onTap:onTapRefresh ,child:  EamIcon(iconName: EamIcon.refresh,width: 24,height: 24).icon(),)
              ],
            ),
          ],
        ),
      ),
    );
  }

  onTapRefresh() async {
    /// code for the refreshing the information
  }
  Future<void> navigateToFaultScreen() async {
    final faultTypeHeader = ref.read(faultTypeListProvider.notifier);
    final faultInsertHeader = ref.read(insertFaultHeaderProvider.notifier);
    final faultHeader = ref.read(faultHeaderProvider.notifier);
    await faultTypeHeader.fetchFaultTypeList();
    final editProvider = ref.read(editFaultFieldProvider.notifier);
    final faultAction = ref.watch(getFaultActionProvider.notifier);
    final faultDocument = ref.watch(getFaultDocumentProvider.notifier);
    final assetLocList = ref.read(assetLocListProvider.notifier);
    final user = ref.watch(userProvider);
    final plant = ref.watch(plantProvider.notifier).state;
    DateTime now = DateTime.now();
    String faultNoticedOnDate = DateFormat('dd MMM yyyy').format(now);
    DateTime dateTime = DateFormat("dd MMM yyyy").parse(faultNoticedOnDate);
    DateTime adjustedDate = DateTime(
      dateTime.year,
      dateTime.month,
      dateTime.day,
    );
    String formattedDate = DateFormat("yyyyMMdd").format(adjustedDate);
    int dateAsInt = int.parse(formattedDate);
    FAULT_HEADER faultHeaderData = FAULT_HEADER(
        fault_id: UIHelper.generateRandomId(),
        reported_by: user!.user_id,
        p_mode: AppConstants.add,
        reported_on: dateAsInt);
    faultHeaderData.location_id = widget.assetHeader is ASSET_HEADER
        ? widget.assetHeader!.parent_loc_id
        : widget.assetHeader.location_id;
    faultHeaderData.asset_no = widget.assetHeader is LOCATION_HEADER
        ? null
        : widget.assetHeader!.asset_no;
    if (widget.assetHeader is ASSET_HEADER) {
      if (widget.assetHeader!.asset_no != null) {
        await assetLocList
            .fetchAssetLocList(faultHeaderData.location_id.toString());
      }
    }
    if (plant.isNotEmpty) {
      faultHeaderData.plant_id = plant;
    }
    await faultInsertHeader.insertFaultHeader(faultHeaderData);
    await faultHeader.getFaultHeader(
        faultId: faultHeaderData.fault_id.toString());
    await faultAction.getFaultAction(faultHeaderData.fault_id.toString());
    await faultDocument.getFaultDocuments(faultHeaderData.fault_id.toString());
    editProvider.getEditFaultFieldEnable(true);
    if (mounted) {
      if (ScreenType.desktop != UIHelper().getScreenType(context)) {
        await Navigator.push(context, MaterialPageRoute(builder: (context) {
          return FaultDetailScreen(
            type: AppConstants.addFault,
          );
        }));
      } else {
        await showDialog(
            context: context,
            barrierDismissible: false,
            builder: (rootDialogContext) => Dialog(
                backgroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0),
                ),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxWidth: 600,
                    maxHeight: 800,
                  ),
                  child: const Padding(
                    padding: EdgeInsets.all(20.0),
                    child: FaultDetailScreen(type: AppConstants.addFault),
                  ),
                )));
      }
    }
  }

  clearFaultStates() {
    final faultHeader = ref.read(faultHeaderProvider.notifier);
    final location = ref.watch(locationProvider.notifier);
    final assetLocList = ref.watch(assetLocListProvider.notifier);
    final asset = ref.watch(assetProvider.notifier);
    final dueOn = ref.watch(faultDueOnProvider.notifier);
    final description = ref.watch(faultDescriptionProvider.notifier);
    final faultMode = ref.watch(faultModeHeaderProvider.notifier);
    final priority = ref.watch(priorityProvider.notifier);
    final faultType = ref.watch(faultTypeProvider.notifier);
    final longText = ref.watch(faultLongTextProvider.notifier);
    final reportedBy = ref.watch(faultReportedByProvider.notifier);
    location.clearLocation();
    assetLocList.clearAssetLocList();
    asset.clearAsset();
    dueOn.clearDueOn();
    description.clearFaultDescription();
    faultMode.clearFaultMode();
    priority.clearPriority();
    faultType.clearFaultType();
    longText.clearFaultLongText();
    reportedBy.clearFaultReportedBy();
    faultHeader.clearFault();
  }
}
