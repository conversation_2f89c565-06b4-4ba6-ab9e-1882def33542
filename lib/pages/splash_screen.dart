import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:rounds/pages/login/login.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/app_extensions.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    implements AuthProtocol {
  double _windowHeight = 0.0;
  double _windowWidth = 0.0;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Future<UnviredAccount> showLoginScreen(List<UnviredAccount> accounts) async {
    UnviredAccount? selectedAccount;
    if (accounts.isEmpty) {
      selectedAccount = await _navigateToLoginPage('', false, accounts);
    } else if (accounts.length == 1) {
      selectedAccount = accounts[0];
      if (selectedAccount.getIsLocalPasswordRequired() ||
          !selectedAccount.getIsLastLoggedIn()) {
        selectedAccount = await _navigateToLoginPage(
            selectedAccount.getErrorMessage().toString().isNotEmpty
                ? selectedAccount.getErrorMessage().toString()
                : context.locale.pleaseEnterYourPasswordToLogin,
            false,
            accounts);
      } else if (selectedAccount.getErrorMessage().toString().isNotEmpty) {
        selectedAccount = await _navigateToLoginPage(
            selectedAccount.getErrorMessage().toString(), false, accounts);
      } else {
        selectedAccount = accounts[0];
      }
    } else {
      selectedAccount = await _navigateToLoginPage(
          context.locale.pleaseSelectAccountToContinue, true, accounts);
    }

    return selectedAccount;
  }

  @override
  Future<SSOResult> showWebView(SSOLoginScreen ssoLoginScreen) async {
    // TODO: implement showWebView
    SSOResult value = await Navigator.push(
        context, MaterialPageRoute(builder: (context) => ssoLoginScreen));
    return value;
  }

  @override
  Widget build(BuildContext context) {
    WidgetsFlutterBinding.ensureInitialized();
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: AppColors.primaryColor,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.primaryColor,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
    _windowHeight = MediaQuery.of(context).size.height;
    _windowWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      body: Center(
        child: Container(
          height: double.infinity,
          width: double.infinity,
          color: AppColors.primaryColor,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _getCompanyLogo(isMobileView: true),
              Text(
                AppLocalizations.of(context)!.rounds,
                style: TextStyle(
                  fontSize: 28,
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Future<UnviredAccount> _navigateToLoginPage(String errorMessage,
      bool isMultipleAccounts, List<UnviredAccount> accountList) async {
    UnviredAccount unviredAccount = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => LoginPage(),
        ));
    return unviredAccount;
  }

  Widget _getCompanyLogo({required bool isMobileView}) {
    return SvgPicture.asset(
      'assets/svg/logo.svg',
      height: 200,
    );
  }
}
