import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/be/INSPECTION_SECTION.dart';
import 'package:rounds/pages/inspection/widget/job_task_card.dart';
import '../../be/DOCUMENT_HEADER.dart';
import '../../be/INSPECTION_TASK.dart';
import '../../../providers/inspection/inspection_plan_header_provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../providers/inspection/inspection_header_provider.dart';
import '../../providers/inspection/inspection_task_provider.dart';
import '../widgets/notaskfoundwidget.dart';

class InspectionTaskListScreen extends ConsumerStatefulWidget {
  final INSPECTION_SECTION section;
  final int sectionList;
  final int secIndex;
  final ScrollController scrollController;

  const InspectionTaskListScreen({required this.section, required this.scrollController, required this.sectionList,required this.secIndex, super.key});

  @override
  ConsumerState<InspectionTaskListScreen> createState() =>
      _InspectionTaskListScreenState();
}

class _InspectionTaskListScreenState
    extends ConsumerState<InspectionTaskListScreen> {
  @override
  void initState() {
    super.initState();
    // Fetch task list on screen load
    Future.microtask(() async {
      final inspHeader = ref.read(inspectionHeaderProvider);
      final inspPlan = ref.read(inspectionPlanHeaderProvider);
      await ref
          .read(inspectionPlanTaskListHeaderProvider.notifier)
          .fetchInspectionPlanTaskListHeaders(
            section: widget.section,
            plantId: inspPlan.plant_id ?? '',
            header: inspHeader,
          );
    });
  }

  @override
  Widget build(BuildContext context) {
    final inspHeader = ref.watch(inspectionHeaderProvider);
    final inspPlan = ref.watch(inspectionPlanHeaderProvider);
    final inspPlanTaskHeader = ref.watch(inspectionPlanTaskListHeaderProvider);
    final filteredTasks = ref.watch(inspectionTaskNotifier);
    final bool isSearch = ref.watch(inspectionTaskNotifier.notifier).isSearch;
    final bool isToggled = ref.watch(inspectionToggleStateProvider);

    List<INSPECTION_TASK> tasksToDisplay = (isSearch || isToggled)
        ? (filteredTasks ?? [])
            .where((element) => element.section_id == widget.section.section_id)
            .toList()
        : (inspPlanTaskHeader.data ?? [])
            .where((element) => element.section_id == widget.section.section_id)
            .toList();

    tasksToDisplay.sort(
      (a, b) => a.seq_no!.compareTo(b.seq_no!),
    );

    if (inspPlanTaskHeader.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (inspPlanTaskHeader.error != null) {
      return Center(
        child: Text(
          '${AppLocalizations.of(context)!.error}: ${inspPlanTaskHeader.error}',
        ),
      );
    }

    if (isSearch && (filteredTasks.isEmpty)) {
      return const NoTasksFoundWidget();
    }

    if (tasksToDisplay.isEmpty) {
      return const SizedBox.shrink();
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const ScrollPhysics(),
      itemCount: tasksToDisplay.length,
      itemBuilder: (context, index) {
        final task = tasksToDisplay[index];
        final execTask = ref
            .watch(inspExecuteTaskListProvider.notifier)
            .getInspectionExecTaskHeaderByTask(task, inspHeader);

        return Padding(
          padding: const EdgeInsets.only(top: 10),
          child: execTask == null
              ? const NoTasksFoundWidget()
              : JobTaskCard(
                  totaltaskcount: widget.sectionList,
                  scrollController: widget.scrollController,
                  sectionIndex: widget.secIndex,
                  key: ValueKey(
                     kIsWeb ?  "${task.task_id.toString()}_${DateTime.now().millisecondsSinceEpoch}" : "${task.task_id.toString()}"),
                  task: task,
                  inspExecHeader: inspHeader,
                  index: index,
                  execTaskHeader: execTask,
                  widref: ref,
                  ctx: context,
                ),
        );
      },
    );
  }
}
