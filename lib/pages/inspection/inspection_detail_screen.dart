import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:rounds/be/INSPECTION_PLAN_HEADER.dart';
import 'package:intl/intl.dart';
import 'package:rounds/pages/inspection/round_plan_insp_detail.dart';
import 'package:rounds/providers/inspection/inspection_plan_header_provider.dart';
import 'package:rounds/utils/app_extensions.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import '../../be/DOCUMENT_HEADER.dart';
import '../../be/INSPECTION_SECTION.dart';
import '../../be/INSPECTION_TASK.dart';
import '../../be/INSP_EXEC_ACTION.dart';
import '../../be/INSP_EXEC_HEADER.dart';
import '../../be/INSP_EXEC_TASK.dart';
import '../../helpers/db_helper.dart';
import '../../helpers/pa_helper.dart';
import '../../helpers/ui_helper.dart';
import '../../providers/assets/asset_provider.dart';
import '../../providers/attachments/attachment_provider.dart';
import '../../providers/fault/fault_header_provider.dart';
import '../../providers/fault/fault_type_provider.dart';
import '../../providers/inspection/inspection_header_provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/app_colors.dart';

import '../../utils/app_constants.dart';
import '../../utils/constants.dart';
import '../../utils/utils.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../widgets/toggle_switch.dart';
import '../widgets/error_longtext_view.dart';
import 'inspection_screen.dart';
import 'inspection_task_section_list_screen.dart';
import 'package:logger/logger.dart';

class InspectionDetailScreen extends ConsumerStatefulWidget {
  final INSPECTION_PLAN_HEADER? inspectionPlanHeader;
  INSP_EXEC_HEADER? inspHeader;
  InspectionDetailScreen(
      {super.key, required this.inspectionPlanHeader, this.inspHeader});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _InspectionDetailScreenState();
}

class _InspectionDetailScreenState extends ConsumerState<InspectionDetailScreen>
    with SingleTickerProviderStateMixin {
  List<Map<String, dynamic>> menuItems = [];
  var selectedSkipReason = "";
  TextEditingController searchController = TextEditingController();
  TextEditingController delayReason = TextEditingController();
  TextEditingController reason = TextEditingController();
  late AnimationController _animationController;
  Future<List<InfoMessageData?>>? fetchInfoMessage;
  bool showSearch = false;
  FocusNode searchFocusNode = FocusNode();

  @override
  void initState() {
    menuItems = [
      {'label': 'Skip Round', 'icon': Icons.fast_forward_outlined},
      {'label': 'Reject', 'icon': Icons.cancel},
      {'label': 'Info', 'icon': Icons.info},
    ];
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    fetchInfoMessage =
        InfoMessageHelper().getInfoMessageByBeLid(widget.inspHeader!.lid);
    Future.delayed(Duration.zero).then((value) async {
      ref.read(inspectionToggleStateProvider.notifier).setToggleState(false);
      await ref
          .read(inspExecuteTaskListProvider.notifier)
          .getInspExecuteTaskList();
      await ref.read(inspectionTaskNotifier.notifier).filter(
          widget.inspectionPlanHeader?.plan_id.toString() ?? "", "", ref);
    });
  }

  @override
  Widget build(BuildContext context) {
    final data = ref.read(inspectionHeaderProvider);
    fetchInfoMessage =
        InfoMessageHelper().getInfoMessageByBeLid(widget.inspHeader!.lid);
    widget.inspHeader = data;

    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Column(
          children: [
            showSearch
                ? Container(
                    height: 60,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 10),
                    child: TextField(
                      focusNode: searchFocusNode,
                      controller: searchController,
                      onChanged: (value) {
                        ref.read(inspectionTaskNotifier.notifier).filter(
                            widget.inspectionPlanHeader!.plan_id!.toString(),
                            value,
                            ref);
                      },
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(
                          vertical: 5.0,
                          horizontal: 10.0,
                        ),
                        hintText: AppLocalizations.of(context)!.search,
                        border: OutlineInputBorder(
                          borderSide: BorderSide(color: AppColors.black),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        suffixIcon: IconButton(
                          icon: const Icon(Icons.cancel),
                          onPressed: () {
                            setState(() {
                              ref.read(inspectionTaskNotifier.notifier).filter(
                                  widget.inspectionPlanHeader!.plan_id!
                                      .toString(),
                                  "",
                                  ref);
                              showSearch = false;
                              searchController.clear();
                            });
                            _animationController.reverse();
                          },
                        ),
                      ),
                    ),
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 10),
                        child: _getSearchWidget(),
                      ),
                    ],
                  ),
            widget.inspHeader?.delay_comments != null &&
                    widget.inspHeader!.delay_comments!.isNotEmpty &&
                    widget.inspHeader!.delay_comments != ''
                ? Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: MobileErrorAndLongTextview(
                        type: TextDisplayType.warning,
                        text: widget.inspHeader!.delay_comments!.toString()))
                : const SizedBox(),
            widget.inspHeader?.delay_comments != null &&
                    widget.inspHeader!.delay_comments!.isNotEmpty &&
                    widget.inspHeader!.delay_comments != ''
                ? 10.0.spaceY
                : const SizedBox(),
            widget.inspHeader?.infoMsgCat == "WARNING" ||
                    widget.inspHeader?.infoMsgCat == "FAILURE"
                ? Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: FutureBuilder<List<InfoMessageData?>>(
                        future: fetchInfoMessage,
                        builder: (context, snapshot) {
                          if (snapshot.hasData) {
                            return (snapshot.data ?? []).isNotEmpty
                                ? MobileErrorAndLongTextview(
                                    type: widget.inspHeader?.infoMsgCat ==
                                            "FAILURE"
                                        ? TextDisplayType.error
                                        : TextDisplayType.warning,
                                    text: snapshot.data!
                                        .map((e) => e!.message)
                                        .join("\n"))
                                : const SizedBox.shrink();
                          } else {
                            return const SizedBox();
                          }
                        }),
                  )
                : const SizedBox(),
            widget.inspHeader?.infoMsgCat == "WARNING" ||
                    widget.inspHeader?.infoMsgCat == "FAILURE"
                ? 10.0.spaceY
                : const SizedBox(),
            Padding(
              padding: UIHelper().getScreenType(context) == ScreenType.desktop
                  ? const EdgeInsets.only(left: 8, right: 18)
                  : UIHelper.horizontalPaddingOf18(),
              child: RoundSegmentedSwitch(
                height: 25,
                firstLabel: AppLocalizations.of(context)!.all,
                secondLabel: AppLocalizations.of(context)!.open,
                value: ref.watch(inspectionToggleStateProvider),
                onChanged: (value) async {
                  if (value) {
                    await ref
                        .read(inspectionTaskNotifier.notifier)
                        .fetchIncompleteTasks(
                            (widget.inspectionPlanHeader?.plan_id.toString() ??
                                ""),
                            ref.read(inspectionHeaderProvider),
                            ref);
                  } else {
                    /*        await ref.read(ciltTaskNotifier.notifier).fetchIncompleteTasks(
                        (widget.ciltPlanHeader?.plan_id.toString() ?? ""),
                        ref.read(ciltHeaderProvider),
                        ref);*/ //All
                    await ref.read(inspectionTaskNotifier.notifier).reset(
                        widget.inspectionPlanHeader!.plan_id.toString(), ref);
                    await ref
                        .read(inspExecuteTaskListProvider.notifier)
                        .getInspExecuteTaskList();
                    ref
                        .read(inspExecuteTaskListProvider.notifier)
                        .findAllInspExecOfInsp(widget.inspHeader!);
                  }
                  ref
                      .read(inspectionToggleStateProvider.notifier)
                      .setToggleState(value);
                },
              ),
            ),
            Expanded(
                child: Padding(
              padding: UIHelper().getScreenType(context) == ScreenType.desktop
                  ? const EdgeInsets.only(left: 8, right: 18)
                  : UIHelper.horizontalPaddingOf18(),
              child: InspectionTaskSectionListScreen(),
            )),
          ],
        ),

/*        CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 20),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 18.0),
                    child: _getSearchWidget(), // This is widget1
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 18.0),
                child: Column(
                  children: [
                    InspectionTaskSectionListScreen(),
                  ],
                ),
              ),
            ),
          ],
        ),*/
      ),
    );
  }

  Map<String, dynamic> _getProgressValue(INSP_EXEC_HEADER? inspHeader) {
    if (inspHeader != null) {
      final numberOfTasks =
          ref.watch(inspTasksProvider)[inspHeader.plan_id] ?? [];
      final numberOfCompletedTasks =
          ref.watch(inspExecuteProvider)[inspHeader.insp_id] ?? [];
      /*    final numberOfTasks =
          ref.watch(inspectionPlanTaskListHeaderProvider).data ?? [];
*/
      int totalTasks = numberOfTasks
          .where((element) =>
              element.plan_id.toString() == inspHeader.plan_id.toString())
          .length;
      int completedTasks = numberOfCompletedTasks.length;
      numberOfCompletedTasks
          .where((element) =>
              element.insp_id.toString() == inspHeader.insp_id.toString())
          .length;
      if (totalTasks == 0) {
        return {
          'percentage': 0.0,
          'completedTasks': 0,
          'totalTasks': 0,
        };
      }
      double percentage =
          double.parse((completedTasks / totalTasks).toStringAsFixed(2));
      return {
        'percentage': percentage,
        'completedTasks': completedTasks,
        'totalTasks': totalTasks,
      };
    } else {
      return {
        'percentage': 0.0,
        'completedTasks': 0,
        'totalTasks': 0,
      };
    }
  }

  //var modifiedList = [];
  Widget _getSearchWidget() {
    var percentage = _getProgressValue(widget.inspHeader);
    if (percentage["percentage"] < 0.0 || percentage["percentage"] > 1.0) {
      percentage["percentage"] = 0.0;
    }
    final taskList = ref.watch(inspExecuteTaskListProvider.notifier).state;
    final modifiedList = taskList
        .where((element) =>
            element.p_mode == AppConstants.modified &&
            element.insp_id == widget.inspHeader!.insp_id)
        .toList();
    return Row(
      children: [
        Visibility(
          visible: UIHelper().getScreenType(context) != ScreenType.desktop,
          child: IconButton(
              onPressed: () {
                Navigator.pop(context);
              },
              icon: Icon(
                Icons.arrow_back_ios,
                color: AppColors.titleTextColor,
                size: 20,
              )),
        ),

        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Visibility(
                visible: getConditionsAccess(),
                child: getConditionsAccessMsg() == ""
                    ? SizedBox()
                    : MobileErrorAndLongTextview(
                        type: TextDisplayType.msg,
                        text: getConditionsAccessMsg()),
              ),
              getConditionsAccess() ? SizedBox(height: 5) : SizedBox(),
              Text("${widget.inspectionPlanHeader!.title}" ?? "",
                  overflow: TextOverflow.ellipsis,
                  style: UIHelper.titleStyle14()),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                    child: LinearPercentIndicator(
                      padding: EdgeInsets.zero,
                      progressColor: Colors.green,
                      lineHeight: 5,
                      backgroundColor: HexColor('#EEEEEE'),
                      percent: percentage["percentage"],
                      center: const Text(''), // animationDuration: 1000,
                      barRadius: const Radius.circular(8.0),
                    ),
                  ),
                  10.0.spaceX,
                  Text(
                      '${percentage["completedTasks"]}/${percentage["totalTasks"]}',
                      style: const TextStyle(fontSize: 12)),
                ],
              ),
            ],
          ),
        ),
        // const Spacer(),
        Expanded(
          flex: 1,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (!showSearch)
                IconButton(
                  icon: const Icon(Icons.search),
                  onPressed: () {
                    setState(() {
                      showSearch = true;
                      searchFocusNode.requestFocus();
                    });
                  },
                ),
              if ((widget.inspHeader!.status != AppConstants.STATE_COMPLETED &&
                  widget.inspHeader?.syncStatus.index != 2) || percentage["completedTasks"] != percentage["totalTasks"])
                isExecutionVisible()
                    ? (widget.inspHeader?.status == AppConstants.STATE_COMPLETED && percentage["completedTasks"] == percentage["totalTasks"])
                        ? SizedBox()
                        : SizedBox(
                            height: 30,
                            width: (!kIsWeb && UIHelper().getScreenType(context) == ScreenType.mobile) ? 24 : 80,
                              child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      backgroundColor: ((modifiedList.isNotEmpty || percentage["completedTasks"] == percentage["totalTasks"]) || widget.inspHeader!.syncStatus.index == 1) ?
                                       AppColors.primaryColor : AppColors.grey,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      )),
                                  onPressed: () {
                                    bool hasModifiedTasks = modifiedList.isNotEmpty;
                                    bool isFullyCompleted = percentage["completedTasks"] == percentage["totalTasks"];
                                    bool shouldEnableButton = hasModifiedTasks || isFullyCompleted;

                                    if ((widget.inspHeader?.status !=
                                            AppConstants.STATE_COMPLETED &&
                                        shouldEnableButton) || widget.inspHeader!.syncStatus.index == 1) {
                                      showDialog(
                                        context: context,
                                        barrierDismissible: false,
                                        builder: (context) {
                                          return AlertDialog(
                                            title: Text(AppLocalizations.of(
                                                    context)!
                                                .are_you_sure_you_want_to_submit_insp),
                                            actions: [
                                              TextButton(
                                                onPressed: () async {
                                                  final current = DateTime.now();
                                                  DateTime endTime =
                                                      Utils.getTimeStamp(
                                                          widget.inspHeader!
                                                              .start_on
                                                              .toString(),
                                                          widget
                                                              .inspHeader!.end_at
                                                              .toString());
                              
                                                  if (current.isAfter(endTime)) {
                                                    Navigator.pop(context);
                                                    showDialog(
                                                      context: context,
                                                      barrierDismissible: false,
                                                      builder: (context) {
                                                        return AlertDialog(
                                                          title: Text(
                                                              AppLocalizations.of(
                                                                      context)!
                                                                  .please_provide_reason_for_submission_delay),
                                                          content: TextField(
                                                            decoration:
                                                                InputDecoration(
                                                              hintText: AppLocalizations
                                                                      .of(context)!
                                                                  .reason_for_submission_delay,
                                                              border: OutlineInputBorder(
                                                                  borderSide: BorderSide(
                                                                      color: AppColors
                                                                          .grey)),
                                                              focusedBorder: OutlineInputBorder(
                                                                  borderSide: BorderSide(
                                                                      color: AppColors
                                                                          .grey)),
                                                              enabledBorder: OutlineInputBorder(
                                                                  borderSide: BorderSide(
                                                                      color: AppColors
                                                                          .grey)),
                                                              contentPadding:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                      vertical:
                                                                          10,
                                                                      horizontal:
                                                                          10),
                                                            ),
                                                            controller:
                                                                delayReason,
                                                            onChanged: (value) {},
                                                          ),
                                                          actions: [
                                                            TextButton(
                                                              onPressed:
                                                                  () async {
                                                                dynamic result =
                                                                    _getProgressValue(
                                                                        widget
                                                                            .inspHeader);
                                                                if (result !=
                                                                    null) {
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop();
                              
                                                                  if (result[
                                                                          "totalTasks"] ==
                                                                      result[
                                                                          "completedTasks"]) {
                                                                    await getSubmitWithCloseAction(
                                                                        widget
                                                                            .inspHeader!);
                                                                  } else {
                                                                    await getSubmitWithoutCloseAction(
                                                                        widget
                                                                            .inspHeader!);
                                                                  }
                                                                }
                                                              },
                                                              child: Text(
                                                                  AppLocalizations.of(
                                                                          context)!
                                                                      .submit),
                                                            ),
                                                            TextButton(
                                                              onPressed: () {
                                                                Navigator.of(
                                                                        context)
                                                                    .pop();
                                                              },
                                                              child: Text(
                                                                  AppLocalizations.of(
                                                                          context)!
                                                                      .cancel),
                                                            ),
                                                          ],
                                                        );
                                                      },
                                                    );
                                                  } else {
                                                    // No delay, process directly
                                                    dynamic result =
                                                        _getProgressValue(
                                                            widget.inspHeader);
                                                    if (result != null) {
                                                      Navigator.of(context)
                                                          .pop(); // Close the confirmation dialog
                              
                                                      if (result["totalTasks"] ==
                                                          result[
                                                              "completedTasks"]) {
                                                        await getSubmitWithCloseAction(
                                                            widget.inspHeader!);
                                                      } else {
                                                      await getSubmitWithoutCloseAction(
                                                            widget.inspHeader!);
                                                      }
                                                    }
                                                  }
                                                },
                                                child: Text(
                                                    AppLocalizations.of(context)!
                                                        .submit),
                                              ),
                                              TextButton(
                                                onPressed: () {
                                                  Navigator.pop(context);
                                                },
                                                child: Text(
                                                    AppLocalizations.of(context)!
                                                        .cancel),
                                              ),
                                            ],
                                          );
                                        },
                                      );
                                    }
                                  },
                                  child:
                                      (!kIsWeb && UIHelper().getScreenType(context) == ScreenType.mobile) ? Icon(Icons.save) :
                                    Text(AppLocalizations.of(context)!.submit)),
                          )
                    : const SizedBox.shrink(),
              // isExecutionVisible() ? _getPopupMenu() : SizedBox(),
              _getPopupMenu(),
            ],
          ),
        )
      ],
    );
  }

  bool isExecutionVisible() {
    final role = ref.watch(roleProvider);
    final todayDate = DateFormat('yyyyMMdd').format(DateTime.now());
    DateTime now = DateTime.now();
    String formattedTime = DateFormat('HHmmss').format(now);
    String startAT = widget.inspHeader!.start_at.toString();
    DateTime executionDateTime =
        Utils.getTimeStamp(widget.inspHeader!.start_on.toString(), startAT);

    if (role != null) {
      if (UIHelper.isExecute(role.inspection!) &&
          (ref.watch(userProvider)?.user_id == widget.inspHeader?.assigned_to &&
              widget.inspHeader?.status != "REJECTED") &&
          executionDateTime.isBefore(now)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool getConditionsAccess() {
    final user_header = ref.watch(userProvider);
    if (widget.inspectionPlanHeader != null) {
      if (widget.inspHeader!.status == AppConstants.STATE_ACCEPTED ||
          widget.inspHeader!.status == AppConstants.STATE_COMPLETED) {
        DateTime now = DateTime.now();
        DateTime ciltDateAndTime =
            getTimeStamps(); // returns combined start_on + start_at

        String startAT = widget.inspHeader!.start_at.toString();
        String startON = widget.inspHeader!.start_on.toString();

        final int startYear = int.parse(startON.substring(0, 4));
        final int startMonth = int.parse(startON.substring(4, 6));
        final int startDay = int.parse(startON.substring(6, 8));
        final DateTime onlyStartDate =
            DateTime(startYear, startMonth, startDay);

        if (now.year == onlyStartDate.year &&
            now.month == onlyStartDate.month &&
            now.day == onlyStartDate.day &&
            now.isBefore(ciltDateAndTime) &&
            (widget.inspHeader!.status != AppConstants.STATE_COMPLETED)) {
          return true;
        } else {
          return false;
        }
      } else {
        if (widget.inspHeader!.assigned_to == null ||
            ((widget.inspHeader?.status == AppConstants.STATE_ASSIGNED) &&
                user_header?.user_id == widget.inspHeader!.assigned_to)) {
          return false;
        } else if (user_header?.user_id == widget.inspHeader!.assigned_to) {
          if (widget.inspHeader!.status != AppConstants.STATE_REJECTED) {
            return false;
          } else {
            if (user_header?.user_id == widget.inspHeader!.assigned_to) {
              return false;
            } else {
              return true;
            }
          }
        } else {
          return true;
        }
      }
    } else {
      return false;
    }
  }

  DateTime getTimeStamps() {
    final String startAT =
        widget.inspHeader!.start_at.toString(); // e.g., "140000"
    final String startON =
        widget.inspHeader!.start_on.toString(); // e.g., "20250323"

    final year = int.parse(startON.substring(0, 4));
    final month = int.parse(startON.substring(4, 6));
    final day = int.parse(startON.substring(6, 8));

    int time = int.parse(startAT);

    int hours = time ~/ 10000;
    int minutes = (time % 10000) ~/ 100;
    int seconds = time % 100; // Extract seconds by taking the remainder

    final DateTime combinedDateTime =
        DateTime(year, month, day, hours, minutes, seconds);
    return combinedDateTime;
  }

  String getConditionsAccessMsg() {
    final user_header = ref.watch(userProvider);
    if (widget.inspectionPlanHeader != null) {
      if (widget.inspHeader!.status == AppConstants.STATE_ACCEPTED ||
          widget.inspHeader!.status == AppConstants.STATE_COMPLETED) {
        DateTime now = DateTime.now();
        DateTime ciltDateAndTime =
            getTimeStamps(); // returns combined start_on + start_at

        String startAT = widget.inspHeader!.start_at.toString();
        String startON = widget.inspHeader!.start_on.toString();

        final int startYear = int.parse(startON.substring(0, 4));
        final int startMonth = int.parse(startON.substring(4, 6));
        final int startDay = int.parse(startON.substring(6, 8));
        final DateTime onlyStartDate =
            DateTime(startYear, startMonth, startDay);

        if (now.year == onlyStartDate.year &&
            now.month == onlyStartDate.month &&
            now.day == onlyStartDate.day &&
            now.isBefore(ciltDateAndTime) &&
            (widget.inspHeader!.status != AppConstants.STATE_COMPLETED)) {
          return "${AppLocalizations.of(context)!.cannot_start_task_before} ${formatStartTimeAsClock(startAT)}";
        } else {
          return "";
        }
      } else {
        if (widget.inspHeader!.assigned_to == null ||
            ((widget.inspHeader?.status == AppConstants.STATE_ASSIGNED) &&
                user_header?.user_id == widget.inspHeader!.assigned_to)) {
          return "";
        } else if (user_header?.user_id == widget.inspHeader!.assigned_to) {
          if (widget.inspHeader!.status != AppConstants.STATE_REJECTED) {
            return "";
          } else {
            if (user_header?.user_id == widget.inspHeader!.assigned_to) {
              return "";
            } else {
              return "${AppLocalizations.of(context)!.cannot_perform_action_as_assigned_to_other}";
            }
          }
        } else {
          return "${AppLocalizations.of(context)!.cannot_perform_action_as_assigned_to_other}";
        }
      }
    } else {
      return "";
    }
  }

  String formatStartTimeAsClock(String startAT) {
    if (startAT.length != 6) return "Invalid Time";

    String hours = startAT.substring(0, 2);
    String minutes = startAT.substring(2, 4);
    String seconds = startAT.substring(4, 6);

    return "$hours:$minutes:$seconds"; // Format as HH:mm:ss
  }
  /* Widget _getSearchWidget() {
    return Row(
      children: [
        Expanded(
            flex: 10,
            child: CustomSearchBar(
              onChanged: (value) {},
              controller: searchController,
              onCancel: () {
                setState(() {
                  searchController.text = "";
                });
              },
            )),
        const SizedBox(width: 10),
        Expanded(
            child: Image.asset(
          'assets/icon/task_icons/qr-code-scan.png',
          height: 25,
          width: 25,
          color: AppColors.black,
        )),
        Expanded(child: _getPopupMenu())
      ],
    );
  }*/
  //var modifiedList = [];
  Widget _getPopupMenu() {
    final taskList = ref.watch(inspExecuteTaskListProvider);
    menuItems = [
      {'label': 'Skip Round', 'icon': Icons.fast_forward_outlined},
      {'label': 'Reject', 'icon': Icons.cancel},
      {'label': 'Info', 'icon': Icons.info},
    ];
    final modifiedList = taskList
        .where((element) =>
            element.objectStatus.index == 0 &&
            element.insp_id == widget.inspHeader!.insp_id &&
            (element.status == AppConstants.STATE_TASK_COMP ||
                element.status == AppConstants.STATE_COMPLETED))
        .toList();
    var percentage = _getProgressValue(widget.inspHeader);

    if (percentage["percentage"] >= 1.0 || !isExecutionVisible()) {
      menuItems.removeWhere((element) => element["label"] == "Skip Round");
    }
    if (ref.read(userProvider)!.user_id != widget.inspHeader!.assigned_to ||
        modifiedList.isNotEmpty) {
      menuItems.removeWhere((element) => element["label"] == "Reject");
    }

   

    return PopupMenuButton<Map<String, dynamic>>(
      padding: EdgeInsets.zero,
      icon: const Icon(Icons.more_vert, size: 30),
      onSelected: (item) {
        onItemSelected(item['label']);
      },
      itemBuilder: (context) => menuItems.map((item) {
        return PopupMenuItem<Map<String, dynamic>>(
          value: item,
          child: Row(
            children: [
              Icon(item['icon'], color: AppColors.black),
              const SizedBox(width: 10),
              Text(item['label']),
            ],
          ),
        );
      }).toList(),
    );
  }

  onItemSelected(String item) {
    switch (item) {
      case 'Maps':
        break;
      case 'Report':
        break;
      case 'Skip Round':
        onSkipRound(context, ref, widget.inspHeader!);
        break;
      case 'Reject':
        onReject();
        break;
      case 'Asset View':
        break;
      case 'Info':
        if (widget.inspectionPlanHeader!.asset_no != null) {
          ref
              .read(singleAssetProvider.notifier)
              .getAssetHeader(widget.inspectionPlanHeader!.asset_no!);
        }
        if (ScreenType.desktop == UIHelper().getScreenType(context)) {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (rootDialogContext) => Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxWidth: 420, // Slightly more than a mobile screen width
                      maxHeight:
                          800, // Not more than the size of a mobile phone
                    ),
                    child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: InspRoundPlanDetailScreen(
                            inspPlanHeader: widget.inspectionPlanHeader!)),
                  )));
        } else {
          Navigator.of(context).push(MaterialPageRoute(
            builder: (context) => InspRoundPlanDetailScreen(
                inspPlanHeader: widget.inspectionPlanHeader!),
          ));
        }

        break;
    }
  }

  onSkipRound(
      BuildContext context, WidgetRef ref, INSP_EXEC_HEADER inspHeader) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return true;
          },
          child: StatefulBuilder(
            builder: (context, setState) {
              return AlertDialog(
                contentPadding: const EdgeInsets.all(5),
                content: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 400),
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                AppLocalizations.of(context)!.select_reason,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold, fontSize: 14),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          getReasonDropdown(context, ref, setState),
                          const SizedBox(height: 10),
                          TextField(
                            style: const TextStyle(fontSize: 14),
                            minLines: 3,
                            controller: reason,
                            onChanged: (value) {
                              TextSelection previousSelection =
                                  reason.selection;
                              reason.text = value;
                              reason.selection = previousSelection;
                              setState(() {});
                            },
                            maxLines: null,
                            decoration: InputDecoration(
                              hintText: AppLocalizations.of(context)!
                                  .additionalComment,
                              border: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              focusedBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              enabledBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              contentPadding: const EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 10),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: reason.text.isEmpty || selectedSkipReason.isEmpty
                        ? null
                        : () async {
                            if (reason.text == '' || reason.text.isEmpty) {
                              Navigator.of(context).pop();
                              UIHelper.showInfoDialogWithtitleAndDescription(
                                  context,
                                  title: AppLocalizations.of(context)!.warning,
                                  description: AppLocalizations.of(context)!
                                      .please_add_additional_message);
                            } else {
                              Navigator.of(context).pop();
                              await onSaveReason(reason.text, inspHeader);
                            }
                          },
                    child: Text(
                      AppLocalizations.of(context)!.save,
                      style: TextStyle(
                        color: reason.text.isEmpty || selectedSkipReason.isEmpty
                            ? AppColors.grey
                            : AppColors.primaryColor,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      selectedSkipReason = '';
                      reason.clear();
                      Navigator.pop(context);
                    },
                    child: Text(
                      AppLocalizations.of(context)!.cancel,
                      style: TextStyle(
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  onReject() {
    UIHelper.showConfirmationDialogWithYesOrNo(
      context,
      description: AppLocalizations.of(context)!.do_you_want_to_reject_inspection,
      yes: () async {
        await rejectInspPlan(widget.inspHeader!);
        roundDetailViewNotifier.value = null;
      },
      no: () {
        Navigator.of(context, rootNavigator: true).pop();
      },
    );
  }

  Future<void> rejectInspPlan(INSP_EXEC_HEADER inspExecHeader) async {
    final plant = ref.watch(plantProvider.notifier).state;
    final plantSection = ref.watch(plantSectionProvider.notifier).state;
    final shift = ref.read(shiftProvider.notifier).state;

    INSP_EXEC_ACTION rejectAction = INSP_EXEC_ACTION(
        insp_id: inspExecHeader.insp_id, user_action: Constants.ACTION_REJECT);
    rejectAction.fid = inspExecHeader.lid;
    await DbHelper().acceptInspAction(rejectAction);

    await DbHelper.updateInsp(inspExecHeader);

    var result = await PAHelper.rejectINSPInSyncMode(context, inspExecHeader);

    final searchkey = ref.read(roundsSearchProvider);
    await ref.read(filterInspectionHeaderListProvider.notifier).filter(plant, plantSection, shift, searchkey, ref);

    await ref
        .read(inspectionPlanListHeaderProvider.notifier)
        .fetchInspectionPlanListHeaders();
    Navigator.of(context, rootNavigator: true).pop();
    if(!kIsWeb && UIHelper().getScreenType(context) == ScreenType.mobile){
      Navigator.of(context, rootNavigator: true).pop();
     // Navigator.of(context, rootNavigator: true).pop();
    }
    if (result.body['InfoMessage'] != null) {
      String? message = result.body['InfoMessage'][0]['message'];
      if (message != null) {
        UIHelper.showErrorDialog(
          context,
          title: "Error",
          description: message,
        );
      }
    }
  }

  Widget getReasonDropdown(
      BuildContext context, WidgetRef ref, StateSetter setState) {
    final skipReasonList = ref
        .watch(skipReasonListProvider)
        .where((element) => (element.category! & 4) != 0)
        .toList();
    final skipReason = ref.watch(skipReasonInspProvider.notifier);
    final dropdownItems = skipReasonList.map((option) {
      return DropdownMenuItem<String>(
        value: option.description,
        child: Padding(
          padding: const EdgeInsets.only(left: 5.0),
          child: Text(option.description!, style: UIHelper.valueStyle()),
        ),
      );
    }).toList();

    if (dropdownItems.where((item) => item.value == '').isEmpty) {
      dropdownItems.insert(
        0,
        DropdownMenuItem<String>(
          value: '',
          child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(AppLocalizations.of(context)!.select,
                style: UIHelper.valueStyle()),
          ),
        ),
      );
    }

    return Container(
      decoration: UIHelper.fieldDecoration(),
      child: DropdownButton<String>(
        elevation: 0,
        isExpanded: true,
        underline: const SizedBox(),
        value: selectedSkipReason.isEmpty ? "" : selectedSkipReason,
        items: dropdownItems,
        onChanged: (newValue) {
          setState(() {
            selectedSkipReason = newValue!;
            skipReason.getInspSkipReason(selectedSkipReason);
          });
        },
      ),
    );
  }

  Future<void> onSaveReason(String reason, INSP_EXEC_HEADER inspHeader) async {
    try {
      final skipReason = ref.watch(skipReasonInspProvider.notifier).state;
      inspHeader.reason = skipReason.reason.toString();
      inspHeader.skip_comments = reason;
      inspHeader.p_mode = AppConstants.modified;

      await AppDatabaseManager().update(
          DBInputEntity(INSP_EXEC_HEADER.TABLE_NAME, inspHeader.toJson()));

      List<INSPECTION_TASK> list =
          await DbHelper.getInspectionTaskPlanListHeaderByPlanId(
              inspHeader.plan_id.toString());

      ///TODO sneha reason code
      for (INSPECTION_TASK task in list) {
        if (task.dependent == 'true') {
          await DbHelper().taskInspectionUpdate(
              task: task,
              status: AppConstants.STATE_TASK_COMP,
              inspHeader: inspHeader,
              reasonCode: '',
              skipreason: '',
              // skipReason.category.toString(),
              // reason,
              p_mode: AppConstants.modified,
              comment: '',
              isIrrelevant: 'true',
              dataString: null,
              dataDouble: null,
              dataBloc: null,
              isSkippingSection: true);
        } else {
          await DbHelper().taskInspectionUpdate(
              task: task,
              status: AppConstants.STATE_TASK_COMP,
              inspHeader: inspHeader,
              reasonCode: skipReason.reason.toString(),
              skipreason: reason,
              p_mode: AppConstants.modified,
              isSkipped: 'true',
              dataString: null,
              dataDouble: null,
              dataBloc: null,
              isSkippingSection: true);
        }
        INSPECTION_SECTION? section =
            await DbHelper.getInspectionSectionByPlanId(
                inspHeader.plan_id.toString());
        await ref.read(inspTasksProvider.notifier).getInspTasks(
            inspHeader.plan_id!,
            header: inspHeader,
            section: section!,
            plantId: inspHeader.plant_id.toString());
        /*        INSPECTION_SECTION? inspection_section =
            await DbHelper.getInspectionSectionByPlanId(
                inspHeader.plan_id.toString());
        await ref
            .read(inspectionPlanTaskListHeaderProvider.notifier)
            .fetchInspectionPlanTaskListHeaders(
                plantId: inspHeader.plant_id.toString(),
                header: inspHeader,
                section: inspection_section!);*/
        await ref.read(inspExecuteProvider.notifier).getInspExecute(inspHeader);
      }

      await ref
          .read(inspExecuteTaskListProvider.notifier)
          .getInspExecuteTaskList();

      await ref.read(inspectionTaskNotifier.notifier).filter(
          widget.inspectionPlanHeader?.plan_id.toString() ?? "", "", ref);

      INSPECTION_SECTION? inspection_section =
          await DbHelper.getInspectionSectionByPlanId(
              inspHeader.plan_id.toString());
      await ref
          .read(inspectionPlanTaskListHeaderProvider.notifier)
          .fetchInspectionPlanTaskListHeaders(
              plantId: inspHeader.plant_id.toString(),
              header: inspHeader,
              section: inspection_section!);
      await ref.read(inspExecuteProvider.notifier).getInspExecute(inspHeader);

      INSP_EXEC_ACTION action = INSP_EXEC_ACTION(
          insp_id: inspHeader.insp_id,
          user_action: AppConstants.STATE_COMPLETED);

      action.fid = inspHeader.lid;

      await AppDatabaseManager().execute(
          "select * from ${INSP_EXEC_ACTION.TABLE_NAME} where ${INSP_EXEC_ACTION.FIELD_INSP_ID} = '${inspHeader.insp_id.toString()}'");
      // Future.delayed(Duration.zero, () {
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return Dialog(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 400),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Center(
                              child: Text(
                                "${AppLocalizations.of(context)!.congratulations} 🎉",
                                style: UIHelper.titleStyle16(),
                              ),
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              Navigator.of(context).pop();
                            },
                            child: Icon(
                              Icons.cancel_outlined,
                              color: AppColors.black,
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 16.0,
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.all_tasks_completed,
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryColor,
                          minimumSize: const Size(double.infinity, 40),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                        onPressed: () async {
                          final current = DateTime.now();
                          DateTime endTime = DateTime(
                            current.year,
                            current.month,
                            current.day,
                            int.parse(
                                inspHeader.end_at.toString().substring(0, 2)),
                            int.parse(
                                inspHeader.end_at.toString().substring(2, 4)),
                          );

                          Navigator.of(context).pop();
                          if (current.isAfter(endTime)) {
                            showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (context) {
                                return AlertDialog(
                                  title: Text(AppLocalizations.of(context)!
                                      .please_provide_reason_for_submission_delay),
                                  content: TextField(
                                    decoration: InputDecoration(
                                      hintText: AppLocalizations.of(context)!
                                          .reason_for_submission_delay,
                                      border: OutlineInputBorder(
                                        borderSide:
                                            BorderSide(color: AppColors.grey),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide:
                                            BorderSide(color: AppColors.grey),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderSide:
                                            BorderSide(color: AppColors.grey),
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                        vertical: 10,
                                        horizontal: 10,
                                      ),
                                    ),
                                    controller: delayReason,
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () async {
                                        Navigator.of(context).pop();
                                        await getSubmitWithCloseAction(
                                            inspHeader);
                                      },
                                      child: Text(
                                          AppLocalizations.of(context)!.submit),
                                    ),
                                    TextButton(
                                      onPressed: () {
                                        delayReason.clear();
                                        Navigator.of(context).pop();
                                      },
                                      child: Text(
                                          AppLocalizations.of(context)!.cancel),
                                    ),
                                  ],
                                );
                              },
                            );
                          } else {
                            await getSubmitWithCloseAction(inspHeader);
                          }
                        },
                        child: Text(
                          AppLocalizations.of(context)!.submit,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      }
    } catch (e) {
      Logger.logError('InspectionDetailScreenDetailScreen', 'onSaveReason', e.toString());
    }
  }

  Future<void> getSubmitWithCloseAction(INSP_EXEC_HEADER inspHeader) async {
    if (delayReason.text.isNotEmpty) {
      inspHeader.delay_comments = delayReason.text;
    }
    await DbHelper().markIrrelevantDependentTasks(
        planHeader: widget.inspectionPlanHeader!, execHeader: inspHeader);
    await AppDatabaseManager().update(
        DBInputEntity(INSP_EXEC_HEADER.TABLE_NAME, inspHeader.toJson()));
    final numberOfCompletedTasks = ref
            .watch(inspExecuteProvider)[inspHeader.insp_id] ??
        [].where((element) => element.cilt_id == inspHeader.insp_id).toList();
    List<Future<void>> updateTasks = [];
    for (var task in numberOfCompletedTasks) {
      await DbHelper.updateInspExecTask(task);
      INSP_EXEC_ACTION action = INSP_EXEC_ACTION(
          insp_id: inspHeader.insp_id,
          user_action: AppConstants.STATE_COMPLETED);
      action.fid = inspHeader.lid;

      var result = await AppDatabaseManager().execute(
          "select * from INSP_EXEC_ACTION where INSP_ID = '${inspHeader.insp_id.toString()}'");
      if (result.isNotEmpty) {
        updateTasks.add(AppDatabaseManager().update(
            DBInputEntity(INSP_EXEC_ACTION.TABLE_NAME, action.toJson())));
      } else {
        updateTasks.add(AppDatabaseManager().insert(
            DBInputEntity(INSP_EXEC_ACTION.TABLE_NAME, action.toJson())));
      }
    }
    await Future.wait(updateTasks);
    List<INSP_EXEC_TASK> insp_exec_tasks = ref
        .read(inspExecuteTaskListProvider.notifier)
        .findAllInspExecOfInsp(inspHeader);

    List<DOCUMENT_HEADER> headerData = [];

    for (var task in insp_exec_tasks) {
      final documentHeaders = ref
          .watch(inspectionTaskExecDocumentHeaderProvider.notifier)
          .getDocumentHeadersForInspectionTask(task);
      if (documentHeaders != null) {
        headerData.addAll(documentHeaders);
      }
    }

    List<DOCUMENT_HEADER> documents = headerData
        .where((element) => element.objectStatus == ObjectStatus.add)
        .toList();

    if (kIsWeb) {
      if (mounted) {
        UIHelper().progressDialog(
            context: context, message: "Submiting Inspection Execution");
      }
      for (DOCUMENT_HEADER document in documents) {
        var doc = await DbHelper()
            .getAttachmentFromIndexDbByUid(document.doc_id ?? "");
        await SyncEngine().uploadAttachmentSync(
            doc ?? "", document.file_name ?? "", document.doc_id ?? "");
      }
      await PAHelper.addDocumentInSyncMode(context, documents);
    } else {
      await PAHelper.addDocumentInAsyncMode(context, documents);
    }
    // }
    if (!kIsWeb) {
      await PAHelper.modifyInspExecInAsyncMode(context, inspHeader);
    } else {
      Result result =
          await PAHelper.modifyInspExecInSyncMode(context, inspHeader);
      Navigator.of(context, rootNavigator: true).pop();
      // if ((result.body['INSP_EXEC'] as List).first['INSP_EXEC_HEADER'] !=
      //     null) {
      await ref
          .read(inspExecuteTaskListProvider.notifier)
          .getInspExecuteTaskList();
      INSP_EXEC_HEADER? data =
          await DbHelper.getInspectionExeHeaderByInspIdPlanId(
              widget.inspHeader!);
      if (data != null) {
        await ref
            .read(inspectionHeaderProvider.notifier)
            .fetchInspectionHeaders(data);
      }
      // }

      // Navigator.of(context).pop();
    }
    delayReason.clear();
    selectedSkipReason = '';
    reason.clear();

    ///TODO : Ranjit
/*    for (var task in numberOfCompletedTasks) {
      task.p_mode = "";
      await DbHelper.updateCiltExecTask(task);
      ref.read(ciltExecuteTaskListProvider.notifier).updateCiltExexTask(task);
    }*/

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text(AppLocalizations.of(context)!.submitted_successfully_insp),
        duration: const Duration(seconds: 2),
      ),
    );

    if (mounted) {
      final plant = ref.read(plantProvider);
      final plantSection = ref.read(plantSectionProvider);
      final shift = ref.read(shiftProvider);
      final searchkey = ref.read(roundsSearchProvider);
      await ref.read(filterInspectionHeaderListProvider.notifier).filter(plant, plantSection, shift, searchkey, ref);


      if (ScreenType.desktop != UIHelper().getScreenType(context)) {
        Navigator.pop(context, inspHeader);
      } else {
        await ref
            .read(inspExecuteTaskListProvider.notifier)
            .getInspExecuteTaskList();

        await ref.read(inspectionTaskNotifier.notifier).filter(
            widget.inspectionPlanHeader?.plan_id.toString() ?? "", "", ref);
      }
    }
   // print('inspection')
  if(!kIsWeb){
     INSP_EXEC_HEADER? newExecHeader = await DbHelper.getInspectionExecHeader(inspHeader);
   
   
   if(newExecHeader != null){
     await ref.read(inspectionHeaderProvider.notifier).fetchInspectionHeaders(inspHeader);
   }
      if ((newExecHeader!.syncStatus.index == 2)) {
     List<INSP_EXEC_TASK> inspExecTasks = ref
         .read(inspExecuteTaskListProvider.notifier)
         .findAllInspExecOfInsp(inspHeader);

     for (var task in inspExecTasks) {
       if (task.p_mode == AppConstants.modified) {
         await DbHelper.updateInspExecTask(task);
       }
     }
     await ref
         .read(inspExecuteTaskListProvider.notifier)
         .getInspExecuteTaskList();

     if (mounted) {
       setState(() {});
     }
   }
  }
   
  }

  Future<void> getSubmitWithoutCloseAction(INSP_EXEC_HEADER inspHeader) async {
    inspHeader.p_mode = AppConstants.modified;
    if (delayReason.text.isNotEmpty) {
      inspHeader.delay_comments = delayReason.text;
    }

    await DbHelper().markIrrelevantDependentTasks(
        planHeader: widget.inspectionPlanHeader!, execHeader: inspHeader);
    await AppDatabaseManager().update(
        DBInputEntity(INSP_EXEC_HEADER.TABLE_NAME, inspHeader.toJson()));
    List<INSP_EXEC_TASK> insp_exec_tasks = ref
        .read(inspExecuteTaskListProvider.notifier)
        .findAllInspExecOfInsp(inspHeader);

    List<DOCUMENT_HEADER> headerData = [];

    for (var task in insp_exec_tasks) {
      final documentHeaders = ref
          .watch(inspectionTaskExecDocumentHeaderProvider.notifier)
          .getDocumentHeadersForInspectionTask(task);
      if (documentHeaders != null) {
        headerData.addAll(documentHeaders);
      }
    }

    List<DOCUMENT_HEADER> documents = headerData
        .where((element) => element.objectStatus == ObjectStatus.add)
        .toList();

    if (kIsWeb) {
      UIHelper().progressDialog(
          context: context, message: "Submiting Inspection Execution");
      for (DOCUMENT_HEADER document in documents) {
        var doc = await DbHelper()
            .getAttachmentFromIndexDbByUid(document.doc_id ?? "");
        await SyncEngine().uploadAttachmentSync(
            doc ?? "", document.file_name ?? "", document.doc_id ?? "");
      }
      await PAHelper.addDocumentInSyncMode(context, documents);
    } else {
      await PAHelper.addDocumentInAsyncMode(context, documents);
    }

    if (!kIsWeb) {
      {
        await PAHelper.modifyInspExecInAsyncMode(context, inspHeader);
      }
    } else {
      Result result =
          await PAHelper.modifyInspExecInSyncMode(context, inspHeader);
      Navigator.of(context).pop();
      // if ((result.body['INSP_EXEC'] as List).first['INSP_EXEC_HEADER'] !=
      //     null) {
      await ref
          .read(inspExecuteTaskListProvider.notifier)
          .getInspExecuteTaskList();
      INSP_EXEC_HEADER? data =
          await DbHelper.getInspectionExeHeaderByInspIdPlanId(inspHeader);
      if (data != null) {
        await ref
            .read(inspectionHeaderProvider.notifier)
            .fetchInspectionHeaders(data);
      }
      // }
    }
    delayReason.clear();
    selectedSkipReason = '';
    reason.clear();

    ///TODO : Ranjit
/*
    for (var task in numberOfCompletedTasks) {
      await DbHelper.updateCiltExecTask(task);
    }
*/
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text(AppLocalizations.of(context)!.submitted_successfully_insp),
        duration: const Duration(seconds: 2),
      ),
    );

    if (mounted) {
      if (ScreenType.desktop != UIHelper().getScreenType(context)) {
        Navigator.pop(context, inspHeader);
      } else {
        setState(() {});
        // final ciltHeaders = ref.read(ciltHeaderListProvider);
        // ref
        //     .read(ciltTasksProvider.notifier)
        //     .fetchTasksForAllHeaders(ciltHeaders);
        // ref
        //     .read(ciltExecuteProvider.notifier)
        //     .getAllExecTasksForAllExecHeaders(ciltHeaders);
        // ref
        //     .read(inspectionPlanListHeaderProvider.notifier)
        //     .fetchInspectionPlanListHeaders();
        await ref
            .read(inspExecuteTaskListProvider.notifier)
            .getInspExecuteTaskList();
        await ref.read(inspectionTaskNotifier.notifier).filter(
            widget.inspectionPlanHeader?.plan_id.toString() ?? "", "", ref);

        ///TODO sneha Check for submit
        /*await ref
            .read(inspectionHeaderProvider.notifier)
            .fetchInspectionHeaders(widget.inspHeader!);*/
      }
    }
    ref.read(inspectionToggleStateProvider.notifier).setToggleState(false);
   
   if(!kIsWeb){
       INSP_EXEC_HEADER? newExecHeader = await DbHelper.getInspectionExecHeader(inspHeader);
   
   
   if(newExecHeader != null){
     await ref.read(inspectionHeaderProvider.notifier).fetchInspectionHeaders(inspHeader);
   }

         if ((newExecHeader!.syncStatus.index == 2)) {
     List<INSP_EXEC_TASK> inspExecTasks = ref
         .read(inspExecuteTaskListProvider.notifier)
         .findAllInspExecOfInsp(inspHeader);

     for (var task in inspExecTasks) {
       if (task.p_mode == AppConstants.modified) {
         await DbHelper.updateInspExecTask(task);
       }
     }
     await ref
         .read(inspExecuteTaskListProvider.notifier)
         .getInspExecuteTaskList();

     if (mounted) {
       setState(() {});
     }
   }
   }

  }
}
