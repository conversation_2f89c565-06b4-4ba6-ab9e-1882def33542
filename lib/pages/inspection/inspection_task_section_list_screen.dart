import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:rounds/utils/app_colors.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import '../../../providers/inspection/inspection_plan_header_provider.dart';
import '../../be/ASSET_HEADER.dart';
import '../../be/DOCUMENT_HEADER.dart';
import '../../be/INSPECTION_SECTION.dart';
import '../../be/INSPECTION_TASK.dart';
import '../../be/INSP_EXEC_ACTION.dart';
import '../../be/INSP_EXEC_HEADER.dart';
import '../../be/INSP_EXEC_SEC.dart';
import '../../be/INSP_EXEC_TASK.dart';
import '../../be/LOCATION_HEADER.dart';
import '../../helpers/db_helper.dart';
import '../../helpers/pa_helper.dart';
import '../../helpers/ui_helper.dart';
import '../../providers/assets/asset_provider.dart';
import '../../providers/assets/floc_provider.dart';
import '../../providers/attachments/attachment_provider.dart';
import '../../providers/fault/fault_header_provider.dart';
import '../../providers/fault/fault_type_provider.dart';
import '../../providers/inspection/inspection_header_provider.dart';
import '../../providers/inspection/inspection_task_provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/app_constants.dart';
import '../../utils/utils.dart';
import '../../widgets/skip_button.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'inspection_task_list_screen.dart' show InspectionTaskListScreen;

class InspectionTaskSectionListScreen extends ConsumerStatefulWidget {
  const InspectionTaskSectionListScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _InspectionTaskSectionListScreenState();
}

class _InspectionTaskSectionListScreenState
    extends ConsumerState<InspectionTaskSectionListScreen> {
  TextEditingController delayReason = TextEditingController();
  TextEditingController reason = TextEditingController();

  @override
  void dispose() {
    // TODO: implement dispose
    delayReason.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final inspPlanSectionHeader =
        ref.watch(inspectionPlanSectionListHeaderProvider);
    final inspHeader = ref.watch(inspectionHeaderProvider);
    final inspExecSecList = ref.watch(inspectionExecSectionListHeaderProvider);
    final skipReasonList = ref.watch(skipReasonListInspProvider.notifier).state;
    dynamic progressResult = _calulateProgress(inspHeader);
    ScrollController _scrollController = ScrollController();
    return inspPlanSectionHeader.isNotEmpty
        ? ListView.builder(
            controller: _scrollController,
            shrinkWrap: true,
            physics: const ScrollPhysics(),
            itemCount: inspPlanSectionHeader.length,
            itemBuilder: (context, index) {
              final section = inspPlanSectionHeader[index];
              INSP_EXEC_SEC? execSecHeader = inspExecSecList.firstWhereOrNull(
                  (element) => element.section_id == section.section_id);
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 20, bottom: 0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: RichText(
                            overflow: TextOverflow.ellipsis,
                              text: TextSpan(
                                  text: section.title.toString(),
                                  style: TextStyle(
                                    fontSize: 18,
                                    color: AppColors.titleTextColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  children: [
                                TextSpan(
                                    text: _getSectionAdditionalInfo(section),
                                    style: TextStyle(
                                        fontSize: 18,
                                        color: AppColors.titleTextColor,
                                        fontWeight: FontWeight.normal))
                              ])),
                        ),
                        isExecutionVisible()
                            ? Column(
                                children: [
                                  Visibility(
                                    visible: ref
                                        .watch(inspExecuteTaskListProvider
                                            .notifier)
                                        .isSectionCompleted(
                                            inspTask: ref
                                                .watch(inspectionTaskNotifier),
                                            section_id: section.section_id ?? 0,
                                            inspHeader: ref.watch(
                                                inspectionHeaderProvider)),
                                    child: (ref.watch(userProvider)?.user_id !=
                                                inspHeader.assigned_to ||
                                            inspHeader.status == "REJECTED")
                                        ? const SizedBox.shrink()
                                        : SkipButton(
                                            isActive: ref
                                                .watch(
                                                    inspExecuteTaskListProvider
                                                        .notifier)
                                                .isSectionCompleted(
                                                  inspTask: ref.watch(
                                                      inspectionTaskNotifier),
                                                  section_id:
                                                      section.section_id ?? 0,
                                                  inspHeader: ref.watch(
                                                      inspectionHeaderProvider),
                                                ),
                                            onPressed: () {
                                              if (inspHeader.status !=
                                                  AppConstants
                                                      .STATE_TASK_COMP) {
                                                performSkip(
                                                    context,
                                                    section,
                                                    ref,
                                                    inspHeader,
                                                    execSecHeader);
                                              }
                                            },
                                            width: 70,
                                            height: 30,
                                          ),
                                  ),
                                  if (execSecHeader?.reason != null &&
                                      execSecHeader?.reason != "")
                                    Text(skipReasonList
                                        .where((element) =>
                                            element.reason ==
                                            execSecHeader!.reason!)
                                        .toList()[0]
                                        .description!)
                                ],
                              )
                            : SizedBox()
                      ],
                    ),
                  ),
                  InspectionTaskListScreen(section: section, scrollController: _scrollController,sectionList: inspPlanSectionHeader.length,secIndex: index,)
                ],
              );
            },
          )
        : progressResult["totalTasks"] == progressResult["completedTasks"] &&
                ref.watch(inspectionToggleStateProvider)
            ? Center(
                child: Text(
                "${AppLocalizations.of(context)!.successfully_completed_all_tasks} \n ${AppLocalizations.of(context)!.great_job}",
                textAlign: TextAlign.center,
              ))
            : Center(child: Text(AppLocalizations.of(context)!.no_tasks_found));
  }

  String _getSectionAdditionalInfo(INSPECTION_SECTION section) {
    // Scenario 4: No technical objects
    if (section.location_id == null && section.asset_no == null) {
      return "";
    }

    // Scenario 3: Both location and asset exist - show only asset info
    if (section.location_id != null && section.asset_no != null) {
      ASSET_HEADER? assetHeader = ref
          .read(assetHeaderProvider.notifier)
          .findAssetHeaderById(section.asset_no ?? -1);
      return UIHelper.formatIdAndDescription(
          section.asset_no.toString(), assetHeader?.description,
          wrap: WrapType.id);
    }

    // Scenario 2: Only asset exists
    if (section.asset_no != null) {
      ASSET_HEADER? assetHeader = ref
          .read(assetHeaderProvider.notifier)
          .findAssetHeaderById(section.asset_no ?? -1);
      return UIHelper.formatIdAndDescription(
          section.asset_no.toString(), assetHeader?.description,
          wrap: WrapType.id);
    }

    // Scenario 1: Only location exists
    if (section.location_id != null) {
      LOCATION_HEADER? locationHeader = ref
          .read(flocHeaderProvider.notifier)
          .findLocationHeadById(section.location_id ?? "");
      return UIHelper.formatIdAndDescription(
          section.location_id, locationHeader?.description,
          wrap: WrapType.id);
    }

    return "";
  }

  bool isExecutionVisible() {
    final role = ref.watch(roleProvider);
    DateTime now = DateTime.now();
    String startAT = ref.read(inspectionHeaderProvider).start_at.toString();
    String startOn = ref.read(inspectionHeaderProvider).start_on.toString();
    DateTime executionDateTime = Utils.getTimeStamp(startOn, startAT);
    if (role != null) {
      if ((UIHelper.isExecute(role.inspection!) &&
          executionDateTime.isBefore(now))) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  void performSkip(
      BuildContext context,
      INSPECTION_SECTION section,
      WidgetRef ref,
      INSP_EXEC_HEADER inspHeader,
      INSP_EXEC_SEC? execSecHeader) {
    ref.read(skipReasonInspProvider.notifier).clearInspSkipReason();
    onSkipYes(context, section, ref, inspHeader, execSecHeader);
  }

  onSkipYes(BuildContext context, INSPECTION_SECTION section, WidgetRef ref,
      INSP_EXEC_HEADER inspHeader, INSP_EXEC_SEC? execSecHeader) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return true;
          },
          child: StatefulBuilder(
            builder: (context, setState) {
              return AlertDialog(
                contentPadding: const EdgeInsets.all(5),
                content: ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxWidth: 400,
                  ),
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                AppLocalizations.of(context)!.select_reason,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold, fontSize: 14),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          getReasonDropdown(context, ref, setState),
                          const SizedBox(height: 10),
                          TextField(
                            style: const TextStyle(fontSize: 14),
                            controller: reason,
                            onChanged: (value) {
                              TextSelection previousSelection =
                                  reason.selection;
                              reason.text = value;
                              reason.selection = previousSelection;
                              setState(() {});
                            },
                            maxLines: 3,
                            decoration: InputDecoration(
                              hintText: AppLocalizations.of(context)!
                                  .additionalComment,
                              border: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              focusedBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              enabledBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              contentPadding: const EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 10),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: reason.text.isEmpty || selectedSkipReason.isEmpty
                        ? null
                        : () async {
                            if (reason.text == '' || reason.text.isEmpty) {
                              UIHelper.showInfoDialogWithtitleAndDescription(
                                  context,
                                  title: AppLocalizations.of(context)!.warning,
                                  description: AppLocalizations.of(context)!
                                      .please_add_additional_message);
                            } else {
                              await onSaveReason(reason.text, inspHeader,
                                  section, execSecHeader);
                            }
                          },
                    child: Text(
                      AppLocalizations.of(context)!.save,
                      style: TextStyle(
                        color: reason.text.isEmpty || selectedSkipReason.isEmpty
                            ? AppColors.grey
                            : AppColors.primaryColor,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      selectedSkipReason = '';
                      reason.clear();
                      Navigator.pop(context);
                    },
                    child: Text(
                      AppLocalizations.of(context)!.cancel,
                      style: TextStyle(
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  String selectedSkipReason = '';
  Widget getReasonDropdown(
      BuildContext context, WidgetRef ref, StateSetter setState) {
    final skipReasonList = ref
        .watch(skipReasonListProvider)
        .where((element) => (element.category! & 2) != 0)
        .toList();

    final skipReason = ref.watch(skipReasonInspProvider.notifier);

    final dropdownItems = skipReasonList.map((option) {
      return DropdownMenuItem<String>(
        value: option.description,
        child: Padding(
          padding: const EdgeInsets.only(left: 5.0),
          child: Text(option.description!, style: UIHelper.valueStyle()),
        ),
      );
    }).toList();

    if (dropdownItems.where((item) => item.value == '').isEmpty) {
      dropdownItems.insert(
        0,
        DropdownMenuItem<String>(
          value: '',
          child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(AppLocalizations.of(context)!.select,
                style: UIHelper.valueStyle()),
          ),
        ),
      );
    }
    return Container(
      width: MediaQuery.of(context).size.width,
      decoration: UIHelper.fieldDecoration(),
      child: DropdownButton<String>(
        elevation: 0,
        isExpanded: true,
        underline: const SizedBox(),
        value: selectedSkipReason.isNotEmpty ? selectedSkipReason : '',
        items: dropdownItems,
        onChanged: (newValue) {
          setState(() {
            selectedSkipReason = newValue!;
            skipReason.getInspSkipReason(selectedSkipReason);
          });
        },
      ),
    );
  }

  Future<void> onSaveReason(String reasonData, INSP_EXEC_HEADER inspHeader,
      INSPECTION_SECTION section, INSP_EXEC_SEC? execSecHeader) async {
    final inspectionPlanHeader = ref.watch(inspectionPlanHeaderProvider);
    final skipReason = ref.watch(skipReasonInspProvider.notifier).state;
    // ciltHeader.reason = skipReason.category.toString();

    await DbHelper.updateInspExecSection(
        section, skipReason.reason.toString(), inspHeader, reasonData);

    List<INSPECTION_TASK> list =
        await DbHelper.getInspectionTaskPlanListHeaderAll(
            section.plan_id.toString(), section.section_id.toString());
    List<INSP_EXEC_TASK> listExec =
        await DbHelper.getInspectionExeTaskPlanListHeaderAll(
            execSecHeader!.insp_id!, execSecHeader.section_id.toString());
    for (INSPECTION_TASK task in list) {
      if (task.dependent == "true") {
        await DbHelper().taskInspectionUpdate(
            task: task,
            status: AppConstants.STATE_TASK_COMP,
            inspHeader: inspHeader,
            reasonCode: '',
            skipreason: '',
            comment: '',
            // skipReason.category.toString(),
            // reasonData,
            p_mode: "M",
            dataString: null,
            dataDouble: null,
            dataBloc: null,
            isIrrelevant: 'true',
            isSkippingSection: true);
        INSP_EXEC_TASK? execTask = listExec.firstWhereOrNull((element) =>
            element.task_id == task.task_id &&
            element.insp_id == inspHeader.insp_id &&
            element.section_id == task.section_id);
        ref
            .read(numericInspectionHeaderProvider.notifier)
            .clearNumericValue(execTask!);
      } else {
        await DbHelper().taskInspectionUpdate(
          task: task,
          status: AppConstants.STATE_TASK_COMP,
          inspHeader: inspHeader,
          reasonCode: skipReason.reason.toString(),
          skipreason: reasonData,
          // null,
          // null,

          ///TODO sneha reason code
          /*         skipReason.category.toString(),
            reasonData,*/
          p_mode: "M",
          dataString: null,
          dataDouble: null,
          dataBloc: null,
          isSkipped: 'true',
          isSkippingSection: true,
        );
        INSP_EXEC_TASK? execTask = listExec.firstWhereOrNull((element) =>
            element.task_id == task.task_id &&
            element.insp_id == inspHeader.insp_id &&
            element.section_id == task.section_id);
        ref
            .read(numericInspectionHeaderProvider.notifier)
            .clearNumericValue(execTask!);
      }

      await ref
          .read(inspectionPlanTaskListHeaderProvider.notifier)
          .fetchInspectionPlanTaskListHeaders(
            section: section,
            plantId: inspHeader.plant_id ?? '',
            header: inspHeader,
          );

      await ref.read(inspTasksProvider.notifier).getInspTasks(
          inspHeader.plan_id!,
          header: inspHeader,
          section: section,
          plantId: inspHeader.plant_id.toString());

/*      await ref
          .read(inspectionPlanTaskListHeaderProvider.notifier)
          .fetchInspectionPlanTaskListHeaders(
              plantId: inspHeader.plant_id.toString(),
              header: inspHeader,
              section: section);*/

      await ref.read(inspExecuteProvider.notifier).getInspExecute(inspHeader);
      final inspCompleted = ref.watch(inspCompletedProvider.notifier);
      final inspExecHeader = ref.watch(inspExecuteTaskListProvider.notifier);
      await inspCompleted.inspCompleted(inspHeader);
      await inspExecHeader.getInspExecuteTaskList();

      await ref
          .read(inspExecuteTaskListProvider.notifier)
          .getInspExecuteTaskList();
      await ref.read(inspectionTaskNotifier.notifier).filter(
          ref.read(inspectionPlanHeaderProvider).plan_id.toString(), "", ref);
      await ref
          .read(inspExecuteTaskListProvider.notifier)
          .getInspExecuteTaskList();
      await ref.read(inspTasksProvider.notifier).getInspTasks(
          inspHeader.plan_id!,
          header: inspHeader,
          section: section,
          plantId: inspHeader.plant_id.toString());
/*      await ref
          .read(inspectionPlanTaskListHeaderProvider.notifier)
          .fetchInspectionPlanTaskListHeaders(
              plantId: inspHeader.plant_id.toString(),
              header: inspHeader,
              section: section);*/

      await ref.read(inspExecuteProvider.notifier).getInspExecute(inspHeader);
    }

    if (ref.read(inspectionToggleStateProvider)) {
      ref.read(inspectionTaskNotifier.notifier).fetchIncompleteTasks(
          (inspectionPlanHeader.plan_id.toString() ?? ""),
          ref.read(inspectionHeaderProvider),
          ref);
    }

    //ref.invalidate(inspectionPlanTaskListHeaderProvider);
   // Future.microtask(() => ref.invalidate(inspectionPlanTaskListHeaderProvider));
    delayReason.clear();
    reason.clear();
    ref.read(skipReasonInspProvider.notifier).clearInspSkipReason();
    selectedSkipReason = '';
    //if (mounted) {
      Navigator.of(context).pop();
   // }

    await _getProgressValue(inspHeader, section);
    INSPECTION_SECTION? inspection_section =
        await DbHelper.getInspectionSectionByPlanId(
            inspHeader.plan_id.toString());
    await ref
        .read(inspectionPlanTaskListHeaderProvider.notifier)
        .fetchInspectionPlanTaskListHeaders(
            plantId: inspHeader.plant_id.toString(),
            header: inspHeader,
            section: inspection_section!);
    await ref.read(inspExecuteProvider.notifier).getInspExecute(inspHeader);

    final inspectionTask = ref.watch(inspectionTaskProvider.notifier).state;
    INSP_EXEC_TASK? execTask = ref
        .read(inspExecuteTaskListProvider.notifier)
        .getInspectionExecTaskHeaderByTask(inspectionTask, inspHeader);
    if (execTask != null) {
      ref
          .read(inspectionExecTaskProvider.notifier)
          .getInspectionExecTask(execTask);
      ref
        .read(numericInspectionHeaderProvider.notifier)
        .clearNumericValue(execTask);
    }
    
  }

  Future<Map<String, dynamic>> _getProgressValue(
      INSP_EXEC_HEADER? inspHeader, INSPECTION_SECTION section) async {
    if (inspHeader != null) {
      await ref.watch(inspTasksProvider.notifier).getInspTasks(
          inspHeader.plan_id!,
          header: inspHeader,
          section: section,
          plantId: inspHeader.plant_id.toString());
      /* await ref
          .watch(inspectionPlanTaskListHeaderProvider.notifier)
          .fetchInspectionPlanTaskListHeaders(
              plantId: inspHeader.plant_id.toString(),
              header: inspHeader,
              section: section);*/

      await ref.watch(inspExecuteProvider.notifier).getInspExecute(inspHeader);
      final numberOfTasks =
          ref.watch(inspTasksProvider)[inspHeader.plan_id] ?? [];
/*
       final numberOfTasks =
           ref.watch(inspectionPlanTaskListHeaderProvider).data ?? [];
*/

      final numberOfCompletedTasks =
          ref.watch(inspExecuteProvider)[inspHeader.insp_id] ?? [];
      int totalTasks = numberOfTasks
          .where((element) =>
              element.plan_id.toString() == inspHeader.plan_id.toString())
          .length;
      int completedTasks = numberOfCompletedTasks.length;
      numberOfCompletedTasks
          .where((element) =>
              element.insp_id.toString() == inspHeader.insp_id.toString())
          .length;
      if (totalTasks == completedTasks) {
        Future.delayed(Duration.zero, () async {
          await showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) {
              return Dialog(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxWidth: 400,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Center(
                                child: Text(
                                  "${AppLocalizations.of(context)!.congratulations} 🎉",
                                  style: UIHelper.titleStyle16(),
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                              child: Icon(
                                Icons.cancel_outlined,
                                color: AppColors.black,
                                size: 20,
                              ),
                            ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 16.0,
                          ),
                          child: Text(
                            AppLocalizations.of(context)!.all_tasks_completed,
                            textAlign: TextAlign.center,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryColor,
                            minimumSize: const Size(double.infinity, 40),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                          onPressed: () async {
                            final current = DateTime.now();

                            DateTime endTime = Utils.getTimeStamp(
                                inspHeader.start_on.toString(),
                                inspHeader.end_at.toString());
                            Navigator.of(context).pop();
                            if (current.isAfter(endTime)) {
                              showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (context) {
                                  return AlertDialog(
                                    title: Text(AppLocalizations.of(context)!
                                        .please_provide_reason_for_submission_delay),
                                    content: TextField(
                                      decoration: InputDecoration(
                                        hintText: AppLocalizations.of(context)!
                                            .reason_for_submission_delay,
                                        border: OutlineInputBorder(
                                          borderSide:
                                              BorderSide(color: AppColors.grey),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderSide:
                                              BorderSide(color: AppColors.grey),
                                        ),
                                        enabledBorder: OutlineInputBorder(
                                          borderSide:
                                              BorderSide(color: AppColors.grey),
                                        ),
                                        contentPadding:
                                            const EdgeInsets.symmetric(
                                          vertical: 10,
                                          horizontal: 10,
                                        ),
                                      ),
                                      controller: delayReason,
                                    ),
                                    actions: [
                                      TextButton(
                                        onPressed: () async {
                                          final progressValue =
                                              await _calculateProgress(
                                                  inspHeader, section);
                                          Navigator.of(context).pop();
                                          if (progressValue.isNotEmpty) {
                                            if (progressValue["totalTasks"] ==
                                                progressValue[
                                                    "completedTasks"]) {
                                              await getSubmitWithCloseAction(
                                                  inspHeader, section);
                                            } else {
                                              await getSubmitWithoutCloseAction(
                                                  inspHeader, section);
                                            }
                                          }
                                        },
                                        child: Text(
                                            AppLocalizations.of(context)!
                                                .submit),
                                      ),
                                      TextButton(
                                        onPressed: () {
                                          delayReason.clear();
                                          Navigator.of(context).pop();
                                        },
                                        child: Text(
                                            AppLocalizations.of(context)!
                                                .cancel),
                                      ),
                                    ],
                                  );
                                },
                              );
                            } else {
                              final progressValue =
                                  await _calculateProgress(inspHeader, section);
                              if (progressValue.isNotEmpty) {
                                if (progressValue["totalTasks"] ==
                                    progressValue["completedTasks"]) {
                                  await getSubmitWithCloseAction(
                                      inspHeader, section);
                                } else {
                                  await getSubmitWithoutCloseAction(
                                      inspHeader, section);
                                }
                              }
                            }
                          },
                          child: Text(
                            AppLocalizations.of(context)!.submit,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(height: 4),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        });
      }
      if (totalTasks == 0) {
        return {
          'percentage': 0.0,
          'completedTasks': 0,
          'totalTasks': 0,
        };
        /*return 0.0;*/
      }
      double percentage =
          double.parse((completedTasks / totalTasks).toStringAsFixed(2));
      return {
        'percentage': percentage,
        'completedTasks': completedTasks,
        'totalTasks': totalTasks,
      };
      /*return percentage/100;*/
    } else {
      return {
        'percentage': 0.0,
        'completedTasks': 0,
        'totalTasks': 0,
      };
    }
  }

  Future<Map<String, dynamic>> _calculateProgress(
      INSP_EXEC_HEADER? inspHeader, INSPECTION_SECTION section) async {
    if (inspHeader != null) {
      await ref.watch(inspTasksProvider.notifier).getInspTasks(
          inspHeader.plan_id!,
          header: inspHeader,
          section: section,
          plantId: inspHeader.plant_id.toString());
/*
      await ref
          .watch(inspectionPlanTaskListHeaderProvider.notifier)
          .fetchInspectionPlanTaskListHeaders(
              plantId: ciltHeader.plant_id.toString(),
              header: ciltHeader,
              section: section);
*/

      await ref.watch(inspExecuteProvider.notifier).getInspExecute(inspHeader);
      final numberOfTasks =
          ref.watch(inspTasksProvider)[inspHeader.plan_id] ?? [];
/*
      final numberOfTasks =
          ref.watch(inspectionPlanTaskListHeaderProvider).data ?? [];
*/

      final numberOfCompletedTasks =
          ref.watch(inspExecuteProvider)[inspHeader.insp_id] ?? [];
      int totalTasks = numberOfTasks
          .where((element) =>
              element.plan_id.toString() == inspHeader.plan_id.toString())
          .length;
      int completedTasks = numberOfCompletedTasks.length;
      numberOfCompletedTasks
          .where((element) =>
              element.insp_id.toString() == inspHeader.insp_id.toString())
          .length;

      if (totalTasks == 0) {
        return {
          'percentage': 0.0,
          'completedTasks': 0,
          'totalTasks': 0,
        };
        /*return 0.0;*/
      }
      double percentage =
          double.parse((completedTasks / totalTasks).toStringAsFixed(2));
      return {
        'percentage': percentage,
        'completedTasks': completedTasks,
        'totalTasks': totalTasks,
      };
      /*return percentage/100;*/
    } else {
      return {
        'percentage': 0.0,
        'completedTasks': 0,
        'totalTasks': 0,
      };
    }
  }

  Future<void> getSubmitWithCloseAction(
      INSP_EXEC_HEADER inspHeader, INSPECTION_SECTION section) async {
    if (delayReason.text.isNotEmpty) {
      inspHeader.delay_comments = delayReason.text;
    }

    final inspectionPlanHeader = ref.watch(inspectionPlanHeaderProvider);

    await DbHelper().markIrrelevantDependentTasks(
        planHeader: inspectionPlanHeader, execHeader: inspHeader);

    await AppDatabaseManager().update(
        DBInputEntity(INSP_EXEC_HEADER.TABLE_NAME, inspHeader.toJson()));
    final numberOfCompletedTasks = ref
            .watch(inspExecuteProvider)[inspHeader.insp_id] ??
        [].where((element) => element.cilt_id == inspHeader.insp_id).toList();
    List<Future<void>> updateTasks = [];
    for (var task in numberOfCompletedTasks) {
      await DbHelper.updateInspExecTask(task);
      INSP_EXEC_ACTION action = INSP_EXEC_ACTION(
          insp_id: inspHeader.insp_id,
          user_action: AppConstants.STATE_COMPLETED);
      action.fid = inspHeader.lid;

      var result = await AppDatabaseManager().execute(
          "select * from INSP_EXEC_ACTION where INSP_ID = '${inspHeader.insp_id.toString()}'");
      if (result.isNotEmpty) {
        updateTasks.add(AppDatabaseManager().update(
            DBInputEntity(INSP_EXEC_ACTION.TABLE_NAME, action.toJson())));
      } else {
        updateTasks.add(AppDatabaseManager().insert(
            DBInputEntity(INSP_EXEC_ACTION.TABLE_NAME, action.toJson())));
      }
    }
    await Future.wait(updateTasks);
    List<INSP_EXEC_TASK> INSP_EXEC_TASKs = ref
        .read(inspExecuteTaskListProvider.notifier)
        .findAllInspExecOfInsp(inspHeader);

    List<DOCUMENT_HEADER> headerData = [];

    for (var task in INSP_EXEC_TASKs) {
      final documentHeaders = ref
          .watch(inspectionTaskExecDocumentHeaderProvider.notifier)
          .getDocumentHeadersForInspectionTask(task);
      if (documentHeaders != null) {
        headerData.addAll(documentHeaders);
      }
    }

    List<DOCUMENT_HEADER> documents = headerData
        .where((element) => element.objectStatus == ObjectStatus.add)
        .toList();

    if (kIsWeb) {
      UIHelper().progressDialog(
          context: context, message: "Submiting Inspection Execution");

      for (DOCUMENT_HEADER document in documents) {
        var doc = await DbHelper()
            .getAttachmentFromIndexDbByUid(document.doc_id ?? "");
        await SyncEngine().uploadAttachmentSync(
            doc ?? "", document.file_name ?? "", document.doc_id ?? "");
      }
      await PAHelper.addDocumentInSyncMode(context, documents);
    } else {
      await PAHelper.addDocumentInAsyncMode(context, documents);
    }

    /*
    for (var task in numberOfCompletedTasks) {
      await DbHelper.updateCiltExecTask(task);
    }
*/

    if (!kIsWeb) {
      await PAHelper.modifyInspExecInAsyncMode(context, inspHeader);
    } else {
      Result result =
          await PAHelper.modifyInspExecInSyncMode(context, inspHeader);
      Navigator.of(context, rootNavigator: true).pop();
      // if ((result.body['INSP_EXEC'] as List).first['INSP_EXEC_HEADER'] !=
      //     null) {
      await ref
          .read(inspExecuteTaskListProvider.notifier)
          .getInspExecuteTaskList();
      INSP_EXEC_HEADER? data =
          await DbHelper.getInspectionExeHeaderByInspIdPlanId(inspHeader);
      if (data != null) {
        await ref
            .read(inspectionHeaderProvider.notifier)
            .fetchInspectionHeaders(data);
      }
      // }
    }
    delayReason.clear();
    selectedSkipReason = '';
    reason.clear();

    if (mounted) {
      if (UIHelper().getScreenType(context) != ScreenType.desktop) {
        Navigator.pop(context, inspHeader);
      } else {
        setState(() {});
        var inspPlanHeader = ref.read(inspectionPlanHeaderProvider);
        await ref
            .read(inspExecuteTaskListProvider.notifier)
            .getInspExecuteTaskList();
        await ref
            .read(inspectionTaskNotifier.notifier)
            .filter(inspPlanHeader.plan_id.toString() ?? "", "", ref);
      }
      final plant = ref.read(plantProvider);
      final plantSection = ref.read(plantSectionProvider);
      final shift = ref.read(shiftProvider);

      await ref
          .read(inspectionHeaderListProvider.notifier)
          .fetchInspectionListHeaders(plant, plantSection, shift, ref);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text(AppLocalizations.of(context)!.submitted_successfully_insp),
          duration: Duration(seconds: 2),
        ),
      );
    }
    
    if(!kIsWeb){
       INSP_EXEC_HEADER? newExecHeader = await DbHelper.getInspectionExecHeader(inspHeader);
   if(newExecHeader != null){
    await ref.read(inspectionHeaderProvider.notifier).fetchInspectionHeaders(inspHeader);
   }
    }
  }

  Future<void> getSubmitWithoutCloseAction(
      INSP_EXEC_HEADER inspHeader, INSPECTION_SECTION section) async {
    inspHeader.p_mode = AppConstants.modified;

    final inspectionPlanHeader = ref.watch(inspectionPlanHeaderProvider);

    await DbHelper().markIrrelevantDependentTasks(
        planHeader: inspectionPlanHeader, execHeader: inspHeader);

    final numberOfCompletedTasks = ref
            .watch(inspExecuteProvider)[inspHeader.insp_id] ??
        [].where((element) => element.insp_id == inspHeader.insp_id).toList();

    if (delayReason.text.isNotEmpty) {
      inspHeader.delay_comments = delayReason.text;
    }
    await AppDatabaseManager().update(
        DBInputEntity(INSP_EXEC_HEADER.TABLE_NAME, inspHeader.toJson()));

    List<INSP_EXEC_TASK> INSP_EXEC_TASKs = ref
        .read(inspExecuteTaskListProvider.notifier)
        .findAllInspExecOfInsp(inspHeader);

    List<DOCUMENT_HEADER> headerData = [];

    for (var task in INSP_EXEC_TASKs) {
      final documentHeaders = ref
          .watch(inspectionTaskExecDocumentHeaderProvider.notifier)
          .getDocumentHeadersForInspectionTask(task);
      if (documentHeaders != null) {
        headerData.addAll(documentHeaders);
      }
    }

    List<DOCUMENT_HEADER> documents = headerData
        .where((element) => element.objectStatus == ObjectStatus.add)
        .toList();

    if (kIsWeb) {
      UIHelper().progressDialog(
          context: context, message: "Submiting Inspection Execution");
      for (DOCUMENT_HEADER document in documents) {
        var doc = await DbHelper()
            .getAttachmentFromIndexDbByUid(document.doc_id ?? "");
        await SyncEngine().uploadAttachmentSync(
            doc ?? "", document.file_name ?? "", document.doc_id ?? "");
      }
      await PAHelper.addDocumentInSyncMode(context, documents);
    } else {
      await PAHelper.addDocumentInAsyncMode(context, documents);
    }

    if (!kIsWeb) {
      await PAHelper.modifyInspExecInAsyncMode(context, inspHeader);
    } else {
      Result result =
          await PAHelper.modifyInspExecInSyncMode(context, inspHeader);
      Navigator.of(context, rootNavigator: true).pop();
      // if ((result.body['INSP_EXEC'] as List).first['INSP_EXEC_HEADER'] !=
      //     null) {
      await ref
          .read(inspExecuteTaskListProvider.notifier)
          .getInspExecuteTaskList();
      INSP_EXEC_HEADER? data =
          await DbHelper.getInspectionExeHeaderByInspIdPlanId(inspHeader);
      if (data != null) {
        await ref
            .read(inspectionHeaderProvider.notifier)
            .fetchInspectionHeaders(data);
      }
      // }
    }

    delayReason.clear();
    selectedSkipReason = '';
    reason.clear();

    ///TODO : Ranjit
/*    for (var task in numberOfCompletedTasks) {
      await DbHelper.updateCiltExecTask(task);
    }*/

    if (mounted) {
      if (UIHelper().getScreenType(context) != ScreenType.desktop) {
        Navigator.pop(context, inspHeader);
      } else {
        setState(() {});
        var inspPlanHeader = ref.read(inspectionPlanHeaderProvider);
        ref.read(inspExecuteTaskListProvider.notifier).getInspExecuteTaskList();
        await ref
            .read(inspectionTaskNotifier.notifier)
            .filter(inspPlanHeader.plan_id.toString() ?? "", "", ref);
      }
      final plant = ref.read(plantProvider);
      final plantSection = ref.read(plantSectionProvider);
      final shift = ref.read(shiftProvider);

      await ref
          .read(inspectionHeaderListProvider.notifier)
          .fetchInspectionListHeaders(plant, plantSection, shift, ref);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text(AppLocalizations.of(context)!.submitted_successfully_insp),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  Map<String, dynamic> _calulateProgress(INSP_EXEC_HEADER? ciltHeader) {
    if (ciltHeader != null) {
      final numberOfTasks =
          ref.watch(inspTasksProvider)[ciltHeader.plan_id] ?? [];
      /*   final numberOfTasks =
          ref.watch(inspectionPlanTaskListHeaderProvider).data ?? [];
*/
      final numberOfCompletedTasks =
          ref.watch(inspExecuteProvider)[ciltHeader.insp_id] ?? [];
      int totalTasks = numberOfTasks
          .where((element) =>
              element.plan_id.toString() == ciltHeader.plan_id.toString())
          .length;
      int completedTasks = numberOfCompletedTasks.length;
      numberOfCompletedTasks
          .where((element) =>
              element.insp_id.toString() == ciltHeader.insp_id.toString())
          .length;
      if (totalTasks == 0) {
        return {
          'percentage': 0.0,
          'completedTasks': 0,
          'totalTasks': 0,
        };
      }
      double percentage =
          double.parse((completedTasks / totalTasks).toStringAsFixed(2));
      return {
        'percentage': percentage,
        'completedTasks': completedTasks,
        'totalTasks': totalTasks,
      };
    } else {
      return {
        'percentage': 0.0,
        'completedTasks': 0,
        'totalTasks': 0,
      };
    }
  }
}
