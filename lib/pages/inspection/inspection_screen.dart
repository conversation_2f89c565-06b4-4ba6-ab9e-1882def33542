import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/be/INSP_EXEC_HEADER.dart';
import 'package:rounds/be/ROLE_HEADER.dart';
import 'package:rounds/helpers/pa_helper.dart';
import 'package:rounds/helpers/pushNotification_helper.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/models/intractive_Item_Model.dart';
import 'package:rounds/pages/dashboard/widgets/top_header.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:rounds/pages/home/<USER>';
import 'package:rounds/pages/inspection/adhoc_cilt/add_adhoc_cilt.dart';
import 'package:rounds/pages/inspection/inspection_detail_screen.dart';
import 'package:rounds/pages/widgets/search_bar.dart';
import 'package:rounds/providers/cilt/cilt_plan_header_provider.dart';
import 'package:rounds/providers/fault/fault_type_provider.dart';
import 'package:rounds/providers/inspection/inspection_header_provider.dart';
import 'package:rounds/providers/inspection/inspection_plan_header_provider.dart';
import 'package:rounds/services/app_notifier.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/notification_toast.dart';
import 'package:rounds/utils/utils.dart';
import 'package:rounds/widgets/calender.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../providers/cilt/cilt_header_provider.dart';
import '../../providers/fault/fault_header_provider.dart';
import '../../providers/job_creation/job_header_provider.dart';
import '../../providers/user_provider.dart';
import '../cilt/cilt_detail_screen.dart';
import 'inspection_list_screen.dart';

final roundsSearchProvider = StateProvider<String>((ref) => '');

ValueNotifier<InteractiveItemModel?> roundDetailViewNotifier =
    ValueNotifier<InteractiveItemModel?>(null);

class InspectionScreen extends ConsumerStatefulWidget {
  const InspectionScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _InspectionScreenState();
}

class _InspectionScreenState extends ConsumerState<InspectionScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController animationController;
  late Animation<Offset> drawerAnimation;
  AppNotifier appNotifier = AppNotifier();
  TextEditingController searchController = TextEditingController();
  late final StreamSubscription<void> subscription;

  // Added for connectivity management
  ConnectivityResult _connectivityStatus = ConnectivityResult.none;
  late StreamSubscription<ConnectivityResult> _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: AppColors.white,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
      ),
    );
    _intitNotifier();
    animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    drawerAnimation =
        Tween<Offset>(begin: const Offset(1.0, 0.0), end: Offset.zero)
            .animate(animationController);

    // Initialize connectivity
    _initializeConnectivityState();
    _connectivitySubscription =
        Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      if (mounted) {
        setState(() {
          _connectivityStatus = result;
        });
      }
    });
  }

  Future<void> _initializeConnectivityState() async {
    ConnectivityResult result;
    try {
      result = await Connectivity().checkConnectivity();
    } on PlatformException catch (e) {
      if (kDebugMode) {
        print('Could not check connectivity status: $e');
      }
      result = ConnectivityResult.none;
    }

    if (!mounted) {
      return;
    }

    setState(() {
      _connectivityStatus = result;
    });
  }

  _intitNotifier() {
    appNotifier.notifyInfoMessages((data) async {
      if (mounted) {
        if (data[EventSyncStatusFieldType] ==
            EventSyncStatusTypeInboxProcessingComplete) {}
      }
    });
    appNotifier.notifySyncStatus((data) async {
      if (mounted) {
        if (data[EventSyncStatusFieldType] ==
            EventSyncStatusTypeInboxProcessingComplete) {
          if (mounted) {
            final plant = ref.watch(plantProvider);
            final plantSection = ref.watch(plantSectionProvider);
            final shift = ref.watch(shiftProvider);
            ref
                .read(ciltHeaderListProvider.notifier)
                .filter(plant, plantSection, shift, searchController.text, ref);
          }
        }
      }
    });

    if (kIsWeb) {
      subscription =
          PushNotifications.onNotificationProcessed.listen((_) async {
        debugPrint("📦 Notification queue processed, updating UI");
        await updateTheState();
        if (mounted) {
          NotificationToastManager.showRefreshToast(context,
              onRefresh: () async {
            final plant = ref.watch(plantProvider);
            final plantSection = ref.watch(plantSectionProvider);
            final shift = ref.watch(shiftProvider);
            final searchProvider =
                ref.read(roundsSearchProvider.notifier).state;
            ref
                .read(filteredCiltProvider.notifier)
                .filter(plant, plantSection, shift, searchProvider, ref);
            ref
                .read(inspectionHeaderListProvider.notifier)
                .fetchInspectionListHeaders(plant, plantSection, shift, ref);
            await ref
                .read(faultHeaderListProvider.notifier)
                .fetchFaultHeaderList(plant);
            ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
            ref.read(faultTypeListProvider.notifier).fetchFaultTypeList();
            ref.read(faultModeHeaderListProvider.notifier).fetchFaultModeList();
          });
        }
      });
    }
  }

  Future<void> refreshData() async {
    if (!kIsWeb) {
      int inBoxCount = await SettingsHelper().getInboxCount();
      int outBoxCont = await SettingsHelper().getOutboxCount();
      await downloadTransactionData();
    } else {
      List<Future> futures = [
        PAHelper.getInspectionPlanInSyncMode(context),
        PAHelper.getCiltPlanInSyncMode(context),
        PAHelper.getInspectionInSyncMode(context),
        PAHelper.getCiltInSyncMode(context),
        PAHelper.getFaultInSyncMode(context),
      ];
      UIHelper().progressDialog(
          context: context, message: AppLocalizations.of(context)!.refreshing);
      await Future.wait(futures).then((value) async {
        await updateTheState();
        if (mounted) {
          Navigator.of(context);
        }
      });
    }
  }

  Future<void> updateTheState() async {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final shift = ref.read(shiftProvider.notifier).state;
    await ref
        .read(ciltHeaderListProvider.notifier)
        .fetchCiltListHeaders(plant, plantSection, shift, ref);
    await ref
        .read(ciltPlanListHeaderProvider.notifier)
        .fetchCiltPlanListHeaders();
    await ref
        .read(inspectionHeaderListProvider.notifier)
        .fetchInspectionListHeaders(plant, plantSection, shift, ref);
    await ref
        .read(inspectionPlanListHeaderProvider.notifier)
        .fetchInspectionPlanListHeaders();
    await ref
        .read(faultHeaderListProvider.notifier)
        .fetchFaultHeaderList(plant);
    ref.read(faultTypeListProvider.notifier).fetchFaultTypeList();
    ref.read(faultModeHeaderListProvider.notifier).fetchFaultModeList();
    ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
    ref.read(filteredCiltProvider.notifier).filter(
          plant,
          plantSection,
          shift,
          ref.read(roundsSearchProvider.notifier).state,
          ref,
        );
    await ref
        .read(ciltTasksProvider.notifier)
        .fetchTasksForAllHeaders(ref.read(ciltHeaderListProvider));
    await ref.read(inspTasksProvider.notifier).fetchTasksForAllHeaders(
        ref.read(inspectionHeaderListProvider), ref.read(plantProvider));
    await ref
        .read(ciltExecuteProvider.notifier)
        .getAllExecTasksForAllExecHeaders(ref.read(ciltHeaderListProvider));
    await ref
        .read(inspExecuteProvider.notifier)
        .getAllExecTasksForAllExecHeaders(
            ref.read(inspectionHeaderListProvider));
  }

  final ScrollController _scrollController = ScrollController();

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeOut,
      );
    });
  }

  downloadTransactionData() async {
    List<Future> futures = [SyncEngine().receive()];
    try {
      await Future.wait(futures);
      UIHelper.showSnackBar(context,
          message: AppLocalizations.of(context)!.refreshing);
    } catch (error) {
      UIHelper.showSnackBar(context, message: error.toString());
    }
  }

  @override
  void dispose() {
    _connectivitySubscription.cancel();
    if (kIsWeb && mounted) { // Check mounted for subscription as it's late final
      subscription.cancel();
    }
    appNotifier.unSubscribeNotifySyncStatus();
    appNotifier.unSubscribeInfoMessage();
    animationController.dispose(); // Dispose animation controller
    searchController.dispose(); // Dispose text editing controller
    _scrollController.dispose(); // Dispose scroll controller
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!kIsWeb && UIHelper().getScreenType(context) == ScreenType.mobile) {
      ref.listen<String>(roundsSearchProvider, (previous, next) {
        if (next.isEmpty && searchController.text.isNotEmpty) {
          searchController.clear();
        }
      });
    }

    final role = ref.watch(roleProvider);
    final plant = ref.watch(plantProvider);
    final plantSection = ref.watch(plantSectionProvider);
    final shift = ref.watch(shiftProvider);
    return SafeArea(
      child: LayoutBuilder(
        builder: (context, constraints) {
          final screenSize = UIHelper().getScreenType(context);
          return Scaffold(
            backgroundColor: AppColors.white,
            body: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Builder(
                builder: (context) {
                  switch (screenSize) {
                    case ScreenType.mobile:
                      return Column(
                        children: [
                          getAppBar(role),
                          Expanded(
                            child: inspectionAndCiltList(
                                role, plant, plantSection, shift),
                          ),
                        ],
                      );
                    case ScreenType.tablet:
                      return Column(
                        children: [
                          getAppBar(role),
                          Expanded(
                            child: inspectionAndCiltList(
                                role, plant, plantSection, shift),
                          ),
                        ],
                      );
                    case ScreenType.desktop:
                      return Row(
                        children: [
                          ConstrainedBox(
                            constraints: const BoxConstraints(
                              minWidth: 390,
                              maxWidth: 390,
                            ),
                            child: Column(
                              children: [
                                getAppBar(role),
                                Expanded(
                                  child: inspectionAndCiltList(
                                      role, plant, plantSection, shift),
                                ),
                              ],
                            ),
                          ),
                          VerticalDivider(
                            thickness: 3,
                            color: AppColors.grey,
                          ),
                          Expanded(
                              child: ValueListenableBuilder(
                            valueListenable: roundDetailViewNotifier,
                            builder: (context, value, child) {
                              if (value == null || value.type == '') {
                                return Container(
                                  color: Colors.white,
                                );
                              } else {
                                if (value.type == "CILT_EXEC_HEADER") {
                                  return CiltDetailScreen(
                                    ciltPlanHeader:
                                        value.getValue("CILT_PLAN_HEADER"),
                                    ciltHeader:
                                        value.getValue("CILT_EXEC_HEADER"),
                                  );
                                }
                                if (value.type == "INSP_EXEC_HEADER") {
                                  return InspectionDetailScreen(
                                    key: ValueKey(
                                        (value.getValue("INSP_EXEC_HEADER")
                                                as INSP_EXEC_HEADER)
                                            .insp_id),
                                    inspectionPlanHeader: value
                                        .getValue("INSPECTION_PLAN_HEADER"),
                                    inspHeader:
                                        value.getValue("INSP_EXEC_HEADER"),
                                  );
                                } else if (value.type == "ACCEPT_REJECT") {
                                  final data = value.data;
                                  return AcceptRejectPage(
                                    isCilt: data["isCilt"] ?? false,
                                    isInsp: data["isInsp"] ?? false,
                                    acceptCondtion:
                                        data["acceptCondition"] ?? true,
                                    assignCondition:
                                        data["assignCondition"] ?? false,
                                    rejectCondition:
                                        data["rejectCondition"] ?? false,
                                    onAcceptPressed: data["onAcceptPressed"]
                                        as VoidCallback?,
                                    onAssignPressed: data["onAssignPressed"]
                                        as VoidCallback?,
                                    onRejectPressed: data["onRejectPressed"]
                                        as VoidCallback?,
                                  );
                                } else {
                                  return Container(
                                    color: AppColors.white,
                                    child: const Text("Not handled"),
                                  );
                                }
                              }
                            },
                          ))
                        ],
                      );
                    default:
                      return const SizedBox.shrink();
                  }
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Container getAppBar(ROLE_HEADER? role) {
    bool isOffline = _connectivityStatus == ConnectivityResult.none;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: TopHeader(
        onAddButton: isOffline
            ? null
            : () async {
                if (UIHelper().getScreenType(context) == ScreenType.desktop) {
                  await showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (rootDialogContext) => Dialog(
                          backgroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20.0),
                          ),
                          child: ConstrainedBox(
                            constraints: const BoxConstraints(
                              maxWidth: 420,
                              maxHeight: 800,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Navigator(
                                onGenerateRoute: (RouteSettings settings) {
                                  return MaterialPageRoute(
                                    builder: (context) => AddAdhocNewCilt(
                                        onClose: (result) {
                                      Navigator.of(rootDialogContext).pop();
                                      if (result != null) {
                                        // _scrollToBottom();
                                      }
                                    }),
                                  );
                                },
                              ),
                            ),
                          )));
                } else {
                  var result = await Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const AddAdhocNewCilt()));
                  if (result != null) {
                    // _scrollToBottom();
                  }
                }
              },
        isAddIconRequired: (role != null
            ? ((!UIHelper.isCreate(role.cilt!) &&
                    !UIHelper.isCreate(role.inspection!))
                ? false
                : !isOffline)
            : false),
      ),
    );
  }

  Column inspectionAndCiltList(ROLE_HEADER? role, String plant,
      List<String> plantSection, String shift) {
    return Column(
      children: [
        Container(
          child: Padding(
            padding: UIHelper().getScreenType(context) == ScreenType.desktop
                ? const EdgeInsets.symmetric(horizontal: 8, vertical: 8)
                : const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomSearchBar(
                    onChanged: (value) async {
                      ref.read(roundsSearchProvider.notifier).state = value;
                      await ref
                          .read(filteredCiltProvider.notifier)
                          .filter(plant, plantSection, shift, value, ref);
                      await ref
                          .read(filterInspectionHeaderListProvider.notifier)
                          .filter(plant, plantSection, shift, value, ref);
                      if (_scrollController.hasClients) {
                        _scrollController.animateTo(
                          0.0,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      }
                      roundDetailViewNotifier.value = InteractiveItemModel(
                        type: "",
                        data: {"type": "", "index": null},
                      );
                    },
                    controller: searchController,
                    onCancel: () async {
                      ref.read(roundsSearchProvider.notifier).state = "";

                      await ref
                          .read(filteredCiltProvider.notifier)
                          .filter(plant, plantSection, shift, "", ref);

                      await ref
                          .read(filterInspectionHeaderListProvider.notifier)
                          .filter(plant, plantSection, shift, "", ref);

                      setState(() {
                        searchController.clear();
                      });
                      if (_scrollController.hasClients) {
                        _scrollController.animateTo(
                          0.0,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      }
                      roundDetailViewNotifier.value = InteractiveItemModel(
                        type: "",
                        data: {"type": "", "index": null},
                      );
                    }),
                Padding(
                  padding: const EdgeInsets.only(top: 10.0),
                  child: HorizontalCalendar(),
                )
              ],
            ),
          ),
        ),
        Expanded(
          child: Padding(
            padding: UIHelper().getScreenType(context) == ScreenType.desktop
                ? const EdgeInsets.symmetric(horizontal: 8)
                : const EdgeInsets.symmetric(horizontal: 16),
            child: RefreshIndicator(
              onRefresh: refreshData,
              child: InspectionListScreen(
                onItemTap: (value) {
                  roundDetailViewNotifier.value = value;
                  if (!kIsWeb &&
                      UIHelper().getScreenType(context) == ScreenType.mobile) {
                    searchController.clear();
                  }
                },
                scrollController: _scrollController,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
