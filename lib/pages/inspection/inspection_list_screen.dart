import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:rounds/be/CILT_PLAN_HEADER.dart';

import 'package:rounds/pages/home/<USER>';
import 'package:rounds/models/intractive_Item_Model.dart';
import 'package:rounds/providers/cilt/cilt_plan_header_provider.dart';
import 'package:rounds/providers/fault/fault_header_provider.dart';

import 'package:shimmer/shimmer.dart';
import 'package:intl/intl.dart';
import '../../be/CILT_EXEC_HEADER.dart';
import '../../be/INSPECTION_PLAN_HEADER.dart';
import '../../be/INSP_EXEC_HEADER.dart';
import '../../helpers/ui_helper.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/cilt/cilt_header_provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../providers/inspection/inspection_header_provider.dart';
import '../../providers/inspection/inspection_plan_header_provider.dart';
import '../../utils/app_colors.dart';
import '../../utils/utils.dart';
import 'inspection_screen.dart';

final inspectionDataLoaderProvider = FutureProvider<void>((ref) async {
  await Future.delayed(Duration.zero);

  final today = DateFormat('yyyyMMdd').format(DateTime.now());
  ref.read(selectedDateProvider.notifier).state = today;

  final ciltHeaders = ref.read(ciltHeaderListProvider);
  final inspHeaders = ref.read(inspectionHeaderListProvider);
  final plant = ref.read(plantProvider);

  await Future.wait([
    ref.read(ciltTasksProvider.notifier).fetchTasksForAllHeaders(ciltHeaders),
    ref
        .read(inspTasksProvider.notifier)
        .fetchTasksForAllHeaders(inspHeaders, plant),
    ref
        .read(ciltExecuteProvider.notifier)
        .getAllExecTasksForAllExecHeaders(ciltHeaders),
    ref
        .read(inspExecuteProvider.notifier)
        .getAllExecTasksForAllExecHeaders(inspHeaders),
    ref
        .read(inspectionPlanListHeaderProvider.notifier)
        .fetchInspectionPlanListHeaders(),
  ]);
});

class InspectionListScreen extends ConsumerStatefulWidget {
  const InspectionListScreen({
    super.key,
    required this.scrollController,
    required this.onItemTap,
  });

  final ScrollController scrollController;
  final Function(InteractiveItemModel) onItemTap;

  @override
  ConsumerState<InspectionListScreen> createState() =>
      _InspectionListScreenState();
}

class _InspectionListScreenState extends ConsumerState<InspectionListScreen> {
  @override
  Widget build(BuildContext context) {
    final dataState = ref.watch(inspectionDataLoaderProvider);

    return dataState.when(
      loading: () => _buildShimmerList(),
      error: (err, stack) => Center(child: Text("Error loading data: $err")),
      data: (_) {
        final selectedDate = ref.watch(selectedDateProvider);
        final parsedDate = int.tryParse(selectedDate.replaceAll('-', '')) ?? 0;

        final ciltHeaders = ref.watch(filteredCiltProvider);
        final ciltPlans = ref.watch(ciltPlanListHeaderProvider);

        final inspectionHeaders = ref.watch(filterInspectionHeaderListProvider);
        final inspectionPlans = ref.watch(inspectionPlanListHeaderProvider);

        final filteredCiltExecs = ciltHeaders
            .where((e) => e.start_on == parsedDate)
            .toList()
          ..sort((a, b) => a.start_at!.compareTo(b.start_at!));

        final filteredInspExecs = inspectionHeaders
            .where((e) => e.start_on == parsedDate)
            .toList()
          ..sort((a, b) => a.start_at!.compareTo(b.start_at!));

        final rounds = _buildRounds(
          ciltExecs: filteredCiltExecs,
          ciltPlans: ciltPlans,
          inspExecs: filteredInspExecs,
          inspPlans: inspectionPlans,
        );

        if (rounds.isEmpty) {
          if (!kIsWeb) {
            final hasData = (ref.watch(ciltHeaderListProvider).isNotEmpty &&
                    filteredCiltExecs.isNotEmpty) ||
                (ref.watch(inspectionHeaderListProvider).isNotEmpty &&
                    filteredInspExecs.isNotEmpty);
            final hasPlanDataNotExecForTodayDate =
                (ref.watch(ciltHeaderListProvider).isNotEmpty ||
                        filteredCiltExecs.isEmpty) ||
                    (ref.watch(inspectionHeaderListProvider).isNotEmpty ||
                        filteredInspExecs.isEmpty);

            return Center(
              child: hasData
                  ? Text(AppLocalizations.of(context)!.no_tasks_found)
                  : hasPlanDataNotExecForTodayDate
                      ? Text(AppLocalizations.of(context)!.no_tasks_found)
                      : _buildShimmerList(),
            );
          } else {
            final hasData = (ref.watch(ciltHeaderListProvider).isNotEmpty &&
                    filteredCiltExecs.isNotEmpty) ||
                (ref.watch(inspectionHeaderListProvider).isNotEmpty &&
                    filteredInspExecs.isNotEmpty);

            if (!hasData) {
              return Center(
                  child: Text(AppLocalizations.of(context)!.no_tasks_found));
            }
          }
        }

        return SlidableAutoCloseBehavior(
          child: ListView.builder(
            controller: widget.scrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: rounds.length,
            itemBuilder: (context, index) {
              final round = rounds[index];
              return Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Row(
                  children: [
                    ValueListenableBuilder(
                      valueListenable: roundDetailViewNotifier,
                      builder: (context, value, child) {
                        return Expanded(
                          child: JobCard(
                            index: index,
                            color: (UIHelper().getScreenType(context) ==
                                        ScreenType.desktop &&
                                    value?.getValue("index") == index)
                                ? AppColors.primaryColor
                                    .withOpacity(0.2)
                                    .withRed(10)
                                : Colors.white,
                            onTap: (value) {
                              roundDetailViewNotifier.value = value;
                              if (!kIsWeb &&
                                  UIHelper().getScreenType(context) ==
                                      ScreenType.mobile) {
                                ref.read(roundsSearchProvider.notifier).state =
                                    '';
                              }
                              widget.onItemTap(value);
                            },
                            ciltHeader: round.ciltExec,
                            ciltPlanHeader: round.ciltPlan,
                            inspectionHeader: round.inspExec,
                            inspectionPlanHeader: round.inspPlan,
                          ),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildShimmerList() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: ListView.builder(
        physics: const ScrollPhysics(),
        itemCount: 10,
        itemBuilder: (context, index) {
          return Padding(
            padding:
                const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
            child: Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                          width: double.infinity,
                          height: 16,
                          color: Colors.white),
                      const SizedBox(height: 8),
                      Container(width: 150, height: 12, color: Colors.white),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  List<Round> _buildRounds({
    required List<CILT_EXEC_HEADER> ciltExecs,
    required List<CILT_PLAN_HEADER> ciltPlans,
    required List<INSP_EXEC_HEADER> inspExecs,
    required List<INSPECTION_PLAN_HEADER> inspPlans,
  }) {
    final ciltRounds = ciltExecs.map((exec) {
      final plan = ciltPlans.firstWhereOrNull((p) => p.plan_id == exec.plan_id);
      return Round(ciltExec: exec, ciltPlan: plan);
    });

    final inspRounds = inspExecs.map((exec) {
      final plan = inspPlans.firstWhereOrNull((p) => p.plan_id == exec.plan_id);
      return Round(inspExec: exec, inspPlan: plan);
    });

    return [...ciltRounds, ...inspRounds];
  }
}

class Round {
  final CILT_EXEC_HEADER? ciltExec;
  final CILT_PLAN_HEADER? ciltPlan;
  final INSP_EXEC_HEADER? inspExec;
  final INSPECTION_PLAN_HEADER? inspPlan;

  Round({
    this.ciltExec,
    this.ciltPlan,
    this.inspExec,
    this.inspPlan,
  });

  bool get isCilt => ciltExec != null;
}
