import 'package:flutter/material.dart';

import '../../../helpers/ui_helper.dart';
import '../../../utils/app_colors.dart';

class ChoiceChipWidget extends StatefulWidget {
  final bool topPaddingRequired;
  final String selectedPriority;
  final ValueChanged<String>? onSelected;
  final List<String?> chipColors;
  final List<String?> data;

  const ChoiceChipWidget({
    Key? key,
    this.topPaddingRequired = true,
    required this.selectedPriority,
    this.onSelected,
    required this.chipColors,
    required this.data,
  }) : super(key: key);

  @override
  _ChoiceChipWidgetState createState() => _ChoiceChipWidgetState();
}

class _ChoiceChipWidgetState extends State<ChoiceChipWidget> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: widget.topPaddingRequired ? 10 : 0),
      child: Row(
        children: [
          Expanded(
            child: Container(
              // decoration: UIHelper.fieldDecoration(),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                child: LayoutBuilder(builder: (context, constraints) {
                  return Wrap(
                    spacing: 12,
                    children: List.generate(widget.data.length, (index) {
                      final option = widget.data[index];
                      final isSelected = widget.selectedPriority == option;
                      final colorHex = (widget.chipColors.isNotEmpty &&
                              index < widget.chipColors.length)
                          ? widget.chipColors[index]
                          : '#D3D3D3';

                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        child: ChoiceChip(
                          labelPadding: const EdgeInsets.symmetric(
                              horizontal: 15, vertical: 8),
                          label: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              option!,
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.titleTextColor,
                                letterSpacing: 0.1,
                              ),
                            ),
                          ),
                          selected: isSelected,
                          selectedColor: hexToColor(colorHex!),
                          //hexToColor(colorHex!),
                          backgroundColor: hexToColor(colorHex),
                          onSelected: (_) => widget.onSelected?.call(option),
                        ),
                      );
                    }),
                  );
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color hexToColor(String hex) {
    hex = hex.replaceFirst('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex';
    }
    return Color(int.parse(hex, radix: 16));
  }
}
