import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../utils/app_colors.dart';

List<String> data = ['Good', 'Bad', 'Better', 'Best'];

class ToggleButtonWidget extends StatefulWidget {
  const ToggleButtonWidget({super.key});

  @override
  State<ToggleButtonWidget> createState() => _ToggleButtonWidgetState();
}

class _ToggleButtonWidgetState extends State<ToggleButtonWidget> {
  final List<bool> toggleButtonsSelection =
      data.map((String e) => e == data.first).toList();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 15, bottom: 0),
      child: Row(
        children: [
          Expanded(
            child: LayoutBuilder(
              builder: (context, constraints) {
                double buttonWidth = (constraints.maxWidth - 10) / data.length;

                return ToggleButtons(
                  splashColor: AppColors.transparent,
                  fillColor: AppColors.primaryColor,
                  selectedColor: AppColors.blackTitleText,
                  isSelected: toggleButtonsSelection,
                  borderRadius: BorderRadius.circular(5),
                  onPressed: (index) {
                    setState(() {
                      for (int i = 0; i < toggleButtonsSelection.length; i++) {
                        toggleButtonsSelection[i] = i == index;
                      }
                    });
                  },
                  constraints:
                      BoxConstraints(minHeight: 40.0, minWidth: buttonWidth),
                  children: data.map((e) {
                    return Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: Text(e, textAlign: TextAlign.center),
                    );
                  }).toList(),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
//
// import '../../../utils/app_colors.dart';
//
// List<String> data = ['Good', 'Bad', 'Better','Best'];
//
// class ToggleButtonWidget extends StatefulWidget {
//   const ToggleButtonWidget({super.key});
//
//   @override
//   State<ToggleButtonWidget> createState() => _ToggleButtonWidgetState();
// }
//
// class _ToggleButtonWidgetState extends State<ToggleButtonWidget> {
//   final List<bool> toggleButtonsSelection =
//       data.map((String e) => e == data.first).toList();
//
//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.only(top: 15, bottom: 0),
//       child: Row(
//         children: [
//           Expanded(
//             child: ToggleButtons(
//               splashColor: AppColors.transparent,
//               fillColor: AppColors.primaryColor,
//               selectedColor: AppColors.blackTitleText,
//               isSelected: toggleButtonsSelection,
//               onPressed: (index) {
//                 setState(() {
//                   for (int i = 0; i < toggleButtonsSelection.length; i++) {
//                     toggleButtonsSelection[i] = i == index;
//                   }
//                 });
//               },
//               constraints: const BoxConstraints(
//                 minHeight: 20.0,
//                 // minWidth: 56.0,
//               ),
//               children: data.map((e) {
//                 return Container(
//                   width: MediaQuery.of(context).size.width / 3.5,
//                   alignment: Alignment.center,
//                   child: Text(e, textAlign: TextAlign.center),
//                 );
//               }).toList(),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
