import 'dart:async';

import 'package:flutter/material.dart';

import '../../../utils/app_colors.dart';

class TimerWidget extends StatefulWidget {
  final int plannedHours;
  final int plannedMinutes;

  TimerWidget({this.plannedHours = 1, this.plannedMinutes = 0});

  @override
  _TimerWidgetState createState() => _TimerWidgetState();
}

class _TimerWidgetState extends State<TimerWidget> {
  int totalTimeInSeconds = 0;
  int remainingTime = 0;
  Timer? _timer;
  bool isRunning = false;

  @override
  void initState() {
    super.initState();
    totalTimeInSeconds =
        (widget.plannedHours * 3600) + (widget.plannedMinutes * 60);
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void startTimer() {
    if (!isRunning) {
      setState(() {
        isRunning = true;
      });
      _timer = Timer.periodic(Duration(seconds: 1), (timer) {
        setState(() {
          remainingTime++;
          if (remainingTime >= totalTimeInSeconds) {
            _timer?.cancel();
            isRunning = false;
          }
        });
      });
    }
  }

  void pauseTimer() {
    if (_timer != null) {
      _timer?.cancel();
      _timer = null;
    }
    setState(() {
      isRunning = false;
    });
  }

  void stopTimer() {
    setState(() {
      remainingTime = 0;
      isRunning = false;
    });
  }

  String getFormattedTime() {
    int hours = remainingTime ~/ 3600;
    int minutes = (remainingTime % 3600) ~/ 60;
    int seconds = remainingTime % 60;
    return "${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
  }

  @override
  Widget build(BuildContext context) {
    double secondsProgress =
        (60 - (remainingTime % 60)) / 60; // Decreases every second
    double minutesProgress =
        (60 - ((remainingTime ~/ 60) % 60)) / 60; // Decreases every minute
    double hoursProgress = (widget.plannedHours == 0)
        ? 1.0
        : (widget.plannedHours - (remainingTime ~/ 3600)) / widget.plannedHours;

    int hours = remainingTime ~/ 3600;
    int minutes = (remainingTime % 3600) ~/ 60;
    int seconds = remainingTime % 60;
    return Expanded(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                TimerSegment(
                    label: 'HR',
                    value: hours.toString().padLeft(2, '0'),
                    progressValue: hoursProgress),
                TimerSegment(
                    label: 'MIN',
                    value: minutes.toString().padLeft(2, '0'),
                    progressValue: minutesProgress),
                TimerSegment(
                    label: 'SEC',
                    value: seconds.toString().padLeft(2, '0'),
                    progressValue: secondsProgress),
              ],
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ActionButton(
                  text: isRunning ? 'PAUSE' : 'START',
                  onPressed: isRunning ? pauseTimer : startTimer),
              SizedBox(width: 8),
              ActionButton(text: 'STOP', onPressed: stopTimer),
            ],
          ),
        ],
      ),
    );
  }
}

class TimerSegment extends StatelessWidget {
  final String label;
  final String value;
  final double progressValue;
  TimerSegment(
      {required this.label, required this.value, required this.progressValue});

  @override
  Widget build(BuildContext context) {
    double width = 47;
    double height = 47;

    return Stack(
      alignment: Alignment.center,
      children: [
        SizedBox(
          width: width,
          height: height,
          child: CircularProgressIndicator(
            strokeWidth: 3,
            value: progressValue,
            backgroundColor: AppColors.white,
            valueColor: AlwaysStoppedAnimation<Color>(
                AppColors.primaryColor.withOpacity(0.5)),
          ),
        ),
        Container(
          width: width,
          height: height,
          margin: const EdgeInsets.symmetric(horizontal: 4),
          decoration:
              BoxDecoration(shape: BoxShape.circle, color: AppColors.white),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                value,
                style: const TextStyle(
                  color: AppColors.blackTitleText,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                label,
                style: const TextStyle(
                  color: AppColors.blackTitleText,
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class ActionButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;

  ActionButton({required this.text, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 48,
      width: 90,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          foregroundColor: AppColors.blackTitleText,
          backgroundColor: AppColors.primaryColor,  ///check

          /*    primary: AppColors.primaryColor,
          onPrimary: AppColors.blackTitleText,*/
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
        ),
        child: Text(text),
      ),
    );
  }
}
