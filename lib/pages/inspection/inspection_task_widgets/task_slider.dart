import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart' as slider;
import 'package:syncfusion_flutter_sliders/sliders.dart';

class TaskSlider extends StatefulWidget {
  const TaskSlider({super.key});

  @override
  State<TaskSlider> createState() => _TaskSliderState();
}

class _TaskSliderState extends State<TaskSlider> {
  double _sliderValue = 20;

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.zero,
        padding: EdgeInsets.zero,
        child: Column(
          children: <Widget>[
            SfSlider(
              min: 0.0,
              max: 10.0,
              interval: 2,
              showLabels: true,
              showTicks: true,
              value: _sliderValue,
              onChanged: (dynamic newValue) {
                setState(() {
                  _sliderValue = newValue;
                });
              },
            ),


            // Padding(
            //   padding: const EdgeInsets.only(top: 10),
            //   child: Center(
            //     child: slider.SfRangeSelector(
            //       min: 1,
            //       max: 10,
            //       initialValues: slider.SfRangeValues(1, 10),
            //       interval: 1,
            //
            //       dateFormat: DateFormat.y(),
            //       showTicks: true,
            //       showLabels: true,
            //       child: SizedBox(
            //         height: 200,
            //         child: Container(),
            //       ),
            //     ),
            //   ),
            // ),
          ],
        ));
    //   Center(
    //   child: SliderTheme(
    //     data: SliderTheme.of(context).copyWith(
    //       activeTrackColor: AppColors.blackTitleText,
    //       inactiveTrackColor: AppColors.cardBorderGrey,
    //       thumbColor: AppColors.white,
    //       trackHeight: 2.0,
    //       thumbShape: RoundSliderThumbShape(
    //         enabledThumbRadius: 10,
    //       ),
    //     ),
    //     child: Slider(
    //       min: 1,
    //       max: 30,
    //       value: _sliderValue,
    //       divisions: 3,
    //       label: _sliderValue.round().toString(),
    //       onChanged: (double value) {
    //         setState(() {
    //           _sliderValue = value;
    //         });
    //       },
    //     ),
    //   ),
    // );
  }
}

/// Storing the spline series data points.
class Data {
  /// Initialize the instance of the [Data] class.
  Data({required this.x, required this.y});

  /// Spline series x points.
  final DateTime x;

  /// Spline series y points.
  final double y;
}
