import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';

import '../../../utils/app_colors.dart';

import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';
import '../../../utils/app_colors.dart';

class SliderWidget extends StatefulWidget {
  final double min;
  final double max;
  final ValueChanged<dynamic>? onChanged;
  final double value;

  const SliderWidget(
      {Key? key,
      required this.min,
      required this.max,
      required this.value,
      this.onChanged})
      : super(key: key);

  @override
  _SliderWidgetState createState() => _SliderWidgetState();
}

class _SliderWidgetState extends State<SliderWidget> {
/*
  late double _value;

  @override
  void initState() {
    super.initState();
    _value = widget.initialValue;
  }
*/

  @override
  Widget build(BuildContext context) {
    return SfSlider(
      min: widget.min,
      max: widget.max,
      value: widget.value,
      activeColor: AppColors.greySubtitleText,
      inactiveColor: AppColors.cardBorderGrey,
      interval: 20,
      showLabels: false,
      showTicks: false,
      showDividers: false,
      enableTooltip: true,
      onChanged: widget.onChanged,
    );
  }
}

/*
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';

import '../../../utils/app_colors.dart';

class SliderWidget extends StatefulWidget {
  final double min;
  final double max;
  final double initialValue;

  const SliderWidget({
    Key? key,
    required this.min,
    required this.max,
    required this.initialValue,
  }) : super(key: key);

  @override
  _SliderWidgetState createState() => _SliderWidgetState();
}

class _SliderWidgetState extends State<SliderWidget> {
  late double _value;

  @override
  void initState() {
    super.initState();
    _value = widget.initialValue;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: SfSlider(
            activeColor: AppColors.greySubtitleText,
            dividerShape: SfDividerShape(),
            inactiveColor: AppColors.cardBorderGrey,
            minorTickShape: SfTickShape(),
            showDividers: true,
            trackShape: SfTrackShape(),
            tickShape: SfTickShape(),
            thumbShape: SfThumbShape(),
            min: widget.min,
            max: widget.max,
            value: _value,
            interval: 20,
            showLabels: true,
            onChanged: (dynamic newValue) {
              setState(() {
                _value = newValue;
              });
            },
          ),
        ),
      ],
    );
  }
}

*/
