import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:rounds/utils/app_colors.dart';

class SignatureWidget extends StatefulWidget {
  final Widget widget;
  final Widget? suffixIcon;
  const SignatureWidget({super.key, required this.widget, this.suffixIcon});

  @override
  State<SignatureWidget> createState() => _SignatureWidgetState();
}

class _SignatureWidgetState extends State<SignatureWidget> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 15),
      child: Stack(
        children: [
          Container(
            width: MediaQuery.of(context).size.width,
            height: 140,
            decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: AppColors.grey),
            ),
            child: widget.widget,
          ),
          widget.suffixIcon ?? SizedBox()
        ],
      ),
    );
  }
}
