import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class CheckboxWidget extends StatefulWidget {
  final bool initialValue;
  final ValueChanged<bool?> onChanged;
  final bool isChecked;

  const CheckboxWidget({
    Key? key,
    this.initialValue = false,
    required this.onChanged,
    required this.isChecked,
  }) : super(key: key);

  @override
  State<CheckboxWidget> createState() => _SingleCheckboxState();
}

class _SingleCheckboxState extends State<CheckboxWidget> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 18,
      height: 18,
      child: Checkbox(
        value: widget.isChecked,
        onChanged: widget.onChanged,
        activeColor: Colors.blue,
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        visualDensity: VisualDensity.compact,
      ),
    );
  }
}

class CheckboxWidget2 extends StatefulWidget {
  final bool initialValue;
  final ValueChanged<bool?> onChanged;
  final bool isChecked;

  const CheckboxWidget2({
    Key? key,
    this.initialValue = false,
    required this.onChanged,
    required this.isChecked,
  }) : super(key: key);

  @override
  State<CheckboxWidget2> createState() => _SingleCheckbox2State();
}

class _SingleCheckbox2State extends State<CheckboxWidget2> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 10,
      height: 10,
      child: Checkbox(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
        value: widget.isChecked,
        onChanged: widget.onChanged,
        activeColor: Colors.blue,
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        visualDensity: VisualDensity.compact,
      ),
    );
  }
}
