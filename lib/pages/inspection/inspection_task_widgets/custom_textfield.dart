import 'package:flutter/material.dart';
import '../../../utils/app_colors.dart';

class CustomTextFieldWidget extends StatefulWidget {
  final Widget label;
  final TextEditingController controller;
  final ValueChanged<String>? onChanged;
  final String? dataString;
  final Widget? suffixIcon;
  final Color? borderColor;
  final int? maxlines;
  final VoidCallback? onSuffixIcon;
  final bool? readOnly;
  final TextInputType? keyboardType;
  final TextStyle? textStyle;

  CustomTextFieldWidget({
    super.key,
    required this.controller,
    this.onChanged,
    this.dataString,
    required this.label,
    this.suffixIcon,
    this.borderColor,
    this.maxlines,
    this.onSuffixIcon,
    this.readOnly,
    this.keyboardType,
    this.textStyle,
  });

  @override
  _CustomTextFieldWidgetState createState() => _CustomTextFieldWidgetState();
}

class _CustomTextFieldWidgetState extends State<CustomTextFieldWidget> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 15, bottom: 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: SizedBox(
              height: widget.maxlines != null ? 40 : null,
              child: TextFormField(
                style: widget.textStyle,
                controller: widget.controller,
                onChanged: widget.onChanged,
                maxLines: widget.maxlines,
                readOnly: widget.readOnly ?? false,
                keyboardType: widget.keyboardType,
                decoration: InputDecoration(
                    label: widget.label,
                    labelStyle: TextStyle(color: AppColors.greySubtitleText),
                    border: OutlineInputBorder(
                      gapPadding: 2,
                        borderSide: BorderSide(
                            color: widget.borderColor ?? AppColors.grey,
                            width: 1.5)),
                    enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                            color: widget.borderColor ?? AppColors.grey,
                            width: 1.5)),
                    focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                            color: widget.borderColor ?? AppColors.grey,
                            width: 1.5)),
                    suffixIcon: IconButton(
                        onPressed:widget.onSuffixIcon,
                        icon: widget.suffixIcon ?? Container())),
              ),
            ),

          ),
        ],
      ),
    );
  }
}
