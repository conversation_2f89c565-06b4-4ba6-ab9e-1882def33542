import 'package:flutter/cupertino.dart';

import '../../../helpers/ui_helper.dart';
import '../../../utils/app_colors.dart';

class TableWidget extends StatefulWidget {
  const TableWidget({super.key});

  @override
  State<TableWidget> createState() => _TableWidgetState();
}

class _TableWidgetState extends State<TableWidget> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 15),
      child: Table(
        border: TableBorder.all(color: AppColors.darkgrey),
        children: [
          TableRow(children: [
            Padding(
              padding: tablePadding(),
              child: Center(
                child: Text(
                  'Time',
                  style:
                      UIHelper().mediumTextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
            Padding(
              padding: tablePadding(),
              child: Center(
                child: Text(
                  'Pressure',
                  style: UIHelper().mediumTextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
            Padding(
              padding:tablePadding(),
              child: Center(
                child: Text(
                  'Temperature',
                  style: UIHelper().mediumTextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ]),
          TableRow(children: [
            Padding(
              padding: tablePadding(),
              child: Center(child: Text('10.15')),
            ),
            Padding(
              padding: tablePadding(),
              child: Center(child: Text('100')),
            ),
            Padding(
              padding: tablePadding(),
              child: Center(child: Text('200')),
            ),
          ])
        ],
      ),
    );
  }

  EdgeInsetsGeometry tablePadding(){
    return const EdgeInsets.only(top: 5,bottom: 5);
  }

}
