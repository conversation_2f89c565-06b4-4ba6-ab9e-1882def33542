import 'package:flutter/material.dart';

import '../../../helpers/ui_helper.dart';
import '../../../utils/app_colors.dart';

class TaskTextFieldWidget extends StatefulWidget {
  final TextEditingController controller;
  final ValueChanged<String>? onChanged;
  final String? dataString;

  const TaskTextFieldWidget(
      {super.key, required this.controller, this.onChanged, this.dataString});

  @override
  State<TaskTextFieldWidget> createState() => _TaskTextFieldWidgetState();
}

class _TaskTextFieldWidgetState extends State<TaskTextFieldWidget> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 15, bottom: 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: SizedBox(
              height: 40,
              // width: 80,
              child: TextForm<PERSON>ield(
                controller: widget.controller,
                onChanged: widget.onChanged,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(
                      borderSide:
                          BorderSide(color: AppColors.greySubtitleText)),
                  enabledBorder: OutlineInputBorder(
                      borderSide:
                          BorderSide(color: AppColors.greySubtitleText)),
                  focusedBorder: OutlineInputBorder(
                      borderSide:
                          BorderSide(color: AppColors.greySubtitleText)),
                ),
              ),
            ),
          ),
          const SizedBox(width: 10),
          Text(
            widget.dataString ?? 'C',
            style: UIHelper()
                .extraLargeTextStyle(fontWeight: FontWeight.bold, fontSize: 20),
          ),
        ],
      ),
    );
  }
}
