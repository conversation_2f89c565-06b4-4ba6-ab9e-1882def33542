import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/be/INSPECTION_PLAN_HEADER.dart';
import 'package:rounds/helpers/pa_helper.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/models/adhoc_cilt.dart';
import 'package:rounds/providers/adhoc/adhoc_cilt_provider.dart';
import 'package:rounds/providers/cilt/cilt_header_provider.dart';
import 'package:rounds/providers/fault/fault_header_provider.dart';
import 'package:rounds/providers/fault/fault_type_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:rounds/utils/utils.dart';

import '../../../providers/inspection/inspection_header_provider.dart';
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, TargetPlatform, kIsWeb;

class AdHocCiltReview extends ConsumerStatefulWidget {
  const AdHocCiltReview({super.key, this.onClose});
  final ValueChanged<dynamic>? onClose;
  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _AdHocCiltReviewState();
}

class _AdHocCiltReviewState extends ConsumerState<AdHocCiltReview> {
  TextEditingController startTimeController = TextEditingController();
  TextEditingController endTimeController = TextEditingController();
  DateFormat timeFormater = DateFormat('HHmmss');
  DateFormat timeFormaterView = DateFormat('HH:mm');
  DateFormat dateFormater = DateFormat("yyyyMMdd");

  Widget getPriorityChoiceChip() {
    final priorityList = ref.watch(priorityListProvider.notifier).state;
    return Container(
      width: double.infinity,
      decoration: UIHelper.fieldDecoration(),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
        child: Wrap(
          spacing: 8.0,
          children: priorityList.map((option) {
            return ChoiceChip(
              label: Text(
                option.description.toString(),
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.titleTextColor,
                  letterSpacing: 0.1,
                ),
              ),
              labelPadding:
                  const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              selected: ref.read(adhocCiltProvider).priority?.lid == option.lid,
              selectedColor: UIHelper.getPriorityColor(
                  ref.read(adhocCiltProvider).priority?.description ?? ""),
              backgroundColor: Colors.grey[200],
              onSelected: (selected) {
                ref.read(adhocCiltProvider.notifier).setPriority(option);
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    startTimeController = TextEditingController(
        text: timeFormaterView
            .format(ref.read(adhocCiltProvider).startTime ?? DateTime.now()));
    endTimeController = TextEditingController(
        text: timeFormaterView
            .format(ref.read(adhocCiltProvider).endTime ?? DateTime.now()));
  }

  void _selectStartTime() async {
    TimeOfDay? picked;

    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      picked = await showTimePicker(
        initialEntryMode: TimePickerEntryMode.input,
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
            child: child!,
          );
        },
        context: context,
        initialTime: TimeOfDay.fromDateTime(
          ref.read(adhocCiltProvider).startTime ?? DateTime.now(),
        ),
      );
    } else if (!kIsWeb && defaultTargetPlatform == TargetPlatform.iOS) {
      picked = await showCupertinoModalPopup<TimeOfDay>(
        context: context,
        builder: (BuildContext context) {
          TimeOfDay tempPicked = TimeOfDay.now(); // Temporary fallback
          return CupertinoActionSheet(
            title: Text(AppLocalizations.of(context)!.select_time),
            message: SizedBox(
              height: 200,
              child: Column(
                children: [
                  Expanded(
                    child: CupertinoTimerPicker(
                      initialTimerDuration: Duration(
                        hours: ref.read(adhocCiltProvider).startTime?.hour ??
                            DateTime.now().hour,
                        minutes:
                            ref.read(adhocCiltProvider).startTime?.minute ??
                                DateTime.now().minute,
                      ),
                      onTimerDurationChanged: (Duration newDuration) {
                        tempPicked = TimeOfDay(
                            hour: newDuration.inHours,
                            minute: newDuration.inMinutes % 60);
                      },
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context, tempPicked);
                },
                child: Text(AppLocalizations.of(context)!.ok),
              )
            ],
            cancelButton: CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context, null);
              },
              child: Text(AppLocalizations.of(context)!.cancel),
            ),
          );
        },
      );
    } else {
      picked = await showTimePicker(
        initialEntryMode: TimePickerEntryMode.input,
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
            child: child!,
          );
        },
        context: context,
        initialTime: TimeOfDay.fromDateTime(
          ref.read(adhocCiltProvider).startTime ?? DateTime.now(),
        ),
      );
    }

    if (picked != null) {
      final pickedDateTime = DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
        picked.hour,
        picked.minute,
      );

      final shiftEndTime =
          ref.read(adhocCiltProvider).endTime ?? DateTime.now();
      final shiftStartTime =
          ref.read(adhocCiltProvider).startTime ?? DateTime.now();

      if (pickedDateTime.isBefore(shiftStartTime)) {
        if (mounted) {
          UIHelper.showEamDialog(context,
              title: AppLocalizations.of(context)!.input_error,
              description: AppLocalizations.of(context)!
                  .selected_time_cannot_before_shift_start_time,
              onOKPressed: () {
            Navigator.of(context, rootNavigator: true).pop();
          });
        }
      } else if (pickedDateTime.isAfter(shiftEndTime)) {
        if (mounted) {
          UIHelper.showEamDialog(context,
              title: AppLocalizations.of(context)!.input_error,
              description: AppLocalizations.of(context)!
                  .selected_time_cannot_after_shift_end_time, onOKPressed: () {
            Navigator.of(context, rootNavigator: true).pop();
          });
        }
      } else {
        ref.read(adhocCiltProvider.notifier).updateStartTime(picked);

        timeFormaterView.format(pickedDateTime);

        startTimeController = TextEditingController(
          text: timeFormaterView.format(
            ref.read(adhocCiltProvider).startTime ?? DateTime.now(),
          ),
        );
      }
    }
  }

  void _selectEndTime() async {
    TimeOfDay? picked;

    final currentStartTime =
        ref.read(adhocCiltProvider).startTime ?? DateTime.now();

    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      picked = await showTimePicker(
        initialEntryMode: TimePickerEntryMode.input,
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
            child: child!,
          );
        },
        context: context,
        initialTime: TimeOfDay.fromDateTime(currentStartTime),
      );
    } else if (!kIsWeb && defaultTargetPlatform == TargetPlatform.iOS) {
      TimeOfDay tempPicked = TimeOfDay(
        hour: currentStartTime.hour,
        minute: currentStartTime.minute,
      );

      picked = await showCupertinoModalPopup<TimeOfDay>(
        context: context,
        builder: (BuildContext context) {
          return CupertinoActionSheet(
            title: Text(AppLocalizations.of(context)!.select_time),
            message: SizedBox(
              height: 200,
              child: Column(
                children: [
                  Expanded(
                    child: CupertinoTimerPicker(
                      initialTimerDuration: Duration(
                        hours: tempPicked.hour,
                        minutes: tempPicked.minute,
                      ),
                      onTimerDurationChanged: (Duration newDuration) {
                        tempPicked = TimeOfDay(
                          hour: newDuration.inHours,
                          minute: newDuration.inMinutes % 60,
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context, tempPicked);
                },
                child: Text(AppLocalizations.of(context)!.ok),
              )
            ],
            cancelButton: CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context, null);
              },
              child: Text(AppLocalizations.of(context)!.cancel),
            ),
          );
        },
      );
    } else {
      // Fallback for web or unknown platforms
      picked = await showTimePicker(
        initialEntryMode: TimePickerEntryMode.input,
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
            child: child!,
          );
        },
        context: context,
        initialTime: TimeOfDay.fromDateTime(currentStartTime),
      );
    }

    if (picked != null) {
      final pickedDateTime = DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
        picked.hour,
        picked.minute,
      );

      final shiftStartTime =
          ref.read(adhocCiltProvider).startTime ?? DateTime.now();
      final shiftEndTime =
          ref.read(adhocCiltProvider).endTime ?? DateTime.now();

      if (pickedDateTime.isBefore(shiftStartTime)) {
        UIHelper.showEamDialog(context,
            title: AppLocalizations.of(context)!.input_error,
            description: AppLocalizations.of(context)!
                .selected_time_cannot_before_shift_start_time, onOKPressed: () {
          Navigator.of(context, rootNavigator: true).pop();
        });
      } else if (pickedDateTime.isAfter(shiftEndTime)) {
        UIHelper.showEamDialog(context,
            title: AppLocalizations.of(context)!.input_error,
            description: AppLocalizations.of(context)!
                .selected_time_cannot_after_shift_end_time, onOKPressed: () {
          Navigator.of(context, rootNavigator: true).pop();
        });
      } else {
        ref.read(adhocCiltProvider.notifier).updateEndTime(picked);

        timeFormaterView.format(pickedDateTime);

        endTimeController = TextEditingController(
          text: timeFormaterView.format(
            ref.read(adhocCiltProvider).endTime ?? DateTime.now(),
          ),
        );
      }
    }
  }

  /*_selectStartTime() async {
    TimeOfDay? picked;

    if (Platform.isAndroid) {
      picked = await showTimePicker(
        initialEntryMode: TimePickerEntryMode.input,
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
            child: child!,
          );
        },
        context: context,
        initialTime: TimeOfDay.fromDateTime(
            ref.read(adhocCiltProvider).startTime ?? DateTime.now()),
      );
    } else if (Platform.isIOS) {
      picked = await showCupertinoModalPopup<TimeOfDay>(
        context: context,
        builder: (BuildContext context) {
          return CupertinoActionSheet(
            title: Text(AppLocalizations.of(context)!.select_time),
            message: SizedBox(
              height: 200,
              child: Column(
                children: [
                  Expanded(
                    child: CupertinoTimerPicker(
                      initialTimerDuration: Duration(
                        hours: ref.read(adhocCiltProvider).startTime?.hour ??
                            DateTime.now().hour,
                        minutes:
                            ref.read(adhocCiltProvider).startTime?.minute ??
                                DateTime.now().minute,
                      ),
                      onTimerDurationChanged: (Duration newDuration) {
                        picked = TimeOfDay(
                            hour: newDuration.inHours,
                            minute: newDuration.inMinutes % 60);
                      },
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context, picked);
                },
                child: Text(AppLocalizations.of(context)!.ok),
              )
            ],
            cancelButton: CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context, picked);
              },
              child: Text(AppLocalizations.of(context)!.cancel),
            ),
          );
        },
      );
    }

    if (picked != null) {
      final pickedDateTime = DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
        picked!.hour,
        picked!.minute,
      );

      final shiftEndTime =
          ref.read(adhocCiltProvider).endTime ?? DateTime.now();
      final shiftStartTime =
          ref.read(adhocCiltProvider).startTime ?? DateTime.now();

      if (pickedDateTime.isBefore(shiftStartTime)) {
        UIHelper.showEamDialog(context,
            title: AppLocalizations.of(context)!.input_error,
            description: AppLocalizations.of(context)!
                .selected_time_cannot_before_shift_start_time);
      } else if (pickedDateTime.isAfter(shiftEndTime)) {
        UIHelper.showEamDialog(context,
            title: AppLocalizations.of(context)!.input_error,
            description: AppLocalizations.of(context)!
                .selected_time_cannot_after_shift_end_time);
      } else {
        ref.read(adhocCiltProvider.notifier).updateStartTime(picked!);

        timeFormaterView.format(DateTime(
            DateTime.now().year,
            DateTime.now().month,
            DateTime.now().day,
            picked!.hour,
            picked!.minute));

        startTimeController = TextEditingController(
            text: timeFormaterView.format(
                ref.read(adhocCiltProvider).startTime ?? DateTime.now()));
      }
    }
  }
*/
  /* _selectEndTime() async {
    TimeOfDay? picked;

    if (Platform.isAndroid) {
      picked = await showTimePicker(
        initialEntryMode: TimePickerEntryMode.input,
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
            child: child!,
          );
        },
        context: context,
        initialTime: TimeOfDay.fromDateTime(
            ref.read(adhocCiltProvider).startTime ?? DateTime.now()),
      );
    } else if (Platform.isIOS) {
      picked = await showCupertinoModalPopup<TimeOfDay>(
        context: context,
        builder: (BuildContext context) {
          return CupertinoActionSheet(
            title: const Text("Select Time"),
            message: SizedBox(
              height: 200,
              child: Column(
                children: [
                  Expanded(
                    child: CupertinoTimerPicker(
                      initialTimerDuration: Duration(
                        hours: ref.read(adhocCiltProvider).startTime?.hour ??
                            DateTime.now().hour,
                        minutes:
                            ref.read(adhocCiltProvider).startTime?.minute ??
                                DateTime.now().minute,
                      ),
                      onTimerDurationChanged: (Duration newDuration) {
                        // Convert duration to TimeOfDay
                        picked = TimeOfDay(
                            hour: newDuration.inHours,
                            minute: newDuration.inMinutes % 60);
                      },
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context, picked);
                },
                child: Text(AppLocalizations.of(context)!.ok),
              )
            ],
            cancelButton: CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context, picked);
              },
              child: Text(AppLocalizations.of(context)!.cancel),
            ),
          );
        },
      );
    }

    if (picked != null) {
      final shiftEndTime =
          ref.read(adhocCiltProvider).endTime ?? DateTime.now();
      final pickedDateTime = DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
        picked!.hour,
        picked!.minute,
      );
      final shiftStartTime =
          ref.read(adhocCiltProvider).startTime ?? DateTime.now();
      if (pickedDateTime.isBefore(shiftStartTime)) {
        // ignore: use_build_context_synchronously
        UIHelper.showEamDialog(context,
            title: AppLocalizations.of(context)!.input_error,
            description: AppLocalizations.of(context)!
                .selected_time_cannot_before_shift_start_time);
        // ignore: use_build_context_synchronously
      } else if (pickedDateTime.isAfter(shiftEndTime)) {
        // ignore: use_build_context_synchronously
        UIHelper.showEamDialog(context,
            title: AppLocalizations.of(context)!.input_error,
            description: AppLocalizations.of(context)!
                .selected_time_cannot_after_shift_end_time);
      } else {
        ref.read(adhocCiltProvider.notifier).updateEndTime(picked!);

        timeFormaterView.format(DateTime(
            DateTime.now().year,
            DateTime.now().month,
            DateTime.now().day,
            picked!.hour,
            picked!.minute));

        endTimeController = TextEditingController(
            text: timeFormaterView
                .format(ref.read(adhocCiltProvider).endTime ?? DateTime.now()));
      }
    }
  }*/

  _createAdhoCilt() async {
    var prov = ref.read(adhocCiltProvider);
    showDialog(
      context: context,
      barrierDismissible:
          false, // Prevent dismissing the dialog by tapping outside
      builder: (BuildContext context) {
        return AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 20),
              Text(AppLocalizations.of(context)!.pleaseWaitThreeDot),
            ],
          ),
        );
      },
    );

    try {
      AdhocCilt adhocCilt = AdhocCilt(
          planId: prov.selectedPlan.plan_id.toString(),
          shift: prov.shift_header?.shift_code ?? "",
          priority: prov.priority?.priority_code.toString() ?? "1",
          startOn: dateFormater.format(DateTime.now()),
          startAt: timeFormater.format(prov.startTime ?? DateTime.now()),
          endAt: timeFormater.format(prov.endTime ?? DateTime.now()),
          assignedTo: prov.user_header?.user_id ?? "");

      var result = null;

      if (prov.selectedPlan is INSPECTION_PLAN_HEADER) {
        result =
            await PAHelper.createAdHocInspectioninSyncMode(context, adhocCilt);
      } else {
        result = await PAHelper.createAdHocCiltinSyncMode(context, adhocCilt);
      }

      if (result.statusCode == HttpStatus.created) {
        final plant = ref.watch(plantProvider.notifier).state;
        final plantSection = ref.watch(plantSectionProvider.notifier).state;
        final shift = ref.read(shiftProvider.notifier).state;

        await ref
            .read(inspectionHeaderListProvider.notifier)
            .fetchInspectionListHeaders(plant, plantSection, shift, ref);

        await ref
            .read(ciltHeaderListProvider.notifier)
            .fetchCiltListHeaders(plant, plantSection, shift, ref);
        if (UIHelper().getScreenType(context) == ScreenType.desktop) {
          Navigator.of(context, rootNavigator: true).pop();
          if (mounted) {
            return showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext dialogContext) {
                return WillPopScope(
                  onWillPop: () async {
                    return false;
                  },
                  child: AlertDialog(
                    title: Text(AppLocalizations.of(context)!.info),
                    content: Text(
                      prov.selectedPlan is INSPECTION_PLAN_HEADER
                          ? AppLocalizations.of(context)!
                              .insp_created_successfully
                          : AppLocalizations.of(context)!
                              .cilt_created_successfully,
                    ),
                    actions: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(dialogContext).pop();
                          Navigator.of(context).pop();
                          widget.onClose!(true);
                        },
                        child: Text(
                          AppLocalizations.of(context)!.ok,
                          style: TextStyle(
                            color: AppColors.primaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          }
        } else {
          Navigator.of(context, rootNavigator: true).pop();
          Navigator.of(context).pop();
          Navigator.of(context).pop(true);
        }
      }
    } catch (error) {
      if (UIHelper().getScreenType(context) == ScreenType.desktop) {
        Navigator.of(context, rootNavigator: true).pop();
        Navigator.of(context).pop();
        widget.onClose!(true);
      } else {
        Navigator.of(context, rootNavigator: true).pop();
        Navigator.of(context).pop();
        Navigator.of(context).pop(true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: LayoutBuilder(
        builder: (context, constraints) => Scaffold(
          backgroundColor: AppColors.white,
          appBar: AppBar(
            elevation: 0,
            backgroundColor: AppColors.white,
            leadingWidth: 30,
            leading: IconButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                icon: Icon(
                  Icons.arrow_back_ios,
                  color: AppColors.titleTextColor,
                  size: 20,
                )),
            title: Text(
              AppLocalizations.of(context)!.review,
              style: UIHelper.titleStyle14(),
            ),
            actions: [],
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: UIHelper.cardDecoration(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Text(
                            AppLocalizations.of(context)!.selectedPlan,
                            style: UIHelper.labelStyle(),
                          ),
                        ),
                        Text(ref.watch(adhocCiltProvider).selectedPlan.title),
                        Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Text(AppLocalizations.of(context)!.priority,
                                style: UIHelper.labelStyle())),
                        getPriorityChoiceChip(),
                        Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Text(AppLocalizations.of(context)!.shift,
                                style: UIHelper.labelStyle())),
                        Text(ref
                                .watch(adhocCiltProvider)
                                .shift_header
                                ?.shift_name ??
                            ""),
                        Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Text(AppLocalizations.of(context)!.startTime,
                                style: UIHelper.labelStyle())),
                        TextField(
                          controller: startTimeController,
                          readOnly: true,
                          decoration: InputDecoration(
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 10,
                            ),
                            suffix: GestureDetector(
                              onTap: _selectStartTime,
                              child: const Icon(
                                Icons.edit,
                                size: 20,
                              ),
                            ),
                            hintText:
                                AppLocalizations.of(context)!.select_start_time,
                            border: const OutlineInputBorder(
                                borderSide:
                                    BorderSide(color: Color(0xffe2e3e2))),
                          ),
                          style: const TextStyle(fontSize: 14),
                          onTap: _selectStartTime,
                        ),
                        Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Text(AppLocalizations.of(context)!.endTime,
                                style: UIHelper.labelStyle())),
                        TextField(
                          controller: endTimeController,
                          readOnly: true,
                          decoration: InputDecoration(
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 10,
                            ),
                            suffix: const Icon(
                              Icons.edit,
                              size: 20,
                            ),
                            hintText:
                                AppLocalizations.of(context)!.select_end_time,
                            border: const OutlineInputBorder(
                                borderSide:
                                    BorderSide(color: Color(0xffe2e3e2))),
                          ),
                          style: const TextStyle(fontSize: 14),
                          onTap: _selectEndTime,
                        ),
                        Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Text(
                                AppLocalizations.of(context)!.assignedTo,
                                style: UIHelper.labelStyle())),
                        Text(
                            "${ref.read(adhocCiltProvider).user_header?.first_name?.toUpperCase() ?? ""} ${ref.read(adhocCiltProvider).user_header?.last_name?.toUpperCase()}"),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
          bottomNavigationBar: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Expanded(
                    child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryColor),
                        onPressed: () {
                          Navigator.of(context)
                              .popUntil((route) => route.isFirst);
                        },
                        child: Text(AppLocalizations.of(context)!.cancel))),
                const SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: ElevatedButton(
                      onPressed: ref.read(adhocCiltProvider).priority != null
                          ? _createAdhoCilt
                          : null,
                      style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryColor),
                      child: Text(AppLocalizations.of(context)!.confirm)),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
