import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/be/CILT_PLAN_HEADER.dart';
import 'package:rounds/be/INSPECTION_PLAN_HEADER.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/pages/inspection/adhoc_cilt/adhoc_cilt_review.dart';

import 'package:rounds/providers/adhoc/adhoc_cilt_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:rounds/utils/utils.dart';
import 'package:rounds/widgets/toggle_switch.dart';

import '../../../providers/user_provider.dart';
import '../../assign_plan_screen.dart';

class AddAdhocNewCilt extends ConsumerStatefulWidget {
  const AddAdhocNewCilt({
    super.key,
    this.onClose,
  });

  final ValueChanged<dynamic>? onClose;

  @override
  ConsumerState<AddAdhocNewCilt> createState() => _AddAdhockNewCiltState();
}

class _AddAdhockNewCiltState extends ConsumerState<AddAdhocNewCilt> {
  TextEditingController searchController = TextEditingController();

  @override
  void dispose() {
    // TODO: implement dispose
    searchController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    Future.delayed(Duration.zero).then((value) {
      ref.read(adhocCiltProvider.notifier)
        ..resetState()
        ..fetchCiltAndInspectionPlans();
    });
    super.initState();
  }

  _navigateToReviewAndAssignmentScreen() async {
    if (ref.read(adhocCiltProvider).selectedPlan != null &&
        ref.read(adhocCiltProvider).selectedPlan is CILT_PLAN_HEADER) {
      USER_HEADER? asignee = null;
      var roleHeader = ref.read(roleProvider);
      if (UIHelper.isAssign(roleHeader?.cilt ?? 0)) {
        asignee = await Navigator.of(context).push(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                AssignScreen(
              title: AppLocalizations.of(context)?.selectUser,
              isConfirmButtonInBottom: true,
              onNext: (value) async {
                ref.read(adhocCiltProvider.notifier).assignUser(value);
                Navigator.of(context).pop();
                var result = await Navigator.push(
                    context,
                    PageRouteBuilder(
                      pageBuilder: (context, animation, secondaryAnimation) =>
                          AdHocCiltReview(onClose: widget.onClose),
                      transitionsBuilder:
                          (context, animation, secondaryAnimation, child) {
                        const begin = Offset(1.0, 0.0);
                        const end = Offset.zero;
                        const curve = Curves.ease;

                        var tween = Tween(begin: begin, end: end)
                            .chain(CurveTween(curve: curve));
                        var offsetAnimation = animation.drive(tween);

                        return SlideTransition(
                          position: offsetAnimation,
                          child: child,
                        );
                      },
                    ));

                if (result != null) {
                  Navigator.of(context).pop(true);
                }
              },
            ),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              const begin = Offset(1.0, 0.0);
              const end = Offset.zero;
              const curve = Curves.ease;

              var tween =
                  Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
              var offsetAnimation = animation.drive(tween);

              return SlideTransition(
                position: offsetAnimation,
                child: child,
              );
            },
          ),
        );
      } else {
        asignee = ref.read(userProvider);
        var result = await Navigator.push(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  AdHocCiltReview(
                onClose: widget.onClose,
              ),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                const begin = Offset(1.0, 0.0);
                const end = Offset.zero;
                const curve = Curves.ease;

                var tween = Tween(begin: begin, end: end)
                    .chain(CurveTween(curve: curve));
                var offsetAnimation = animation.drive(tween);

                return SlideTransition(
                  position: offsetAnimation,
                  child: child,
                );
              },
            ));

        if (result != null) {
          Navigator.of(context).pop(true);
        }
      }
      if (asignee != null) {}
    } else if (ref.read(adhocCiltProvider).selectedPlan != null &&
        ref.read(adhocCiltProvider).selectedPlan is INSPECTION_PLAN_HEADER) {
      USER_HEADER? asignee = null;
      var roleHeader = ref.read(roleProvider);
      if (UIHelper.isAssign(roleHeader?.inspection ?? 0)) {
        asignee = await Navigator.of(context).push(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                AssignScreen(
              title: AppLocalizations.of(context)?.selectUser,
              isConfirmButtonInBottom: true,
              onNext: (value) async {
                ref.read(adhocCiltProvider.notifier).assignUser(value);
                Navigator.of(context).pop();
                var result = await Navigator.push(
                    context,
                    PageRouteBuilder(
                      pageBuilder: (context, animation, secondaryAnimation) =>
                          AdHocCiltReview(onClose: widget.onClose),
                      transitionsBuilder:
                          (context, animation, secondaryAnimation, child) {
                        const begin = Offset(1.0, 0.0);
                        const end = Offset.zero;
                        const curve = Curves.ease;

                        var tween = Tween(begin: begin, end: end)
                            .chain(CurveTween(curve: curve));
                        var offsetAnimation = animation.drive(tween);

                        return SlideTransition(
                          position: offsetAnimation,
                          child: child,
                        );
                      },
                    ));

                if (result != null) {
                  Navigator.of(context).pop(true);
                }
              },
            ),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              const begin = Offset(1.0, 0.0);
              const end = Offset.zero;
              const curve = Curves.ease;

              var tween =
                  Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
              var offsetAnimation = animation.drive(tween);

              return SlideTransition(
                position: offsetAnimation,
                child: child,
              );
            },
          ),
        );
      } else {
        asignee = ref.read(userProvider);
        var result = await Navigator.push(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  AdHocCiltReview(
                onClose: widget.onClose,
              ),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                const begin = Offset(1.0, 0.0);
                const end = Offset.zero;
                const curve = Curves.ease;

                var tween = Tween(begin: begin, end: end)
                    .chain(CurveTween(curve: curve));
                var offsetAnimation = animation.drive(tween);

                return SlideTransition(
                  position: offsetAnimation,
                  child: child,
                );
              },
            ));

        if (result != null) {
          Navigator.of(context).pop(true);
        }
      }
      if (asignee != null) {}
    } else {
      UIHelper.showSnackBar(context,
          message: AppLocalizations.of(context)!
              .please_selected_A_plan_from_inspection);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(child: LayoutBuilder(builder: (context, constraints) {
      return Scaffold(
        backgroundColor: AppColors.white,
        appBar: AppBar(
          elevation: 0,
          backgroundColor: AppColors.white,
          leadingWidth: 30,
          leading: IconButton(
              onPressed: () {
                if (UIHelper().getScreenType(context) == ScreenType.desktop) {
                  widget.onClose!(null);
                } else {
                  Navigator.of(context).pop(true);
                }
                if (kIsWeb) {
                  //TODO: Ranjith
                  Navigator.pop(context);
                }
              
              },
              icon: Icon(
                Icons.arrow_back_ios,
                color: AppColors.titleTextColor,
                size: 20,
              )),
          title: Text(
            AppLocalizations.of(context)!.selectPlan,
            style: UIHelper.titleStyle14(),
          ),
          actions: [],
        ),
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            children: [
              TextField(
                controller: searchController,
                onChanged: (value) {
                  final role = ref.read(roleProvider);

                  ref.read(adhocCiltProvider.notifier).searchPlans(
                      searchQuery: value,
                      isCreateInspection: UIHelper.isCreate(role!.inspection!),
                      isCreateCilt: UIHelper.isCreate(role.cilt!));
                },
                decoration: InputDecoration(
                  contentPadding: const EdgeInsets.symmetric(
                    vertical: 5.0,
                    horizontal: 10.0,
                  ),
                  hintText: AppLocalizations.of(context)!.search,
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.black),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.cancel),
                    onPressed: () {
                      ref.read(adhocCiltProvider.notifier).resetPlans();
                      searchController.clear();
                    },
                  ),
                ),
              ),
              Expanded(child: getCiltAndInspectionToggles()),
            ],
          ),
        ),
        bottomNavigationBar: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: ElevatedButton(
              onPressed: _navigateToReviewAndAssignmentScreen,
              style: ElevatedButton.styleFrom(
                  backgroundColor:
                      ref.read(adhocCiltProvider).selectedPlan != null
                          ? AppColors.primaryColor
                          : AppColors.grey),
              child: Text(AppLocalizations.of(context)!.next),
            )),
      );
    }));
  }

  Widget getCiltAndInspectionToggles() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isCreate(role.cilt!) &&
          UIHelper.isCreate(role.inspection!)) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: RoundSegmentedSwitch(
                  height: 30,
                  value: ref.watch(adhocCiltProvider).isToggled,
                  firstLabel: (UIHelper.isCreate(role.inspection!)
                      ? AppLocalizations.of(context)!.inspection
                      : ''),
                  secondLabel: (UIHelper.isCreate(role.cilt!)
                      ? AppLocalizations.of(context)!.cilt
                      : ''),
                  onChanged: (value) {
                    ref
                        .read(adhocCiltProvider.notifier)
                        .updateToggleState(value);
                  }),
            ),
            !ref.watch(adhocCiltProvider).isToggled
                ? Expanded(
                    child: ListView.builder(
                        physics: const BouncingScrollPhysics(),
                        shrinkWrap: true,
                        itemCount:
                            ref.watch(adhocCiltProvider).inspectionPlans.length,
                        itemBuilder: (context, index) {
                          INSPECTION_PLAN_HEADER paln_header = ref
                              .watch(adhocCiltProvider)
                              .inspectionPlans[index];

                          return InkWell(
                            onTap: () {
                              if (!ref
                                  .read(adhocCiltProvider.notifier)
                                  .isPlanSelectedOrNot(paln_header)) {
                                ref
                                    .watch(adhocCiltProvider.notifier)
                                    .selectPlan(paln_header);
                              } else {
                                ref
                                    .watch(adhocCiltProvider.notifier)
                                    .selectPlan(null);
                              }
                            },
                            child: Container(
                              margin: const EdgeInsets.symmetric(vertical: 2.5),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10.0, vertical: 5),
                              decoration: BoxDecoration(
                                // border: Border.all(
                                //     color: Colors.transparent, width: 1.0),
                                borderRadius: BorderRadius.circular(5.0),
                                color: ref
                                        .watch(adhocCiltProvider.notifier)
                                        .isPlanSelectedOrNot(paln_header)
                                    ? AppColors.primaryColor
                                    : null,
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  paln_header.title ?? "",
                                  style: TextStyle(
                                      color: ref
                                              .watch(adhocCiltProvider.notifier)
                                              .isPlanSelectedOrNot(paln_header)
                                          ? AppColors.white
                                          : null,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500),
                                ),
                              ),
                            ),
                          );
                        }),
                  )
                : Expanded(
                    child: ListView.builder(
                        physics: const BouncingScrollPhysics(),
                        shrinkWrap: true,
                        itemCount:
                            ref.watch(adhocCiltProvider).ciltPlans.length,
                        itemBuilder: (context, index) {
                          CILT_PLAN_HEADER cilt_paln_header =
                              ref.watch(adhocCiltProvider).ciltPlans[index];

                          return InkWell(
                            onTap: () {
                              if (!ref
                                  .watch(adhocCiltProvider.notifier)
                                  .isPlanSelectedOrNot(cilt_paln_header)) {
                                ref
                                    .watch(adhocCiltProvider.notifier)
                                    .selectPlan(cilt_paln_header);
                              } else {
                                ref
                                    .watch(adhocCiltProvider.notifier)
                                    .selectPlan(null);
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10.0, vertical: 5),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5.0),
                                color: ref
                                        .watch(adhocCiltProvider.notifier)
                                        .isPlanSelectedOrNot(cilt_paln_header)
                                    ? AppColors.primaryColor
                                    : null,
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  cilt_paln_header.title ?? "",
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: ref
                                              .watch(adhocCiltProvider.notifier)
                                              .isPlanSelectedOrNot(
                                                  cilt_paln_header)
                                          ? AppColors.white
                                          : null,
                                      fontWeight: FontWeight.w500),
                                ),
                              ),
                            ),
                          );
                        }),
                  )
          ],
        );
      } else if (UIHelper.isCreate(role.cilt!)) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Container(
                height: 32,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: AppColors.primaryColor, width: 2),
                  color: AppColors.white,
                ),
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Text(
                      AppLocalizations.of(context)!.cilt,
                      style: TextStyle(
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
              ),
            ),
            Flexible(
              fit: FlexFit.loose,
              child: ListView.builder(
                  itemCount: ref.watch(adhocCiltProvider).ciltPlans.length,
                  shrinkWrap: true,
                  physics: const BouncingScrollPhysics(),
                  itemBuilder: (context, index) {
                    CILT_PLAN_HEADER cilt_paln_header =
                        ref.watch(adhocCiltProvider).ciltPlans[index];

                    return InkWell(
                      onTap: () {
                        if (!ref
                            .watch(adhocCiltProvider.notifier)
                            .isPlanSelectedOrNot(cilt_paln_header)) {
                          ref
                              .watch(adhocCiltProvider.notifier)
                              .selectPlan(cilt_paln_header);
                        } else {
                          ref
                              .watch(adhocCiltProvider.notifier)
                              .selectPlan(null);
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10.0, vertical: 5),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5.0),
                          color: ref
                                  .watch(adhocCiltProvider.notifier)
                                  .isPlanSelectedOrNot(cilt_paln_header)
                              ? AppColors.primaryColor
                              : null,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            cilt_paln_header.title ?? "",
                            style: TextStyle(
                                fontSize: 16,
                                color: ref
                                        .watch(adhocCiltProvider.notifier)
                                        .isPlanSelectedOrNot(cilt_paln_header)
                                    ? AppColors.white
                                    : null,
                                fontWeight: FontWeight.w500),
                          ),
                        ),
                      ),
                    );
                  }),
            )
          ],
        );
      } else if (UIHelper.isCreate(role.inspection!)) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Container(
                height: 32,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: AppColors.primaryColor, width: 2),
                  color: AppColors.white,
                ),
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Text(
                      AppLocalizations.of(context)!.inspection,
                      style: TextStyle(
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
              ),
            ),
            Flexible(
              fit: FlexFit.loose,
              child: ListView.builder(
                  itemCount:
                      ref.watch(adhocCiltProvider).inspectionPlans.length,
                  physics: const BouncingScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    INSPECTION_PLAN_HEADER paln_header =
                        ref.watch(adhocCiltProvider).inspectionPlans[index];

                    return InkWell(
                      onTap: () {
                        if (!ref
                            .read(adhocCiltProvider.notifier)
                            .isPlanSelectedOrNot(paln_header)) {
                          ref
                              .watch(adhocCiltProvider.notifier)
                              .selectPlan(paln_header);
                        } else {
                          ref
                              .watch(adhocCiltProvider.notifier)
                              .selectPlan(null);
                        }
                      },
                      child: Container(
                        margin: const EdgeInsets.symmetric(vertical: 2.5),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10.0, vertical: 5),
                        decoration: BoxDecoration(
                          // border: Border.all(
                          //     color: Colors.transparent, width: 1.0),
                          borderRadius: BorderRadius.circular(5.0),
                          color: ref
                                  .watch(adhocCiltProvider.notifier)
                                  .isPlanSelectedOrNot(paln_header)
                              ? AppColors.primaryColor
                              : null,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            paln_header.title ?? "",
                            style: TextStyle(
                                color: ref
                                        .watch(adhocCiltProvider.notifier)
                                        .isPlanSelectedOrNot(paln_header)
                                    ? AppColors.white
                                    : null,
                                fontSize: 16,
                                fontWeight: FontWeight.w500),
                          ),
                        ),
                      ),
                    );
                  }),
            )
          ],
        );
      } else {
        return Container();
      }
    } else {
      return Container();
    }
  }
}
