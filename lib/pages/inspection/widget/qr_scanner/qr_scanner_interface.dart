// lib/qr_scanner/qr_scanner_interface.dart
import 'package:flutter/material.dart';

/// Abstract interface for a QR code scanner widget.
/// All concrete implementations (web, mobile) must extend this class.
abstract class QrScannerView extends StatelessWidget {
  const QrScannerView({
    super.key,
    required this.scanController,
    required this.onCompleteButton,
    required this.onUndo,
  });

  final TextEditingController scanController;
  final Function({required String dataString}) onCompleteButton;
  final Function() onUndo;

  // No build method here; it's implemented by the concrete subclasses.
  // This class primarily defines the contract (the properties it expects).
}

/// A factory function to get the platform-specific QR scanner view.
/// This function's implementation will be conditionally provided
/// by either 'qr_scanner_web.dart' or 'qr_scanner_mobile.dart'.
QrScannerView getQrScannerView({
  Key? key,
  required TextEditingController scanController,
  required Function({required String dataString}) onCompleteButton,
  required Function() onUndo,
}) =>
    // This line should technically never be reached if conditional imports work correctly.
    // It's a fallback for unsupported platforms or if there's a misconfiguration.
    throw UnsupportedError('Unsupported platform for QR Scanner');
