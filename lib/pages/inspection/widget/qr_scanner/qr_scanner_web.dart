// lib/qr_scanner/qr_scanner_web.dart
import 'package:flutter/material.dart';
// Conditional import for flutter_web_qrcode_scanner, though in this specific file
// it's okay to have it without the 'if' since this file itself is conditionally imported.
// However, adding the 'if' is good practice for consistency and clarity.
import 'package:flutter_web_qrcode_scanner/flutter_web_qrcode_scanner.dart'
    if (dart.library.html) 'package:flutter_web_qrcode_scanner/flutter_web_qrcode_scanner.dart';

import 'qr_scanner_interface.dart'; // Import the abstract interface

/// Web implementation of the QR code scanner view.
class WebQrScannerView extends QrScannerView {
  const WebQrScannerView({
    super.key,
    required super.scanController,
    required super.onCompleteButton,
    required super.onUndo,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: FlutterWebQrcodeScanner(
        cameraDirection: CameraDirection.front,
        onGetResult: (result) async {
          // This pop assumes the scanner is shown in a dialog or new route.
          // If the scanner is embedded directly, you might not pop here.
          Navigator.pop(
              context); // Close the dialog/route containing the scanner

          // Update the TextEditingController
          scanController.text = result;

          // Call the provided callbacks
          if (scanController.text.isNotEmpty) {
            await onCompleteButton(dataString: scanController.text);
          } else {
            scanController.text = ''; // Clear if no result
            await onUndo();
          }
        },
        stopOnFirstResult: true,
        width: 280,
        height: 280,
        onError: (error) {
          debugPrint('Web QR Scanner Error: $error');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Web scanner error: $error')),
          );
        },
        onPermissionDeniedError: () {
          debugPrint('Web QR Scanner Permission Denied');
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('Camera permission denied for web scanner.')),
          );
        },
      ),
    );
  }
}

// Concrete factory implementation for web.
// This overrides the default 'throw UnsupportedError' from qr_scanner_interface.dart.
QrScannerView getQrScannerView({
  Key? key,
  required TextEditingController scanController,
  required Function({required String dataString}) onCompleteButton,
  required Function() onUndo,
}) =>
    WebQrScannerView(
      key: key,
      scanController: scanController,
      onCompleteButton: onCompleteButton,
      onUndo: onUndo,
    );
