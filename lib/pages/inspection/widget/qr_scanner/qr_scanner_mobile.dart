// lib/qr_scanner/qr_scanner_mobile.dart
import 'package:flutter/material.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

import 'qr_scanner_interface.dart'; // Import the abstract interface

/// Mobile implementation of the QR code scanner view using qr_code_scanner package.
class MobileQrScannerView extends QrScannerView {
  const MobileQrScannerView({
    super.key,
    required super.scanController,
    required super.onCompleteButton,
    required super.onUndo,
  });

  @override
  Widget build(BuildContext context) {
    return MobileQrScannerWidget(
      scanController: scanController,
      onCompleteButton: onCompleteButton,
      onUndo: onUndo,
    );
  }
}

class MobileQrScannerWidget extends StatefulWidget {
  final TextEditingController scanController;
  final Function({required String dataString}) onCompleteButton;
  final Function() onUndo;

  const MobileQrScannerWidget({
    Key? key,
    required this.scanController,
    required this.onCompleteButton,
    required this.onUndo,
  }) : super(key: key);

  @override
  State<MobileQrScannerWidget> createState() => _MobileQrScannerWidgetState();
}

class _MobileQrScannerWidgetState extends State<MobileQrScannerWidget> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  bool isScanning = true;

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) {
      if (isScanning && scanData.code != null) {
        isScanning = false; // Prevent multiple scans

        // Update the controller
        widget.scanController.text = scanData.code!;

        // Call the completion callback
        widget.onCompleteButton(dataString: scanData.code!);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 280,
      height: 280,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: QRView(
          key: qrKey,
          onQRViewCreated: _onQRViewCreated,
          formatsAllowed: [
            BarcodeFormat.qrcode,
            BarcodeFormat.aztec,
            BarcodeFormat.dataMatrix,
            BarcodeFormat.pdf417,
            BarcodeFormat.code39,
            BarcodeFormat.code93,
            BarcodeFormat.code128,
            BarcodeFormat.ean8,
            BarcodeFormat.ean13,
            BarcodeFormat.itf,
            BarcodeFormat.upcE,
            BarcodeFormat.codabar,
          ],
          overlay: QrScannerOverlayShape(
            borderColor: Colors.blue,
            borderRadius: 10,
            borderLength: 30,
            borderWidth: 10,
            cutOutSize: 200,
          ),
        ),
      ),
    );
  }
}

// Concrete factory implementation for mobile.
// This overrides the default 'throw UnsupportedError' from qr_scanner_interface.dart.
QrScannerView getQrScannerView({
  Key? key,
  required TextEditingController scanController,
  required Function({required String dataString}) onCompleteButton,
  required Function() onUndo,
}) =>
    MobileQrScannerView(
      key: key,
      scanController: scanController,
      onCompleteButton: onCompleteButton,
      onUndo: onUndo,
    );
