import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:rounds/be/INSPECTION_PLAN_HEADER.dart';
import 'package:rounds/be/INSPECTION_SECTION.dart';
import 'package:rounds/be/KPI_HEADER.dart';
import 'package:rounds/be/PICKLIST_CODE.dart';
import 'package:rounds/pages/inspection/inspection_screen.dart';
import 'package:rounds/pages/inspection/widget/qr_scanner/qr_scanner_view.dart';
import 'package:rounds/widgets/qr_scan_page.dart';
import 'package:rounds/widgets/scanner/web.dart';
import 'package:signature/signature.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/providers/inspection/inspection_header_provider.dart';
import 'package:rounds/providers/job_creation/job_header_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:intl/intl.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:uuid/uuid.dart';
import 'package:webviewx_plus/webviewx_plus.dart';
import '../../../be/ASSET_HEADER.dart';
import '../../../be/DOCUMENT_ATTACHMENT.dart';
import '../../../be/DOCUMENT_HEADER.dart';
import '../../../be/FAULT_HEADER.dart';
import '../../../be/INSPECTION_TASK.dart';
import '../../../be/INSP_EXEC_ACTION.dart';
import '../../../be/INSP_EXEC_DOC.dart';
import '../../../be/INSP_EXEC_HEADER.dart';
import '../../../be/INSP_EXEC_TASK.dart';
import '../../../be/LOCATION_HEADER.dart';
import '../../../be/SKIP_REASON_HEADER.dart';
import '../../../be/SYSTEM_CONDITION_HEADER.dart';
import '../../../helpers/db_helper.dart';
import '../../../helpers/pa_helper.dart';
import '../../../helpers/ui_helper.dart';
import '../../../providers/assets/asset_provider.dart';
import '../../../providers/assets/floc_provider.dart';
import '../../../providers/assets/kpi_provider.dart';
import '../../../providers/attachments/attachment_provider.dart';
import '../../../providers/fault/fault_header_provider.dart';
import '../../../providers/fault/fault_type_provider.dart';
import '../../../providers/inspection/inspection_plan_header_provider.dart';
import '../../../providers/inspection/inspection_task_provider.dart';
import '../../../providers/user_provider.dart';
import '../../../utils/app_constants.dart';
import '../../../utils/utils.dart';
import '../../../widgets/inspection_custom_field.dart';
import '../../../widgets/round_attachment_picker.dart';
import '../../fault/fault_detail_screen.dart';
import '../../fault/tabs/edit_fault_field_provider.dart';
import '../inspection_task_widgets/check_box_widget.dart';
import '../inspection_task_widgets/choice_chip_widget.dart';
import 'package:flutter_html/flutter_html.dart';
import '../inspection_task_widgets/signature_widget.dart';
import '../inspection_task_widgets/slider_widget.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:path/path.dart' as path;

final expandedTileInspectionProvider = StateProvider<int>((ref) => -1);
final expandedAttachmentInspectionProvider = StateProvider<int>((ref) => -1);
final expandedCommentInspectionProvider = StateProvider<int>((ref) => -1);
final expandedInfoInspectionProvider = StateProvider<int>((ref) => -1);

class JobTaskCard extends ConsumerStatefulWidget {
  final INSPECTION_TASK task;
  final int index;
  INSP_EXEC_TASK? execTaskHeader;
  final INSP_EXEC_HEADER? inspExecHeader;
  final bool isDuratonRequried;
  final int totaltaskcount;
  final int sectionIndex;
  final ScrollController scrollController;
  final WidgetRef widref;
  final BuildContext ctx;
  JobTaskCard({
    super.key,
    required this.task,
    required this.index,
    this.execTaskHeader,
    required this.inspExecHeader,
    required this.scrollController,
    required this.sectionIndex,
    required this.totaltaskcount,
    required this.widref,
    required this.ctx,
    this.isDuratonRequried = false,
  });

  @override
  _JobTaskCardState createState() => _JobTaskCardState();
}

class _JobTaskCardState extends ConsumerState<JobTaskCard> {
  TextEditingController commentController = TextEditingController();
  TextEditingController infoController = TextEditingController();
  late TextEditingController controller;
  late TextEditingController inputController;
  late TextEditingController timerController;
  late TextEditingController dateAndTimerController;
  late TextEditingController dateRangeController;
  late TextEditingController numericController;
  late TextEditingController locationController;
  late TextEditingController linkController;

  late TextEditingController instructionController;
  WebViewXController? webviewController;
  bool? canEdit = false;

  TextEditingController delayReason = TextEditingController();
  TextEditingController reasonController = TextEditingController();
  List<DOCUMENT_HEADER> addedAttachments = [];
  int selectedImageIndex = -1;
  bool isUploading = false;
  bool isHovered = false;
  bool isEditSignature = false;
  int hoveredIndices = -1;
  double uploadProgress = 0.0;
  List<SKIP_REASON_HEADER> skipReasonList = [];
  final SignatureController signatureController = SignatureController(
    penStrokeWidth: 2,
    penColor: Colors.black,
    exportBackgroundColor: Colors.white,
  );
  final TextEditingController scanController = TextEditingController();

  late SYSTEM_CONDITION_HEADER systemcondition;

  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');

  bool showScanner = false;
  Widget sign = SizedBox();
  double min = 0;
  double max = 0;
  double numericLower = 0;
  double numericUpper = 0;
  double numericStandard = 0;
  String? numericLowerMsg;
  String? numericUpperMsg;
  bool onCheckBox = false;
  String? validationMessage;
  String selectedChoiceChip = '';
  List<String?> choiceChipData = [];
  List<String?> choiceChipColorsData = [];

  double _sliderValue = 0;
  bool _isChecked = false;
  bool _isCheckedLink = false;
  bool _isCheckedInstruction = false;
  bool _isCheckedInput = false;
  final FocusNode _focusNode = FocusNode();
  final FocusNode _commentFocusNode = FocusNode();
  String? previousNumericData;
  double? previousSliderData;

  @override
  void initState() {
    super.initState();
    getData();
    controller = TextEditingController();
    inputController = TextEditingController();
    timerController = TextEditingController();
    dateAndTimerController = TextEditingController();
    dateRangeController = TextEditingController();
    locationController = TextEditingController();
    linkController = TextEditingController();
    instructionController = TextEditingController();
    numericController = TextEditingController();
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        numericController.selection = TextSelection.collapsed(
          offset: numericController.text.length,
        );
      }
    });
    _commentFocusNode.addListener(() async {
      if (!_commentFocusNode.hasFocus) {
        final inspExecHeader =
            ref.read(inspectionHeaderProvider.notifier).state;

        await DbHelper().taskInspectionUpdate(
          task: widget.task,
          inspHeader: inspExecHeader,
          p_mode: "M",
          comment: commentController.text,
        );

        ref
            .read(inspExecuteTaskListProvider.notifier)
            .updateInspExexTask(widget.execTaskHeader!);

        await ref
            .read(inspExecuteTaskListProvider.notifier)
            .getInspExecuteTaskList();

        setState(() {
          widget.execTaskHeader?.p_mode = AppConstants.modified;
          widget.execTaskHeader?.comments = commentController.text;
        });
      }
    });
    initializingControllers();
  }

  @override
  void dispose() {
    controller.dispose();
    inputController.dispose();
    timerController.dispose();
    dateAndTimerController.dispose();
    dateRangeController.dispose();
    locationController.dispose();
    linkController.dispose();
    scanController.dispose();
    instructionController.dispose();
    commentController.dispose();
    _focusNode.dispose();
    _commentFocusNode.dispose();
    super.dispose();
  }

  getData() {
    if (widget.inspExecHeader!.insp_id != null) {
      widget.execTaskHeader = ref
          .read(inspExecuteTaskListProvider.notifier)
          .getInspectionExecTaskHeaderByTask(
              widget.task, widget.inspExecHeader!);
    }
  }

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
    skipReasonList = ref.watch(skipReasonListProvider.notifier).state;
    commentController = (widget.execTaskHeader?.comments != null &&
            widget.execTaskHeader?.comments != '')
        ? TextEditingController(
            text: "${widget.execTaskHeader?.comments}" ?? "")
        : TextEditingController();
  }

  @override
  void didUpdateWidget(covariant JobTaskCard oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.task.task_id == oldWidget.task.task_id &&
        widget.execTaskHeader?.status != oldWidget.execTaskHeader?.status) {
      getData();
      initializingControllers();
    }
  }

  @override
  Widget build(BuildContext context) {
    final expandedIndex = ref.watch(expandedTileInspectionProvider);
    final inspHeader = ref.watch(inspectionHeaderProvider.notifier).state;
    final isExpanded = expandedIndex ==
        "${widget.index} ${widget.task.task_id.toString()}".hashCode;
    return GestureDetector(
      onTap: () {
        ref
            .read(documentAttachmentProvider.notifier)
            .fetchDocumentAttachments();
        ref
            .read(inspectionTaskExecDocumentHeaderProvider.notifier)
            .fetchInspectionTaskDocumentHeaders(
                widget.execTaskHeader!, inspHeader, widget.task);

        ref.read(expandedTileInspectionProvider.notifier).state = isExpanded
            ? -1
            : "${widget.index} ${widget.task.task_id.toString()}".hashCode;
        if (kIsWeb) {
          final expandedAttachmentIndex =
              ref.watch(expandedAttachmentInspectionProvider);
          final isAttachmentExpanded = expandedAttachmentIndex == widget.index;
          ref.read(expandedAttachmentInspectionProvider.notifier).state =
              isAttachmentExpanded ? -1 : widget.index;
          ref.read(expandedCommentInspectionProvider.notifier).state = -1;
          ref.read(expandedInfoInspectionProvider.notifier).state = -1;
          ref
              .read(inspectionTaskDocumentHeaders.notifier)
              .fetchInspectionTaskDocumentHeaders(
                  widget.execTaskHeader!, widget.task);
        }
        if (showAttachmentDot(widget.execTaskHeader!)) {
          ref.read(expandedAttachmentInspectionProvider.notifier).state =
              widget.index;
          ref.read(expandedCommentInspectionProvider.notifier).state = -1;
          ref.read(expandedInfoInspectionProvider.notifier).state = -1;
        } else if (showNoteDot(widget.execTaskHeader!)) {
          ref.read(expandedAttachmentInspectionProvider.notifier).state = -1;
          ref.read(expandedInfoInspectionProvider.notifier).state = -1;
          ref.read(expandedCommentInspectionProvider.notifier).state =
              widget.index;
        } else if (showDot(widget.execTaskHeader?.insp_task_id!)) {
          ref.read(expandedAttachmentInspectionProvider.notifier).state = -1;
          ref.read(expandedCommentInspectionProvider.notifier).state = -1;
          ref.read(expandedInfoInspectionProvider.notifier).state =
              widget.index;
        }
        if (widget.sectionIndex == widget.totaltaskcount - 1) {
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            await Future.delayed(const Duration(milliseconds: 400));
            if (widget.scrollController.hasClients) {
              widget.scrollController.animateTo(
                widget.scrollController.position.maxScrollExtent,
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeOut,
              );
            }
          });
        }
      },
      child: Container(
        decoration: UIHelper.cardDecoration(),
        child: Padding(
          padding: UIHelper.columnFieldPadding(),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                key: navigatorKey,
                children: [
                  _getTitleRowWidget(widget.task),
                  AbsorbPointer(
                      absorbing: false,
                      child: _getTaskWidget(widget.task, isExpanded)),
                ],
              ),
              UIHelper.sizedBox10(),
              AnimatedCrossFade(
                firstChild: const SizedBox(
                  height: 0,
                  width: double.infinity,
                ),
                secondChild: _buildExpandedContent(),
                crossFadeState: isExpanded
                    ? CrossFadeState.showSecond
                    : CrossFadeState.showFirst,
                duration: Duration(milliseconds: 300),
                sizeCurve: Curves.easeInOut,
              ),
            ],
          ),
        ),
      ),
    );
  }

  final GlobalKey navigatorKey = GlobalKey();

  Widget _getTitleRowWidget(INSPECTION_TASK task) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Align(
          alignment: Alignment.topLeft,
          child: _getTaskTypeWidget(task.task_type.toString(), true),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ScreenType.desktop != UIHelper().getScreenType(context)
                      ? Flexible(
                          child: Text(
                            task.title.toString(),
                            style: TextStyle(
                                fontSize: 14,
                                color: AppColors.titleTextColor,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 0.1,
                                overflow: TextOverflow.ellipsis),
                          ),
                        )
                      : Text(
                          task.title.toString(),
                          style: TextStyle(
                              fontSize: 14,
                              color: AppColors.titleTextColor,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.1,
                              overflow: TextOverflow.ellipsis),
                        ),
                  task.mandatory != null
                      ? task.mandatory == 'true'
                          ? const Padding(
                              padding: EdgeInsets.only(left: 5),
                              child: Text(
                                '*',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: AppColors.redAccentColor,
                                  fontWeight: FontWeight.w600,
                                  letterSpacing: 0.1,
                                ),
                              ),
                            )
                          : SizedBox()
                      : SizedBox(),
                  if (task.task_type.toString() == 'h')
                    Padding(
                      padding: const EdgeInsets.only(left: 13, top: 2),
                      child: CheckboxWidget(
                        onChanged:
                            isExecutionVisible(task: widget.execTaskHeader!)
                                ? (value) async {
                                    setState(() {
                                      _isChecked = value ?? false;
                                    });
                                    if (_isChecked) {
                                      await onCompleteButton(
                                          dataDouble: _isChecked ? 1.0 : 0.0);
                                    } else {
                                      await onUndo();
                                    }
                                  }
                                : (v) {},
                        initialValue: false,
                        isChecked: _isChecked,
                      ),
                    ),
                ],
              ),
              (task.location_id != null && task.location_id != '')
                  ? Row(
                      children: [
                        Align(
                          alignment: Alignment.topLeft,
                          child: Text(
                            '${AppLocalizations.of(context)!.location_id} : ',
                            style: TextStyle(
                              fontSize: 12,
                              color: AppColors.titleTextColor,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.1,
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 3,
                        ),
                        Text(
                          task.location_id.toString(),
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.titleTextColor,
                            letterSpacing: 0.1,
                          ),
                        ),
                      ],
                    )
                  : const SizedBox(),
              (task.asset_no != null && task.asset_no != '')
                  ? Row(
                      children: [
                        Align(
                          alignment: Alignment.topLeft,
                          child: Text(
                            '${AppLocalizations.of(context)!.asset} : ',
                            style: TextStyle(
                              fontSize: 12,
                              color: AppColors.titleTextColor,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.1,
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 3,
                        ),
                        Text(
                          task.asset_no.toString(),
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.titleTextColor,
                            fontWeight: FontWeight.w300,
                            letterSpacing: 0.1,
                          ),
                        ),
                      ],
                    )
                  : SizedBox(),
            ],
          ),
        ),
        Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              isExecutionVisible(task: widget.execTaskHeader!)
                  ? _getSkipButtons()
                  : widget.execTaskHeader?.status ==
                          AppConstants.STATE_TASK_COMP
                      ? _getSkipButtons()
                      : SizedBox(),
              widget.execTaskHeader?.status == AppConstants.STATE_TASK_COMP &&
                      widget.execTaskHeader?.reason != null &&
                      (widget.execTaskHeader?.reason ?? "").isNotEmpty
                  ? Center(
                      child: Padding(
                        padding: const EdgeInsets.only(top: 5.0, left: 8),
                        child: Text(
                          getReasonDescriptionValue(),
                        ),
                      ),
                    )
                  : const SizedBox(),
            ])

        // : SizedBox(),
      ],
    );
  }

  String getReasonDescriptionValue() {
    String? reasonReason;
    if (widget.execTaskHeader?.reason != null &&
        widget.execTaskHeader!.reason!.isNotEmpty) {
      reasonReason = widget.execTaskHeader!.reason!;
    }
    var filteredList = skipReasonList
        .where((element) => element.reason == reasonReason)
        .toList();

    String description = filteredList.isNotEmpty
        ? filteredList[0].description!
        : AppLocalizations.of(context)!.no_reason_found;
    return description;
  }

  _getSkipButtons() {
    return Align(
      alignment: Alignment.centerRight,
      child: Row(
        children: [
          (widget.task.task_type.toString() == 'l' ||
                  widget.task.task_type.toString() == 'j' ||
                  widget.task.task_type.toString() == 'd')
              ? ((widget.execTaskHeader?.status ==
                          AppConstants.STATE_TASK_COMP &&
                      (widget.execTaskHeader?.p_mode == null)))
                  ? Container()
                  : widget.execTaskHeader?.status ==
                              AppConstants.STATE_TASK_COMP &&
                          (widget.execTaskHeader?.reason != null &&
                              widget.execTaskHeader?.reason != "")
                      ? ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            elevation: 0,
                            padding: widget.execTaskHeader?.status ==
                                    AppConstants.STATE_TASK_COMP
                                ? EdgeInsets.all(10)
                                : EdgeInsets.zero,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(15)),
                            backgroundColor: AppColors.offWhiteColor,
                          ),
                          onPressed: widget.execTaskHeader?.status ==
                                  AppConstants.STATE_TASK_COMP
                              ? () {}
                              : () {
                                  onSkipButton();
                                },
                          child: Text(
                            AppLocalizations.of(context)!.skipped,
                            style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: AppColors.redAccentColor,
                                fontSize: 14),
                          ))
                      : Padding(
                          padding: const EdgeInsets.only(top: 5, right: 8.0),
                          child: Row(
                            children: [
                              Padding(
                                padding: EdgeInsets.only(right: 8.0),
                                child: Text(AppLocalizations.of(context)!
                                    .mark_as_read_instructions),
                              ),
                              CheckboxWidget2(
                                onChanged: isExecutionVisible(
                                        task: widget.execTaskHeader!)
                                    ? (value) async {
                                        if (widget.task.task_type.toString() ==
                                            'l') {
                                          setState(() {
                                            _isCheckedInput = value ?? false;
                                          });
                                          if (_isCheckedInput) {
                                            await onCompleteButton(
                                                dataDouble:
                                                    _isCheckedInput ? 1 : 0);
                                          } else {
                                            await onUndo();
                                          }
                                        } else if (widget.task.task_type
                                                .toString() ==
                                            'j') {
                                          setState(() {
                                            _isCheckedLink = value ?? false;
                                          });
                                          if (_isCheckedLink) {
                                            await onCompleteButton(
                                                dataDouble:
                                                    _isCheckedLink ? 1 : 0);
                                          } else {
                                            await onUndo();
                                          }
                                        } else if (widget.task.task_type
                                                .toString() ==
                                            'd') {
                                          setState(() {
                                            _isCheckedInstruction =
                                                value ?? false;
                                          });
                                          if (_isCheckedInstruction) {
                                            await onCompleteButton(
                                                dataDouble:
                                                    _isCheckedInstruction
                                                        ? 1
                                                        : 0);
                                          } else {
                                            await onUndo();
                                          }
                                        }
                                      }
                                    : (v) {},
                                initialValue: false,
                                isChecked: widget.task.task_type.toString() ==
                                        'l'
                                    ? _isCheckedInput
                                    : widget.task.task_type.toString() == 'j'
                                        ? _isCheckedLink
                                        : widget.task.task_type.toString() ==
                                                'd'
                                            ? _isCheckedInstruction
                                            : false,
                              ),
                            ],
                          ),
                        )
              : widget.execTaskHeader?.status == AppConstants.STATE_TASK_COMP &&
                      (widget.execTaskHeader?.reason == null ||
                          widget.execTaskHeader?.reason == "")
                  ? Container()
                  : ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        elevation: 0,
                        padding: widget.execTaskHeader?.status ==
                                AppConstants.STATE_TASK_COMP
                            ? EdgeInsets.all(10)
                            : EdgeInsets.zero,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15)),
                        backgroundColor: AppColors.offWhiteColor,
                      ),
                      onPressed: widget.execTaskHeader?.status ==
                              AppConstants.STATE_TASK_COMP
                          ? () {}
                          : () {
                              onSkipButton();
                            },
                      child: Text(
                        widget.execTaskHeader?.status ==
                                AppConstants.STATE_TASK_COMP
                            ? AppLocalizations.of(context)!.skipped
                            : AppLocalizations.of(context)!.skip,
                        style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppColors.redAccentColor,
                            fontSize: 14),
                      )),
        ],
      ),
    );
  }

  onSkipButton() {
    final inspHeader = ref.watch(inspectionHeaderProvider);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return true;
          },
          child: StatefulBuilder(
            builder: (context, setState) {
              return Dialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.0),
                ),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxWidth: 400,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              AppLocalizations.of(context)!.select_reason,
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 14),
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        getReasonDropdown(context, widget.widref, setState),
                        const SizedBox(height: 10),
                        TextField(
                          style: const TextStyle(fontSize: 14),
                          controller: reasonController,
                          onChanged: (value) {
                            TextSelection previousSelection =
                                reasonController.selection;
                            reasonController.text = value;
                            reasonController.selection = previousSelection;
                            setState(() {});
                          },
                          maxLines: 3,
                          decoration: InputDecoration(
                            hintText:
                                AppLocalizations.of(context)!.additionalComment,
                            border: OutlineInputBorder(
                                borderSide: BorderSide(color: AppColors.grey)),
                            focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(color: AppColors.grey)),
                            enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(color: AppColors.grey)),
                            contentPadding: const EdgeInsets.symmetric(
                                vertical: 10, horizontal: 10),
                          ),
                        ),
                        const SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton(
                              onPressed: reasonController.text.isEmpty ||
                                      selectedSkipReason.isEmpty
                                  ? null
                                  : () async {
                                      if (reasonController.text == '' ||
                                          reasonController.text == null) {
                                        UIHelper.showInfoDialogWithtitleAndDescription(
                                            context,
                                            title: AppLocalizations.of(context)!
                                                .warning,
                                            description: AppLocalizations.of(
                                                    context)!
                                                .please_add_additional_message);
                                      } else {
                                        await onSaveReason(
                                            reasonController.text, inspHeader);
                                      }
                                    },
                              child: Text(
                                AppLocalizations.of(context)!.save,
                                style: TextStyle(
                                  color: reasonController.text.isEmpty ||
                                          selectedSkipReason.isEmpty
                                      ? AppColors.grey
                                      : AppColors.primaryColor,
                                ),
                              ),
                            ),
                            TextButton(
                              onPressed: () {
                                selectedSkipReason = '';
                                reasonController.clear();
                                Navigator.pop(context);
                              },
                              child: Text(
                                AppLocalizations.of(context)!.cancel,
                                style: TextStyle(
                                  color: AppColors.primaryColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  String selectedSkipReason = '';
  Widget getReasonDropdown(
      BuildContext context, WidgetRef ref, StateSetter setState) {
    final skipReasonList = ref
        .watch(skipReasonListProvider)
        .where((element) => (element.category! & 1) != 0)
        .toList();
    final skipReason = ref.watch(skipReasonInspProvider.notifier);

    final dropdownItems = skipReasonList.map((option) {
      return DropdownMenuItem<String>(
        value: option.description,
        child: Padding(
          padding: const EdgeInsets.only(left: 5.0),
          child: Text(option.description!, style: UIHelper.valueStyle()),
        ),
      );
    }).toList();

    if (dropdownItems.where((item) => item.value == '').isEmpty) {
      dropdownItems.insert(
        0,
        DropdownMenuItem<String>(
          value: '',
          child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(AppLocalizations.of(context)!.select,
                style: UIHelper.valueStyle()),
          ),
        ),
      );
    }
    return Container(
      width: MediaQuery.of(context).size.width,
      decoration: UIHelper.fieldDecoration(),
      child: DropdownButton<String>(
        elevation: 0,
        isExpanded: true,
        underline: const SizedBox(),
        value: selectedSkipReason.isNotEmpty ? selectedSkipReason : '',
        items: dropdownItems,
        onChanged: (newValue) {
          setState(() {
            selectedSkipReason = newValue!;
            skipReason.getInspSkipReason(selectedSkipReason);
          });
        },
      ),
    );
  }

  Future<void> onSaveReason(
      String reasonData, INSP_EXEC_HEADER inspHeader) async {
    final inspPlanHeader = widget.widref.watch(inspectionPlanHeaderProvider);
    final inspectionHeaderNotifier =
        await widget.widref.read(inspectionHeaderProvider.notifier);
    final inspHeader = widget.widref.watch(inspectionHeaderProvider);
    final inspCompleted = widget.widref.watch(inspCompletedProvider.notifier);
    final inspExecHeader =
        widget.widref.watch(inspExecuteTaskListProvider.notifier);
    final skipReason =
        widget.widref.watch(skipReasonInspProvider.notifier).state;

    String comment = "";
    if (widget.execTaskHeader!.comments != null) {
      comment = commentController.text;
    }

    await DbHelper().taskInspectionUpdate(
      task: widget.task,
      status: AppConstants.STATE_TASK_COMP,
      inspHeader: inspHeader,
      reasonCode: skipReason.reason.toString(),
      skipreason: reasonData,
      p_mode: "M",
      comment: comment,
      isSkipped: "true",
      dataString: null,
      dataDouble: null,
      dataBloc: null,
    );

    final dependents = await DbHelper.getInspectionTaskPlanListHeaderDependent(
      widget.task.plan_id!.toString(),
      widget.task,
    );

    if (dependents.isNotEmpty) {
      for (final dependent in dependents) {
        await _markDependentTasksRecursively(dependent, inspHeader);
      }
    }

    widget.widref
        .read(inspExecuteTaskListProvider.notifier)
        .updateInspExexTask(widget.execTaskHeader!);

    INSPECTION_SECTION? section = await DbHelper.getInspectionSectionByPlanId(
      inspHeader.plan_id.toString(),
    );

    await widget.widref.read(inspTasksProvider.notifier).getInspTasks(
          inspHeader.plan_id!,
          header: inspHeader,
          section: section!,
          plantId: inspHeader.plant_id.toString(),
        );

    await widget.widref
        .read(inspExecuteProvider.notifier)
        .getInspExecute(inspHeader);
    await inspCompleted.inspCompleted(inspHeader);
    await inspExecHeader.getInspExecuteTaskList();

    widget.execTaskHeader = widget.widref
        .read(inspExecuteTaskListProvider.notifier)
        .getInspectionExecTaskHeaderByTask(widget.task, inspHeader);

    final progresssData = await _getProgressValue(inspHeader);

    delayReason.clear();
    reasonController.clear();
    widget.widref.read(skipReasonInspProvider.notifier).clearInspSkipReason();
    selectedSkipReason = '';

    INSPECTION_SECTION? inspection_section =
        await DbHelper.getInspectionSectionByPlanId(
            inspHeader.plan_id.toString());

    await widget.widref
        .read(inspectionPlanTaskListHeaderProvider.notifier)
        .fetchInspectionPlanTaskListHeaders(
          plantId: inspHeader.plant_id.toString(),
          header: inspHeader,
          section: inspection_section!,
        );

    await widget.widref
        .read(inspExecuteProvider.notifier)
        .getInspExecute(inspHeader);
    widget.widref
        .read(numericInspectionHeaderProvider.notifier)
        .clearNumericValue(widget.execTaskHeader!);
    final numberOfCompletedTasks = widget.widref
            .watch(inspExecuteProvider)[inspHeader.insp_id] ??
        [].where((element) => element.cilt_id == inspHeader.insp_id).toList();
    final inspTasknotifier =
        widget.widref.read(inspectionTaskNotifier.notifier);
    final inspExecTaskListNotifier =
        widget.widref.read(inspExecuteTaskListProvider.notifier);
    final inspectionHeaderListNotifier =
        widget.widref.read(inspectionHeaderListProvider.notifier);
    final plant = widget.widref.read(plantProvider.notifier).state;
    final plantSection =
        widget.widref.read(plantSectionProvider.notifier).state;
    final shift = widget.widref.read(shiftProvider.notifier).state;
    final togglenotifier = widget.widref.read(inspectionToggleStateProvider);

    final List<INSP_EXEC_TASK> inspExecTasks =
        inspExecTaskListNotifier.findAllInspExecOfInsp(inspHeader);

    final docHeaderNotifier =
        widget.widref.read(inspectionTaskExecDocumentHeaderProvider.notifier);
    final List<DOCUMENT_HEADER> headerData = [];

    for (var task in inspExecTasks) {
      final headers =
          docHeaderNotifier.getDocumentHeadersForInspectionTask(task);
      if (headers != null) {
        headerData.addAll(headers);
      }
    }

    //  if (mounted) {
    Navigator.of(widget.ctx).pop();
    // }
    if (progresssData["totalTasks"] == progresssData["completedTasks"]) {
      // final dialogContext = navigatorKey.currentContext;

      await _showTaskCompletionDialog(
        inspHeader,
        widget.ctx,
        progresssData,
        widget.widref,
        inspPlanHeader,
        numberOfCompletedTasks,
        inspExecTasks,
        headerData,
        inspExecTaskListNotifier,
        inspectionHeaderNotifier: inspectionHeaderNotifier,
        inspTaskNotifier: inspTasknotifier,
        inspheaderListNotifier: inspectionHeaderListNotifier,
        plant: plant,
        plantsec: plantSection,
        shift: shift,
        togglenotifier: togglenotifier,
      ).then((value) {});
    } else {
      widget.widref
          .watch(inspExecuteTaskListProvider.notifier)
          .updateInspExexTask(widget.execTaskHeader!);

      if (widget.widref.read(inspectionToggleStateProvider)) {
        await widget.widref
            .read(inspectionTaskNotifier.notifier)
            .fetchIncompleteTasks(
              (inspPlanHeader.plan_id.toString() ?? ""),
              widget.widref.read(inspectionHeaderProvider),
              widget.widref,
            );
      }

      widget.execTaskHeader = widget.widref
          .read(inspExecuteTaskListProvider.notifier)
          .getInspectionExecTaskHeaderByTask(
              widget.task, widget.inspExecHeader!);

      widget.widref
          .read(inspectionExecTaskProvider.notifier)
          .getInspectionExecTask(widget.execTaskHeader!);

      initializingControllers();
    }
  }

  Future<void> _markDependentTasksRecursively(
    INSPECTION_TASK dependentTask,
    INSP_EXEC_HEADER inspHeader,
  ) async {
    await DbHelper().taskInspectionUpdate(
      task: dependentTask,
      status: AppConstants.STATE_TASK_COMP,
      inspHeader: inspHeader,
      reasonCode: '',
      skipreason: '',
      p_mode: "M",
      comment: "",
      isIrrelevant: "true",
      dataString: null,
      dataDouble: null,
      dataBloc: null,
    );

    final subDependents =
        await DbHelper.getInspectionTaskPlanListHeaderDependent(
      dependentTask.plan_id!.toString(),
      dependentTask,
    );

    for (final subDep in subDependents) {
      await _markDependentTasksRecursively(subDep, inspHeader);
    }
  }

  Future<Map<String, dynamic>> _getProgressValue(
      INSP_EXEC_HEADER? inspHeader) async {
    if (inspHeader != null) {
      INSPECTION_SECTION? section = await DbHelper.getInspectionSectionByPlanId(
          inspHeader.plan_id.toString());
      await widget.widref.watch(inspTasksProvider.notifier).getInspTasks(
          inspHeader.plan_id!,
          header: inspHeader,
          section: section!,
          plantId: inspHeader.plant_id.toString());
      await widget.widref
          .watch(inspExecuteProvider.notifier)
          .getInspExecute(inspHeader);
      final numberOfTasks =
          widget.widref.watch(inspTasksProvider)[inspHeader.plan_id] ?? [];
      final numberOfCompletedTasks =
          widget.widref.watch(inspExecuteProvider)[inspHeader.insp_id] ?? [];
      int totalTasks = numberOfTasks
          .where((element) =>
              element.plan_id.toString() == inspHeader.plan_id.toString())
          .length;
      int completedTasks = numberOfCompletedTasks.length;
      numberOfCompletedTasks
          .where((element) =>
              element.insp_id.toString() == inspHeader.insp_id.toString())
          .length;

      if (totalTasks == 0) {
        return {
          'percentage': 0.0,
          'completedTasks': 0,
          'totalTasks': 0,
        };
      }
      double percentage =
          double.parse((completedTasks / totalTasks).toStringAsFixed(2));
      return {
        'percentage': percentage,
        'completedTasks': completedTasks,
        'totalTasks': totalTasks,
      };
    } else {
      return {
        'percentage': 0.0,
        'completedTasks': 0,
        'totalTasks': 0,
      };
    }
  }

  Future<void> _showTaskCompletionDialog(
      INSP_EXEC_HEADER inspHeader,
      BuildContext context,
      dynamic progressValue,
      WidgetRef ref2,
      INSPECTION_PLAN_HEADER inspPlanHeader,
      List<dynamic> numberOfCompletedTasks,
      List<INSP_EXEC_TASK> execTasks,
      List<DOCUMENT_HEADER> docHeaders,
      InspExecuteTaskListNotifier inspExecuteTaskListProvider,
      {InspectionHeaderNotifier? inspectionHeaderNotifier,
      InspectionTaskListNotifier? inspTaskNotifier,
      InspectionHeaderListNotifier? inspheaderListNotifier,
      dynamic plant,
      dynamic plantsec,
      dynamic shift,
      dynamic togglenotifier}) async {
    return await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return Dialog(
          child: ConstrainedBox(
            constraints: const BoxConstraints(
              maxWidth: 400,
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Center(
                          child: Text(
                            "${AppLocalizations.of(context)!.congratulations} 🎉",
                            style: UIHelper.titleStyle16(),
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Icon(
                          Icons.cancel_outlined,
                          color: AppColors.black,
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(
                      vertical: 16.0,
                    ),
                    child: Text(
                      AppLocalizations.of(context)!
                          .all_tasks_have_successfully_completed,
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      minimumSize: const Size(double.infinity, 40),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                    onPressed: () async {
                      final current = DateTime.now();

                      DateTime endTime = Utils.getTimeStamp(
                          inspHeader.start_on.toString(),
                          inspHeader.end_at.toString());
                      Navigator.of(context).pop();
                      if (current.isAfter(endTime)) {
                        showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (context) {
                            return AlertDialog(
                              title: Text(AppLocalizations.of(context)!
                                  .please_provide_reason_for_submission_delay),
                              content: TextField(
                                decoration: InputDecoration(
                                  hintText: AppLocalizations.of(context)!
                                      .reason_for_submission_delay,
                                  border: OutlineInputBorder(
                                    borderSide:
                                        BorderSide(color: AppColors.grey),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderSide:
                                        BorderSide(color: AppColors.grey),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide:
                                        BorderSide(color: AppColors.grey),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                    vertical: 10,
                                    horizontal: 10,
                                  ),
                                ),
                                controller: delayReason,
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () async {
                                    final capturedContext = context;

                                    Navigator.of(context).pop();

                                    // dynamic progressValue =
                                    //     await _calculateProgress(inspHeader,ref);
                                    if (progressValue.isNotEmpty) {
                                      if (progressValue["totalTasks"] ==
                                          progressValue["completedTasks"]) {
                                        await getSubmitWithCloseAction(
                                            inspHeader,
                                            capturedContext,
                                            ref2,
                                            inspPlanHeader,
                                            numberOfCompletedTasks,
                                            execTasks,
                                            docHeaders,
                                            inspExecuteTaskListProvider,
                                            inspectionHeaderNotifier!,
                                            inspTaskNotifier!,
                                            inspheaderListNotifier!,
                                            plant!,
                                            plantsec!,
                                            shift!,
                                            togglenotifier);
                                      } else {
                                        await getSubmitWithoutCloseAction(
                                            inspHeader, context);
                                      }
                                    }
                                  },
                                  child: Text(
                                      AppLocalizations.of(context)!.submit),
                                ),
                                TextButton(
                                  onPressed: () {
                                    delayReason.clear();
                                    Navigator.of(context).pop();
                                  },
                                  child: Text(
                                      AppLocalizations.of(context)!.cancel),
                                ),
                              ],
                            );
                          },
                        );
                      } else {
                        // final numberOfCompletedTasks = ref
                        //      .watch(inspExecuteProvider)[inspHeader.insp_id] ??
                        //      [].where((element) => element.cilt_id == inspHeader.insp_id).toList();
                        // dynamic progressValue =
                        //     await _calculateProgress(inspHeader,ref);
                        if (progressValue.isNotEmpty) {
                          if (progressValue["totalTasks"] ==
                              progressValue["completedTasks"]) {
                            await getSubmitWithCloseAction(
                                inspHeader,
                                context,
                                ref2,
                                inspPlanHeader,
                                numberOfCompletedTasks,
                                execTasks,
                                docHeaders,
                                inspExecuteTaskListProvider,
                                inspectionHeaderNotifier!,
                                inspTaskNotifier!,
                                inspheaderListNotifier!,
                                plant!,
                                plantsec!,
                                shift!,
                                togglenotifier);
                          } else {
                            // var inspPlanHeader = ref.read(inspectionPlanHeaderProvider);
                            await getSubmitWithoutCloseAction(
                                inspHeader, context);
                          }
                        }
                      }
                    },
                    child: Text(
                      AppLocalizations.of(context)!.submit,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> getSubmitWithCloseAction(
      INSP_EXEC_HEADER inspHeader,
      BuildContext context,
      WidgetRef ref,
      INSPECTION_PLAN_HEADER inspPlanHeader,
      List<dynamic> numberOfCompletedTasks,
      List<INSP_EXEC_TASK> execTasks,
      List<DOCUMENT_HEADER> headerData,
      InspExecuteTaskListNotifier inspExecuteTaskListProvider,
      InspectionHeaderNotifier inspectionHeaderNotifier,
      InspectionTaskListNotifier inspectionTaskNotifier,
      InspectionHeaderListNotifier inspheaderListNotifier,
      dynamic plant,
      dynamic plantsec,
      dynamic shift,
      dynamic togglenotifier) async {
    final navigator = Navigator.of(context, rootNavigator: true);
    final scafcontext = ScaffoldMessenger.of(context);
    final appContext = AppLocalizations.of(context)!;
    final BuildContext buildContext = context;

    await DbHelper().markIrrelevantDependentTasks(
        planHeader: inspPlanHeader, execHeader: inspHeader);

    if (delayReason.text.isNotEmpty) {
      inspHeader.delay_comments = delayReason.text;
    }

    await AppDatabaseManager().update(
      DBInputEntity(INSP_EXEC_HEADER.TABLE_NAME, inspHeader.toJson()),
    );

    List<Future<void>> updateTasks = [];
    for (var task in numberOfCompletedTasks) {
      await DbHelper.updateInspExecTask(task);
      INSP_EXEC_ACTION action = INSP_EXEC_ACTION(
          insp_id: inspHeader.insp_id,
          user_action: AppConstants.STATE_COMPLETED);
      action.fid = inspHeader.lid;

      var result = await AppDatabaseManager().execute(
          "select * from INSP_EXEC_ACTION where INSP_ID = '${inspHeader.insp_id.toString()}'");
      if (result.isNotEmpty) {
        updateTasks.add(AppDatabaseManager().update(
            DBInputEntity(INSP_EXEC_ACTION.TABLE_NAME, action.toJson())));
      } else {
        updateTasks.add(AppDatabaseManager().insert(
            DBInputEntity(INSP_EXEC_ACTION.TABLE_NAME, action.toJson())));
      }
    }
    await Future.wait(updateTasks);

    List<DOCUMENT_HEADER> documents = headerData
        .where((element) => element.objectStatus == ObjectStatus.add)
        .toList();

    if (kIsWeb) {
      // if(mounted){
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.submitting_insp_execution);
      //}
      for (DOCUMENT_HEADER document in documents) {
        var doc = await DbHelper()
            .getAttachmentFromIndexDbByUid(document.doc_id ?? "");
        await SyncEngine().uploadAttachmentSync(
          doc ?? "",
          document.file_name ?? "",
          document.doc_id ?? "",
        );
      }

      await PAHelper.addDocumentInSyncMode(buildContext, documents);
    } else {
      await PAHelper.addDocumentInAsyncMode(buildContext, documents);
    }

    if (!kIsWeb) {
      await PAHelper.modifyInspExecInAsyncMode(buildContext, inspHeader);
    } else {
      Result? result =
          await PAHelper.modifyInspExecInSyncMode(buildContext, inspHeader);

      navigator.pop();

      await inspExecuteTaskListProvider.getInspExecuteTaskList();
      INSP_EXEC_HEADER? data =
          await DbHelper.getInspectionExeHeaderByInspIdPlanId(
              widget.inspExecHeader!);
      if (data != null) {
        await inspectionHeaderNotifier.fetchInspectionHeaders(data);
      }
    }

    delayReason.clear();
    selectedSkipReason = '';
    reasonController.clear();
    await inspectionTaskNotifier.filter(
        inspPlanHeader.plan_id.toString(), "", widget.widref);

    scafcontext.showSnackBar(
      SnackBar(
        content: Text(appContext.submitted_successfully_insp),
        duration: const Duration(seconds: 2),
      ),
    );

    final plant = widget.widref.read(plantProvider.notifier).state;
    final plantSection =
        widget.widref.read(plantSectionProvider.notifier).state;
    final shift = widget.widref.read(shiftProvider.notifier).state;
    final searchkey = widget.widref.read(roundsSearchProvider.notifier).state;

    await inspheaderListNotifier.fetchInspectionListHeaders(
        plant, plantSection, shift, widget.widref);
    if (widget.execTaskHeader != null) {
      inspExecuteTaskListProvider.updateInspExexTask(widget.execTaskHeader!);
    }

    if (togglenotifier) {
      await inspectionTaskNotifier.fetchIncompleteTasks(
          (inspPlanHeader.plan_id.toString() ?? ""), inspHeader, widget.widref);
    }
    await inspExecuteTaskListProvider.getInspExecuteTaskList();

    await widget.widref
        .read(filterInspectionHeaderListProvider.notifier)
        .filter(plant, plantSection, shift, searchkey, widget.widref);

    if (!kIsWeb) {
      INSP_EXEC_HEADER? newExecHeader =
          await DbHelper.getInspectionExecHeader(inspHeader);
      if (newExecHeader != null) {
        await widget.widref
            .read(inspectionHeaderProvider.notifier)
            .fetchInspectionHeaders(inspHeader);
      }
    }
  }

  Future<void> getSubmitWithoutCloseAction(
      INSP_EXEC_HEADER inspHeader, BuildContext context) async {
    var inspPlanHeader = ref.read(inspectionPlanHeaderProvider);

    await DbHelper().markIrrelevantDependentTasks(
        planHeader: inspPlanHeader, execHeader: inspHeader);
    inspHeader.p_mode = AppConstants.modified;
    final numberOfCompletedTasks = ref
            .watch(inspExecuteProvider)[inspHeader.insp_id] ??
        [].where((element) => element.cilt_id == inspHeader.insp_id).toList();

    if (delayReason.text.isNotEmpty) {
      inspHeader.delay_comments = delayReason.text;
    }

    await AppDatabaseManager().update(
      DBInputEntity(INSP_EXEC_HEADER.TABLE_NAME, inspHeader.toJson()),
    );

    List<INSP_EXEC_TASK> INSP_EXEC_TASKs = ref
        .read(inspExecuteTaskListProvider.notifier)
        .findAllInspExecOfInsp(inspHeader);

    List<DOCUMENT_HEADER> headerData = [];
    for (var task in INSP_EXEC_TASKs) {
      final documentHeaders = ref
          .watch(inspectionTaskExecDocumentHeaderProvider.notifier)
          .getDocumentHeadersForInspectionTask(task);
      if (documentHeaders != null) {
        headerData.addAll(documentHeaders);
      }
    }

    List<DOCUMENT_HEADER> documents = headerData
        .where((element) => element.objectStatus == ObjectStatus.add)
        .toList();

    if (kIsWeb) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.submitting_insp_execution);

      for (DOCUMENT_HEADER document in documents) {
        var doc = await DbHelper()
            .getAttachmentFromIndexDbByUid(document.doc_id ?? "");
        await SyncEngine().uploadAttachmentSync(
          doc ?? "",
          document.file_name ?? "",
          document.doc_id ?? "",
        );
      }

      if (mounted) {
        await PAHelper.addDocumentInSyncMode(context, documents);
      }
    } else {
      if (mounted) {
        await PAHelper.addDocumentInAsyncMode(context, documents);
      }
    }

    if (!kIsWeb) {
      await PAHelper.modifyInspExecInAsyncMode(context, inspHeader);
    } else {
      Result? result =
          await PAHelper.modifyInspExecInSyncMode(context, inspHeader);
      Navigator.of(context, rootNavigator: true).pop();

      await ref
          .read(inspExecuteTaskListProvider.notifier)
          .getInspExecuteTaskList();
      INSP_EXEC_HEADER? data =
          await DbHelper.getInspectionExeHeaderByInspIdPlanId(
              widget.inspExecHeader!);
      if (data != null) {
        await ref
            .read(inspectionHeaderProvider.notifier)
            .fetchInspectionHeaders(data);
      }
    }

    delayReason.clear();
    selectedSkipReason = '';
    reasonController.clear();

    if (mounted) {
      if (UIHelper().getScreenType(context) != ScreenType.desktop) {
        Navigator.pop(context, inspHeader);
      } else {
        setState(() {});
        await ref
            .read(inspExecuteTaskListProvider.notifier)
            .getInspExecuteTaskList();
        await ref
            .read(inspectionTaskNotifier.notifier)
            .filter(inspPlanHeader.plan_id.toString(), "", ref);
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text(AppLocalizations.of(context)!.submitted_successfully_insp),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );

      final plant = ref.read(plantProvider.notifier).state;
      final plantSection = ref.read(plantSectionProvider.notifier).state;
      final shift = ref.read(shiftProvider.notifier).state;
      await ref
          .read(inspectionPlanListHeaderProvider.notifier)
          .fetchInspectionPlanListHeaders();
      await ref
          .read(inspectionHeaderListProvider.notifier)
          .fetchInspectionListHeaders(plant, plantSection, shift, ref);
      await ref
          .read(inspExecuteTaskListProvider.notifier)
          .getInspExecuteTaskList();
      await ref
          .read(inspectionTaskNotifier.notifier)
          .filter(inspPlanHeader.plan_id.toString(), "", ref);
    }
  }

  bool isExecutionVisible({String? checkFor, required INSP_EXEC_TASK task}) {
    final todayDate = DateFormat('yyyyMMdd').format(DateTime.now());
    DateTime now = DateTime.now();

    String startAT = widget.inspExecHeader!.start_at.toString();
    DateTime executionDateTime =
        Utils.getTimeStamp(widget.inspExecHeader!.start_on.toString(), startAT);
    final role = ref.watch(roleProvider);

    bool isAssignedUser =
        (ref.read(userProvider)?.user_id == widget.inspExecHeader?.assigned_to);
    if (role != null &&
        (checkFor == AppConstants.faults || isAssignedUser) &&
        (widget.inspExecHeader?.status != "REJECTED") &&
        (executionDateTime.isBefore(now)) &&
        (widget.execTaskHeader?.status != AppConstants.STATE_TASK_COMP &&
                widget.execTaskHeader?.p_mode == null ||
            widget.execTaskHeader?.p_mode == 'M') &&
        (task.is_skipped != 'true')) {
      if (UIHelper.isExecute(role.inspection!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isExecutionVisibleFault(
      {String? checkFor, required INSP_EXEC_TASK task}) {
    final todayDate = DateFormat('yyyyMMdd').format(DateTime.now());
    DateTime now = DateTime.now();

    String startAT = widget.inspExecHeader!.start_at.toString();
    DateTime executionDateTime =
        Utils.getTimeStamp(widget.inspExecHeader!.start_on.toString(), startAT);
    final role = ref.watch(roleProvider);

    bool isAssignedUser =
        (ref.read(userProvider)?.user_id == widget.inspExecHeader?.assigned_to);
    if (role != null &&
        (checkFor == AppConstants.faults || isAssignedUser) &&
        (widget.inspExecHeader?.status != "REJECTED") &&
        (executionDateTime.isBefore(now))) {
      if (UIHelper.isExecute(role.inspection!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  getDuration(String time) {
    double appendedTime = double.parse(time);
    return "${appendedTime.floor()} ${AppLocalizations.of(context)!.m}";
  }

  Widget _buildExpandedContent() {
    final role = ref.watch(roleProvider);
    final expandedAttachmentIndex =
        ref.watch(expandedAttachmentInspectionProvider);
    final expandedCommentIndex = ref.watch(expandedCommentInspectionProvider);
    final expandedInfoIndex = ref.watch(expandedInfoInspectionProvider);

    final isAttachmentExpanded = expandedAttachmentIndex == widget.index;
    final isCommentExpanded = expandedCommentIndex == widget.index;
    final isInfoExpanded = expandedInfoIndex == widget.index;
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.only(bottom: 3),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              getExpandedIcon(
                  type: AppConstants.attachments,
                  child: Center(
                    child: Transform.rotate(
                      angle: 45 * (3.14159 / 180),
                      child: Icon(
                        Icons.attach_file,
                        size: 25,
                        color: AppColors.black,
                      ),
                    ),
                  ),
                  showDot: showAttachmentDot(widget.execTaskHeader!)),
              getExpandedIcon(
                  type: AppConstants.notes,
                  child: Center(
                    child: Image.asset(
                      'assets/icon/task_icons/notes.png',
                      width: 20,
                      height: 20,
                    ),
                  ),
                  showDot: showNoteDot(widget.execTaskHeader!)),
              role != null
                  ? (UIHelper.isCreate(role.fault!)
                      ? getExpandedIcon(
                          type: AppConstants.faults,
                          child: Center(
                            child: Icon(
                              Icons.error_outline,
                              color: AppColors.titleTextColor,
                              size: 25,
                            ),
                          ),
                          showDot:
                              showDot(widget.execTaskHeader?.insp_task_id!))
                      : SizedBox())
                  : SizedBox(),
              widget.task.description != null && widget.task.description != ""
                  ? getExpandedIcon(
                      type: AppConstants.info,
                      child: Center(
                        child: Icon(
                          Icons.info_outline,
                          color: AppColors.titleTextColor,
                          size: 25,
                        ),
                      ),
                    )
                  : SizedBox(),
            ],
          ),
        ),
        if (isAttachmentExpanded)
          attachmentWidget(context, widget.execTaskHeader!),
        if (isCommentExpanded) _buildCommentInput(),
        if (isInfoExpanded) _buildInfoInput(),
      ],
    );
  }

  bool showAttachmentDot(INSP_EXEC_TASK inspExecTaskHeader) {
    bool data = false;
    final documentHeaders = ref
        .watch(inspectionTaskExecDocumentHeaderProvider.notifier)
        .getDocumentHeadersForInspectionTask(inspExecTaskHeader);

    final documentAttachments = ref.watch(documentAttachmentProvider);

    List<DOCUMENT_ATTACHMENT> attachmentList = documentAttachments.where(
      (attachment) {
        return documentHeaders
                ?.where((docHeader) => docHeader.lid == attachment.fid)
                .toList()
                .length ==
            1;
      },
    ).toList();
    if (attachmentList.isNotEmpty) {
      data = true;
    }
    return data;
  }

  static String? generate32BitDocId() {
    var uuid = const Uuid();
    return uuid.v4();
  }

  Widget _buildCommentInput() {
    final inspExecHeader = ref.watch(inspectionHeaderProvider.notifier).state;
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.all(10.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextField(
                style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black),
                controller: commentController,
                enabled: isAllowedToModify(),
                focusNode: _commentFocusNode,
                maxLines: 4,
                decoration: InputDecoration(
                  hintText: AppLocalizations.of(context)!.enter_comment,
                  border: OutlineInputBorder(
                      borderSide: BorderSide(color: AppColors.grey)),
                  focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: AppColors.grey)),
                  enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: AppColors.grey)),
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInfoInput() {
    final inspExecHeader = ref.watch(inspectionHeaderProvider.notifier).state;
    INSPECTION_TASK task = widget.task;
    if (task.description != null) {
      infoController.text =
          task.description != null ? task.description.toString() : '';
    }
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.grey, width: 1.5),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  constraints: const BoxConstraints(
                    minHeight: 95,
                  ),
                  child: Html(
                    data: infoController.text,
                    style: {
                      "body": Style(
                          fontSize: FontSize.medium,
                          fontWeight: FontWeight.bold,
                          color: Colors.black),
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget attachmentWidget(
      BuildContext context, INSP_EXEC_TASK inspExecTaskHeader) {
    final documentHeaders = ref
        .watch(inspectionTaskExecDocumentHeaderProvider.notifier)
        .getDocumentHeadersForInspectionTask(inspExecTaskHeader);

    final documentAttachments = ref.watch(documentAttachmentProvider);

    List<DOCUMENT_ATTACHMENT> attachmentList = documentAttachments.where(
      (attachment) {
        return documentHeaders
                ?.where((docHeader) => docHeader.lid == attachment.fid)
                .toList()
                .length ==
            1;
      },
    ).toList();
    if (attachmentList.isNotEmpty) {
      if (mounted) {
        setState(() {});
      }
    }

    return RoundsAttachmentPicker(
        isAddButtonRequired: isExecutionVisible(task: widget.execTaskHeader!)
            ? isAllowedToModify()
            : false,
        onAttachmentPicked: onAttachmentPicked,
        viewType: RoundsAttachmentPickerViewType.list,
        attachments: attachmentList
            .map((e) => DocumentAttachmentContainer(
                  height: 80,
                  width: 80,
                  isFromPlanner: e.objectStatus == ObjectStatus.global,
                  attachment: e,
                  uploadProgress:
                      ref.watch(attachMentUploadProgressProvider)[e.lid] ?? 1.0,
                  isUploading:
                      (ref.watch(attachMentUploadProgressProvider)[e.lid] ??
                              1.0) <
                          1.0,
                  onDelete: (value) async {
                    UIHelper.showEamDialog(context,
                        title: AppLocalizations.of(context)!.delete_attachment,
                        description: AppLocalizations.of(context)!
                            .do_you_want_delete_attachment,
                        positiveActionLabel: AppLocalizations.of(context)!.yes,
                        negativeActionLabel: AppLocalizations.of(context)!
                            .cancel, onPositiveClickListener: () async {
                      await ref
                          .read(documentAttachmentProvider.notifier)
                          .deleteDocumentAttachments(e);
                      await ref
                          .watch(documentAttachmentProvider.notifier)
                          .fetchDocumentAttachments();
                      Navigator.of(context, rootNavigator: true).pop();

                      final documentHeadersData = ref
                          .watch(
                              inspectionTaskExecDocumentHeaderProvider.notifier)
                          .getDocumentHeadersForInspectionTask(
                              inspExecTaskHeader);

                      final documentAttachmentsData =
                          ref.watch(documentAttachmentProvider);

                      List<DOCUMENT_ATTACHMENT> attachmentListData =
                          documentAttachmentsData.where(
                        (attachment) {
                          return documentHeadersData
                                  ?.where((docHeader) =>
                                      docHeader.lid == attachment.fid)
                                  .toList()
                                  .length ==
                              1;
                        },
                      ).toList();

                      if (widget.task.task_type.toString() == 'a') {
                        if (attachmentListData.isEmpty) {
                          await onUndo();
                        }
                      }
                    });
                  },
                ))
            .toList());
  }

  isAllowedToModify() {
    final inspHeader = ref.watch(inspectionHeaderProvider);

    return (inspHeader.status != AppConstants.STATE_COMPLETED &&
            widget.execTaskHeader?.status != AppConstants.STATE_TASK_COMP)
        ? true
        : (widget.execTaskHeader?.p_mode == "M" &&
                inspHeader.syncStatus.index != 2)
            ? true
            : false;
  }

  onAttachmentPicked(FilePickerResult result) async {
    final inspHeader = ref.read(inspectionHeaderProvider.notifier).state;
    if (kIsWeb) {
      onAttachmentPickedWeb(result);
    } else {
      await onAttchmentPickedMobile(result, inspHeader);
    }
  }

  Future<void> onAttachmentPickedWeb(FilePickerResult result) async {
    final inspHeader = ref.read(inspectionHeaderProvider.notifier).state;

    for (var data in result.files) {
      Uint8List? fileBytes = data.bytes;
      if (fileBytes == null) continue; // No bytes, skip

      String fileName = data.name;
      String fileDocType = fileName.split('.').last.toUpperCase();
      String base64String = '';
      base64String = base64Encode(fileBytes);

      String? docId = generate32BitDocId();

      DOCUMENT_HEADER documentHeader = DOCUMENT_HEADER(
        doc_id: docId,
        doc_type: fileDocType,
        file_name: fileName,
        title: fileName,
        mime_type: 'application/${fileDocType.toLowerCase()}',
      );

      if(kIsWeb){
        DbHelper().saveAttachmentinIndexDbByUid(docId ?? "", base64String);
      }

      List list1 = await AppDatabaseManager().select(
          DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, {})
            ..setWhereClause("${DOCUMENT_HEADER.FIELD_DOC_ID} = '$docId'"));

      if (list1.isEmpty) {
        await DbHelper.insertDocumentsHeader(documentHeader);
      } else {
        await DbHelper.updateDocumentsHeader(documentHeader);
      }

      DOCUMENT_ATTACHMENT documentAttachment = DOCUMENT_ATTACHMENT(uid: '');
      documentAttachment.fid = documentHeader.lid;
      documentAttachment.uid = docId;
      documentAttachment.local_path = ''; // no local path on web
      documentAttachment.file_name = fileName;
      documentAttachment.mime_type = 'application/${fileDocType.toLowerCase()}';
      documentAttachment.external_url = "";
      documentAttachment.url_requires_auth = "";
      documentAttachment.attachment_status = AttachmentStatusSavedForUpload;

      List list2 =
          await DbHelper.getDocumentAttachmentsByFid(documentHeader.lid);

      if (list2.isEmpty) {
        await DbHelper.insertDocumentAttachment(documentAttachment);
      } else {
        await DbHelper.updateDocumentAttachment(documentAttachment);
      }

      INSP_EXEC_DOC document = INSP_EXEC_DOC(
        insp_id: widget.execTaskHeader?.insp_id,
        doc_id: documentAttachment.uid,
        insp_task_id: widget.execTaskHeader?.insp_task_id,
      );

      var data1 = await AppDatabaseManager().select(
          DBInputEntity(INSP_EXEC_DOC.TABLE_NAME, {})
            ..setWhereClause(
                "${INSP_EXEC_DOC.FIELD_DOC_ID} = '${documentAttachment.uid}'"));

      try {
        if (data1.isNotEmpty) {
          await DbHelper.updateInspExeDoc(document);
        } else {
          document.fid = inspHeader.lid;
          await DbHelper.insertInspExeDoc(document);
        }

        await DbHelper.updateInsp(inspHeader);

        setState(() {
          ref
              .read(documentAttachmentProvider.notifier)
              .fetchDocumentAttachments();
          ref
              .read(inspectionTaskExecDocumentHeaderProvider.notifier)
              .fetchInspectionTaskDocumentHeaders(
                  widget.execTaskHeader!, inspHeader, widget.task);
        });

        if (widget.task.task_type.toString() == 'a') {
          await onCompleteButton(data: Uint8List(0));
        }
      } catch (e) {
        Logger.logError("JobTaskCard", "onAttachmentPickedWeb", e.toString());
      }
    }
  }

  Future<void> onAttchmentPickedMobile(
      FilePickerResult result, INSP_EXEC_HEADER inspHeader) async {
    if (result != null && result.files.single.path != null) {
      for (var data in result.files) {
        File fileData = File(data.path!);
        String? docId = generate32BitDocId();
        String fileName = path.basename(fileData.path);
        String filePath = fileData.path;
        String fileDocType = fileData.path.split('.').last.toUpperCase();
        String base64String = '';
        if (filePath.isNotEmpty) {
          final extension = filePath.split('.').last.toLowerCase();
          if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
              .contains(extension)) {
            base64String = base64Encode(fileData.readAsBytesSync());
          } else if (['mp4', 'mov', 'avi', 'mkv', 'flv', 'wmv', 'webm']
              .contains(extension)) {
            ByteData byteData =
                await rootBundle.load('assets/icon/task_icons/video.png');
            Uint8List bytes = byteData.buffer.asUint8List();
            base64String = base64Encode(bytes);
          } else if (extension == 'pdf') {
            ByteData byteData =
                await rootBundle.load('assets/icon/task_icons/pdff.png');
            Uint8List bytes = byteData.buffer.asUint8List();
            base64String = base64Encode(bytes);
          }
        }
        DOCUMENT_HEADER documentHeader = DOCUMENT_HEADER(
            doc_id: docId,
            doc_type: fileDocType,
            file_name: fileName,
            title: fileName,
            mime_type: 'application/${fileDocType.toLowerCase()}',
            thumbnail: base64String);
        List list1 = await AppDatabaseManager().select(
            DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, {})
              ..setWhereClause("${DOCUMENT_HEADER.FIELD_DOC_ID} = '$docId'"));

        if (list1.isEmpty) {
          await DbHelper.insertDocumentsHeader(documentHeader);
        } else {
          await DbHelper.updateDocumentsHeader(documentHeader);
        }
        DOCUMENT_ATTACHMENT documentAttachment = DOCUMENT_ATTACHMENT(uid: '');
        documentAttachment.fid = documentHeader.lid;
        documentAttachment.uid = docId;
        documentAttachment.local_path = filePath;
        documentAttachment.file_name = fileName;
        documentAttachment.mime_type =
            'application/${fileDocType.toLowerCase()}';
        documentAttachment.external_url = "";
        documentAttachment.url_requires_auth = "";
        documentAttachment.attachment_status = AttachmentStatusSavedForUpload;

        List list2 =
            await DbHelper.getDocumentAttachmentsByFid(documentHeader.lid);

        if (list2.isEmpty) {
          await DbHelper.insertDocumentAttachment(documentAttachment);
        } else {
          await DbHelper.updateDocumentAttachment(documentAttachment);
        }

        INSP_EXEC_DOC document = INSP_EXEC_DOC(
          insp_id: widget.execTaskHeader?.insp_id,
          doc_id: documentAttachment.uid,
          insp_task_id: widget.execTaskHeader?.insp_task_id,
        );

        var data1 = await AppDatabaseManager().select(DBInputEntity(
            INSP_EXEC_DOC.TABLE_NAME, {})
          ..setWhereClause(
              "${INSP_EXEC_DOC.FIELD_DOC_ID} = '${documentAttachment.uid}'"));
        try {
          if (data1.isNotEmpty) {
            await DbHelper.updateInspExeDoc(document);
          } else {
            document.fid = inspHeader.lid;
            await DbHelper.insertInspExeDoc(document);
          }

          await DbHelper.updateInsp(inspHeader);

          setState(() {
            ref
                .read(documentAttachmentProvider.notifier)
                .fetchDocumentAttachments();
            ref
                .read(inspectionTaskExecDocumentHeaderProvider.notifier)
                .fetchInspectionTaskDocumentHeaders(
                    widget.execTaskHeader!, inspHeader, widget.task);
          });

          uploadFile(ref, documentAttachment.lid);
          if (widget.task.task_type.toString() == 'a') {
            await onCompleteButton(data: Uint8List(0));
          }
        } catch (e) {
          Logger.logError(
              "JobTaskCard", "onAttchmentPickedMobile", e.toString());
        }
      }
    }
  }

  Future<void> uploadFile(WidgetRef ref, String lid) async {
    final uploadProgressNotifier =
        ref.read(attachMentUploadProgressProvider.notifier);
    uploadProgressNotifier.startUpload(lid);

    for (double progress = 0.0; progress <= 1.0; progress += 0.1) {
      await Future.delayed(Duration(milliseconds: 500));
      uploadProgressNotifier.updateProgress(lid, progress);
    }

    uploadProgressNotifier.finishUpload(lid);
  }

  Widget getExpandedIcon(
      {required Widget child, required String type, bool showDot = false}) {
    final expandedAttachmentIndex =
        ref.watch(expandedAttachmentInspectionProvider);
    final isAttachmentExpanded = expandedAttachmentIndex == widget.index;
    final expandedCommentIndex = ref.watch(expandedCommentInspectionProvider);
    final expandedInfoIndex = ref.watch(expandedInfoInspectionProvider);

    return Expanded(
      child: InkWell(
        onTap: () async {
          if (type == AppConstants.attachments) {
            ref.read(expandedAttachmentInspectionProvider.notifier).state =
                isAttachmentExpanded ? -1 : widget.index;
            ref.read(expandedCommentInspectionProvider.notifier).state = -1;
            ref.read(expandedInfoInspectionProvider.notifier).state = -1;
            ref
                .read(inspectionTaskDocumentHeaders.notifier)
                .fetchInspectionTaskDocumentHeaders(
                    widget.execTaskHeader!, widget.task);
          }
          if (type == AppConstants.notes) {
            ref.read(expandedCommentInspectionProvider.notifier).state =
                (expandedCommentIndex == widget.index) ? -1 : widget.index;
            ref.read(expandedInfoInspectionProvider.notifier).state = -1;
            ref.read(expandedAttachmentInspectionProvider.notifier).state = -1;
          }
          if (isExecutionVisibleFault(
              checkFor: AppConstants.faults, task: widget.execTaskHeader!)) {
            if (type == AppConstants.faults) {
              await navigateToFaultScreen();
            }
          }

          if (type == AppConstants.info) {
            ref.read(expandedInfoInspectionProvider.notifier).state =
                (expandedCommentIndex == widget.index) ? -1 : widget.index;
            ref.read(expandedCommentInspectionProvider.notifier).state = -1;
            ref.read(expandedAttachmentInspectionProvider.notifier).state = -1;
          }
        },
        child: Stack(
          children: [
            Container(color: AppColors.white, child: child),
            if (showDot)
              Positioned(
                top: 1,
                right: 35,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> navigateToFaultScreen() async {
    clearFaultStates();
    final user = ref.watch(userProvider);
    final inspectionPlanHeader = ref.watch(inspectionPlanHeaderProvider);
    final inspectionTask = ref.watch(inspectionTaskProvider.notifier);
    final faultTypeHeader = ref.read(faultTypeListProvider.notifier);
    final faultInsertHeader = ref.read(insertFaultHeaderProvider.notifier);
    final faultHeader = ref.read(faultHeaderProvider.notifier);
    final faultAction = ref.watch(getFaultActionProvider.notifier);
    final faultDocument = ref.watch(getFaultDocumentProvider.notifier);
    await faultTypeHeader.fetchFaultTypeList();
    final faultTypeHeaderList =
        ref.read(faultHeaderListByPlanIdProvider.notifier);
    final editProvider = ref.read(editFaultFieldProvider.notifier);
    await faultTypeHeaderList.fetchFaultHeaderListByPlanId(
        plantId: inspectionPlanHeader.plant_id.toString());
    inspectionTask.getInspectionTask(widget.task);
    await getFaultData(widget.execTaskHeader!.insp_task_id.toString());
    if (faultHeader.state.insp_task_id != null) {
      faultHeader.state.p_mode = AppConstants.modified;
      faultHeader.state.reported_by = ref.read(userProvider)?.user_id;
      DateTime now = DateTime.now();
      String faultNoticedOnDate = DateFormat('dd MMM yyyy').format(now);
      DateTime dateTime = DateFormat("dd MMM yyyy").parse(faultNoticedOnDate);
      DateTime adjustedDate = DateTime(
        dateTime.year,
        dateTime.month,
        dateTime.day,
      );
      String formattedDate = DateFormat("yyyyMMdd").format(adjustedDate);
      int dateAsInt = int.parse(formattedDate);
      faultHeader.state.reported_on = dateAsInt;
      await DbHelper.updateFault(faultHeader.state);
      await faultHeader.getFaultHeader(
          faultId: faultHeader.state.fault_id.toString());
      editProvider.getEditFaultFieldEnable(false);
      if (mounted) {
        ref.read(faultDescriptionProvider.notifier).resetFaultDescription();
        ref.read(faultLongTextProvider.notifier).resetFaultLongText();
        ref.read(faultTypeProvider.notifier).clearFaultType();
        ref.read(priorityProvider.notifier).clearPriority();
        ref.read(faultModeHeaderProvider.notifier).clearFaultMode();
        await faultDocument
            .getFaultDocuments(faultHeader.state.fault_id.toString());
        if (UIHelper().getScreenType(context) == ScreenType.desktop) {
          await showDialog(
              barrierDismissible: false,
              context: context,
              builder: (context) => Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxWidth: 600,
                      maxHeight: 800,
                    ),
                    child: const Padding(
                      padding: EdgeInsets.all(20.0),
                      child: FaultDetailScreen(
                        type: AppConstants.inspFault,
                      ),
                    ),
                  )));
        } else {
          Navigator.push(context, MaterialPageRoute(builder: (context) {
            return const FaultDetailScreen(
              type: AppConstants.inspFault,
            );
          }));
        }
      }
    } else {
      if (isExecutionVisibleFault(task: widget.execTaskHeader!)) {
        clearNewFaultStates();
        int? inspectionTaskId;
        final inspectionExecHeader =
            ref.watch(inspectionHeaderProvider.notifier).state;
        final inspectionFaultHeader =
            ref.watch(getInspectionHeaderByFaultHeaderProvider.notifier);
        INSP_EXEC_TASK? exec = await DbHelper.getInspExecTaskByInspHeaderInspId(
            inspectionExecHeader.insp_id.toString(),
            widget.task.task_id.toString());
        if (exec != null) {
          inspectionTaskId = exec.insp_task_id;
        }
        DateTime now = DateTime.now();
        String faultNoticedOnDate = DateFormat('dd MMM yyyy').format(now);
        DateTime dateTime = DateFormat("dd MMM yyyy").parse(faultNoticedOnDate);
        DateTime adjustedDate = DateTime(
          dateTime.year,
          dateTime.month,
          dateTime.day,
        );
        String formattedDate = DateFormat("yyyyMMdd").format(adjustedDate);
        int dateAsInt = int.parse(formattedDate);
        String? locationId;
        int? assetNo;
        INSPECTION_SECTION? section;
        final inspPlanSectionHeader =
            ref.watch(inspectionPlanSectionListHeaderProvider);
        section = inspPlanSectionHeader.firstWhere((element) =>
            (element.section_id == widget.task.section_id &&
                element.plan_id == widget.task.plan_id));

        if (widget.task.location_id != null && widget.task.location_id != '') {
          locationId = widget.task.location_id;
        } else if (widget.task.asset_no != null && widget.task.asset_no != '') {
          assetNo = widget.task.asset_no;
          ASSET_HEADER header = await DbHelper.getAssetByAssetno(assetNo!);
          locationId = (await DbHelper.getLocationUsingParentLocFromAsset(
                  header.parent_loc_id.toString()))!
              .location_id;
        } else if (section.location_id != null && section.location_id != '') {
          locationId = section.location_id;
        } else if (section.asset_no != null && section.asset_no != '') {
          assetNo = section.asset_no;
          ASSET_HEADER header = await DbHelper.getAssetByAssetno(assetNo!);
          locationId = (await DbHelper.getLocationUsingParentLocFromAsset(
                  header.parent_loc_id.toString()))!
              .location_id;
        } else if (inspectionPlanHeader.location_id != null &&
            inspectionPlanHeader.location_id != '') {
          locationId = inspectionPlanHeader.location_id;
        } else if (inspectionPlanHeader.asset_no != null &&
            inspectionPlanHeader.asset_no != '') {
          assetNo = inspectionPlanHeader.asset_no;
          ASSET_HEADER header = await DbHelper.getAssetByAssetno(assetNo!);
          locationId = (await DbHelper.getLocationUsingParentLocFromAsset(
                  header.parent_loc_id.toString()))!
              .location_id;
        }
        FAULT_HEADER faultHeaderData = FAULT_HEADER(
          fault_id: UIHelper.generateRandomId(),
          asset_no: assetNo,
          plant_id: inspectionPlanHeader.plant_id,
          location_id: locationId,
          p_mode: AppConstants.add,
          insp_task_id: inspectionTaskId,
          reported_on: dateAsInt,
          reported_by: user!.user_id,
        );
        await faultInsertHeader.insertFaultHeader(faultHeaderData);
        await faultHeader.getFaultHeader(
            faultId: faultHeaderData.fault_id.toString());
        await faultAction.getFaultAction(faultHeaderData.fault_id.toString());
        await faultDocument
            .getFaultDocuments(faultHeaderData.fault_id.toString());
        await inspectionFaultHeader
            .getInspectionPlanHeaderByFaultHeader(faultHeaderData);
        editProvider.getEditFaultFieldEnable(true);
        docAttachments.clear();
        if (mounted) {
          ref.read(faultDescriptionProvider.notifier).resetFaultDescription();
          ref.read(faultLongTextProvider.notifier).resetFaultLongText();
          ref.read(faultTypeProvider.notifier).clearFaultType();
          ref.read(priorityProvider.notifier).clearPriority();
          ref.read(faultModeHeaderProvider.notifier).clearFaultMode();
          if (UIHelper().getScreenType(context) == ScreenType.desktop) {
            await showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => Dialog(
                    backgroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(
                        maxWidth:
                            600, // Slightly more than a mobile screen width
                        maxHeight:
                            800, // Not more than the size of a mobile phone
                      ),
                      child: const Padding(
                          padding: EdgeInsets.all(20.0),
                          child: FaultDetailScreen(
                            type: AppConstants.inspFault,
                          )),
                    )));
          } else {
            await Navigator.push(context, MaterialPageRoute(builder: (context) {
              return const FaultDetailScreen(
                type: AppConstants.inspFault,
              );
            }));
          }
        }
      } else {
        if (mounted) {
          UIHelper.showSnackBar(context,
              message: "No Fault created against this task");
        }
      }
    }
  }

  _getTaskTypeWidget(String title, bool isExpanded) {
    if (title == 'a') {
      return getTaskIcon(
        'assets/inspection_task_icons/attachment.svg',
      );
    } else if (title == 'l') {
      return getTaskIcon(
        'assets/inspection_task_icons/text.svg',
      );
    } else if (title == 'n') {
      return getTaskIcon(
        'assets/inspection_task_icons/time.svg',
      );
    } else if (title == 'b') {
      return getTaskIcon(
        'assets/inspection_task_icons/choice.svg',
      );
    } else if (title == 'm') {
      return getTaskIcon(
        'assets/inspection_task_icons/slider.svg',
      );
    } else if (title == 'h') {
      return getTaskIcon(
        'assets/inspection_task_icons/check_box.svg',
      );
    } else if (title == 'i') {
      return getTaskIcon(
        'assets/inspection_task_icons/date_time.svg',
      );
    } else if (title == 'c') {
      return getTaskIcon(
        'assets/inspection_task_icons/date.svg',
      );
    } else if (title == 'g') {
      return getTaskIcon(
        'assets/inspection_task_icons/signature.svg',
      );
    } else if (title == 'k') {
      return getTaskIcon(
        'assets/inspection_task_icons/location.svg',
      );
    } else if (title == 'j') {
      return getTaskIcon(
        'assets/inspection_task_icons/hyper_link.svg',
      );
    } else if (title == 'd') {
      return getTaskIcon(
        'assets/inspection_task_icons/instructions.svg',
      );
    } else if (title == 'f') {
      return getTaskIcon(
        'assets/inspection_task_icons/scan.svg',
      );
    } else if (title == 'e') {
      return getTaskIcon(
        'assets/inspection_task_icons/numeric.svg',
      );
    } else if (title == 'o') {
      return getTaskIconPng(
        'assets/icons/kpi.png',
      );
    } else {
      return Padding(
        padding: getTaskPadding(isExpanded: isExpanded),
      );
    }
  }

  getTaskIcon(String icon) {
    return SvgPicture.asset(
      icon,
      width: 25.0,
      height: 25.0,
    );
  }

  getTaskIconPng(String icon) {
    return Image.asset(
      icon,
      width: 25.0,
      height: 25.0,
    );
  }

  _getTaskWidget(INSPECTION_TASK task, bool isExpanded) {
    final pickListCodeList = ref.read(pickListCodeListProvider);
    List<String?> choiceChipData;
    final plant = ref.read(plantProvider);
    List<KPI_HEADER> headers = ref.read(kpiHeaderProvider);
    KPI_HEADER? kpiHeader = headers.firstWhere(
      (element) =>
          element.plant_id == plant &&
          element.kpi_id.toString() == task.kpi_id.toString(),
      orElse: () => KPI_HEADER(kpi_id: 0),
    );
    if (task.task_type.toString() == 'o' && kpiHeader.kpi_id != 0) {
      choiceChipData = pickListCodeList
          .where((element) =>
              element.picklist_id == kpiHeader.picklist_id.toString())
          .map((e) => e.description)
          .toList();
    } else {
      choiceChipData = pickListCodeList
          .where(
              (element) => element.picklist_id == task.picklist_id.toString())
          .map((e) => e.description)
          .toList();
    }

    final documentHeaders = ref
        .watch(inspectionTaskExecDocumentHeaderProvider.notifier)
        .getDocumentHeadersForInspectionTask(widget.execTaskHeader!);

    final documentAttachments = ref.watch(documentAttachmentProvider);

    List<DOCUMENT_ATTACHMENT> attachmentList = documentAttachments.where(
      (attachment) {
        return documentHeaders
                ?.where((docHeader) => docHeader.lid == attachment.fid)
                .toList()
                .length ==
            1;
      },
    ).toList();

    if (task.task_type.toString() == 'a') {
      return Padding(
        padding: getTaskPadding(isExpanded: isExpanded),
        child: Padding(
          padding: const EdgeInsets.only(left: 8.0, top: 8.0),
          child: Text(
              (widget.execTaskHeader != null
                  ? widget.execTaskHeader!.status !=
                          AppConstants.STATE_TASK_COMP
                      ? AppLocalizations.of(context)!.add_attachment
                      : (attachmentList.isNotEmpty
                          ? '${AppLocalizations.of(context)!.there_are} ${attachmentList.length} ${AppLocalizations.of(context)!.attachments_available}'
                          : AppLocalizations.of(context)!.attachment_not_added)
                  : ''),
              style: TextStyle(
                  fontSize: 14,
                  color: AppColors.titleTextColor,
                  letterSpacing: 0.1)),
        ),
      );
    } else if (task.task_type.toString() == 'n') {
      return Padding(
          padding: getTaskPadding(isExpanded: isExpanded),
          child: InspCustomTextFieldWidget(
              controller: timerController,
              maxlines: 1,
              onTap: isExecutionVisible(task: widget.execTaskHeader!)
                  ? () async {
                      await onTime(context);
                      if (timerController.text.isNotEmpty) {
                        await onCompleteButton(
                            dataString: timerController.text);
                      }
                    }
                  : null,
              readOnly: true,
              label: 'Enter Time',
              suffixIcon: InkWell(
                onTap: null,
                child: SvgPicture.asset(
                  'assets/inspection_task_icons/time.svg',
                  color: AppColors.greySubtitleText,
                  height: 24,
                  width: 24,
                ),
              )));
    } else if (task.task_type.toString() == 'l') {
      return Padding(
          padding: getTaskPadding(isExpanded: isExpanded),
          child: Html(data: inputController.text));
    } else if (task.task_type.toString() == 'm') {
      return Padding(
        padding: getTaskPadding(isExpanded: isExpanded),
        child: Row(
          children: [
            Expanded(
              child: SliderWidget(
                min: min,
                max: max,
                onChanged: isExecutionVisible(task: widget.execTaskHeader!)
                    ? (dynamic newValue) async {
                        setState(() {
                          _sliderValue = newValue;
                        });
                        widget.execTaskHeader!.num_value = _sliderValue;
                        ref
                            .read(numericInspectionHeaderProvider.notifier)
                            .setNumericValue(widget.execTaskHeader!);
                      }
                    : null,
                value: _sliderValue.clamp(min, max),
              ),
            ),
            if (_sliderValue != 0 &&
                (_sliderValue != previousSliderData) &&
                (widget.execTaskHeader?.status ==
                        AppConstants.STATE_TASK_COMP ||
                    widget.execTaskHeader?.status ==
                        AppConstants.STATE_TASK_OPEN))
              Padding(
                padding: const EdgeInsets.only(top: 5.0, left: 5.0),
                child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      elevation: 0,
                      padding: EdgeInsets.all(10),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15)),
                      backgroundColor: AppColors.offWhiteColor,
                    ),
                    onPressed: () async {
                      if (_sliderValue != 0) {
                        INSPECTION_SECTION? inspection_section =
                            await DbHelper.getInspectionSection(
                                widget.task.section_id.toString(),
                                widget.task.plan_id.toString());

                        ///completion of parent task
                        await onCompleteButton(dataDouble: _sliderValue);

                        if (inspection_section != null) {
                          {
                            await ref
                                .read(inspectionPlanTaskListHeaderProvider
                                    .notifier)
                                .fetchInspDependentTasks(
                                  widget.inspExecHeader!.plant_id.toString(),
                                  widget.inspExecHeader!,
                                );
                          }
                        }
                      } else {
                        INSPECTION_SECTION? inspection_section =
                            await DbHelper.getInspectionSection(
                                widget.task.section_id.toString(),
                                widget.task.plan_id.toString());

                        ///completion of parent task
                        await onUnCompleteButton();

                        if (inspection_section != null) {
                          {
                            await ref
                                .read(inspectionPlanTaskListHeaderProvider
                                    .notifier)
                                .fetchInspDependentTasks(
                                  widget.inspExecHeader!.plant_id.toString(),
                                  widget.inspExecHeader!,
                                );
                          }
                        }
                      }
                    },
                    child: Text(
                      AppLocalizations.of(context)!.done,
                      style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppColors.greenColor,
                          fontSize: 14),
                    )),
              )
          ],
        ),
      );
    } else if (task.task_type.toString() == 'i') {
      return Padding(
        padding: getTaskPadding(isExpanded: isExpanded),
        child: InspCustomTextFieldWidget(
          controller: dateAndTimerController,
          onTap: isExecutionVisible(task: widget.execTaskHeader!)
              ? () async {
                  await pickDateTime(context);
                  if (dateAndTimerController.text.isNotEmpty) {
                    await onCompleteButton(
                        dataString: dateAndTimerController.text);
                  }
                }
              : null,
          readOnly: true,
          maxlines: 1,
          label: AppLocalizations.of(context)!.select_date_and_time,
          suffixIcon: InkWell(
            onTap: null,
            child: SvgPicture.asset(
              'assets/inspection_task_icons/date_time.svg',
              // color: AppColors.greySubtitleText,
              height: 24,
              width: 24,
            ),
          ),
        ),
      );
    } else if (task.task_type.toString() == 'c') {
      return Padding(
        padding: const EdgeInsets.only(bottom: 20),
        child: InspCustomTextFieldWidget(
          maxlines: 1,
          controller: dateRangeController,
          onTap: isExecutionVisible(task: widget.execTaskHeader!)
              ? () async {
                  await onDate(context);
                  if (dateRangeController.text.isNotEmpty) {
                    await onCompleteButton(
                        dataString: dateRangeController.text);
                  }
                }
              : null,
          readOnly: true,
          label: AppLocalizations.of(context)!.select_date,
          suffixIcon: InkWell(
            onTap: null,
            child: SvgPicture.asset(
              'assets/inspection_task_icons/date.svg',
              color: AppColors.greySubtitleText,
              height: 24,
              width: 24,
            ),
          ),
        ),
      );
    } else if (task.task_type.toString() == 'b') {
      String previousSelectedChoiceChip = selectedChoiceChip;
      return Padding(
        padding: const EdgeInsets.only(bottom: 10),
        child: ChoiceChipWidget(
          data: choiceChipData,
          chipColors: choiceChipColorsData,
          selectedPriority: selectedChoiceChip,
          onSelected: isExecutionVisible(task: widget.execTaskHeader!)
              ? (selectedOptions) async {
                  String? prevDataString;
                  if (previousSelectedChoiceChip != '' &&
                      previousSelectedChoiceChip.isNotEmpty) {
                    prevDataString =
                        getPickListCode(previousSelectedChoiceChip);
                  }

                  String tempSelectedChip = selectedOptions;

                  String? dataString = getPickListCode(tempSelectedChip);
                  if (dataString != null && dataString.isNotEmpty) {
                    if (dataString != prevDataString) {
                      INSPECTION_SECTION? inspection_section =
                          await DbHelper.getInspectionSection(
                              task.section_id.toString(),
                              task.plan_id.toString());

                      await onUnCompleteButton();

                      if (inspection_section != null) {
                        await widget.widref
                            .read(inspectionPlanTaskListHeaderProvider.notifier)
                            .fetchInspDependentTasks(
                              widget.inspExecHeader!.plant_id.toString(),
                              widget.inspExecHeader!,
                            );
                      }
                    }

                    INSPECTION_SECTION? inspection_section =
                        await DbHelper.getInspectionSection(
                            task.section_id.toString(),
                            task.plan_id.toString());

                    await onCompleteButton(dataString: dataString);

                    if (inspection_section != null) {
                      await widget.widref
                          .read(inspectionPlanTaskListHeaderProvider.notifier)
                          .fetchInspDependentTasks(
                            widget.inspExecHeader!.plant_id.toString(),
                            widget.inspExecHeader!,
                          );
                    }

                    setState(() {
                      selectedChoiceChip = tempSelectedChip;

                      final filteredList = pickListCodeList
                          .where((element) =>
                              element.picklist_id ==
                              task.picklist_id.toString())
                          .toList();

                      final selectedIndex =
                          choiceChipData.indexOf(selectedChoiceChip);
                      choiceChipColorsData = List<String?>.filled(
                          choiceChipData.length, '#D3D3D3');

                      if (selectedIndex != -1) {
                        final matchedColor = filteredList
                            .firstWhere(
                              (e) => e.description == selectedChoiceChip,
                              orElse: () => PICKLIST_CODE(
                                  picklist_id: '', code: '', color: '#D3D3D3'),
                            )
                            .color;

                        choiceChipColorsData[selectedIndex] =
                            matchedColor ?? '#D3D3D3';
                      }
                    });
                  }
                }
              : null,
        ),
      );
    } else if (task.task_type.toString() == 'g') {
      return Padding(
          padding: EdgeInsets.only(bottom: 10),
          child: SignatureWidget(
            widget: sign,
            suffixIcon: widget.inspExecHeader!.status !=
                    AppConstants.STATE_TASK_COMP
                ? !isEditSignature
                    ? Positioned(
                        top: 4,
                        right: 6,
                        child: GestureDetector(
                          onTap:
                              isExecutionVisible(task: widget.execTaskHeader!)
                                  ? () {
                                      isEditSignature = !isEditSignature;
                                      signatureController.clear();
                                      sign = Signature(
                                        controller: signatureController,
                                        backgroundColor: Colors.white,
                                      );
                                      setState(() {});
                                    }
                                  : null,
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Colors.white,
                            ),
                            padding: const EdgeInsets.all(6),
                            child: const Icon(
                              Icons.edit,
                              size: 24,
                              color: AppColors.greySubtitleText,
                            ),
                          ),
                        ),
                      )
                    : Positioned(
                        top: 4,
                        right: 6,
                        child: Row(
                          children: [
                            GestureDetector(
                              onTap: () async {
                                if (signatureController.isNotEmpty) {
                                  final data =
                                      await signatureController.toPngBytes();
                                  if (data != null) {
                                    sign = Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Image.memory(
                                        data,
                                        fit: BoxFit.contain,
                                      ),
                                    );
                                  }
                                  isEditSignature = false;
                                  setState(() {});
                                  if (sign != const SizedBox()) {
                                    await onCompleteButton(data: data);
                                  }
                                }
                              },
                              child: Container(
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                ),
                                padding: const EdgeInsets.all(6),
                                child: const Icon(
                                  Icons.check,
                                  size: 24,
                                  color: AppColors.greySubtitleText,
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 3,
                            ),
                            GestureDetector(
                              onTap: () async {
                                Uint8List signatureData = base64Decode(
                                    widget.execTaskHeader!.blob_value!);
                                signatureController.clear();
                                isEditSignature = false;
                                sign = (widget.execTaskHeader!.blob_value !=
                                            null &&
                                        widget.execTaskHeader!.blob_value != '')
                                    ? Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Image.memory(
                                          signatureData,
                                          fit: BoxFit.contain,
                                        ),
                                      )
                                    : SizedBox();
                                setState(() {});
                                /*      if (widget.task!. != null) {
                                  if (sign == SizedBox()) {
                                    await onUndo();
                                  }
                                }*/
                              },
                              child: Container(
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                ),
                                padding: const EdgeInsets.all(6),
                                child: const Icon(
                                  Icons.clear,
                                  size: 24,
                                  color: AppColors.greySubtitleText,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                : SizedBox(),
          ));
    } else if (task.task_type.toString() == 'k') {
      return Padding(
        padding: getTaskPadding(isExpanded: isExpanded),
        child: InspCustomTextFieldWidget(
          controller: locationController,
          maxlines: 1,
          onTap: isExecutionVisible(task: widget.execTaskHeader!)
              ? () async {
                  Position? position = await getCoordinates(context);
                  if (position != null) {
                    locationController.text =
                        "${position.latitude}, ${position.longitude}";
                  }
                  if (locationController.text.isNotEmpty) {
                    await onCompleteButton(dataString: locationController.text);
                  }
                }
              : null,
          readOnly: true,
          label: AppLocalizations.of(context)!.enter_location,
          suffixIcon: InkWell(
            onTap: null,
            child: SvgPicture.asset(
              'assets/inspection_task_icons/location.svg',
              color: AppColors.greySubtitleText,
              height: 24,
              width: 24,
            ),
          ),
        ),
      );
    } else if (task.task_type.toString() == 'j') {
      print('Could not launch ${linkController.text}');
      //var link;
      String url = '';
      if(!kIsWeb){
        final regex = RegExp(r'href="([^"]+)"');
        final link = regex.firstMatch(linkController.text);
        if (link != null) {
          url = link.group(1)!; // Extracted URL
          print("Extracted URL: $url");
        } else {
          print("No URL found");
        }
      }
      return Padding(
          padding: getTaskPadding(isExpanded: isExpanded),
          child: kIsWeb ? Html(data: linkController.text) : GestureDetector(
      onTap: () async{
        
        if (!await launchUrl(
      Uri.parse(url),
      mode: LaunchMode.externalApplication, // opens in browser
    )) {
      print('Could not launch ${linkController.text}');
      throw 'Could not launch ${linkController.text}';
    }
      },
      child: Text(
        url,
        style: TextStyle(color: Colors.blue, decoration: TextDecoration.underline),
      ),
    ));
    } else if (task.task_type.toString() == 'd') {
      return Padding(
          padding: getTaskPadding(isExpanded: isExpanded),
          child: Html(data: instructionController.text));
    } else if (task.task_type.toString() == 'f') {
      return Padding(
        padding: getTaskPadding(isExpanded: isExpanded),
        child: Column(
          children: [
            InspCustomTextFieldWidget(
              controller: scanController,
              maxlines: 1,
              onTap: isExecutionVisible(task: widget.execTaskHeader!)
                  ? () async {
                      showDialog(
                          context: context,
                          barrierDismissible: true,
                          builder: (BuildContext dialogContext) {
                            return Dialog(
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12)),
                              insetPadding: const EdgeInsets.all(20),
                              child: Container(
                                width: 320,
                                height: 420,
                                padding: const EdgeInsets.all(16),
                                child: Column(
                                  children: [
                                    Text(
                                      AppLocalizations.of(context)!.scan_code,
                                      style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold),
                                    ),
                                    const SizedBox(height: 20),
                                    Padding(
                                      padding: const EdgeInsets.all(0.0),
                                      child: BarcodeScannerWidget(
                                        (result, isScanned) async {
                                          scanController.text = result;

                                          if (Navigator.of(dialogContext)
                                              .canPop()) {
                                            Navigator.of(dialogContext).pop();
                                          }
                                          if (scanController.text.isNotEmpty) {
                                            await onCompleteButton(
                                                dataString:
                                                    scanController.text);
                                          }
                                        },
                                      ),
                                    ),
                                    ElevatedButton(
                                      style: ElevatedButton.styleFrom(
                                          backgroundColor:
                                              AppColors.primaryColor),
                                      onPressed: () =>
                                          Navigator.of(dialogContext).pop(),
                                      child: Text(
                                          AppLocalizations.of(context)!.cancel),
                                    )
                                  ],
                                ),
                              ),
                            );
                          });
                      // onScanner();
                    }
                  : null,
              readOnly: true,
              label: AppLocalizations.of(context)!.scan,
              suffixIcon: InkWell(
                  onTap: null,
                  child: SvgPicture.asset(
                    'assets/inspection_task_icons/scan.svg',
                    color: AppColors.greySubtitleText,
                    height: 24,
                    width: 24,
                  )),
            ),
          ],
        ),
      );
    } else if (task.task_type.toString() == 'e') {
      return Padding(
        padding: getTaskPadding(isExpanded: isExpanded),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: InspCustomTextFieldWidget(
                    controller: numericController,
                    focusNode: _focusNode,
                    maxlines: 1,
                    readOnly: !isExecutionVisible(task: widget.execTaskHeader!),
                    onChanged: isExecutionVisible(task: widget.execTaskHeader!)
                        ? (v) {
                            final value = int.tryParse(v);
                            setState(() {
                              if (value != null) {
                                if (value < numericLower) {
                                  validationMessage = numericLowerMsg;
                                  numericController.text = value.toString();
                                } else if (value > numericUpper) {
                                  validationMessage = numericUpperMsg;
                                  numericController.text = value.toString();
                                } else {
                                  validationMessage = null;
                                  numericController.text = value.toString();
                                }
                                widget.execTaskHeader!.num_value =
                                    double.parse(numericController.text);
                                ref
                                    .read(numericInspectionHeaderProvider
                                        .notifier)
                                    .setNumericValue(widget.execTaskHeader!);
                              } else {
                                validationMessage = null;
                              }
                            });
                            numericController.selection =
                                TextSelection.collapsed(
                              offset: numericController.text.length,
                            );
                          }
                        : null,
                    keyboardType: TextInputType.number,
                    label: 'Enter Number',
                    suffixIcon: SvgPicture.asset(
                      'assets/inspection_task_icons/numeric.svg',
                      color: AppColors.greySubtitleText,
                      height: 24,
                      width: 24,
                    ),
                  ),
                ),
                if (numericController.text.isNotEmpty &&
                    numericController.text != '' &&
                    (numericController.text != previousNumericData) &&
                    (widget.execTaskHeader?.status ==
                            AppConstants.STATE_TASK_COMP ||
                        widget.execTaskHeader?.status ==
                            AppConstants.STATE_TASK_OPEN))
                  Padding(
                    padding: const EdgeInsets.only(top: 18.0, left: 5.0),
                    child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          elevation: 0,
                          padding: EdgeInsets.all(10),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15)),
                          backgroundColor: AppColors.offWhiteColor,
                        ),
                        onPressed: () async {
                          if (numericController.text.isNotEmpty) {
                            numericController.selection =
                                TextSelection.collapsed(
                                    offset: numericController.text.length);

                            ///Refresh list of child tasks
                            INSPECTION_SECTION? inspection_section =
                                await DbHelper.getInspectionSection(
                                    widget.task.section_id.toString(),
                                    widget.task.plan_id.toString());

                            ///completion of parent task
                            await onCompleteButton(
                                dataDouble:
                                    double.parse(numericController.text),
                                tasktype: 'e');

                            if (inspection_section != null) {
                              {
                                await ref
                                    .read(inspectionPlanTaskListHeaderProvider
                                        .notifier)
                                    .fetchInspDependentTasks(
                                      widget.inspExecHeader!.plant_id
                                          .toString(),
                                      widget.inspExecHeader!,
                                    );
                              }
                            }
                            //   }
                          } else {
                            INSPECTION_SECTION? inspection_section =
                                await DbHelper.getInspectionSection(
                                    widget.task.section_id.toString(),
                                    widget.task.plan_id.toString());

                            ///completion of parent task
                            await onUnCompleteButton();

                            if (inspection_section != null) {
                              {
                                await ref
                                    .read(inspectionPlanTaskListHeaderProvider
                                        .notifier)
                                    .fetchInspDependentTasks(
                                      widget.inspExecHeader!.plant_id
                                          .toString(),
                                      widget.inspExecHeader!,
                                    );
                              }
                            }
                          }
                        },
                        child: Text(
                          AppLocalizations.of(context)!.done,
                          style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: AppColors.greenColor,
                              fontSize: 14),
                        )),
                  )
              ],
            ),
            if (validationMessage != null)
              Padding(
                padding: const EdgeInsets.only(top: 4, left: 4),
                child: Text(
                  validationMessage!,
                  style: const TextStyle(
                    color: AppColors.redAccentColor,
                    fontSize: 14,
                  ),
                ),
              ),
          ],
        ),
      );
    } else if (task.task_type.toString() == 'o') {
      if (task.kpi_id != null) {
        if (kpiHeader.kpi_id != null && kpiHeader.kpi_id != 0) {
          if (kpiHeader.kpi_type == '1') {
            String previousSelectedChoiceChip = selectedChoiceChip;
            return Padding(
                padding: const EdgeInsets.only(bottom: 10),
                child: ChoiceChipWidget(
                  data: choiceChipData,
                  chipColors: choiceChipColorsData,
                  onSelected: isExecutionVisible(task: widget.execTaskHeader!)
                      ? (selectedOptions) async {
                          String? prevDataString;
                          if (previousSelectedChoiceChip != '' &&
                              previousSelectedChoiceChip.isNotEmpty) {
                            prevDataString =
                                getPickListCode(previousSelectedChoiceChip);
                          }
                          setState(() {
                            selectedChoiceChip = selectedOptions;
                            final filteredList = pickListCodeList
                                .where(
                                  (element) =>
                                      element.picklist_id ==
                                      task.picklist_id.toString(),
                                )
                                .toList();
                            final selectedIndex =
                                choiceChipData.indexOf(selectedChoiceChip);
                            choiceChipColorsData = List<String?>.filled(
                                choiceChipData.length, '#D3D3D3');
                            if (selectedIndex != -1) {
                              final matchedColor = filteredList
                                  .firstWhere(
                                    (e) => e.description == selectedChoiceChip,
                                    orElse: () => PICKLIST_CODE(
                                        picklist_id: '',
                                        code: '',
                                        color: '#D3D3D3'),
                                  )
                                  .color;

                              choiceChipColorsData[selectedIndex] =
                                  matchedColor ?? '#D3D3D3';
                            }
                          });
                          if (selectedChoiceChip.isNotEmpty &&
                              selectedChoiceChip != '') {
                            String? dataString =
                                getPickListCode(selectedChoiceChip);
                            if (dataString != null && dataString != '') {
                              if (dataString != prevDataString) {
                                ///Refresh list of child tasks
                                INSPECTION_SECTION? inspection_section =
                                    await DbHelper.getInspectionSection(
                                        task.section_id.toString(),
                                        task.plan_id.toString());

                                ///completion of parent task
                                await onUnCompleteButton();

                                if (inspection_section != null) {
                                  {
                                    await ref
                                        .read(
                                            inspectionPlanTaskListHeaderProvider
                                                .notifier)
                                        .fetchInspDependentTasks(
                                          widget.inspExecHeader!.plant_id
                                              .toString(),
                                          widget.inspExecHeader!,
                                        );
                                  }
                                }
                              }

                              ///Refresh list of child tasks
                              INSPECTION_SECTION? inspection_section =
                                  await DbHelper.getInspectionSection(
                                      task.section_id.toString(),
                                      task.plan_id.toString());

                              ///completion of parent task
                              await onCompleteButton(dataString: dataString);

                              if (inspection_section != null) {
                                {
                                  await ref
                                      .read(inspectionPlanTaskListHeaderProvider
                                          .notifier)
                                      .fetchInspDependentTasks(
                                        widget.inspExecHeader!.plant_id
                                            .toString(),
                                        widget.inspExecHeader!,
                                      );
                                }
                              }
                            }
                          }
                        }
                      : null,
                  selectedPriority: selectedChoiceChip,
                ));
          } else if (kpiHeader.kpi_type == '2' ||
              kpiHeader.kpi_type == '3' ||
              kpiHeader.kpi_type == '4') {
            return Padding(
              padding: getTaskPadding(isExpanded: isExpanded),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: InspCustomTextFieldWidget(
                          controller: numericController,
                          maxlines: 1,
                          focusNode: _focusNode,
                          readOnly:
                              !isExecutionVisible(task: widget.execTaskHeader!),
                          onChanged: isExecutionVisible(
                                  task: widget.execTaskHeader!)
                              ? (v) {
                                  final value = int.tryParse(v);
                                  setState(() {
                                    if (value != null) {
                                      if (value < numericLower) {
                                        validationMessage = numericLowerMsg;
                                        numericController.text =
                                            value.toString();
                                      } else if (value > numericUpper) {
                                        validationMessage = numericUpperMsg;
                                        numericController.text =
                                            value.toString();
                                      } else {
                                        validationMessage = null;
                                        numericController.text =
                                            value.toString();
                                      }
                                      widget.execTaskHeader!.num_value =
                                          double.parse(numericController.text);
                                      ref
                                          .read(numericInspectionHeaderProvider
                                              .notifier)
                                          .setNumericValue(
                                              widget.execTaskHeader!);
                                    } else {
                                      validationMessage = null;
                                    }
                                  });
                                  numericController.selection =
                                      TextSelection.collapsed(
                                    offset: numericController.text.length,
                                  );
                                }
                              : null,
                          keyboardType: TextInputType.number,
                          label: 'Enter Number',
                          suffixIcon: SvgPicture.asset(
                            'assets/inspection_task_icons/numeric.svg',
                            color: AppColors.greySubtitleText,
                            height: 24,
                            width: 24,
                          ),
                        ),
                      ),
                      if (numericController.text.isNotEmpty &&
                          numericController.text != '' &&
                          (numericController.text != previousNumericData) &&
                          (widget.execTaskHeader?.status ==
                                  AppConstants.STATE_TASK_COMP ||
                              widget.execTaskHeader?.status ==
                                  AppConstants.STATE_TASK_OPEN))
                        Padding(
                          padding: const EdgeInsets.only(top: 18.0, left: 5.0),
                          child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                elevation: 0,
                                padding: EdgeInsets.all(10),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(15)),
                                backgroundColor: AppColors.offWhiteColor,
                              ),
                              onPressed: () async {
                                if (numericController.text.isNotEmpty) {
                                  numericController.selection =
                                      TextSelection.collapsed(
                                          offset:
                                              numericController.text.length);

                                  ///Refresh list of child tasks
                                  INSPECTION_SECTION? inspection_section =
                                      await DbHelper.getInspectionSection(
                                          task.section_id.toString(),
                                          task.plan_id.toString());

                                  ///completion of parent task
                                  await onCompleteButton(
                                      dataDouble:
                                          double.parse(numericController.text),
                                      tasktype: 'e');

                                  if (inspection_section != null) {
                                    {
                                      await ref
                                          .read(
                                              inspectionPlanTaskListHeaderProvider
                                                  .notifier)
                                          .fetchInspDependentTasks(
                                            widget.inspExecHeader!.plant_id
                                                .toString(),
                                            widget.inspExecHeader!,
                                          );
                                    }
                                  }
                                  //   }
                                } else {
                                  INSPECTION_SECTION? inspection_section =
                                      await DbHelper.getInspectionSection(
                                          task.section_id.toString(),
                                          task.plan_id.toString());

                                  ///completion of parent task
                                  await onUnCompleteButton();

                                  if (inspection_section != null) {
                                    {
                                      await ref
                                          .read(
                                              inspectionPlanTaskListHeaderProvider
                                                  .notifier)
                                          .fetchInspDependentTasks(
                                            widget.inspExecHeader!.plant_id
                                                .toString(),
                                            widget.inspExecHeader!,
                                          );
                                    }
                                  }
                                }
                              },
                              child: Text(
                                AppLocalizations.of(context)!.done,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.greenColor,
                                    fontSize: 14),
                              )),
                        )
                    ],
                  ),
                ],
              ),
            );
          }
        }
      }
    } else {
      return Padding(
        padding: getTaskPadding(isExpanded: isExpanded),
        child: SizedBox(),
      );
    }
  }

  EdgeInsetsGeometry getTaskPadding({required bool isExpanded, double? value}) {
    return EdgeInsets.only(bottom: isExpanded ? (value ?? 20) : (value ?? 10));
  }

  Widget getExpandedIconSpace() {
    return const SizedBox(width: 20);
  }

  selectDate() async {
    String date = '';
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2050),
    );
    if (picked != null) {
      final formattedDate = DateFormat('dd MMMM yyyy').format(picked);
      date = formattedDate;
    } else {
      date = DateFormat('dd MMMM yyyy').format(DateTime.now());
    }
    return date;
  }

  Future<Position?> getCoordinates(BuildContext context) async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        if (context.mounted) {
          UIHelper.showErrorDialog(context,
              description: AppLocalizations.of(context)!
                  .location_permission_not_granted);
        }
        return null;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.deniedForever) {
          if (context.mounted) {
            UIHelper.showErrorDialog(context,
                description: AppLocalizations.of(context)!
                    .location_permission_not_granted);
          }
          return null;
        }
      }

      if (permission == LocationPermission.always ||
          permission == LocationPermission.whileInUse) {
        if (context.mounted) {
          UIHelper().progressDialog(
            context: context,
            message: AppLocalizations.of(context)!.getting_location,
          );
        }

        Position position = await Geolocator.getCurrentPosition(
            desiredAccuracy:
                kIsWeb ? LocationAccuracy.low : LocationAccuracy.high);

        if (context.mounted) Navigator.pop(context);

        return position;
      }

      return null;
    } catch (e) {
      if (context.mounted) {
        UIHelper.showErrorDialog(context,
            description:
                AppLocalizations.of(context)!.location_permission_not_granted);
      }
      return null;
    }
  }

  bool showDot(int? task_id) {
    bool data = false;
    final inspExecHeader =
        ref.watch(inspExecuteTaskListProvider.notifier).state;
    final inspHeader = ref.watch(inspectionHeaderProvider.notifier).state;
    final faultListHeader = ref.watch(faultHeaderListProvider);
    if (inspHeader.plan_id == widget.task.plan_id) {
      for (var datas in inspExecHeader) {
        if (inspHeader.insp_id == datas.insp_id) {
          INSP_EXEC_TASK exec = datas;
          if (exec.insp_task_id == task_id) {
            for (var element in faultListHeader) {
              if (exec.insp_task_id == element.insp_task_id) {
                if (element.insp_task_id != null) {
                  data = true;
                  break;
                }
              }
            }
          }
        }
      }
    }
    return data;
  }

  bool showNoteDot(INSP_EXEC_TASK task) {
    bool data = false;
    final inspExecHeader =
        ref.watch(inspExecuteTaskListProvider.notifier).state;
    final inspHeader = ref.watch(inspectionHeaderProvider.notifier).state;
    if (inspHeader.insp_id == widget.execTaskHeader?.insp_id &&
        task.insp_task_id == widget.execTaskHeader?.insp_task_id) {
      for (var datas in inspExecHeader) {
        if (task.section_id == datas.section_id) {
          INSP_EXEC_TASK exec = datas;
          if (exec.insp_task_id == task.insp_task_id) {
            if (exec.comments != null && exec.comments != "") {
              data = true;
              break;
            }
          }
        }
      }
    }
    return data;
  }

  Future<void> getFaultData(String taskId) async {
    final inspHeader = ref.watch(inspectionHeaderProvider.notifier).state;
    final faultListHeader = ref.watch(faultHeaderListProvider.notifier).state;
    final faultHeader = ref.read(faultHeaderProvider.notifier);
    if (inspHeader.plan_id == widget.task.plan_id) {
      INSP_EXEC_TASK? exec = await DbHelper.getInspExecTaskByInspHeaderInspId(
          inspHeader.insp_id.toString(), widget.task.task_id.toString());
      if (exec != null) {
        if (exec.insp_task_id.toString() == taskId.toString()) {
          bool isMatched = false;
          for (var element in faultListHeader) {
            if (exec.insp_task_id == element.insp_task_id) {
              if (element.insp_task_id != null) {
                await faultHeader.getFaultHeader(
                    taskNo: element.insp_task_id.toString(), isInsp: true);
                isMatched = true;
                break;
              }
            }
          }

          if (!isMatched) {
            await faultHeader.getFaultHeader(taskNo: null);
          }
        }
      }
    }
  }

  Future<void> onDate(BuildContext context) async {
    DateTime? startDate = await showDatePicker(
      context: context,
      helpText: 'Select Start Date',
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Colors.blue,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (startDate == null) return;

    DateTime? endDate = await showDatePicker(
      context: context,
      helpText: 'Select End Date',
      initialDate: startDate,
      firstDate: startDate,
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Colors.blue,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (endDate == null) return;

    setState(() {
      final formattedStartDate =
          "${startDate.day.toString().padLeft(2, '0')}-${startDate.month.toString().padLeft(2, '0')}-${startDate.year}";
      final formattedEndDate =
          "${endDate.day.toString().padLeft(2, '0')}-${endDate.month.toString().padLeft(2, '0')}-${endDate.year}";

      dateRangeController.text = "$formattedStartDate - $formattedEndDate";
    });
  }

  Future<void> onTime(BuildContext context) async {
    TimeOfDay? picked;

    picked = await showTimePicker(
      initialEntryMode: TimePickerEntryMode.input,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
          child: child!,
        );
      },
      context: context,
      initialTime: TimeOfDay.fromDateTime(DateTime.now()),
    );

    if (picked != null) {
      final now = DateTime.now();
      final selectedDateTime = DateTime(
        now.year,
        now.month,
        now.day,
        picked.hour,
        picked.minute,
      );

      final formattedTime =
          "${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}";
      setState(() {
        timerController.text = formattedTime;
      });
    }
  }

  Future<void> pickDateTime(BuildContext context) async {
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Colors.blue,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(DateTime.now()),
        initialEntryMode: TimePickerEntryMode.input,
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
            child: child!,
          );
        },
      );

      if (pickedTime != null) {
        final combinedDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          pickedTime.hour,
          pickedTime.minute,
        );

        final formatted =
            "${combinedDateTime.day}-${_twoDigits(combinedDateTime.month)}-${_twoDigits(combinedDateTime.year)} "
            "${_twoDigits(combinedDateTime.hour)}:${_twoDigits(combinedDateTime.minute)}";

        setState(() {
          dateAndTimerController.text = formatted;
        });
      }
    }
  }

  String _twoDigits(int n) => n.toString().padLeft(2, '0');

  clearNewFaultStates() {
    final faultHeader = ref.read(faultHeaderProvider.notifier);
    final location = ref.watch(locationProvider.notifier);
    final assetLocList = ref.watch(assetLocListProvider.notifier);
    final asset = ref.watch(assetProvider.notifier);
    final dueOn = ref.watch(faultDueOnProvider.notifier);
    final description = ref.watch(faultDescriptionProvider.notifier);
    final faultMode = ref.watch(faultModeHeaderProvider.notifier);
    final priority = ref.watch(priorityProvider.notifier);
    final faultType = ref.watch(faultTypeProvider.notifier);
    final longText = ref.watch(faultLongTextProvider.notifier);
    final reportedBy = ref.watch(faultReportedByProvider.notifier);
    location.clearLocation();
    assetLocList.clearAssetLocList();
    asset.clearAsset();
    dueOn.clearDueOn();
    description.clearFaultDescription();
    faultMode.clearFaultMode();
    priority.clearPriority();
    faultType.clearFaultType();
    longText.clearFaultLongText();
    reportedBy.clearFaultReportedBy();
    faultHeader.clearFault();
  }

  clearFaultStates() {
    final location = ref.watch(locationProvider.notifier);
    final assetLocList = ref.watch(assetLocListProvider.notifier);
    final asset = ref.watch(assetProvider.notifier);
    final dueOn = ref.watch(faultDueOnProvider.notifier);
    final description = ref.watch(faultDescriptionProvider.notifier);
    final faultMode = ref.watch(faultModeHeaderProvider.notifier);
    final priority = ref.watch(priorityProvider.notifier);
    final faultType = ref.watch(faultTypeProvider.notifier);
    final longText = ref.watch(faultLongTextProvider.notifier);
    final reportedBy = ref.watch(faultReportedByProvider.notifier);
    location.clearLocation();
    assetLocList.clearAssetLocList();
    asset.clearAsset();
    dueOn.clearDueOn();
    description.clearFaultDescription();
    faultMode.clearFaultMode();
    priority.clearPriority();
    faultType.clearFaultType();
    longText.clearFaultLongText();
    reportedBy.clearFaultReportedBy();
  }

  Future<void> onCompleteButton(
      {String? dataString,
      double? dataDouble,
      Uint8List? data,
      String? tasktype}) async {
    final inspHeader = widget.widref.watch(inspectionHeaderProvider);
    final inspCompleted = widget.widref.watch(inspCompletedProvider.notifier);
    final inspExecHeader =
        widget.widref.watch(inspExecuteTaskListProvider.notifier);
    final inspPlanHeader = widget.widref.watch(inspectionPlanHeaderProvider);

    final inspTasknotifier =
        widget.widref.read(inspectionTaskNotifier.notifier);
    final inspExecTaskListNotifier =
        widget.widref.read(inspExecuteTaskListProvider.notifier);
    final inspectionHeaderListNotifier =
        widget.widref.read(inspectionHeaderListProvider.notifier);
    final plant = widget.widref.read(plantProvider.notifier).state;
    final plantSection =
        widget.widref.read(plantSectionProvider.notifier).state;
    final shift = widget.widref.read(shiftProvider.notifier).state;
    final togglenotifier = widget.widref.read(inspectionToggleStateProvider);
    final inspectionHeaderNotifier =
        widget.widref.read(inspectionHeaderProvider.notifier);

    final List<INSP_EXEC_TASK> inspExecTasks =
        inspExecTaskListNotifier.findAllInspExecOfInsp(inspHeader);

    if (tasktype != null) {
      if (tasktype == 'e') {
        bool isDiscrepant = false;
        if (dataDouble! < numericLower) {
          isDiscrepant = true;
        } else if (dataDouble > numericUpper) {
          isDiscrepant = true;
        }
        await DbHelper().taskInspectionUpdate(
            task: widget.task,
            status: AppConstants.STATE_TASK_COMP,
            inspHeader: inspHeader,
            reasonCode: '',
            skipreason: '',
            p_mode: "M",
            dataString: dataString,
            dataDouble: dataDouble,
            dataBloc: data,
            isDiscrepant: isDiscrepant.toString());
      }
    } else {
      await DbHelper().taskInspectionUpdate(
          task: widget.task,
          status: AppConstants.STATE_TASK_COMP,
          inspHeader: inspHeader,
          reasonCode: '',
          skipreason: '',
          p_mode: "M",
          dataString: dataString,
          dataDouble: dataDouble,
          dataBloc: data);
    }

    INSPECTION_SECTION? inspection_section =
        await DbHelper.getInspectionSectionByPlanId(
            inspHeader.plan_id.toString());
    await widget.widref
        .read(inspectionPlanTaskListHeaderProvider.notifier)
        .fetchInspectionPlanTaskListHeaders(
            plantId: inspHeader.plant_id.toString(),
            header: inspHeader,
            section: inspection_section!);
    await widget.widref
        .read(inspExecuteProvider.notifier)
        .getInspExecute(inspHeader);
    await inspCompleted.inspCompleted(inspHeader);
    await inspExecHeader.getInspExecuteTaskList();
    widget.execTaskHeader?.status = AppConstants.STATE_TASK_COMP;
    widget.execTaskHeader?.p_mode = "M";
    widget.execTaskHeader = widget.widref
        .read(inspExecuteTaskListProvider.notifier)
        .getInspectionExecTaskHeaderByTask(widget.task, inspHeader);

    final progresssData = await _getProgressValue(inspHeader);

    final docHeaderNotifier =
        widget.widref.read(inspectionTaskExecDocumentHeaderProvider.notifier);
    final List<DOCUMENT_HEADER> headerData = [];

    for (var task in inspExecTasks) {
      final headers =
          docHeaderNotifier.getDocumentHeadersForInspectionTask(task);
      if (headers != null) {
        headerData.addAll(headers);
      }
    }
    final numberOfCompletedTasks = widget.widref
            .watch(inspExecuteProvider)[inspHeader.insp_id] ??
        [].where((element) => element.cilt_id == inspHeader.insp_id).toList();
    if (progresssData["totalTasks"] == progresssData["completedTasks"]) {
      //final dialogContext = navigatorKey.currentContext;

      await _showTaskCompletionDialog(
        inspHeader,
        widget.ctx,
        progresssData,
        ref,
        inspPlanHeader,
        numberOfCompletedTasks,
        inspExecTasks,
        headerData,
        inspExecTaskListNotifier,
        inspectionHeaderNotifier: inspectionHeaderNotifier,
        inspTaskNotifier: inspTasknotifier,
        inspheaderListNotifier: inspectionHeaderListNotifier,
        plant: plant,
        plantsec: plantSection,
        shift: shift,
        togglenotifier: togglenotifier,
      ).then((value) {});
    } else {
      widget.widref
          .read(inspExecuteTaskListProvider.notifier)
          .updateInspExexTask(widget.execTaskHeader!);
      if (widget.widref.read(inspectionToggleStateProvider)) {
        await widget.widref
            .read(inspectionTaskNotifier.notifier)
            .fetchIncompleteTasks((inspPlanHeader.plan_id.toString() ?? ""),
                ref.read(inspectionHeaderProvider), ref);
      }
    }
    widget.widref
        .read(numericInspectionHeaderProvider.notifier)
        .clearNumericValue(widget.execTaskHeader!);
  }

  Future<void> onUnCompleteButton() async {
    final inspHeader = ref.watch(inspectionHeaderProvider);
    final inspCompleted = ref.watch(inspCompletedProvider.notifier);
    final inspExecHeader = ref.watch(inspExecuteTaskListProvider.notifier);
    final inspPlanHeader = ref.watch(inspectionPlanHeaderProvider);
    final visited = <String>{};
    await resetTaskAndNestedDependents(widget.task, inspHeader, visited);

    INSPECTION_SECTION? inspection_section =
        await DbHelper.getInspectionSectionByPlanId(
            inspHeader.plan_id.toString());
    await widget.widref
        .read(inspectionPlanTaskListHeaderProvider.notifier)
        .fetchInspectionPlanTaskListHeaders(
            plantId: inspHeader.plant_id.toString(),
            header: inspHeader,
            section: inspection_section!);

    await widget.widref
        .read(inspExecuteProvider.notifier)
        .getInspExecute(inspHeader);

    await inspCompleted.inspCompleted(inspHeader);
    await inspExecHeader.getInspExecuteTaskList();
    await widget.widref
        .read(inspExecuteTaskListProvider.notifier)
        .getInspExecuteTaskList();
    widget.execTaskHeader?.status = AppConstants.STATE_TASK_OPEN;
    widget.execTaskHeader?.p_mode = "M";
    final progresssData = await _getProgressValue(inspHeader);
    final numberOfCompletedTasks = widget.widref
            .watch(inspExecuteProvider)[inspHeader.insp_id] ??
        [].where((element) => element.cilt_id == inspHeader.insp_id).toList();
    final inspExecTaskListNotifier =
        widget.widref.read(inspExecuteTaskListProvider.notifier);
    final List<INSP_EXEC_TASK> inspExecTasks =
        inspExecTaskListNotifier.findAllInspExecOfInsp(inspHeader);

    // Extract document headers
    final docHeaderNotifier =
        widget.widref.read(inspectionTaskExecDocumentHeaderProvider.notifier);
    final List<DOCUMENT_HEADER> headerData = [];

    for (var task in inspExecTasks) {
      final headers =
          docHeaderNotifier.getDocumentHeadersForInspectionTask(task);
      if (headers != null) {
        headerData.addAll(headers);
      }
    }
    if (progresssData["totalTasks"] == progresssData["completedTasks"]) {
      await _showTaskCompletionDialog(
              inspHeader,
              context,
              progresssData,
              ref,
              inspPlanHeader,
              numberOfCompletedTasks,
              inspExecTasks,
              headerData,
              inspExecTaskListNotifier)
          .then((value) {});
    } else {
      widget.widref
          .read(inspExecuteTaskListProvider.notifier)
          .updateInspExexTask(widget.execTaskHeader!);
      if (widget.widref.read(inspectionToggleStateProvider)) {
        await widget.widref
            .read(inspectionTaskNotifier.notifier)
            .fetchIncompleteTasks((inspPlanHeader.plan_id.toString() ?? ""),
                ref.read(inspectionHeaderProvider), ref);
      }
    }
    widget.widref
        .read(numericInspectionHeaderProvider.notifier)
        .clearNumericValue(widget.execTaskHeader!);
  }

  Future<void> resetTaskAndNestedDependents(
    INSPECTION_TASK task,
    INSP_EXEC_HEADER inspHeader,
    Set<String> visited,
  ) async {
    if (visited.contains(task.task_id.toString())) return;
    visited.add(task.task_id.toString());
    await DbHelper().taskInspectionUpdate(
      task: task,
      status: AppConstants.STATE_TASK_OPEN,
      inspHeader: inspHeader,
      reasonCode: '',
      skipreason: '',
      p_mode: "M",
      comment: '',
      dataString: null,
      dataDouble: null,
      dataBloc: null,
      isSkipped: 'false',
      isIrrelevant: 'false',
      isDiscrepant: 'false',
    );

    final dependents = await DbHelper.getInspectionTaskPlanListHeaderDependent(
      task.plan_id.toString(),
      task,
    );

    for (var child in dependents) {
      await resetTaskAndNestedDependents(child, inspHeader, visited);
    }

    final inspExecHeader =
        widget.widref.watch(inspExecuteTaskListProvider.notifier);
    await inspExecHeader.getInspExecuteTaskList();
    widget.execTaskHeader = widget.widref
        .read(inspExecuteTaskListProvider.notifier)
        .getInspectionExecTaskHeaderByTask(widget.task, widget.inspExecHeader!);
  }

  Future<void> onUndo() async {
    final inspHeader = ref.watch(inspectionHeaderProvider);
    await DbHelper().taskInspectionUpdate(
        task: widget.task,
        status: AppConstants.STATE_TASK_OPEN,
        inspHeader: inspHeader,
        reasonCode: '',
        skipreason: '',
        p_mode: null,
        dataString: null,
        dataDouble: null,
        dataBloc: null,
        isIrrelevant: 'false',
        isDiscrepant: 'false',
        isSkipped: 'false');
    INSPECTION_SECTION? section = await DbHelper.getInspectionSectionByPlanId(
        inspHeader.plan_id.toString());
    await ref.read(inspTasksProvider.notifier).getInspTasks(inspHeader.plan_id!,
        header: inspHeader,
        section: section!,
        plantId: inspHeader.plant_id.toString());

    await ref.read(inspExecuteProvider.notifier).getInspExecute(inspHeader);
    widget.execTaskHeader?.str_value = '';
    widget.execTaskHeader?.num_value = 0;
    widget.execTaskHeader?.blob_value = '';
    widget.execTaskHeader?.status = AppConstants.STATE_TASK_OPEN;
    widget.execTaskHeader?.p_mode = null;
    ref
        .read(inspExecuteTaskListProvider.notifier)
        .updateInspExexTask(widget.execTaskHeader!);
    ref.read(inspectionTaskNotifier.notifier).updateTask(widget.task);
    setState(() {
      widget.execTaskHeader?.status = AppConstants.STATE_TASK_OPEN;
      widget.execTaskHeader?.p_mode = null;
    });
    ref
        .read(numericInspectionHeaderProvider.notifier)
        .clearNumericValue(widget.execTaskHeader!);
  }

  String? getPickListCode(String data) {
    final pickListCodeList = ref.read(pickListCodeListProvider);
    List<PICKLIST_CODE?> choiceChipData = pickListCodeList
        .where((element) =>
            element.picklist_id == widget.task.picklist_id.toString())
        .toList();
    String? code;
    code = choiceChipData
        .firstWhere(
          (element) => element?.description == data,
        )
        ?.code;

    return code;
  }

  String? getPickListString(String data) {
    final pickListCodeList = ref.read(pickListCodeListProvider);
    List<PICKLIST_CODE?> choiceChipData = pickListCodeList
        .where((element) =>
            element.picklist_id == widget.task.picklist_id.toString())
        .toList();
    String? code;
    code = choiceChipData
        .firstWhere(
          (element) => element?.code == data,
        )
        ?.description;
    return code;
  }

  void initializingControllers() {
    INSPECTION_TASK task = widget.task;
    // widget.execTaskHeader = ref.read(inspectionExecTaskProvider);
    INSP_EXEC_TASK? execTask = widget.execTaskHeader;

    if (task.task_type.toString() == 'n') {
      if (execTask != null) {
        if (execTask.str_value != null && execTask.str_value != '') {
          timerController.text =
              execTask.str_value != null ? execTask.str_value.toString() : '';
        }
      }
    }
    if (task.task_type.toString() == 'h') {
      if (execTask != null) {
        if (execTask.num_value != null && execTask.num_value != 0) {
          _isChecked = (execTask.num_value != null && execTask.num_value != 0)
              ? execTask.num_value == 1
                  ? true
                  : false
              : false;
        }
      }
    } else if (task.task_type.toString() == 'i') {
      if (execTask != null) {
        if (execTask.str_value != null && execTask.str_value != '') {
          dateAndTimerController.text =
              execTask.str_value != null ? execTask.str_value.toString() : '';
        }
      }
    } else if (task.task_type.toString() == 'c') {
      if (execTask != null) {
        if (execTask.str_value != null && execTask.str_value != '') {
          dateRangeController.text =
              execTask.str_value != null ? execTask.str_value.toString() : '';
        }
      }
    } else if (task.task_type.toString() == 'k') {
      if (execTask != null) {
        if (execTask.str_value != null && execTask.str_value != '') {
          locationController.text =
              execTask.str_value != null ? execTask.str_value.toString() : '';
        }
      }
    } else if (task.task_type.toString() == 'g') {
      if (execTask != null) {
        if (execTask.blob_value != null && execTask.blob_value != '') {
          Uint8List signatureData = base64Decode(execTask.blob_value!);
          sign = execTask.blob_value != null
              ? Image.memory(
                  signatureData,
                  fit: BoxFit.contain,
                )
              : SizedBox();
        }
      }
    } else if (task.task_type.toString() == 'f') {
      if (execTask != null) {
        if (execTask.str_value != null && execTask.str_value != '') {
          scanController.text =
              execTask.str_value != null ? execTask.str_value.toString() : '';
        }
      }
    } else if (task.task_type.toString() == 'l') {
      inputController.text =
          task.description != null ? task.description.toString() : '';
      if (execTask != null) {
        if (execTask.num_value != null) {
          _isCheckedInput =
              (execTask.num_value != null && execTask.num_value != 0)
                  ? true
                  : false;
        }
      }
    } else if (task.task_type.toString() == 'j') {
      if (execTask != null) {
        if (execTask.num_value != null) {
          _isCheckedLink =
              (execTask.num_value != null && execTask.num_value != 0)
                  ? true
                  : false;
        }
      }
      linkController.text =
          task.description != null ? task.description.toString() : '';
    } else if (task.task_type.toString() == 'd') {
      if (execTask != null) {
        if (execTask.num_value != null) {
          _isCheckedInstruction =
              (execTask.num_value != null && execTask.num_value != 0)
                  ? true
                  : false;
        }
      }
      instructionController.text =
          task.description != null ? task.description.toString() : '';
    } else if (task.task_type.toString() == 'm') {
      min = task.lower_limit != null ? task.lower_limit!.toDouble() : 0;
      max = task.upper_limit != null ? task.upper_limit!.toDouble() : 1;
      if (execTask != null) {
        final taskKey =
            '${execTask.insp_id}_${execTask.insp_task_id}_${execTask.section_id}_${execTask.task_id}_${execTask.dep_insp_task_id}_${execTask.task_no}';

        final numericTask = ref.read(numericInspectionHeaderProvider)[taskKey];
        if (numericTask != null &&
            numericTask.num_value != null &&
            numericTask.num_value != 0) {
          final num = numericTask.num_value!;
          _sliderValue = num;
        } else {
          if (execTask.num_value != null) {
            _sliderValue = (execTask.num_value ?? 0);
            previousSliderData = _sliderValue;
          } else {
            _sliderValue = 0;
          }
        }
/*        if (execTask.num_value != null) {
          _sliderValue = (execTask.num_value ?? 0);
          previousSliderData = _sliderValue;
        } else {
          final taskKey =
              '${execTask.insp_id}_${execTask.insp_task_id}_${execTask.section_id}_${execTask.task_id}_${execTask.dep_insp_task_id}_${execTask.task_no}';

          final numericTask =
              ref.read(numericInspectionHeaderProvider)[taskKey];

          if (numericTask != null &&
              numericTask.num_value != null &&
              numericTask.num_value != 0) {
            final num = numericTask.num_value!;
            _sliderValue = num;
          } else {
            _sliderValue = 0;
          }
        }*/
      }
    } else if (task.task_type.toString() == 'e') {
      numericLower =
          task.lower_limit != null ? task.lower_limit!.toDouble() : 0;
      numericUpper =
          task.upper_limit != null ? task.upper_limit!.toDouble() : 0;
      numericStandard =
          task.standard_value != null ? task.standard_value!.toDouble() : 0;
      numericLowerMsg =
          task.lower_limit_msg != null ? task.lower_limit_msg!.toString() : '';
      numericUpperMsg =
          task.upper_limit_msg != null ? task.upper_limit_msg!.toString() : '';
      if (execTask != null) {
        final taskKey =
            '${execTask.insp_id}_${execTask.insp_task_id}_${execTask.section_id}_${execTask.task_id}_${execTask.dep_insp_task_id}_${execTask.task_no}';

        final numericTask =
            widget.widref.read(numericInspectionHeaderProvider)[taskKey];

        if (numericTask != null &&
            numericTask.num_value != null &&
            numericTask.num_value != 0) {
          final num = numericTask.num_value!;

          if (num < numericLower) {
            validationMessage = numericLowerMsg;
          } else if (num > numericUpper) {
            validationMessage = numericUpperMsg;
          } else {
            validationMessage = null;
          }

          numericController.text = formatDouble(num);
          // previousNumericData = numericController.text;
        } else {
          if (execTask.num_value != null && execTask.num_value != 0) {
            if (execTask.num_value! < numericLower) {
              validationMessage = numericLowerMsg;
            } else if (execTask.num_value! > numericUpper) {
              validationMessage = numericUpperMsg;
            } else {
              validationMessage = null;
            }

            numericController.text = execTask.num_value != null
                ? formatDouble(execTask.num_value!)
                : '';
            previousNumericData = numericController.text;
          } else {
            numericController.text = '';
            validationMessage = '';
          }
        }
      }
    } else if (task.task_type.toString() == 'b') {
      final pickListCodeList = widget.widref.read(pickListCodeListProvider);
      choiceChipData = pickListCodeList
          .where(
              (element) => element.picklist_id == task.picklist_id.toString())
          .map((e) => e.description)
          .toList();

      if (pickListCodeList.isNotEmpty) {
        choiceChipColorsData = choiceChipData.map((label) {
          if (label == selectedChoiceChip) {
            final matched = pickListCodeList.firstWhere(
              (e) =>
                  e.picklist_id == task.picklist_id.toString() &&
                  e.description == label,
              orElse: () => PICKLIST_CODE(picklist_id: '', code: ''),
            );
            return matched.color ?? '#D3D3D3';
          } else {
            return '#D3D3D3';
          }
        }).toList();
      }

      if (execTask != null) {
        if (execTask.str_value != null && execTask.str_value != '') {
          selectedChoiceChip = getPickListString(execTask.str_value!)!;
          final filteredList = pickListCodeList
              .where(
                (element) => element.picklist_id == task.picklist_id.toString(),
              )
              .toList();
          final selectedIndex = choiceChipData.indexOf(selectedChoiceChip);
          choiceChipColorsData =
              List<String?>.filled(choiceChipData.length, '#D3D3D3');
          if (selectedIndex != -1) {
            final matchedColor = filteredList
                .firstWhere(
                  (e) => e.description == selectedChoiceChip,
                  orElse: () => PICKLIST_CODE(
                      picklist_id: '', code: '', color: '#D3D3D3'),
                )
                .color;

            choiceChipColorsData[selectedIndex] = matchedColor ?? '#D3D3D3';
          }
        }
      }
    } else if (task.task_type.toString() == 'o') {
      if (task.kpi_id != null) {
        final plant = widget.widref.read(plantProvider);
        // final kpiHeader = ref.read(kpiIdHeaderProvider);
        List<KPI_HEADER> headers = widget.widref.read(kpiHeaderProvider);
        KPI_HEADER kpiHeader = headers.firstWhere((element) =>
            (element.plant_id == plant &&
                element.kpi_id.toString() == task.kpi_id.toString()));

        if (kpiHeader.kpi_id != null && kpiHeader.kpi_id != 0) {
          if (kpiHeader.kpi_type == '2' ||
              kpiHeader.kpi_type == '3' ||
              kpiHeader.kpi_type == '4') {
            numericLower = kpiHeader.lower_limit != null
                ? kpiHeader.lower_limit!.toDouble()
                : 0;
            numericUpper = kpiHeader.upper_limit != null
                ? kpiHeader.upper_limit!.toDouble()
                : 0;
            numericStandard = kpiHeader.standard_value != null
                ? kpiHeader.standard_value!.toDouble()
                : 0;
            numericLowerMsg = kpiHeader.lower_limit_msg != null
                ? kpiHeader.lower_limit_msg!.toString()
                : '';
            numericUpperMsg = kpiHeader.upper_limit_msg != null
                ? kpiHeader.upper_limit_msg!.toString()
                : '';

            if (execTask != null) {
              final taskKey =
                  '${execTask.insp_id}_${execTask.insp_task_id}_${execTask.section_id}_${execTask.task_id}_${execTask.dep_insp_task_id}_${execTask.task_no}';

              final numericTask =
                  widget.widref.read(numericInspectionHeaderProvider)[taskKey];

              if (numericTask != null &&
                  numericTask.num_value != null &&
                  numericTask.num_value != 0) {
                final num = numericTask.num_value!;

                if (num < numericLower) {
                  validationMessage = numericLowerMsg;
                } else if (num > numericUpper) {
                  validationMessage = numericUpperMsg;
                } else {
                  validationMessage = null;
                }

                numericController.text = formatDouble(num);
                // previousNumericData = numericController.text;
              } else {
                if (execTask.num_value != null && execTask.num_value != 0) {
                  if (execTask.num_value! < numericLower) {
                    validationMessage = numericLowerMsg;
                  } else if (execTask.num_value! > numericUpper) {
                    validationMessage = numericUpperMsg;
                  } else {
                    validationMessage = null;
                  }

                  numericController.text = execTask.num_value != null
                      ? formatDouble(execTask.num_value!)
                      : '';
                  previousNumericData = numericController.text;
                } else {
                  numericController.text = '';
                  validationMessage = '';
                }
              }
            }
          } else if (kpiHeader.kpi_type == '1') {
            final pickListCodeList =
                widget.widref.read(pickListCodeListProvider);
            choiceChipData = pickListCodeList
                .where((element) =>
                    element.picklist_id == kpiHeader.picklist_id.toString())
                .map((e) => e.description)
                .toList();

            if (pickListCodeList.isNotEmpty) {
              choiceChipColorsData = choiceChipData.map((label) {
                if (label == selectedChoiceChip) {
                  final matched = pickListCodeList.firstWhere(
                    (e) =>
                        e.picklist_id == kpiHeader.picklist_id.toString() &&
                        e.description == label,
                    orElse: () => PICKLIST_CODE(picklist_id: '', code: ''),
                  );
                  return matched.color ?? '#D3D3D3';
                } else {
                  return '#D3D3D3';
                }
              }).toList();
            }

            if (execTask != null) {
              if (execTask.str_value != null && execTask.str_value != '') {
                selectedChoiceChip = getPickListString(execTask.str_value!)!;
                final filteredList = pickListCodeList
                    .where(
                      (element) =>
                          element.picklist_id ==
                          kpiHeader.picklist_id.toString(),
                    )
                    .toList();
                final selectedIndex =
                    choiceChipData.indexOf(selectedChoiceChip);
                choiceChipColorsData =
                    List<String?>.filled(choiceChipData.length, '#D3D3D3');
                if (selectedIndex != -1) {
                  final matchedColor = filteredList
                      .firstWhere(
                        (e) => e.description == selectedChoiceChip,
                        orElse: () => PICKLIST_CODE(
                            picklist_id: '', code: '', color: '#D3D3D3'),
                      )
                      .color;

                  choiceChipColorsData[selectedIndex] =
                      matchedColor ?? '#D3D3D3';
                }
              }
            }
          }
        }
      }
    }
  }

  String _getSectionAdditionalInfo(INSPECTION_SECTION section) {
    // Scenario 4: No technical objects
    if (section.location_id == null && section.asset_no == null) {
      return "";
    }

    // Scenario 3: Both location and asset exist - show only asset info
    if (section.location_id != null && section.asset_no != null) {
      ASSET_HEADER? assetHeader = ref
          .read(assetHeaderProvider.notifier)
          .findAssetHeaderById(section.asset_no ?? -1);
      return UIHelper.formatIdAndDescription(
          section.asset_no.toString(), assetHeader?.description,
          wrap: WrapType.id);
    }

    // Scenario 2: Only asset exists
    if (section.asset_no != null) {
      ASSET_HEADER? assetHeader = ref
          .read(assetHeaderProvider.notifier)
          .findAssetHeaderById(section.asset_no ?? -1);
      return UIHelper.formatIdAndDescription(
          section.asset_no.toString(), assetHeader?.description,
          wrap: WrapType.id);
    }

    // Scenario 1: Only location exists
    if (section.location_id != null) {
      LOCATION_HEADER? locationHeader = ref
          .read(flocHeaderProvider.notifier)
          .findLocationHeadById(section.location_id ?? "");
      return UIHelper.formatIdAndDescription(
          section.location_id, locationHeader?.description,
          wrap: WrapType.id);
    }

    return "";
  }

  String formatDouble(double value) {
    if (value == value.toInt()) {
      return value.toInt().toString();
    } else {
      return value.toString();
    }
  }
}
