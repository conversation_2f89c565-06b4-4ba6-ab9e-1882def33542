import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/be/DOCUMENT_ATTACHMENT.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/providers/attachments/attachment_provider.dart';
import 'package:rounds/providers/fault/fault_header_provider.dart';
import 'package:rounds/providers/user_provider.dart';
import 'package:rounds/utils/utils.dart';
import 'package:rounds/widgets/round_attachment_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../utils/app_colors.dart';
import '../../be/ASSET_HEADER.dart';
import 'package:intl/intl.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../be/INSPECTION_PLAN_HEADER.dart';
import '../../be/INSP_EXEC_HEADER.dart';
import '../../providers/assets/asset_provider.dart';
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart' as dom;

import '../../providers/fault/fault_type_provider.dart';
import '../../providers/inspection/inspection_header_provider.dart';
import '../../providers/inspection/inspection_plan_header_provider.dart';
import 'inspection_detail_screen.dart';

class InspRoundPlanDetailScreen extends ConsumerStatefulWidget {
  INSPECTION_PLAN_HEADER? inspPlanHeader;
  bool? navigate;
  InspRoundPlanDetailScreen(
      {required this.inspPlanHeader, this.navigate = false, super.key});

  @override
  _InspRoundPlanDetailScreenState createState() =>
      _InspRoundPlanDetailScreenState();
}

class _InspRoundPlanDetailScreenState
    extends ConsumerState<InspRoundPlanDetailScreen> {
  String shift = "";

  bool toggleValue = false;
  INSP_EXEC_HEADER? insp_header;
  List<String> addedFaults = [];
  List<USER_HEADER> userList = [];
  TextEditingController faultNoticedOn = TextEditingController();
  TextEditingController dueOn = TextEditingController();

  @override
  void initState() {
    DateTime now = DateTime.now();
    String formattedDate = DateFormat('d MMMM yyyy').format(now);
    faultNoticedOn.text = formattedDate;
    Future.delayed(Duration.zero).then((value) async {
      await ref
          .read(documentAttachmentProvider.notifier)
          .fetchDocumentAttachments();
      await ref
          .read(inspectionPlanAndTaskDocProvider.notifier)
          .fetchInspPlanAndTaskDoc(widget.inspPlanHeader!);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ASSET_HEADER? asset = ref.watch(singleAssetProvider);
    shift = ref.watch(shiftProvider);
    userList = ref.watch(usersListProvider);
    insp_header = ref.watch(inspectionHeaderProvider);
    USER_HEADER? userHeader =
        DbHelper.getAssignUser(userList, insp_header!.assigned_to!);

    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        elevation: 0,
        titleSpacing: 0,
        leadingWidth: 35,
        backgroundColor: AppColors.white,
        leading: IconButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          icon: Padding(
            padding: const EdgeInsets.only(left: 8.0),
            child: Icon(
              Icons.arrow_back_ios,
              size: 20,
              color: AppColors.black,
            ),
          ),
        ),
        actions: [
          if (widget.navigate!)
            TextButton(
                onPressed: () async {
                  final preference = await SharedPreferences.getInstance();
                  preference.setString(insp_header!.insp_id.toString(),
                      AppLocalizations.of(context)!.visited);
                  Navigator.of(context).push(MaterialPageRoute(
                    builder: (context) => InspectionDetailScreen(
                      inspectionPlanHeader: widget.inspPlanHeader,
                      inspHeader: insp_header,
                    ),
                  ));
                },
                child: Text(AppLocalizations.of(context)!.execute))
        ],
        title: Text(
          widget.inspPlanHeader!.title ?? "",
          style: const TextStyle(color: AppColors.blackTitleText),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: UIHelper.columnFieldOnlhorizontalPadding(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                (widget.inspPlanHeader!.description ?? "").isNotEmpty
                    ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Text(
                          extractTextFromHtml(
                              widget.inspPlanHeader!.description!),
                          style: const TextStyle(
                              color: AppColors.greySubtitleText),
                        ),
                      )
                    : const SizedBox(),
                getLocationDetails(asset),
                UIHelper.sizedBox10(),
                getGeneralDetails(),
                UIHelper.sizedBox10(),
                getSchedules(),
                UIHelper.sizedBox10(),
                getAssignment(userHeader!),
                UIHelper.sizedBox10(),
                Padding(
                    padding: UIHelper.columnFieldPadding(),
                    child: Column(children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            AppLocalizations.of(context)!.attachments,
                            style: UIHelper.headerStyle(),
                          ),
                        ],
                      ),
                      UIHelper.sizedBox8(),
                    ])),
                getAttachments(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget getLocationDetails(ASSET_HEADER? asset) {
    return Padding(
      padding: UIHelper.columnFieldPadding(),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppLocalizations.of(context)!.locationDetails,
                style: UIHelper.headerStyle(),
              ),
            ],
          ),
          UIHelper.sizedBox8(),
          Container(
            decoration: UIHelper.cardDecoration(),
            child: Padding(
              padding: UIHelper.allPaddingOf10(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTextField(
                    AppLocalizations.of(context)!.plantSection,
                    widget.inspPlanHeader!.plant_sec_id.toString(),
                  ),
                  _buildTextField(
                    AppLocalizations.of(context)!.functionalLocation,
                    (widget.inspPlanHeader!.location_id ?? "").toString(),
                  ),
                  _buildTextField(
                    AppLocalizations.of(context)!.asset,
                    (asset?.description ?? "").toString(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget getGeneralDetails() {
    return Padding(
      padding: UIHelper.columnFieldPadding(),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppLocalizations.of(context)!.generalInformation,
                style: UIHelper.headerStyle(),
              ),
            ],
          ),
          UIHelper.sizedBox8(),
          Container(
            decoration: UIHelper.cardDecoration(),
            child: Padding(
              padding: UIHelper.allPaddingOf10(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  widget.inspPlanHeader!.description != null &&
                          widget.inspPlanHeader!.description != ''
                      ? _buildTextFieldHtmlWidget(
                          AppLocalizations.of(context)!.detail_description,
                          widget.inspPlanHeader!.description.toString())
                      : SizedBox(),
                  _buildTextField(AppLocalizations.of(context)!.created_by,
                      insp_header!.created_by.toString()),
                  _buildTextField(
                      AppLocalizations.of(context)!.priority,
                      ref
                          .watch(priorityListProvider.notifier)
                          .fetchPriorityCode(insp_header!.priority.toString())),
                ],
              ),
            ),
          ),
          // _buildAddedFaults(addedFaults, setState),
        ],
      ),
    );
  }

  Widget getSchedules() {
    // int assignedOn = insp_header!.start_on ?? 0;
    return Padding(
      padding: UIHelper.columnFieldPadding(),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppLocalizations.of(context)!.schedule,
                style: UIHelper.headerStyle(),
              ),
            ],
          ),
          UIHelper.sizedBox8(),
          Container(
            decoration: UIHelper.cardDecoration(),
            child: Padding(
              padding: UIHelper.allPaddingOf10(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTextField(AppLocalizations.of(context)!.staerAndEndDate,
                      formatDate(insp_header!.start_on.toString())),
                  _buildTextField(AppLocalizations.of(context)!.staerAndEndTime,
                      "${formatTime(insp_header!.start_at)} - ${formatTime(insp_header!.end_at)}"),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget getAssignment(USER_HEADER user) {
    return Padding(
      padding: UIHelper.columnFieldPadding(),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppLocalizations.of(context)!.assignment,
                style: UIHelper.headerStyle(),
              ),
            ],
          ),
          UIHelper.sizedBox8(),
          Container(
            decoration: UIHelper.cardDecoration(),
            child: Padding(
              padding: UIHelper.allPaddingOf10(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTextField(AppLocalizations.of(context)!.assignedto,
                      "${toCamelCase(user.first_name.toString())} ${toCamelCase(user.last_name.toString())}"),
                ],
              ),
            ),
          ),
          // _buildAddedFaults(addedFaults, setState),
        ],
      ),
    );
  }

  Widget getAttachments() {
    final documentAttachments = ref.watch(documentAttachmentProvider);
    List<DOCUMENT_HEADER> documentHeaders = ref
        .watch(inspectionPlanAndTaskDocProvider.notifier)
        .getInspPlanDocs(widget.inspPlanHeader!.plan_id!);
    List<DOCUMENT_ATTACHMENT> attachmentList = documentAttachments.where(
      (attachment) {
        return documentHeaders
                .where((docHeader) => docHeader.lid == attachment.fid)
                .toList()
                .length ==
            1;
      },
    ).toList();

    return attachmentList.isEmpty
        ? Padding(
            padding: UIHelper.columnFieldPadding(),
            child: SizedBox(child: Text("Attachments not found")),
          )
        : RoundsAttachmentPicker(
            onAttachmentPicked: (value) {},
            isAddButtonRequired: false,
            viewType: RoundsAttachmentPickerViewType.list,
            attachments: attachmentList
                .map((e) => DocumentAttachmentContainer(
                      height: 80,
                      width: 80,
                      isFromPlanner: documentHeaders
                          .where((element) => element.lid == e.fid)
                          .toList()
                          .isNotEmpty,
                      attachment: e,
                      uploadProgress:
                          ref.watch(attachMentUploadProgressProvider)[e.lid] ??
                              1.0,
                      isUploading:
                          (ref.watch(attachMentUploadProgressProvider)[e.lid] ??
                                  1.0) <
                              1.0,
                      onDelete: (value) async {
                        UIHelper.showEamDialog(context,
                            title:
                                AppLocalizations.of(context)!.delete_attachment,
                            description: AppLocalizations.of(context)!
                                .do_you_want_delete_attachment,
                            positiveActionLabel:
                                AppLocalizations.of(context)!.yes,
                            negativeActionLabel: AppLocalizations.of(context)!
                                .cancel, onPositiveClickListener: () async {
                          await ref
                              .read(documentAttachmentProvider.notifier)
                              .deleteDocumentAttachments(e);
                          Navigator.of(context).pop();
                        }, onNegativeClickListener: () {
                          Navigator.of(context).pop();
                        });
                      },
                      onTap: (value) {
                        // TODO impliment onTap
                      },
                    ))
                .toList());
  }

  /*Widget getAttachments1() {
    final documentAttachments = ref.watch(documentAttachmentProvider);
    List<DOCUMENT_HEADER> documentHeaders = ref
        .watch(ciltPlanAndTaskDocProvider.notifier)
        .getCiltPlanDocs( widget.inspPlanHeader!.plan_id!);
    List<DOCUMENT_ATTACHMENT> attachmentList = documentAttachments.where(
      (attachment) {
        return documentHeaders
                .where((docHeader) => docHeader.lid == attachment.fid)
                .toList()
                .length ==
            1;
      },
    ).toList();
    return attachmentList.isEmpty
        ? SizedBox()
        : Padding(
            padding: UIHelper.columnFieldPadding(),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.attachments,
                      style: UIHelper.headerStyle(),
                    ),
                  ],
                ),
                UIHelper.sizedBox8(),
                RoundsAttachmentPicker(
                    onAttachmentPicked: (value) {},
                    isAddButtonRequired: false,
                    viewType: RoundsAttachmentPickerViewType.list,
                    attachments: attachmentList
                        .map((e) => DocumentAttachmentContainer(
                              isFromPlanner: documentHeaders
                                  .where((element) => element.lid == e.fid)
                                  .toList()
                                  .isNotEmpty,
                              attachment: e,
                              uploadProgress:
                                  ref.watch(attachMentUploadProgressProvider)[
                                          e.lid] ??
                                      1.0,
                              isUploading:
                                  (ref.watch(attachMentUploadProgressProvider)[
                                              e.lid] ??
                                          1.0) <
                                      1.0,
                              onDelete: (value) async {
                                UIHelper.showEamDialog(context,
                                    title: AppLocalizations.of(context)!
                                        .delete_attachment,
                                    description: AppLocalizations.of(context)!
                                        .do_you_want_delete_attachment,
                                    positiveActionLabel:
                                        AppLocalizations.of(context)!.yes,
                                    negativeActionLabel:
                                        AppLocalizations.of(context)!.cancel,
                                    onPositiveClickListener: () {
                                  ref
                                      .read(documentAttachmentProvider.notifier)
                                      .deleteDocumentAttachments(e);
                                  Navigator.of(context).pop();
                                }, onNegativeClickListener: () {
                                  Navigator.of(context).pop();
                                });
                              },
                              onTap: (value) {
                                // TODO impliment onTap
                              },
                            ))
                        .toList())
              ],
            ),
          );
  }*/

  Widget _buildTextField(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.titleTextColor,
                fontWeight: FontWeight.w600,
                letterSpacing: 0.1,
              ),
            ),
          ),
          const SizedBox(
            height: 3,
          ),
          Text(
            value,
            style: UIHelper.valueStyle14(),
          )
        ],
      ),
    );
  }

  Widget _buildTextFieldHtmlWidget(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.titleTextColor,
                fontWeight: FontWeight.w600,
                letterSpacing: 0.1,
              ),
            ),
          ),
          const SizedBox(
            height: 3,
          ),
          Html(
            data: value,
            style: {
              "body": Style(
                margin: Margins.zero,
                padding: HtmlPaddings.zero,
              ).merge(Style.fromTextStyle(UIHelper.valueStyle14())),
              "p": Style(
                margin: Margins.zero,
                padding: HtmlPaddings.zero,
              ),
            },
          )
        ],
      ),
    );
  }

  String extractTextFromHtml(String htmlString) {
    dom.Document document = html_parser.parse(htmlString);
    return document.body?.text ?? ''; // Extracts text from the body
  }

  String formatDate(String dateStr) {
    DateTime date = DateTime.parse(dateStr);
    return "${date.day.toString().padLeft(2, '0')} ${_getMonthAbbreviation(date.month)} ${date.year}";
  }

  String _getMonthAbbreviation(int month) {
    const List<String> months = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec"
    ];
    return months[month - 1];
  }

  String formatTime(int? timeStr) {
    return convertTimeString(timeStr);
  }

  String toCamelCase(String input) {
    if (input.isEmpty) return '';
    return input[0].toUpperCase() + input.substring(1).toLowerCase();
  }
}
