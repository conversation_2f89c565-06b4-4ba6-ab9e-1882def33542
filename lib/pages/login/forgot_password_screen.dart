import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/pages/login/reset_password_screen_by_token.dart';
import 'package:rounds/utils/app_extensions.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../helpers/ui_helper.dart';
import '../../utils/app_colors.dart';
import 'package:http/http.dart' as http;

import '../../utils/app_constants.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';

class ForgotPasswordScreen extends ConsumerStatefulWidget {
  bool login;

  ForgotPasswordScreen({super.key, this.login = false});

  @override
  ConsumerState<ForgotPasswordScreen> createState() =>
      _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends ConsumerState<ForgotPasswordScreen> {
  bool isEdit = true;
  TextEditingController emailController = TextEditingController();
  String emailErrorMsg = '';
  String errorMsg = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: AppColors.whiteColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.white,
        leadingWidth: 10,
        leading: IconButton(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: Icon(
              Icons.arrow_back_ios,
              color: AppColors.titleTextColor,
              size: 20,
            )),
        title: Text(
          context.locale.forgot_password,
          style: const TextStyle(
            fontSize: 23,
            fontWeight: FontWeight.w900,
            color: AppColors.blackTitleText,
          ),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: UIHelper.columnFieldPadding(),
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            child: Padding(
              padding: UIHelper.allPaddingOf10(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 10),
                    child: Text(
                      context.locale.info_message_for_forgot_password,
                      style: TextStyle(
                        fontSize: 16,
                        color: AppColors.blackTitleText,
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                  emailField()
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget emailField() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UIHelper.buildLabelAndValueAsWidgetOfProfileScreen(
          label: AppLocalizations.of(context)!.email,
          value: '',
          controller: emailController,
          onChanged: (v) {
            final cursorPosition = emailController.selection.baseOffset;

            List<String> parts = v.split('@');

            String beforeAt = parts[0]
                .replaceAll(RegExp(r"[^a-zA-Z0-9.!_\-#$&%',*+=/?^|{}~]"), '');

            String afterAt = parts.length > 1
                ? parts[1].replaceAll(RegExp(r"[^a-zA-Z0-9.-]"), '')
                : '';
            String filteredText =
                beforeAt + (parts.length > 1 ? '@' + afterAt : '');

            emailController.value = TextEditingValue(
              text: filteredText,
              selection: TextSelection.collapsed(
                offset: cursorPosition > filteredText.length
                    ? filteredText.length
                    : cursorPosition,
              ),
            );

            if (emailController.text.isNotEmpty) {
              setState(() {
                emailErrorMsg = UIHelper().isValidEmail(emailController.text)
                    ? ''
                    : AppLocalizations.of(context)!.invalid_email_format;
              });
            }
          },
          isPassword: false,
        ),
        emailErrorMsg != ''
            ? Container(
                padding: const EdgeInsets.only(left: 5.0, bottom: 5.0),
                width: double.infinity,
                child: Text(
                  emailErrorMsg,
                  style: const TextStyle(color: Colors.redAccent),
                ),
              )
            : SizedBox(),
        errorMsg != ''
            ? Align(
                alignment: Alignment.center,
                child: Text(
                  errorMsg,
                  style: const TextStyle(color: Colors.redAccent),
                ))
            : SizedBox(),
        errorMsg != '' ? SizedBox(height: 10) : SizedBox(),
        Padding(
          padding: EdgeInsets.only(top: 10),
          child: Row(
            children: [
              Expanded(
                child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                            side: BorderSide(color: AppColors.primaryColor),
                            borderRadius: BorderRadius.circular(8.0)),
                        backgroundColor: AppColors.primaryColor,
                        padding:
                            EdgeInsets.symmetric(vertical: 12, horizontal: 16)),
                    onPressed: () async {
                      if (validate()) {
                      } else {
                        await submit(userData: emailController.text);
                      }
                    },
                    child: Text(AppLocalizations.of(context)!.submit)),
              ),
            ],
          ),
        ),
      ],
    );
  }

  bool validate() {
    if (emailController.text.isEmpty) {
      setState(() {
        emailErrorMsg = AppLocalizations.of(context)!.please_enter_email;
      });
      return true;
    }
    return false;
  }

  Future<void> submit({
    required String userData,
  }) async {
    String loginCompany = AppConstants.companyName;
    String loginApplication = AppConstants.appName;
    String loginUrl = 'https://umpdev.unvired.io/UMP/';
    String user = userData;

    // String company =
    //     widget.login ? loginCompany : await SettingsHelper().getCompany();

    final prefs = await SharedPreferences.getInstance();
    String company = prefs.getString('domain') ?? '';

    String application =
        widget.login ? loginApplication : SettingsHelper().getApplicationName();

    // String umpUrl = widget.login ? loginUrl : await SettingsHelper().getUrl();

    String? storedUrl = prefs.getString('url');
    String umpUrl = (storedUrl?.isNotEmpty == true)
        ? '${storedUrl!.endsWith('/') ? storedUrl : '$storedUrl/'}UMP/'
        : loginUrl;

    try {
      if (!(await URLService.isInternetConnected())) {
        errorMsg = AppLocalizations.of(context)!.noInternetConnectivityString;
      } else {
        UIHelper().progressDialog(
            context: context,
            message: AppLocalizations.of(context)!.submitting);
        var headers = {
          'Content-Type': 'application/json',
        };

        var url = Uri.parse(
            '$umpUrl/API/v3/companies/$company/users/$user/forgotpassword?application=$application');

        var request = http.Request('POST', url);
        request.headers.addAll(headers);

        http.StreamedResponse response = await request.send();
        Navigator.pop(context);
        if (response.statusCode == 204) {
          Navigator.push(context, MaterialPageRoute(builder: (context) {
            return ResetPasswordScreenByToken(
                user: userData,
                login: widget.login,
                AppLocalizations.of(context)!.reset_token_success_message);
          }));
        } else {
          String responseBody = await response.stream.bytesToString();
          Map<String, dynamic> responseData = jsonDecode(responseBody);
          setState(() {
            errorMsg = responseData['error'] ??
                AppLocalizations.of(context)!.unknown_error;
          });
        }
      }
    } catch (e) {
      Logger.logError('ForgotPasswordScreen', 'changePassword', e.toString());
    }
  }
}
