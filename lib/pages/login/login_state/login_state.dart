import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum LoginState { domain, register, login, verify, success }

final loginStateProvider =
    StateNotifierProvider<LoginStateNotifier, LoginState>((ref) {
  return LoginStateNotifier();
});

class LoginStateNotifier extends StateNotifier<LoginState> {
  LoginStateNotifier() : super(LoginState.domain);

  void setLoginState(LoginState newState) {
    state = newState;
  }
}

final domainProvider =
    StateNotifierProvider<DomainStateNotifier, String>((ref) {
  return DomainStateNotifier();
});

class DomainStateNotifier extends StateNotifier<String> {
  DomainStateNotifier() : super(''); // Default state

  void setDomain(String domain) {
    state = domain;
  }
}

Future<void> saveDomainDataAndUrl(String key, String value) async {
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  await prefs.setString(key, value);
}

Future<String?> getDomainDataAndUrl(String key) async {
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.getString(key);
}
