import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

void showMultipleFrontEndIdDialog(
    {required BuildContext context,
    required String title,
    required List<String> list,
    required Function callback}) {
  int? selectedIndex;
  showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
            builder: (context, setState) {
              return Center(
                child: Material(
                  color: Colors.transparent,
                  child: Container(
                    width: double.infinity,
                    margin: const EdgeInsets.symmetric(horizontal: 25),
                    color: Colors.white,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          margin: const EdgeInsets.only(top: 10),
                          child: Center(
                            child: Text(
                              title,
                              style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black),
                            ),
                          ),
                        ),
                        Container(
                          height: 2,
                          margin: const EdgeInsets.only(top: 5),
                          color: Colors.blueGrey,
                        ),
                        Container(
                          margin: const EdgeInsets.only(top: 8),
                          child: ListView.builder(
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              return InkWell(
                                onTap: () {
                                  setState(() {
                                    selectedIndex = index;
                                    callback(list[selectedIndex!]);
                                  });
                                },
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Container(
                                          width: double.infinity,
                                          margin: const EdgeInsets.symmetric(
                                              vertical: 5, horizontal: 5),
                                          child: Padding(
                                            padding: const EdgeInsets.only(
                                                left: 8.0),
                                            child: Text(list[index],
                                                style: const TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black)),
                                          )),
                                    ),
                                    Visibility(
                                      visible: (selectedIndex != null &&
                                              selectedIndex == index)
                                          ? true
                                          : false,
                                      child: Container(
                                        width: 100,
                                        height: 25,
                                        child: Icon(
                                          Icons.check,
                                          color: Theme.of(context)
                                              .primaryColorDark,
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              );
                            },
                            itemCount: list.length,
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            width: double.infinity,
                            margin: const EdgeInsets.only(
                                left: 8, right: 8, top: 15, bottom: 8),
                            child: Center(
                              child: Padding(
                                padding: EdgeInsets.all(10.0),
                                child: Text(
                                  AppLocalizations.of(context)!.done,
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            ),
                            color: Theme.of(context).primaryColorDark,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              );
            },
          ));
}
