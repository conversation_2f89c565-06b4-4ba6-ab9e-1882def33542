import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_otp_text_field/flutter_otp_text_field.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:logger/Logger.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/pages/login/login_state/login_state.dart';
import 'package:rounds/pages/widgets/custom_text_widget.dart';
import 'package:rounds/providers/register_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/app_extensions.dart';
import 'package:rounds/widgets/custom_icon_button.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../../models/app_url.dart';
import '../../../providers/screen_state_management.dart';
import '../../../utils/constants.dart';
import 'domain_card.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';

const Color primaryColor = Colors.white;
const Color accentPurpleColor = Color(0xFF6A53A1);
const Color accentPinkColor = Color(0xFFF99BBD);
const Color accentDarkGreenColor = Color(0xFF115C49);
const Color accentYellowColor = Color(0xFFFFB612);
const Color accentOrangeColor = Color(0xFFEA7A3B);

class OtpVerificationCard extends ConsumerStatefulWidget {
  const OtpVerificationCard({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _OtpVerificationCardState();
}

class _OtpVerificationCardState extends ConsumerState<OtpVerificationCard> {
  late List<TextStyle?> otpTextStyles;
  TextEditingController otpController = TextEditingController();
  String currentOtp = "";
  String errorMsg = '';

  @override
  void initState() {
    super.initState();

    otpTextStyles = List.generate(6, (_) => createStyle(primaryColor));

    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: AppColors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final email = ref.watch(registerEmailProvider.notifier).state;

    return WillPopScope(
      onWillPop: () async {
        UIHelper.showConfirmationDialogWithYesOrNo(context,
            description: AppLocalizations.of(context)!
                .want_to_go_back_register_screen, yes: () {
          Navigator.pop(context);
          ref
              .read(loginStateProvider.notifier)
              .setLoginState(LoginState.register);
        }, no: () {
          Navigator.pop(context);
        });

        return false;
      },
      child: Column(
        children: [
          CustomHeadingText(
            text: context.locale.register_otp_heading,
          ),
          const SizedBox(
            height: 10,
          ),
          CustomSubTitleText(
            text: '${context.locale.register_otp_subtitle} "$email".',
          ),
          const SizedBox(
            height: 80,
          ),
          OtpTextField(
            margin: const EdgeInsets.only(right: 10.0),
            numberOfFields: 6,
            borderColor: accentPurpleColor,
            focusedBorderColor: accentPurpleColor,
            styles: otpTextStyles,
            showFieldAsBox: false,
            fieldHeight: 40,
            fieldWidth: 40,
            borderWidth: 2.0,
            onCodeChanged: (String code) {
              currentOtp = code;
              otpController.text = code;
            },
            onSubmit: (String verificationCode) {
              currentOtp = verificationCode;
              otpController.text = verificationCode;
            }, // end onSubmit
            cursorColor: Colors.black,
          ),
          const SizedBox(
            height: 30,
          ),
          errorMsg != ''
              ? Align(
                  alignment: Alignment.center,
                  child: Text(
                    errorMsg,
                    style: const TextStyle(color: Colors.redAccent),
                  ))
              : SizedBox(),
          Row(
            children: [
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: () {
                    onResendCode();
                  },
                  child: Text(
                    context.locale.resendCode,
                    style: TextStyle(color: AppColors.primaryColor),
                  ),
                ),
              ),
              const Spacer(),
              Align(
                alignment: Alignment.centerRight,
                child: _submitOtpButton(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  _submitOtpButton() {
    return CustomIconButton(
      ontap: () {
        onSubmit();
      },
    );
  }

  TextStyle? createStyle(Color color) {
    return TextStyle(color: AppColors.black, fontSize: 16);
  }

  void onSubmit() async {
    final domainProviderr = ref.watch(domainProvider.notifier).state;
    String domainName = domainProviderr;
    String baseUrl = '${baseURL}createdomain';
    url = baseUrl.replaceAll(domain, domainName);
    Uri finalUrl = Uri.parse(url);
    try {
      if (!(await URLService.isInternetConnected())) {
        errorMsg = AppLocalizations.of(context)!.noInternetConnectivityString;
      } else {
        var headers = {'Content-Type': 'application/x-www-form-urlencoded'};
        if (selectedAppUrl != null) {
          if (selectedAppUrl!.url.isEmpty) {
            selectedAppUrl!.url = await getDomainDataAndUrl('selectedAppUrl') ??
                selectedAppUrl!.url;
          }
        }

        var request = http.Request('POST',
            Uri.parse('${selectedAppUrl!.url}/onboarding/createdomain'));
        if (domain.isEmpty) {
          domain = await getDomainDataAndUrl('domain') ?? domain;
        }
        request.bodyFields = {
          'domain': domain,
          'app': Constants.APP_NAME,
          'otp': otpController.text,
        };
        request.headers.addAll(headers);

        http.StreamedResponse response = await request.send();

        if (response.statusCode == Status.httpOk) {
          responseObject = jsonDecode(await response.stream.bytesToString());
          List<MapEntry<String, dynamic>> entries =
              responseObject!.entries.toList();
          String message = entries[1].value;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: AppColors.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
              behavior: SnackBarBehavior.floating,
              margin: EdgeInsets.all(20),
              duration: Duration(seconds: 30),
            ),
          );
          String? domainData = await getDomainDataAndUrl('domain');
          await _initURLs();
          await ScreenStateManager.saveLastScreen("login");
          ref
              .read(loginStateProvider.notifier)
              .setLoginState(LoginState.domain);
        } else if (response.statusCode == Status.httpUnauthorized) {
          responseObject = jsonDecode(await response.stream.bytesToString());
          List<MapEntry<String, dynamic>> entries =
              responseObject!.entries.toList();
          String message = entries[1].value;
          setState(() {
            errorMsg = message;
          });

          ref
              .read(loginStateProvider.notifier)
              .setLoginState(LoginState.verify);
        } else if (response.statusCode == Status.httpBadRequest) {
          responseObject = jsonDecode(await response.stream.bytesToString());
          List<MapEntry<String, dynamic>> entries =
              responseObject!.entries.toList();
          setState(() {
            errorMsg = entries[1].value;
          });

          ref
              .read(loginStateProvider.notifier)
              .setLoginState(LoginState.verify);
        } else if (response.statusCode == Status.httpInternalServerError) {
          responseObject = jsonDecode(await response.stream.bytesToString());
          List<MapEntry<String, dynamic>> entries =
              responseObject!.entries.toList();
          String message = entries[1].value;
          setState(() {
            errorMsg = message;
          });

          ref
              .read(loginStateProvider.notifier)
              .setLoginState(LoginState.verify);
        } else {
          responseObject = jsonDecode(await response.stream.bytesToString());
          List<MapEntry<String, dynamic>> entries =
              responseObject!.entries.toList();
          String message = entries[1].value;
          setState(() {
            errorMsg = message;
          });

          ref
              .read(loginStateProvider.notifier)
              .setLoginState(LoginState.verify);
        }
      }
    } catch (e) {
      Logger.logError('OtpVerificationCard', 'onSubmit', e.toString());
    }
  }

  void onResendCode() async {}
  Future<List<AppUrl>> _initURLs({Map<String, dynamic>? urlData}) async {
    urlList = [];

    if (!kIsWeb) {
      if (urlData == null) {
        final contents = await rootBundle.loadString(
          Constants.APP_CONFIG_PATH,
        );

        final Map<String, dynamic> config = jsonDecode(contents);
        if (config.containsKey('urls')) {
          urlList = List<dynamic>.from(config['urls'])
              .map((e) => AppUrl.fromMap(e))
              .toList();
        }
      } else {
        if (urlData.containsKey('data')) {
          urlList = List<dynamic>.from(urlData['data'])
              .map((e) => AppUrl.fromMap(e))
              .toList();
        }
      }

      selectedAppUrl = await urlList.firstWhere((element) => element.isDefault);
      return urlList;
    } else {
      final contents = await rootBundle.loadString(
        Constants.WEB_APP_CONFIG_PATH,
      );
      final Map<String, dynamic> config = jsonDecode(contents);

      selectedAppUrl = config.containsKey('umpUrl')
          ? AppUrl(url: config['umpUrl'], isDefault: true)
          : AppUrl(url: 'https://umpdev.unvired.io', isDefault: true);
      return [];
    }
  }
}
