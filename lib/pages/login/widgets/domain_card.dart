import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:rounds/models/app_url.dart';
import 'package:rounds/pages/login/login_state/login_state.dart';
import 'package:rounds/pages/widgets/custom_text_widget.dart';
import 'package:rounds/providers/register_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/app_extensions.dart';
import 'package:rounds/utils/constants.dart';
import 'package:rounds/utils/utils.dart';
import 'package:rounds/widgets/custom_icon_button.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:rounds/widgets/inputs.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../../providers/screen_state_management.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';
import 'package:rounds/helpers/url_detector/url_detector_mobile.dart'
    if (dart.library.html) 'package:rounds/helpers/url_detector/url_detector_web.dart';

const urlKey = '@URL@';
const domainKey = '@DOMAIN@';
const liveURL = 'https://live.unvired.io';
String url = 'https://umpdev.unvired.io';
String url1 = 'https://api.easyrounds.com';
String url2 = 'https://api.sbox.easyrounds.com';

String baseURL = '$url/onboarding/';
String baseURL1 = '$url1/onboarding/';
String baseURL2 = '$url2/onboarding/';

LoginType loginType = LoginType.unvired;
AppUrl? selectedAppUrl;
String port = '';
String domain = '';
List<AppUrl> urlList = [];
TextEditingController domainController = TextEditingController();
Map<String, dynamic>? responseObject;

class DomainCard extends ConsumerStatefulWidget {
  const DomainCard({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _DomainScreenState();
}

class _DomainScreenState extends ConsumerState<DomainCard> {
  bool isLoading = false;
  String errorMsg = '';
  late FocusNode _focusNode;
  late FocusScopeNode _continueFocusNode;

  @override
  void initState() {
    super.initState();
    domainController.text = '';
    ref.read(loginStateProvider.notifier);
    _loadUrlInfo();
    _focusNode = FocusNode();
    _continueFocusNode = FocusScopeNode();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _continueFocusNode.dispose();
    super.dispose();
  }

  void _loadUrlInfo() {
    final urlDetector = getUrlDetector(); // Get the correct implementation

    if (kIsWeb) {
      String? domain = Utils.findDomainFromUrl(urlDetector.getCurrentUrl());

      if (domain != null) {
        if (domain == "localhost") {
          domainController.text = domain ?? "unvired";
          _getDetailsByDomain("unvired");
        } else {
          domainController.text = domain;
          _getDetailsByDomain(domain);
        }
      }
    } else {}
  }

  @override
  Widget build(BuildContext context) {
    WidgetsFlutterBinding.ensureInitialized();
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: AppColors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomHeadingText(
            text: context.locale.domain_heading,
          ),
          const SizedBox(
            height: 10,
          ),
          CustomSubTitleText(
            text: context.locale.domain_subtitle,
          ),
          const SizedBox(
            height: 50,
          ),
          _domainTextBox(),
          _getErrorMsgString(),
          SizedBox(
            height: (errorMsg == '') ? 0.0 : 15.0,
          ),
          Row(
            children: [
              Align(
                alignment: Alignment.bottomLeft,
                child: TextButton(
                  onPressed: () {
                    onRegisterButton();
                  },
                  child: Text(
                    context.locale.register,
                    style: const TextStyle(
                      color: Color.fromARGB(255, 74, 61, 186),
                    ),
                  ),
                ),
              ),
              const Spacer(),
              Align(
                alignment: Alignment.centerRight,
                child: _continueIconButton(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  _domainTextBox() {
    return CustomTextField(
      controller: domainController,
      labelName: context.locale.domain,
      isRequiredField: false,
      onChanged: (v) {
        TextSelection previousSelection = domainController.selection;
        domainController.text = v;
        domainController.selection = previousSelection;
      },
      onFieldSubmitted: (v) {
        FocusScope.of(context).unfocus();
        _handleSubmit();
      },
      hintText: '',
      height: 50,
      maxLines: 1,
      autoFocus: true,
      focusNode: _focusNode,
    );
  }

  Widget _continueIconButton() {
    return CustomIconButton(
      focusNode: _continueFocusNode,
      isLoading: isLoading,
      ontap: isLoading ? () {} : _handleSubmit,
    );
  }

  _handleSubmit() {
    errorMsg = '';
    if (domainController.text.isEmpty) {
      setState(() {
        errorMsg = AppLocalizations.of(context)!.domain_name_cannot_be_empty;
      });
    } else {
      _getDetailsByDomain(domainController.text);
    }
  }

  Future<void> _saveAndSetDomain(
      String validDomain, String urlToSave, AppUrl appUrl) async {
    await saveDomainDataAndUrl('domain', validDomain);
    String? domainData = await getDomainDataAndUrl('domain');
    if (domainData != null) {
      ref.read(domainProvider.notifier).setDomain(domainData);
      await UrlStateManager.saveUrl(urlToSave);
      selectedAppUrl = appUrl;
      ref.read(loginStateProvider.notifier).setLoginState(LoginState.login);
    }
  }

  void _getDetailsByDomain(text) async {
    setState(() {
      isLoading = true;
      errorMsg = '';
    });
    domain = text;
    String baseUrl1 = '${baseURL1}validatedomain?domain=$domain';
    String baseUrl2 = '${baseURL2}validatedomain?domain=$domain';
    String baseUrl = '${baseURL}validatedomain?domain=$domain';
    try {
      if (!(await URLService.isInternetConnected())) {
        setState(() {
          errorMsg = AppLocalizations.of(context)!.noInternetConnectivityString;
        });
        return;
      }
      if (!kIsWeb) {
        if (true) {
          // Only validate against umpdev.unvired.io in debug mode
          final contents =
              await rootBundle.loadString(Constants.APP_CONFIG_PATH);
          final Map<String, dynamic> config = jsonDecode(contents);
          AppUrl? foundUrl;
          if (config.containsKey('urls') && config['urls'] is List) {
            List<dynamic> urlsList = config['urls'];
            for (var urlEntry in urlsList) {
              if (urlEntry is Map<String, dynamic>) {
                String? umpUrlValue = urlEntry['ump_url'] as String?;
                // Assuming the key for default is 'is_default' and it's a boolean
                bool isDefault = urlEntry['use_as_default'] == true;

                if (umpUrlValue != null &&
                    umpUrlValue.isNotEmpty &&
                    isDefault) {
                  foundUrl = AppUrl(url: umpUrlValue, isDefault: true);
                  break;
                }
              }
            }
          }
          selectedAppUrl = foundUrl ??
              AppUrl(url: 'https://umpdev.unvired.io/UMP', isDefault: true);
          url = selectedAppUrl!.url;
          // baseURL = '$url/onboarding/';
          http.Response response = await http.get(Uri.parse(baseUrl));
          if (response.statusCode == Status.httpBadRequest) {
            Map<String, dynamic> data = jsonDecode(response.body);
            if (data["result"] == false) {
              await _saveAndSetDomain(domain, url, AppUrl(url: url));
              return;
            }
          }
          // If not valid, go to registration
          ref
              .read(loginStateProvider.notifier)
              .setLoginState(LoginState.register);
          return;
        }
        // Production: stepwise validation
        // 1. Try api.easyrounds.com
        http.Response response = await http.get(Uri.parse(baseUrl1));
        if (response.statusCode == Status.httpBadRequest) {
          Map<String, dynamic> data = jsonDecode(response.body);
          if (data["result"] == false) {
            await _saveAndSetDomain(domain, baseUrl1, AppUrl(url: url1));
            return;
          }
        }
        // 2. Try api.sbox.easyrounds.com
        response = await http.get(Uri.parse(baseUrl2));
        if (response.statusCode == Status.httpBadRequest) {
          Map<String, dynamic> data = jsonDecode(response.body);
          if (data["result"] == false) {
            await _saveAndSetDomain(domain, baseUrl2, AppUrl(url: url2));
            return;
          }
        }
        // // 3. Try umpdev.unvired.io
        // response = await http.get(Uri.parse(baseUrl));
        // if (response.statusCode == Status.httpBadRequest) {
        //   Map<String, dynamic> data = jsonDecode(response.body);
        //   if (data["result"] == false) {
        //     await _saveAndSetDomain(domain, baseUrl, AppUrl(url: url));
        //     return;
        //   }
        // }
        // 4. If all fail, go to registration screen
        ref
            .read(loginStateProvider.notifier)
            .setLoginState(LoginState.register);
        return;
      } else {
        // Web logic remains unchanged
        final contents =
            await rootBundle.loadString(Constants.WEB_APP_CONFIG_PATH);
        final Map<String, dynamic> config = jsonDecode(contents);
        AppUrl? foundUrl;
        if (config.containsKey('urls') && config['urls'] is List) {
          List<dynamic> urlsList = config['urls'];
          for (var urlEntry in urlsList) {
            if (urlEntry is Map<String, dynamic>) {
              String? umpUrlValue = urlEntry['ump_url'] as String?;
              // Assuming the key for default is 'is_default' and it's a boolean
              bool isDefault = urlEntry['use_as_default'] == true;

              if (umpUrlValue != null && umpUrlValue.isNotEmpty && isDefault) {
                foundUrl = AppUrl(url: umpUrlValue, isDefault: true);
                break;
              }
            }
          }
        }
        selectedAppUrl = foundUrl ??
            AppUrl(url: 'https://umpdev.unvired.io/UMP', isDefault: true);
        url = selectedAppUrl!.url;
        // baseURL = '$url/onboarding/';
        http.Response response = await http.get(Uri.parse(baseUrl));
        if (response.statusCode == Status.httpBadRequest) {
          // String? validDomain = _extractValidDomain(jsonDecode(response.body));
          String? validDomain = domainController.text;
          if (validDomain != null) {
            await _saveAndSetDomain(
                validDomain, selectedAppUrl!.url, AppUrl(url: url));
          }
          return;
        }
        setState(() {
          errorMsg = context.locale.invalid_domain;
        });
        return;
      }
    } catch (e) {
      setState(() {
        errorMsg = context.locale.invalid_domain;
      });
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  void onRegisterButton() async {
    ref.read(registerDomainProvider.notifier).clearRegisterDomain();
    ref.read(registerFullNameProvider.notifier).clearRegisterFullName();
    ref.read(registerEmailProvider.notifier).clearRegisterEmail();
    ref.read(registerPasswordProvider.notifier).clearRegisterPassword();
    ref
        .read(registerConfirmPasswordProvider.notifier)
        .clearRegisterConfirmPassword();
    ref.read(registerPhoneProvider.notifier).clearRegisterPhone();
    ref.read(loginStateProvider.notifier).setLoginState(LoginState.register);
  }

  _getErrorMsgString() {
    return errorMsg == ''
        ? const SizedBox.shrink()
        : Container(
            margin: EdgeInsets.only(top: isLoading ? 0.0 : 10.0),
            padding: const EdgeInsets.only(left: 5.0),
            width: double.infinity,
            child: Text(
              errorMsg,
              style: const TextStyle(color: Colors.redAccent),
            ),
          );
  }
}
