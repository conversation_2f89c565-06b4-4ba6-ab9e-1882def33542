import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/models/app_url.dart';
import 'package:rounds/pages/login/widgets/domain_card.dart';
import 'package:rounds/pages/login/widgets/server_option_dialog.dart';
import 'package:rounds/pages/login/widgets/user_error_alert.dart';
import 'package:rounds/pages/widgets/custom_text_widget.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/widgets/inputs.dart';
import 'package:rounds/utils/app_extensions.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

class LoginCard extends ConsumerStatefulWidget {
  bool selectFromMultipleAccounts;
  List<UnviredAccount> accountList;
  UnviredAccount? selectedAccount;
  String error;

  LoginCard(
      {Key? key,
      required this.selectFromMultipleAccounts,
      required this.accountList,
      required this.error,
      this.selectedAccount});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _LoginCardState();
}

class _LoginCardState extends ConsumerState<LoginCard> {
  TextEditingController userNameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();

  bool isLoading = false;

  String userNameError = '';
  String passwordError = '';
  String commonError = '';

  @override
  void initState() {
    if (widget.error.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        commonError = widget.error;

        if (widget.selectedAccount != null) {
          userNameController.text = widget.selectedAccount!.getUserName();
          if (widget.selectedAccount!.getAvailableFrontendIds().length > 1) {
            Future.delayed(const Duration(seconds: 1), () {
              showMultipleFrontEndIdDialog(
                  context: context,
                  title: context.locale.selectFrontEndId,
                  list: widget.selectedAccount!.getAvailableFrontendIds(),
                  callback: _onFrontEndIdSelected);
            });
          }
        }
      });
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    WidgetsFlutterBinding.ensureInitialized();
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: AppColors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
    return _getLoginCard();
  }

  void _onFrontEndIdSelected(String frontEndId) {
    widget.selectedAccount!.setFrontendId(frontEndId);
  }

  _getLoginCard() {
    return Column(
      children: [
        const SizedBox(
          height: 80,
        ),
        CustomHeadingText(
          text: context.locale.domain_heading,
        ),
        const SizedBox(
          height: 10,
        ),
        CustomSubTitleText(
          text: context.locale.domain_subtitle,
        ),
        const SizedBox(
          height: 50,
        ),
        _getUsernameTextBox(),
        _getErrorMsgString(userNameError),
        const SizedBox(height: 24),
        _getPasswordTextBox(),
        _getErrorMsgString(passwordError),
        _getErrorMsgString(commonError),
        const SizedBox(height: 24),
        // _getLoginButton(),
        Align(
            alignment: Alignment.centerRight,
            child: TextButton(
                onPressed: () {
                  showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (BuildContext context) {
                        return ServerDialogBox(
                          title: context.locale.selectServer,
                          selectedUrl: selectedAppUrl,
                          serverList: urlList,
                          onUrlChanged: (AppUrl appUrl) {
                            selectedAppUrl = appUrl;
                            //
                          },
                        );
                      });
                },
                child: Text(
                  context.locale.changeServerString,
                  style:
                      const TextStyle(color: Color.fromARGB(255, 74, 61, 186)),
                )))
      ],
    );
  }

  _getUsernameTextBox() {
    return CustomTextField(
      controller: userNameController,
      labelName: context.locale.email,
      isRequiredField: false,
      hintText: '',
      height: 50,
      maxLines: 1,
    );
  }

  _getPasswordTextBox() {
    return CustomTextField(
      controller: passwordController,
      labelName: context.locale.password,
      isRequiredField: false,
      hintText: '',
      height: 50,
      isPassword: true,
      maxLines: 1,
    );
  }

  _getErrorMsgString(String errorMsg) {
    return errorMsg == ''
        ? const SizedBox.shrink()
        : Container(
            margin: EdgeInsets.only(top: isLoading ? 0.0 : 10.0),
            padding: const EdgeInsets.only(left: 5.0),
            width: double.infinity,
            child: Text(
              errorMsg,
              style: const TextStyle(color: Colors.redAccent),
            ),
          );
  }

  LoginType stringToLoginType(String value) {
    switch (value) {
      case 'UNVIRED':
        return LoginType.unvired;
      case 'ADS':
        return LoginType.ads;
      case 'sap':
        return LoginType.sap;
      case 'email':
        return LoginType.email;
      case 'custom':
        return LoginType.custom;
      case 'saml':
        return LoginType.saml;
      case 'passwordless':
        return LoginType.passwordless;
      default:
        throw ArgumentError('Invalid LoginType: $value');
    }
  }
}
