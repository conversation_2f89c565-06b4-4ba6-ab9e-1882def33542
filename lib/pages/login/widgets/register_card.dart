import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:logger/Logger.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/pages/login/login_state/login_state.dart';
import 'package:rounds/pages/login/widgets/domain_card.dart';
import 'package:rounds/pages/widgets/custom_text_widget.dart';
import 'package:rounds/providers/register_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/app_extensions.dart';
import 'package:rounds/utils/constants.dart';
import 'package:rounds/widgets/custom_icon_button.dart';
import 'package:rounds/widgets/inputs.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../../models/app_url.dart';
import '../../../providers/screen_state_management.dart';
import '../../../widgets/phone_number_textfield.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';

class RegisterCard extends ConsumerStatefulWidget {
  const RegisterCard({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _RegisterCardState();
}

class _RegisterCardState extends ConsumerState<RegisterCard> {
  TextEditingController domainController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  TextEditingController phoneNumberController = TextEditingController();
  String errorMsg = '';
  String domainErrorMsg = '';
  String nameErrorMsg = '';
  String lastNameErrorMsg = '';
  String emailErrorMsg = '';
  String passwordErrorMsg = '';
  String confirmPasswordErrorMsg = '';
  String phoneNumberErrorMsg = '';
  bool isLoading = false;

  @override
  void initState() {
    super.initState();

    Future.microtask(() {
      final domainProvider = ref.read(registerDomainProvider);
      final fullNameProvider = ref.read(registerFullNameProvider);
      final lastNameProvider = ref.read(registerLastFullNameProvider);
      final emailProvider = ref.read(registerEmailProvider);
      final passwordProvider = ref.read(registerPasswordProvider);
      final confirmPasswordProvider = ref.read(registerConfirmPasswordProvider);
      final phoneProvider = ref.read(registerPhoneProvider);

      if (domainProvider.isNotEmpty) {
        domainController.text = domainProvider;
      }
      if (fullNameProvider.isNotEmpty) {
        nameController.text = fullNameProvider;
      }
      if (lastNameProvider.isNotEmpty) {
        lastNameController.text = fullNameProvider;
      }
      if (emailProvider.isNotEmpty) {
        emailController.text = emailProvider;
      }
      if (passwordProvider.isNotEmpty) {
        passwordController.text = passwordProvider;
      }
      if (confirmPasswordProvider.isNotEmpty) {
        confirmPasswordController.text = confirmPasswordProvider;
      }
      if (phoneProvider.isNotEmpty) {
        phoneNumberController.text = phoneProvider;
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    final domainProvider = ref.watch(registerDomainProvider);
    final fullNameProvider = ref.watch(registerFullNameProvider);
    final fullLastNameProvider = ref.watch(registerLastFullNameProvider);
    final emailProvider = ref.watch(registerEmailProvider);
    final passwordProvider = ref.watch(registerPasswordProvider);
    final confirmPasswordProvider = ref.watch(registerConfirmPasswordProvider);
    final phoneProvider = ref.watch(registerPhoneProvider);

    if (domainProvider.isNotEmpty) {
      domainController.text = domainProvider;
    }
    if (fullNameProvider.isNotEmpty) {
      nameController.text = fullNameProvider;
    }
    if (fullLastNameProvider.isNotEmpty) {
      lastNameController.text = fullNameProvider;
    }
    if (emailProvider.isNotEmpty) {
      emailController.text = emailProvider;
    }
    if (passwordProvider.isNotEmpty) {
      passwordController.text = passwordProvider;
    }
    if (confirmPasswordProvider.isNotEmpty) {
      confirmPasswordController.text = confirmPasswordProvider;
    }
    if (phoneProvider.isNotEmpty) {
      phoneNumberController.text = phoneProvider;
    }
  }

  unSavedData() {
    if (domainController.text.isNotEmpty ||
        nameController.text.isNotEmpty ||
        lastNameController.text.isNotEmpty ||
        emailController.text.isNotEmpty ||
        passwordController.text.isNotEmpty ||
        confirmPasswordController.text.isNotEmpty ||
        phoneNumberController.text.isNotEmpty) {
      return true;
    } else {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    WidgetsFlutterBinding.ensureInitialized();
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: AppColors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    return WillPopScope(
      onWillPop: () async {
        bool unSave = unSavedData();
        if (unSave) {
          UIHelper.showConfirmationDialogWithYesOrNo(
            context,
            description: AppLocalizations.of(context)!
                .do_you_want_to_discard_changes_go_back,
            yes: () {
              Navigator.pop(context);
              ref
                  .read(loginStateProvider.notifier)
                  .setLoginState(LoginState.domain);
            },
            no: () {
              Navigator.pop(context);
            },
          );
        } else {
          ref
              .read(loginStateProvider.notifier)
              .setLoginState(LoginState.domain);
        }
        return false;
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomHeadingText(
            text: context.locale.register_email_heading,
          ),
          const SizedBox(
            height: 10,
          ),
          CustomSubTitleText(
            text: context.locale.register_email_subtitle,
          ),
          const SizedBox(
            height: 50,
          ),
          _domainTextBox(),
          domainErrorMsg != ''
              ? _getErrorMsgString(domainErrorMsg)
              : Container(),
          _nameTextBox(),
          nameErrorMsg != '' ? _getErrorMsgString(nameErrorMsg) : Container(),
          _lastNameTextBox(),
          lastNameErrorMsg != ''
              ? _getErrorMsgString(lastNameErrorMsg)
              : Container(),
          _emailTextBox(),
          emailErrorMsg != '' ? _getErrorMsgString(emailErrorMsg) : Container(),
          _passwordTextBox(),
          passwordErrorMsg != ''
              ? _getErrorMsgString(passwordErrorMsg)
              : Container(),
          _confirmPasswordTextBox(),
          confirmPasswordErrorMsg != ''
              ? _getErrorMsgString(confirmPasswordErrorMsg)
              : Container(),
          _phoneNumberTextBox(),
          phoneNumberErrorMsg != ''
              ? _getErrorMsgString(phoneNumberErrorMsg)
              : Container(),
          const SizedBox(
            height: 15,
          ),
          errorMsg != ''
              ? Align(
                  alignment: Alignment.center,
                  child: Text(
                    errorMsg,
                    style: const TextStyle(color: Colors.redAccent),
                  ))
              : SizedBox(),
          Row(
            children: [
              const Spacer(),
              Align(
                alignment: Alignment.centerRight,
                child: _getOtpButton(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  _domainTextBox() {
    return CustomTextField(
      controller: domainController,
      labelName: context.locale.domain,
      onChanged: (v) {
        final filteredText = v.replaceAll(RegExp(r'[^a-zA-Z0-9_]'), '');
        if (filteredText != v) {
          final previousSelection = domainController.selection;
          domainController.value = TextEditingValue(
            text: filteredText,
            selection: previousSelection.copyWith(
              baseOffset: previousSelection.baseOffset - 1,
              extentOffset: previousSelection.extentOffset - 1,
            ),
          );
        }
        ref
            .read(registerDomainProvider.notifier)
            .getRegisterDomain(filteredText);
        if (domainController.text.isNotEmpty) {
          setState(() {
            domainErrorMsg = '';
          });
        }
      },
      isRequiredField: true,
      hintText: '',
      height: 50,
      maxLines: 1,
      autoFocus: true,
    );
  }

  _nameTextBox() {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0),
      child: CustomTextField(
        controller: nameController,
        labelName: context.locale.name,
        onChanged: (v) {
          final filteredText = v.replaceAll(RegExp(r'[^a-zA-Z0-9_ ]'), '');
          if (filteredText != v) {
            final previousSelection = nameController.selection;
            nameController.value = TextEditingValue(
              text: filteredText,
              selection: previousSelection.copyWith(
                baseOffset: previousSelection.baseOffset - 1,
                extentOffset: previousSelection.extentOffset - 1,
              ),
            );
          }
          ref
              .read(registerFullNameProvider.notifier)
              .getRegisterFullName(filteredText);
          if (nameController.text.isNotEmpty) {
            setState(() {
              nameErrorMsg = '';
            });
          }
        },
        isRequiredField: true,
        hintText: '',
        height: 50,
        maxLines: 1,
        autoFocus: true,
      ),
    );
  }

  _lastNameTextBox() {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0),
      child: CustomTextField(
        controller: lastNameController,
        labelName: context.locale.last_name,
        onChanged: (v) {
          final filteredText = v.replaceAll(RegExp(r'[^a-zA-Z0-9_ ]'), '');
          if (filteredText != v) {
            final previousSelection = lastNameController.selection;
            lastNameController.value = TextEditingValue(
              text: filteredText,
              selection: previousSelection.copyWith(
                baseOffset: previousSelection.baseOffset - 1,
                extentOffset: previousSelection.extentOffset - 1,
              ),
            );
          }
          ref
              .read(registerFullNameProvider.notifier)
              .getRegisterFullName(filteredText);
          if (lastNameController.text.isNotEmpty) {
            setState(() {
              lastNameErrorMsg = '';
            });
          }
        },
        isRequiredField: true,
        hintText: '',
        height: 50,
        maxLines: 1,
        autoFocus: true,
      ),
    );
  }

  _emailTextBox() {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0),
      child: CustomTextField(
        controller: emailController,
        labelName: context.locale.email,
        onChanged: (v) {
          final cursorPosition = emailController.selection.baseOffset;

          List<String> parts = v.split('@');

          String beforeAt = parts[0]
              .replaceAll(RegExp(r"[^a-zA-Z0-9.!_\-#$&%',*+=/?^|{}~]"), '');

          String afterAt = parts.length > 1
              ? parts[1].replaceAll(RegExp(r"[^a-zA-Z0-9.-]"),
                  '') // Only letters, numbers, - and .
              : '';
          String filteredText =
              beforeAt + (parts.length > 1 ? '@' + afterAt : '');

          emailController.value = TextEditingValue(
            text: filteredText,
            selection: TextSelection.collapsed(
              offset: cursorPosition > filteredText.length
                  ? filteredText.length
                  : cursorPosition,
            ),
          );
          ref
              .read(registerEmailProvider.notifier)
              .getRegisterEmail(filteredText);

          if (emailController.text.isNotEmpty) {
            setState(() {
              emailErrorMsg = isValidEmail(emailController.text)
                  ? ''
                  : AppLocalizations.of(context)!.invalid_email_format;
            });
          }
        },
        isRequiredField: true,
        hintText: '',
        height: 50,
        maxLines: 1,
        autoFocus: true,
      ),
    );
  }

  _passwordTextBox() {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0),
      child: CustomTextField(
        controller: passwordController,
        labelName: context.locale.password,
        onChanged: (v) {
          final cursorPosition = passwordController.selection.baseOffset;

          String filteredText =
              v.replaceAll(RegExp(r"[^A-Za-z\d!@#\$%^&*()-_=+<>?]"), '');

          passwordController.value = TextEditingValue(
            text: filteredText,
            selection: TextSelection.collapsed(
              offset: cursorPosition > filteredText.length
                  ? filteredText.length
                  : cursorPosition,
            ),
          );
          ref
              .read(registerPasswordProvider.notifier)
              .getRegisterPassword(passwordController.text);
          if (passwordController.text.isNotEmpty) {
            setState(() {
              passwordErrorMsg = isValidPassword(passwordController.text)
                  ? ''
                  : AppLocalizations.of(context)!.password_validation;
            });
          }
        },
        isRequiredField: true,
        hintText: '',
        height: 50,
        maxLines: 1,
        autoFocus: true,
        isPassword: true,
      ),
    );
  }

  _confirmPasswordTextBox() {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0),
      child: CustomTextField(
        controller: confirmPasswordController,
        labelName: context.locale.confirm_password,
        onChanged: (v) {
          final cursorPosition = confirmPasswordController.selection.baseOffset;

          String filteredText =
              v.replaceAll(RegExp(r"[^A-Za-z\d!@#\$%^&*()-_=+<>?]"), '');

          confirmPasswordController.value = TextEditingValue(
            text: filteredText,
            selection: TextSelection.collapsed(
              offset: cursorPosition > filteredText.length
                  ? filteredText.length
                  : cursorPosition,
            ),
          );

          ref
              .read(registerConfirmPasswordProvider.notifier)
              .getRegisterConfirmPassword(confirmPasswordController.text);
          if (confirmPasswordController.text.isNotEmpty) {
            setState(() {
              confirmPasswordErrorMsg =
                  isValidPassword(confirmPasswordController.text)
                      ? ''
                      : AppLocalizations.of(context)!.password_validation;
            });
          }
        },
        isRequiredField: true,
        hintText: '',
        height: 50,
        maxLines: 1,
        autoFocus: true,
        isPassword: true,
      ),
    );
  }

  _phoneNumberTextBox() {
    return Padding(
        padding: const EdgeInsets.only(top: 15.0),
        child: PhoneNumberTextField(
          controller: phoneNumberController,
        ));
  }

  _getOtpButton() {
    return CustomIconButton(
      ontap: () {
        if (validate()) {
        } else {
          onSignUp();
        }
      },
    );
  }

  _getErrorMsgString(String errorMsg) {
    return errorMsg == ''
        ? const SizedBox.shrink()
        : Container(
            margin: EdgeInsets.only(top: isLoading ? 0.0 : 3.0),
            padding: const EdgeInsets.only(left: 5.0),
            width: double.infinity,
            child: Text(
              errorMsg,
              style: const TextStyle(color: Colors.redAccent),
            ),
          );
  }

  bool validate() {
    if (domainController.text.isEmpty) {
      setState(() {
        domainErrorMsg = AppLocalizations.of(context)!.please_enter_domain;
      });
      return true;
    } else if (nameController.text.isEmpty) {
      setState(() {
        nameErrorMsg = AppLocalizations.of(context)!.please_enter_name;
      });
      return true;
    } else if (lastNameController.text.isEmpty) {
      setState(() {
        lastNameErrorMsg = AppLocalizations.of(context)!.please_enter_last_name;
      });
      return true;
    } else if (emailController.text.isEmpty) {
      setState(() {
        emailErrorMsg = AppLocalizations.of(context)!.please_enter_email;
      });
      return true;
    } else if (passwordController.text.isEmpty) {
      setState(() {
        passwordErrorMsg = AppLocalizations.of(context)!.please_enter_pasword;
      });
      return true;
    } else if (!isValidPassword(passwordController.text)) {
      setState(() {
        passwordErrorMsg = AppLocalizations.of(context)!.password_validation;
      });
      return true;
    } else if (confirmPasswordController.text.isEmpty) {
      setState(() {
        confirmPasswordErrorMsg =
            AppLocalizations.of(context)!.please_enter_confirm_password;
      });
      return true;
    } else if (passwordController.text != confirmPasswordController.text) {
      setState(() {
        confirmPasswordErrorMsg =
            AppLocalizations.of(context)!.password_doesnot_match;
      });
      return true;
    }
    return false;
  }

  bool isValidEmail(String email) {
    final RegExp emailRegex = RegExp(
        r"^(?!.*@.*@)[a-zA-Z0-9.!_\-#$&%',*+=/?^|{}~]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");
    return emailRegex.hasMatch(email);
  }

  bool isValidPassword(String password) {
    final RegExp passwordRegex = RegExp(
        r'^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#\$%^&*()-_=+<>?])[A-Za-z\d!@#\$%^&*()-_=+<>?]{8,}$');
    return passwordRegex.hasMatch(password);
  }

  Future<void> onSignUp() async {
    final domainProviderr = ref.watch(domainProvider.notifier);
    domainProviderr.setDomain(domainController.text);
    setState(() {
      isLoading = true;
    });
    String domainName = domainProviderr.state;

/*    List<String> nameParts =
        nameController.text.split(RegExp(r'\s+'));

    String firstName = nameParts.first;
    String lastName = nameParts.sublist(1).join(' ');*/

    String firstName = nameController.text;
    String lastName = lastNameController.text;

    String baseUrl = '${baseURL}registerdomain';
    if (domain.isEmpty) {
      if (domainName.isNotEmpty) {
        domain = domainName;
      } else {
        domain = await getDomainDataAndUrl('domain') ?? domain;
      }
    }
    url = baseUrl.replaceAll(domain, domainName);

    Uri finalUrl = Uri.parse(url);

    if (selectedAppUrl != null) {
      if (selectedAppUrl!.url.isEmpty) {
        selectedAppUrl?.url =
            await getDomainDataAndUrl('selectedAppUrl') ?? domain;
      }
    }

    try {
      if (!(await URLService.isInternetConnected())) {
        errorMsg = AppLocalizations.of(context)!.noInternetConnectivityString;
      } else {
        var headers = {'Content-Type': 'application/x-www-form-urlencoded'};
        var request = http.Request('POST',
            Uri.parse('${selectedAppUrl!.url}/onboarding/registerdomain'));
        request.bodyFields = {
          'domain': domain,
          'app': Constants.APP_NAME,
          'firstname': firstName,
          'lastname': lastName,
          'email': emailController.text,
          'password': passwordController.text,
          'phone': phoneNumberController.text,
        };
        request.headers.addAll(headers);

        http.StreamedResponse response = await request.send();

        if (response.statusCode == Status.httpOk) {
          await saveDomainDataAndUrl('domain', domain);
          selectedAppUrl = AppUrl(url: url2);
          responseObject = jsonDecode(await response.stream.bytesToString());
          List<MapEntry<String, dynamic>> entries =
              responseObject!.entries.toList();
          String otpMessage = entries[1].value;

          Logger.logInfo('Register Card', 'onSignUp', '${otpMessage}');
          await ScreenStateManager.saveLastScreen("otp");
          ref
              .read(loginStateProvider.notifier)
              .setLoginState(LoginState.verify);
        } else if (response.statusCode == Status.httpBadRequest) {
          responseObject = jsonDecode(await response.stream.bytesToString());
          List<MapEntry<String, dynamic>> entries =
              responseObject!.entries.toList();
          if (errorMsg == "Invalid domain") {
            setState(() {
              domainErrorMsg = entries[1].value;
            });
            ref
                .read(loginStateProvider.notifier)
                .setLoginState(LoginState.register);
          } else {
            setState(() {
              errorMsg = entries[1].value;
            });
            ref
                .read(loginStateProvider.notifier)
                .setLoginState(LoginState.register);
          }
        } else if (response.statusCode == Status.httpInternalServerError) {
          responseObject = jsonDecode(await response.stream.bytesToString());
          List<MapEntry<String, dynamic>> entries =
              responseObject!.entries.toList();
          String message = entries[1].value;
          setState(() {
            errorMsg = message;
          });
          if (errorMsg == "Invalid domain") {
            ref
                .read(loginStateProvider.notifier)
                .setLoginState(LoginState.domain);
          } else {
            ref
                .read(loginStateProvider.notifier)
                .setLoginState(LoginState.register);
          }
        } else {
          responseObject = jsonDecode(await response.stream.bytesToString());
          List<MapEntry<String, dynamic>> entries =
              responseObject!.entries.toList();
          String message = entries[1].value;
          setState(() {
            errorMsg = message;
          });
          if (errorMsg == "Invalid domain") {
            ref
                .read(loginStateProvider.notifier)
                .setLoginState(LoginState.domain);
          } else {
            ref
                .read(loginStateProvider.notifier)
                .setLoginState(LoginState.register);
          }
        }
      }
    } catch (e) {
      Logger.logError('RegisterCard', 'onSignUp', e.toString());
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }
}
