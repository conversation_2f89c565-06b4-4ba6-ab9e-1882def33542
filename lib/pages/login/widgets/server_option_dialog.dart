import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:rounds/icons/app_icons.dart';
import 'package:rounds/models/app_url.dart';
import 'package:rounds/utils/app_styles.dart';
import 'package:rounds/utils/responsive.dart';
import 'package:rounds/utils/screen_util.dart';

class ServerDialogBox extends StatefulWidget {
  final String _title;
  final AppUrl _selectedUrl;
  final List<AppUrl> _serverList;
  final Function(AppUrl appUrl) _onUrlChanged;

  const ServerDialogBox(
      {Key? key,
      required title,
      required selectedUrl,
      required serverList,
      img,
      required onUrlChanged})
      : _title = title,
        _selectedUrl = selectedUrl,
        _serverList = serverList,
        _onUrlChanged = onUrlChanged,
        super(key: key);

  @override
  _ServerDialogBoxState createState() => _ServerDialogBoxState();
}

class _ServerDialogBoxState extends State<ServerDialogBox> {
  static const double padding = 20;
  late AppUrl _selectedUrl = widget._selectedUrl;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(padding),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: contentBox(context),
    );
  }

  contentBox(context) {
    return Container(
      width: ResponsiveWidget.isWebView(context)
          ? 400
          : MediaQuery.of(context).size.width,
      padding: EdgeInsets.only(
          left: padding, top: padding, right: padding, bottom: padding),
      decoration: BoxDecoration(
          shape: BoxShape.rectangle,
          color: Colors.white,
          borderRadius: BorderRadius.circular(padding),
          boxShadow: [
            BoxShadow(
                color: Colors.black, offset: Offset(0, 10), blurRadius: 10),
          ]),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            widget._title,
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          SizedBox(
            height: 16,
          ),
          Divider(
            color: HexColor("#D5DADD"),
          ),
          ListView.builder(
            shrinkWrap: true,
            itemCount: widget._serverList.length,
            itemBuilder: (context, index) {
              return InkWell(
                onTap: () {
                  setState(() {
                    _selectedUrl = widget._serverList[index];
                  });
                },
                child: ListTile(
                    contentPadding: EdgeInsets.zero,
                    visualDensity: VisualDensity(horizontal: 0, vertical: -4),
                    leading: _selectedUrl.url == widget._serverList[index].url
                        ? EamIcon(
                                iconName: EamIcon.radio_checked,
                                color: HexColor('#3B7DDD'),
                                height: 18)
                            .icon()
                        : EamIcon(
                                iconName: EamIcon.radio_unchecked,
                                color: HexColor('#D2D2D2'),
                                height: 18)
                            .icon(),
                    title: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget._serverList[index].name,
                          style: ScreenUtils.getValueTextStyle(),
                        ),
                        Text(
                          widget._serverList[index].url,
                          style: ScreenUtils.getValueLinkTextStyle(),
                        )
                      ],
                    )),
              );
            },
          ),
          SizedBox(
            height: 32,
          ),
          Align(
              alignment: Alignment.bottomRight,
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    SizedBox(
                      height: 46,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          foregroundColor: Colors.black,
                          elevation: 0.0,
                          backgroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: Text(
                          AppLocalizations.of(context)!.cancel,
                          style: AppStyles.headLine16,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 8,
                    ),
                    SizedBox(
                      height: 46,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        )),
                        onPressed: () {
                          widget._onUrlChanged(_selectedUrl);
                          Navigator.of(context).pop();
                        },
                        child: Text(AppLocalizations.of(context)!.save),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }
}
