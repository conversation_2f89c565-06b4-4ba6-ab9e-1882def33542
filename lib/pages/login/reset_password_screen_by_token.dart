import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/Logger.dart';

import 'package:rounds/utils/app_extensions.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../helpers/ui_helper.dart';
import '../../utils/app_colors.dart';
import 'package:http/http.dart' as http;

import '../../utils/app_constants.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';

class ResetPasswordScreenByToken extends ConsumerStatefulWidget {
  final String message;
  final String user;
  bool login;
  ResetPasswordScreenByToken(this.message,
      {super.key, this.login = false, required this.user});

  @override
  ConsumerState<ResetPasswordScreenByToken> createState() =>
      _ResetPasswordScreenByTokenState();
}

class _ResetPasswordScreenByTokenState
    extends ConsumerState<ResetPasswordScreenByToken> {
  bool isEdit = true;
  TextEditingController resetTokenController = TextEditingController();
  TextEditingController newPasswordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  bool showTextOfNewPassword = false;
  bool showTextOfConfirmNewPassword = false;
  String resetTokenErrorMsg = '';
  String newPasswordErrorMsg = '';
  String confirmNewPasswordErrorMsg = '';
  String errorMsg = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: AppColors.whiteColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.white,
        leadingWidth: 10,
        leading: IconButton(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: Icon(
              Icons.arrow_back_ios,
              color: AppColors.titleTextColor,
              size: 20,
            )),
        title: Text(
          context.locale.resetPassword,
          style: TextStyle(
            fontSize: 23,
            fontWeight: FontWeight.w900,
            color: AppColors.blackTitleText,
          ),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: UIHelper.columnFieldPadding(),
          child: SizedBox(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            child: Padding(
              padding: UIHelper.allPaddingOf10(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 10),
                    child: Text(
                      widget.message,
                      style: TextStyle(
                        fontSize: 16,
                        color: AppColors.blackTitleText,
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                  passwordFields()
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget passwordFields() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UIHelper.buildLabelAndValueAsWidgetOfProfileScreen(
          label: AppLocalizations.of(context)!.token,
          value: '',
          controller: resetTokenController,
          onChanged: (v) {},
          isPassword: false,
        ),
        resetTokenErrorMsg != ''
            ? Container(
                padding: const EdgeInsets.only(left: 5.0, bottom: 5.0),
                width: double.infinity,
                child: Text(
                  resetTokenErrorMsg,
                  style: const TextStyle(color: Colors.redAccent),
                ),
              )
            : SizedBox(),
        UIHelper.buildLabelAndValueAsWidgetOfProfileScreen(
            label: AppLocalizations.of(context)!.newPassword,
            value: '',
            controller: newPasswordController,
            onChanged: (v) {
              final cursorPosition = newPasswordController.selection.baseOffset;

              String filteredText =
                  v.replaceAll(RegExp(r"[^A-Za-z\d!@#\$%^&*()-_=+<>?]"), '');

              newPasswordController.value = TextEditingValue(
                text: filteredText,
                selection: TextSelection.collapsed(
                  offset: cursorPosition > filteredText.length
                      ? filteredText.length
                      : cursorPosition,
                ),
              );

              if (newPasswordController.text.isNotEmpty) {
                setState(() {
                  newPasswordErrorMsg =
                      UIHelper().isValidPassword(newPasswordController.text)
                          ? ''
                          : AppLocalizations.of(context)!.password_validation;
                });
              }
            },
            isPassword: true,
            showText: showTextOfNewPassword,
            customSuffixIcon: IconButton(
              onPressed: () {
                setState(() {
                  showTextOfNewPassword = !showTextOfNewPassword;
                });
              },
              icon: Icon(
                showTextOfNewPassword ? Icons.visibility : Icons.visibility_off,
                color: AppColors.buttonColor,
              ),
            )),
        newPasswordErrorMsg != ''
            ? Container(
                padding: const EdgeInsets.only(left: 5.0, bottom: 5.0),
                width: double.infinity,
                child: Text(
                  newPasswordErrorMsg,
                  style: const TextStyle(color: Colors.redAccent),
                ),
              )
            : SizedBox(),
        UIHelper.buildLabelAndValueAsWidgetOfProfileScreen(
            label: AppLocalizations.of(context)!.confirmPassword,
            value: '',
            controller: confirmPasswordController,
            onChanged: (v) {
              final cursorPosition =
                  confirmPasswordController.selection.baseOffset;

              String filteredText =
                  v.replaceAll(RegExp(r"[^A-Za-z\d!@#\$%^&*()-_=+<>?]"), '');

              confirmPasswordController.value = TextEditingValue(
                text: filteredText,
                selection: TextSelection.collapsed(
                  offset: cursorPosition > filteredText.length
                      ? filteredText.length
                      : cursorPosition,
                ),
              );

              if (confirmPasswordController.text.isNotEmpty) {
                setState(() {
                  confirmNewPasswordErrorMsg =
                      UIHelper().isValidPassword(confirmPasswordController.text)
                          ? ''
                          : AppLocalizations.of(context)!.password_validation;
                });
              }
            },
            isPassword: true,
            showText: showTextOfConfirmNewPassword,
            customSuffixIcon: IconButton(
              onPressed: () {
                setState(() {
                  showTextOfConfirmNewPassword = !showTextOfConfirmNewPassword;
                });
              },
              icon: Icon(
                showTextOfConfirmNewPassword
                    ? Icons.visibility
                    : Icons.visibility_off,
                color: AppColors.buttonColor,
              ),
            )),
        confirmNewPasswordErrorMsg != ''
            ? Container(
                padding: const EdgeInsets.only(left: 5.0, bottom: 5.0),
                width: double.infinity,
                child: Text(
                  confirmNewPasswordErrorMsg,
                  style: const TextStyle(color: Colors.redAccent),
                ),
              )
            : SizedBox(),
        Padding(
          padding: const EdgeInsets.only(right: 2.0),
          child: Align(
            alignment: Alignment.centerRight,
            child: InkWell(
              onTap: () {
                resendToken(userData: widget.user);
              },
              child: Text(
                AppLocalizations.of(context)!.resend_token,
                style: TextStyle(color: AppColors.primaryColor, fontSize: 14),
              ),
            ),
          ),
        ),
        SizedBox(height: 20),
        errorMsg != ''
            ? Align(
                alignment: Alignment.center,
                child: Text(
                  errorMsg,
                  style: const TextStyle(color: Colors.redAccent),
                ))
            : SizedBox(),
        errorMsg != '' ? SizedBox(height: 20) : SizedBox(),
        Row(
          children: [
            Expanded(
              child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                          side: BorderSide(color: AppColors.primaryColor),
                          borderRadius: BorderRadius.circular(8.0)),
                      backgroundColor: AppColors.primaryColor,
                      padding:
                          EdgeInsets.symmetric(vertical: 12, horizontal: 16)),
                  onPressed: () async {
                    if (validate()) {
                    } else {
                      await submit(
                          tokenData: resetTokenController.text,
                          newPasswordData: newPasswordController.text);
                    }
                  },
                  child: Text(AppLocalizations.of(context)!.submit)),
            ),
          ],
        ),
      ],
    );
  }

  bool validate() {
    if (resetTokenController.text.isEmpty) {
      setState(() {
        resetTokenErrorMsg = AppLocalizations.of(context)!.please_enter_token;
      });
      return true;
    } else if (newPasswordController.text.isEmpty) {
      setState(() {
        newPasswordErrorMsg =
            AppLocalizations.of(context)!.please_enter_new_password;
      });
      return true;
    } else if (confirmPasswordController.text.isEmpty) {
      setState(() {
        confirmNewPasswordErrorMsg =
            AppLocalizations.of(context)!.please_enter_confirm_password;
      });
      return true;
    } else if (newPasswordController.text != confirmPasswordController.text) {
      setState(() {
        confirmNewPasswordErrorMsg =
            AppLocalizations.of(context)!.password_confirm_password_not_match;
      });
      return true;
    }
    return false;
  }

  Future<void> submit(
      {required String tokenData, required String newPasswordData}) async {
    try {
      if (!(await URLService.isInternetConnected())) {
        errorMsg = AppLocalizations.of(context)!.noInternetConnectivityString;
      } else {
        UIHelper().progressDialog(
            context: context,
            message: AppLocalizations.of(context)!.resetting_password);
        String loginCompany = AppConstants.companyName;
        String loginApplication = AppConstants.appName;
        String loginUrl = 'https://umpdev.unvired.io/UMP/';
        String user =
            widget.login ? widget.user : await SettingsHelper().getUserName();
        String company =
            widget.login ? loginCompany : await SettingsHelper().getCompany();
        String application = widget.login
            ? loginApplication
            : SettingsHelper().getApplicationName();
        String umpUrl =
            widget.login ? loginUrl : await SettingsHelper().getUrl();
        String resetToken = tokenData;
        String newPassword = newPasswordData;

        var headers = {'Content-Type': 'application/json'};

        var request = http.Request(
            'PUT',
            Uri.parse(
                '$umpUrl/API/v3/companies/$company/users/$user/changepassword?application=$application&newPassword=$newPassword&resetToken=$resetToken'));
        request.headers.addAll(headers);
        http.StreamedResponse response = await request.send();
        Navigator.pop(context);
        if (response.statusCode == 204) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  AppLocalizations.of(context)!.password_successfully_sent),
              backgroundColor: AppColors.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              behavior: SnackBarBehavior.floating,
              margin: EdgeInsets.all(20),
              duration: Duration(seconds: 30),
            ),
          );
          Navigator.pop(context);
          Navigator.pop(context);
          if (!widget.login) {
            Navigator.pop(context);
          }
        } else {
          String responseBody = await response.stream.bytesToString();
          Map<String, dynamic> responseData = jsonDecode(responseBody);
          setState(() {
            errorMsg = responseData['error'] ??
                AppLocalizations.of(context)!.unknown_error;
          });
        }
      }
    } catch (e) {
      Logger.logError(
          'ResetPasswordScreenByToken', 'changePassword', e.toString());
    }
  }

  Future<void> resendToken({
    required String userData,
  }) async {
    String loginCompany = AppConstants.companyName;
    String loginApplication = AppConstants.appName;
    String loginUrl = 'https://umpdev.unvired.io/UMP/';
    String user = userData;
    String company =
        widget.login ? loginCompany : await SettingsHelper().getCompany();
    String application =
        widget.login ? loginApplication : SettingsHelper().getApplicationName();
    String umpUrl = widget.login ? loginUrl : await SettingsHelper().getUrl();
    try {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.resending_token);
      var headers = {
        'Content-Type': 'application/json',
      };

      var url = Uri.parse(
          '$umpUrl/API/v3/companies/$company/users/$user/forgotpassword?application=$application');

      var request = http.Request('POST', url);
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();
      Navigator.pop(context);
      if (response.statusCode == 204) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(AppLocalizations.of(context)!.token_successfully_sent),
            backgroundColor: AppColors.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.all(20),
            duration: Duration(seconds: 1),
          ),
        );
      } else {
        String responseBody = await response.stream.bytesToString();
        Map<String, dynamic> responseData = jsonDecode(responseBody);
        setState(() {
          errorMsg = responseData['error'] ??
              AppLocalizations.of(context)!.unknown_error;
        });
      }
    } catch (e) {
      Logger.logError(
          'ResetPasswordScreenByToken', 'resendToken', e.toString());
    }
  }
}
