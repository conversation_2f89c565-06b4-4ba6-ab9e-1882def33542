import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:html/dom_parsing.dart';
import 'package:logger/Logger.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/models/app_url.dart';
import 'package:rounds/pages/initialization_page.dart';
import 'package:rounds/pages/login/login_state/login_state.dart';
import 'package:rounds/pages/login/widgets/domain_card.dart';
import 'package:rounds/pages/login/widgets/otp_verfication_card.dart';
import 'package:rounds/pages/login/widgets/register_card.dart';
import 'package:rounds/pages/login/widgets/server_option_dialog.dart';
import 'package:rounds/pages/widgets/custom_text_widget.dart';
import 'package:rounds/providers/server_connection_provider.dart';
import 'package:rounds/providers/user_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/app_extensions.dart';
import 'package:rounds/utils/platform_details.dart';
import 'package:rounds/utils/utils.dart';
import 'package:rounds/widgets/custom_icon_button.dart';
import 'package:rounds/widgets/inputs.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../helpers/db_helper.dart';
import '../../helpers/pa_helper.dart';
import '../../providers/fault/fault_header_provider.dart';
import '../../providers/screen_state_management.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../utils/constants.dart';
import '../profile/profile_screen.dart';
import 'forgot_password_screen.dart';

enum SplashState { splash, login, domain, register, verify }

class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage>
    with WidgetsBindingObserver
    implements AuthProtocol {
  SplashState _currentState = SplashState.splash;
  UnviredAccount? selectedAccount;
  List<UnviredAccount> _accounts = [];

  TextEditingController userNameController = TextEditingController();
  String? userFullName;
  TextEditingController passwordController = TextEditingController();

  bool isUserNameTextFieldDisabled = true;

  bool isLoading = true;

  late FocusScopeNode _getUsernameFocusNode;
  late FocusScopeNode _getPasswordFocusNode;
  late FocusScopeNode _loginFocusNode;

  String userNameError = '';
  String passwordError = '';
  String commonError = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _getUsernameFocusNode = FocusScopeNode();
    _getPasswordFocusNode = FocusScopeNode();
    _loginFocusNode = FocusScopeNode();
    WidgetsFlutterBinding.ensureInitialized();
    Future.delayed(const Duration(milliseconds: 100), () {
      checkLoginStatus(selectedAccount);
    });
    getData();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _getUsernameFocusNode.dispose();
    _getPasswordFocusNode.dispose();
    _loginFocusNode.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {}

  void getData() async {
    final prefs = await SharedPreferences.getInstance();
    String? lastScreen = prefs.getString("last_screen");
    String? userEmail = prefs.getString("userEmail");
    userFullName = prefs.getString("userName");
    userNameController.text = userEmail ?? "";
    if (lastScreen == "otp") {
      final login = ref.read(loginStateProvider.notifier);
      login.setLoginState(LoginState.verify);
    } else {
      final login = ref.read(loginStateProvider.notifier);
      login.setLoginState(LoginState.domain);
      final prefs = await SharedPreferences.getInstance();
      String? url = prefs.getString("url");
      if (url != null) {
        selectedAppUrl = AppUrl(url: url);
        await saveDomainDataAndUrl('selectedAppUrl', selectedAppUrl!.url);
        if (!kIsWeb) {
          final contents = await rootBundle.loadString(
            Constants.APP_CONFIG_PATH,
          );

          final Map<String, dynamic> config = jsonDecode(contents);
          if (config.containsKey('urls')) {
            urlList = List<dynamic>.from(config['urls'])
                .map((e) => AppUrl.fromMap(e))
                .toList();
          }
          selectedAppUrl = urlList.firstWhere((element) => element.url == url);
        }
      } else {
        await _initURLs();
      }
    }
  }

  @override
  Future<UnviredAccount> showLoginScreen(List<UnviredAccount> accounts) async {
    if (accounts.isEmpty) {
      setState(() {
        isLoading = false;
        _accounts = accounts;
        _currentState = SplashState.login;
      });
    } else if (accounts.length == 1) {
      selectedAccount = accounts[0];
      if (selectedAccount!.getErrorMessage().toString().isNotEmpty) {
        setState(() {
          _accounts = accounts;
          _currentState = SplashState.login;
          isLoading = false;
          commonError = selectedAccount!.getErrorMessage().toString();
        });
      } else if (selectedAccount!.getIsLocalPasswordRequired() ||
          !selectedAccount!.getIsLastLoggedIn()) {
        userNameController.text = selectedAccount!.getUserName();
        isUserNameTextFieldDisabled = false;
        setState(() {
          isLoading = false;
          _accounts = accounts;
          _currentState = SplashState.login;
        });
      } else {
        selectedAccount = accounts[0];
      }
    } else {
      setState(() {
        isLoading = false;
        _accounts = accounts;
        _currentState = SplashState.login;
      });
    }
    return selectedAccount!;
  }

  @override
  Future<SSOResult> showWebView(SSOLoginScreen ssoLoginScreen) async {
    SSOResult value = await Navigator.push(
        context, MaterialPageRoute(builder: (context) => ssoLoginScreen));
    return value;
  }

  Future<void> checkLoginStatus(UnviredAccount? account,
      {bool firstLogin = false}) async {
    String metaDataJson = await DefaultAssetBundle.of(context)
        .loadString("assets/json/metadata.json");
    try {
      setState(() {
        isLoading = true;
      });
      List<UnviredAccount> accounts = await (AuthenticationService()
            ..setAuthProtocol(this)
            ..setMetadataJSON(metaDataJson)
            ..setAppName("Rounds")
            ..setContext(context)
            ..setMessageInterval(15))
          .loginV2(account);
      if (accounts.isEmpty) {
        setState(() {
          isLoading = false;
          _accounts = accounts;
          _currentState = SplashState.login;
        });
      } else if (accounts.length == 1) {
        selectedAccount = accounts[0];
        if (selectedAccount!.getErrorMessage().toString().isNotEmpty) {
          setState(() {
            _accounts = accounts;
            _currentState = SplashState.login;
            isLoading = false;
            commonError = selectedAccount!.getErrorMessage().toString();
          });
        } else if (selectedAccount!.getIsLocalPasswordRequired() ||
            !selectedAccount!.getIsLastLoggedIn()) {
          userNameController.text = selectedAccount!.getUserName();
          isUserNameTextFieldDisabled = false;
          setState(() {
            _accounts = accounts;
            _currentState = SplashState.login;
            isLoading = false;
          });
        } else {
          selectedAccount = accounts[0];
          if (account == null) {
            checkLoginStatus(selectedAccount);
            return;
          }
          String user = await SettingsHelper().getUserName();
          if (user.isNotEmpty) {}
          setState(() {
            _currentState = SplashState.splash;
          });
          if (!kIsWeb) {
            await checkAndRequestPermissions();
          }
          if (firstLogin || kIsWeb) {
            if (mounted) {
              await PAHelper.getCustomizationInSyncMode(context)
                  .then((value) async {
                final prefs = await SharedPreferences.getInstance();
                prefs.setString("userEmail", userNameController.text);
                await ref.read(userProvider.notifier).getUser();
                if (kIsWeb) {
                  String? imageString = await DbHelper()
                      .getAttachmentFromIndexDbByUid(
                          ref.read(userProvider)!.user_id!);
                  if (imageString != null && imageString.isNotEmpty) {
                    ref
                        .read(profileImageProvider.notifier)
                        .getImage(base64Decode(imageString));
                  }
                }
                String firstName = UIHelper()
                    .toCamelCase(ref.read(userProvider)?.first_name ?? "");
                String lastName = UIHelper()
                    .toCamelCase(ref.read(userProvider)?.last_name ?? "");
                prefs.setString("userName", "$firstName $lastName");
              });
            }
          }
          setState(() {
            isLoading = false;
          });
          _navigateToInitializationPage();
        }
      } else {
        setState(() {
          isLoading = false;
          _accounts = accounts;
          _currentState = SplashState.login;
        });
      }
    } catch (e) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(ScreenStateManager.keyLastScreen, "Login");
      setState(() {
        isLoading = false;
        _currentState = SplashState.login;
      });
      Logger.logError('LoginPage', 'checkLoginStatus', e.toString());
    }
  }

  Future<void> checkAndRequestPermissions() async {
    PermissionStatus status = await Permission.storage.status;

    if (status.isDenied || status.isPermanentlyDenied) {
      status = await Permission.storage.request();
    }

    if (await Permission.storage.isDenied) {
      await Permission.storage.request();
    }

    if (await Permission.manageExternalStorage.isDenied) {
      await Permission.manageExternalStorage.request();
    }

    if (status.isPermanentlyDenied) {
      openAppSettings();
    }
  }

  void _navigateToInitializationPage() async {
    final url = (await AuthenticationService().getSelectedAccount())!.getUrl();
    ref.read(serverConnectionProvider.notifier).setUrl(url);
    await ScreenStateManager.saveLastScreen("userpreference");
    await ref.read(plantListProvider.notifier).fetchPlantsList();
    if (mounted) {
      Navigator.pushAndRemoveUntil(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation1, animation2) =>
              const InitializationPage(),
          transitionDuration: Duration.zero,
          reverseTransitionDuration: Duration.zero,
        ),
        (Route<dynamic> route) => false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: AppColors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    switch (_currentState) {
      case SplashState.splash:
        return Scaffold(
          resizeToAvoidBottomInset: true,
          backgroundColor: AppColors.scaffoldBackgroundGrey,
          body: Center(
            child: Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              child: Image.asset('assets/gif/rounds_animate_logo.gif'),
            ),
          ),
        );
      case SplashState.login:
        return Scaffold(
          resizeToAvoidBottomInset: true,
          backgroundColor: AppColors.scaffoldBackgroundGrey,
          body: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 20),
                Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: PlatformDetails.isMobileScreen(context)
                          ? 20
                          : MediaQuery.of(context).size.width * 0.20,
                      vertical: 5),
                  child: Padding(
                    padding: const EdgeInsets.only(
                        left: 25, right: 25, top: 25, bottom: 10),
                    child: AnimatedSize(
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeInOut,
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        transitionBuilder:
                            (Widget child, Animation<double> animation) {
                          return FadeTransition(
                            opacity: animation,
                            child: child,
                          );
                        },
                        child: Container(
                          child: _buildContentBasedOnState(),
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        );

      default:
        return const Scaffold(
          body: Center(
            child: Text('Unknown State'),
          ),
        );
    }
  }

  Widget _buildContentBasedOnState() {
    final loginState = ref.watch(loginStateProvider);
    switch (loginState) {
      case LoginState.domain:
        return const DomainCard();
      case LoginState.register:
        return const RegisterCard();
      case LoginState.login:
        return _getLoginCard();
      case LoginState.verify:
        return const OtpVerificationCard();
      default:
        throw Exception('Unexpected LoginState');
    }
  }

  _getLoginCard() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomHeadingText(
          text: context.locale.login_heading,
        ),
        const SizedBox(
          height: 10,
        ),
        Visibility(
          visible: userNameController.text.isNotEmpty &&
              userFullName != null &&
              userFullName!.isNotEmpty,
          child: Text(
            "Welcome Back ${userFullName ?? ''}!",
            style: TextStyle(
              color: AppColors.primaryColor,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        CustomSubTitleText(
          text: userNameController.text.isEmpty
              ? context.locale.login_subtitle
              : context.locale.login_password_only_subtitle,
        ),
        Visibility(
          visible: userNameController.text.isNotEmpty &&
              userFullName != null &&
              userFullName!.isNotEmpty,
          child: Text(
            userNameController.text,
            style: TextStyle(
              color: AppColors.primaryColor,
              fontSize: 16,
            ),
          ),
        ),
        const SizedBox(
          height: 50,
        ),
        Visibility(
          visible: userFullName == null || userFullName!.trim().isEmpty,
          child: Column(
            children: [
              _getUsernameTextBox(),
              _getErrorMsgString(userNameError),
            ],
          ),
        ),
        const SizedBox(height: 24),
        _getPasswordTextBox(),
        _getErrorMsgString(passwordError),
        Padding(
          padding: const EdgeInsets.only(right: 2.0, top: 5),
          child: Align(
            alignment: Alignment.centerRight,
            child: InkWell(
              onTap: () {
                Navigator.push(context, MaterialPageRoute(builder: (context) {
                  return ForgotPasswordScreen(login: true);
                }));
              },
              child: Text(
                AppLocalizations.of(context)!.forgot_password,
                style: TextStyle(color: AppColors.primaryColor, fontSize: 14),
              ),
            ),
          ),
        ),
        _getErrorMsgString(commonError),
        const SizedBox(height: 24),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Visibility(
                    visible: userFullName != null && userFullName!.isNotEmpty,
                    child: TextButton(
                      onPressed: () async {
                        userNameController.clear();
                        userFullName = null;
                        final prefs = await SharedPreferences.getInstance();
                        prefs.remove("userName");
                        prefs.remove("userEmail");
                        setState(() {});
                      },
                      child: Text(
                        AppLocalizations.of(context)!.loginWithAnotherAccount,
                        style: TextStyle(color: AppColors.primaryColor),
                      ),
                    )),
                Visibility(
                  visible: !kIsWeb,
                  child: TextButton(
                      onPressed: () {
                        showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return ServerDialogBox(
                                title: context.locale.selectServer,
                                selectedUrl: selectedAppUrl,
                                serverList: urlList,
                                onUrlChanged: (AppUrl appUrl) {
                                  selectedAppUrl = appUrl;
                                  //
                                },
                              );
                            });
                      },
                      child: Text(
                        context.locale.changeServerString,
                        style: const TextStyle(
                            color: Color.fromARGB(255, 74, 61, 186)),
                      )),
                ),
              ],
            ),
            const Spacer(),
            _getLoginButton(),
          ],
        ),
      ],
    );
  }

  _getUsernameTextBox() {
    return CustomTextField(
      // focusNode: _getUsernameFocusNode,
      // onFieldSubmitted: (value) {
      //   if (value.isNotEmpty) {
      //     _getPasswordFocusNode.requestFocus();
      //   }
      // },
      controller: userNameController,
      enable: isUserNameTextFieldDisabled,
      labelName: context.locale.email,
      isRequiredField: false,
      hintText: '',
      height: 50,
      maxLines: 1,
      autoFocus: true,
      //enable: widget.isFreshLogin,
    );
  }

  _getPasswordTextBox() {
    return CustomTextField(
      // onFieldSubmitted: (value) {
      //   if (value.isNotEmpty) {
      //     _loginFocusNode.requestFocus();
      //     _validateData();
      //   }
      // },
      controller: passwordController,
      labelName: context.locale.password,
      isRequiredField: false,
      hintText: '',
      height: 50,
      isPassword: true,
      maxLines: 1,
    );
  }

  _getLoginButton() {
    return CustomIconButton(
      focusNode: _loginFocusNode,
      isLoading: isLoading,
      ontap: () {
        _validateData();
      },
    );
  }

  _getErrorMsgString(String errorMsg) {
    return errorMsg == ''
        ? const SizedBox.shrink()
        : Container(
            margin: EdgeInsets.only(top: isLoading ? 0.0 : 10.0),
            padding: const EdgeInsets.only(left: 5.0),
            width: double.infinity,
            child: Text(
              errorMsg,
              style: const TextStyle(color: Colors.redAccent),
            ),
          );
  }

  Future<void> _validateData() async {
    _resetErrorMsg();
    setState(() {
      isLoading = true;
    });
    if (selectedAppUrl == null) {
      commonError = context.locale.selectServerToContinue;
      setState(() {
        isLoading = false;
      });
    } else if (userNameController.text.trim().isEmpty) {
      userNameError = context.locale.userNameCannotBeEmpty;
      setState(() {
        isLoading = false;
      });
    } else if (passwordController.text.trim().isEmpty) {
      passwordError = context.locale.passwordCannotBeEmpty;
      setState(() {
        isLoading = false;
      });
    } else {
      loginType = LoginType.email;

      String? domainData = await getDomainDataAndUrl('domain');
      if (domainData != null) {
        domainController.text = domainData;
      }
      if (selectedAccount == null) {
        selectedAccount = UnviredAccount()
          ..setUserName(userNameController.text.trim().toString())
          ..setPassword(passwordController.text.toString())
          ..setCompany(domainController.text)
          ..setDomain(domainController.text)
          ..setUrl(selectedAppUrl?.url ?? 'https://umpdev.unvired.io')
          ..setLoginType(loginType);
      } else {
        selectedAccount!
            .setUrl(selectedAppUrl?.url ?? 'https://umpdev.unvired.io');
        selectedAccount!.setUserName(userNameController.text.trim().toString());
        selectedAccount!.setPassword(passwordController.text.toString());
        selectedAccount!.setCompany(domainController.text.toString().trim());
        selectedAccount!.setDomain(domainController.text);
        selectedAccount!.setLoginType(loginType);
      }
      await checkLoginStatus(selectedAccount, firstLogin: true);
    }
  }

  LoginType stringToLoginType(String value) {
    switch (value) {
      case 'UNVIRED':
        return LoginType.unvired;
      case 'ADS':
        return LoginType.ads;
      case 'sap':
        return LoginType.sap;
      case 'email':
        return LoginType.email;
      case 'custom':
        return LoginType.custom;
      case 'saml':
        return LoginType.saml;
      case 'passwordless':
        return LoginType.passwordless;
      default:
        throw ArgumentError('Invalid LoginType: $value');
    }
  }

  void _resetErrorMsg() {
    userNameError = '';
    passwordError = '';
    commonError = '';
  }

  Future<List<AppUrl>> _initURLs({Map<String, dynamic>? urlData}) async {
    urlList = [];

    if (!kIsWeb) {
      if (urlData == null) {
        final contents = await rootBundle.loadString(
          Constants.APP_CONFIG_PATH,
        );

        final Map<String, dynamic> config = jsonDecode(contents);
        if (config.containsKey('urls')) {
          urlList = List<dynamic>.from(config['urls'])
              .map((e) => AppUrl.fromMap(e))
              .toList();
        }
      } else {
        if (urlData.containsKey('data')) {
          urlList = List<dynamic>.from(urlData['data'])
              .map((e) => AppUrl.fromMap(e))
              .toList();
        }
      }

      selectedAppUrl = urlList.firstWhere((element) => element.isDefault);
      return urlList;
    } else {
      final contents = await rootBundle.loadString(
        Constants.WEB_APP_CONFIG_PATH,
      );
      final Map<String, dynamic> config = jsonDecode(contents);

      selectedAppUrl = config.containsKey('umpUrl')
          ? AppUrl(url: config['umpUrl'], isDefault: true)
          : AppUrl(url: 'https://umpdev.unvired.io', isDefault: true);

      return [];
    }
  }
}
