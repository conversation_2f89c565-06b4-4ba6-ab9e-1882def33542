import 'dart:async';
import 'dart:convert';
import 'dart:ui';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:logger/Logger.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:rounds/be/CILT_EXEC_HEADER.dart';
import 'package:rounds/be/FAULT_HEADER.dart';
import 'package:rounds/be/INSP_EXEC_HEADER.dart';
import 'package:rounds/be/JOB_HEADER.dart';
import 'package:rounds/firebase_options.dart';
import 'package:rounds/helpers/pushNotification_helper.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/pages/dashboard/widgets/today_task.dart';
import 'package:rounds/pages/dashboard/widgets/top_header.dart';
import 'package:rounds/pages/inspection/inspection_screen.dart';
import 'package:rounds/providers/calendar_provider.dart';
import 'package:rounds/providers/fab_visibility_provider.dart';
import 'package:rounds/providers/job_creation/job_creation_filter_provider.dart';
import 'package:rounds/providers/screen_state_management.dart';
import 'package:rounds/providers/system_condition_provider.dart';
import 'package:rounds/services/app_notifier.dart';
import 'package:rounds/utils/app_constants.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/notification_toast.dart';
import 'package:rounds/utils/utils.dart';
import 'package:rounds/widgets/attachment_count.dart';
import 'package:rounds/widgets/chart_widget.dart';
import 'package:rounds/widgets/dashboard_card.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:rounds/helpers/web_message_background_listener_stub.dart'
    if (dart.library.html) 'package:rounds/helpers/web_message_background_listener.dart';
import 'package:unvired_settings/main.dart';

import '../../be/APP_SETTING_HEADER.dart';
import '../../helpers/db_helper.dart';
import '../../helpers/pa_helper.dart';
import '../../providers/assets/asset_provider.dart';
import '../../providers/assets/floc_provider.dart';
import '../../providers/assets/kpi_provider.dart';
import '../../providers/attachments/attachment_provider.dart';
import '../../providers/cilt/cilt_header_provider.dart';
import '../../providers/cilt/cilt_plan_header_provider.dart';
import '../../providers/fault/fault_header_provider.dart';
import '../../providers/fault/fault_type_provider.dart';
import '../../providers/inspection/inspection_header_provider.dart';
import '../../providers/inspection/inspection_plan_header_provider.dart';
import '../../providers/job_creation/job_header_provider.dart';
import '../../providers/progress_provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/constants.dart';
import '../profile/profile_screen.dart';
import '../widgets/progress_bar.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:provider/provider.dart' as legacy_provider;

Color fontColor = Colors.black;

class SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double minHeight;
  final double maxHeight;

  SliverAppBarDelegate({
    required this.child,
    required this.minHeight,
    required this.maxHeight,
  });

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight > minHeight ? maxHeight : minHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    final double effectiveHeight = maxHeight - shrinkOffset;
    return SizedBox.expand(
      child: Container(
        height: effectiveHeight,
        color: Colors.transparent, // Set the background color to transparent
        child: child,
      ),
    );
  }

  @override
  bool shouldRebuild(SliverAppBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}

class Home extends ConsumerStatefulWidget {
  const Home({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _HomeState();
}

class _HomeState extends ConsumerState<Home>
    with SingleTickerProviderStateMixin {
  bool _isLoading = false;
  String? _errorMessage;
  AppNotifier _appNotifier = AppNotifier();
  late final StreamSubscription<void> _subscription;

  @override
  void initState() {
    _initNotifier();
    getMessage();
    _initDataFromDb();

    // TODO: implement initState
    super.initState();
    Future.delayed(Duration.zero).then((value) async {
      SyncEngine().receive();
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(ScreenStateManager.keyLastScreen, "Home");
      if (kIsWeb) {
        FirebaseMessaging.instance.getToken().then((value) {
          SyncEngine()
              .registerNotification(value ?? "")
              .then((value) {})
              .catchError((e) {
            Logger.logError('Home', 'initState', e.toString());
          });
        });
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(fabVisibilityProvider.notifier).updateContext("Home");
    });
  }

  getMessage() async {
    if (!kIsWeb) {
      await SettingsHelper().setFetchInterval(15);
    }
  }

  @override
  Widget build(BuildContext context) {
    final faults = ref.read(faultHeaderListProvider);

    final jobs = ref.read(jobHeaderListProvider);

    final selectedDate = ref.watch(selectedDateProvider);
    final ciltHeaders = ref.watch(ciltHeaderListProvider);
    final inspectionHeaders = ref.watch(inspectionHeaderListProvider);

    List<CILT_EXEC_HEADER> ciltHeader = ciltHeaders
        .where((element) =>
            element.start_on == int.parse(selectedDate.replaceAll('-', '')))
        .toList();

    List<INSP_EXEC_HEADER> inspHeader = inspectionHeaders
        .where((element) =>
            element.start_on == int.parse(selectedDate.replaceAll('-', '')))
        .toList();

    WidgetsFlutterBinding.ensureInitialized();
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        systemNavigationBarColor: AppColors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
        statusBarColor: AppColors.white,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return Scaffold(
              backgroundColor: Colors.white,
              body: NotificationListener<ScrollNotification>(
                  onNotification: (scrollNotification) {
                    return false;
                  },
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 18,
                            vertical: UIHelper().getScreenType(context) ==
                                    ScreenType.desktop
                                ? 8
                                : 0),
                        child: TopHeader(
                          titleWidget: _headerTitle(),
                        ),
                      ),
                      UIHelper().getScreenType(context) != ScreenType.desktop
                          ? getDashBoadCards(context, inspHeader, ciltHeader,
                              selectedDate, faults, jobs)
                          : Expanded(
                              child: getDashBoadCards(context, inspHeader,
                                  ciltHeader, selectedDate, faults, jobs)),
                      UIHelper().getScreenType(context) != ScreenType.desktop
                          ? Expanded(
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 18),
                                child: RefreshIndicator(
                                  onRefresh: refreshData,
                                  child: const TodayTask(),
                                ),
                              ),
                            )
                          : const SizedBox.shrink()
                    ],
                  )),
            );
          },
        ),
      ),
      floatingActionButton: PendingAttachmentCount(
          count: ref.read(progressProvider).pendingAttachments),
    );
  }

  SingleChildScrollView getDashBoadCards(
      BuildContext context,
      List<INSP_EXEC_HEADER> inspHeader,
      List<CILT_EXEC_HEADER> ciltHeader,
      String selectedDate,
      List<FAULT_HEADER> faults,
      List<JOB_HEADER> jobs) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: ProgressIndicators(),
          ),
          UIHelper().getScreenType(context) != ScreenType.desktop
              ? ExpandableDashboardCards(items: [
                  DashboardCardItem(
                    title: AppLocalizations.of(context)!.inspections,
                    count: inspHeader.length,
                    icon: const Icon(Icons.assignment, color: Colors.blue),
                    backgroundColor: Colors.white,
                    textColor: Colors.black87,
                    valueColor: Colors.blue,
                    expansionDirection: ExpansionDirection.topToBottom,
                    detailsWidget: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: RoundsPieChart(
                            centerWidget: Text(
                              inspHeader.length.toString(),
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            data: ref
                                .read(inspectionHeaderListProvider.notifier)
                                .fetchRoundsPieChartData(selectedDate),
                          ),
                        ),
                      ],
                    ),
                  ),
                  DashboardCardItem(
                    title: AppLocalizations.of(context)!.cilt,
                    count: ciltHeader.length,
                    icon: const Icon(Icons.assignment, color: Colors.blue),
                    backgroundColor: Colors.white,
                    textColor: Colors.black87,
                    valueColor: Colors.blue,
                    expansionDirection: ExpansionDirection.topToBottom,
                    detailsWidget: Column(
                      children: [
                        Expanded(
                          child: RoundsPieChart(
                            centerWidget: Text(
                              ciltHeader.length.toString(),
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            data: ref
                                .read(ciltHeaderListProvider.notifier)
                                .fetchRoundsPieChartData(selectedDate),
                          ),
                        ),
                      ],
                    ),
                  ),
                  DashboardCardItem(
                    title: AppLocalizations.of(context)!.faults,
                    count: faults.length,
                    icon: const Icon(Icons.assignment, color: Colors.blue),
                    backgroundColor: Colors.white,
                    textColor: Colors.black87,
                    valueColor: Colors.blue,
                    expansionDirection: ExpansionDirection.topToBottom,
                    detailsWidget: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: RoundsPieChart(
                            centerWidget: Text(
                              faults.length.toString(),
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            data: ref
                                .read(faultHeaderListProvider.notifier)
                                .fetchRoundsPieChartData(),
                          ),
                        )
                      ],
                    ),
                  ),
                  DashboardCardItem(
                    title: AppLocalizations.of(context)!.jobs,
                    count: jobs.length,
                    icon: const Icon(Icons.assignment, color: Colors.blue),
                    backgroundColor: Colors.white,
                    textColor: Colors.black87,
                    valueColor: Colors.blue,
                    expansionDirection: ExpansionDirection.topToBottom,
                    detailsWidget: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: RoundsPieChart(
                            centerWidget: Text(
                              jobs.length.toString(),
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            data: ref
                                .read(jobHeaderListProvider.notifier)
                                .fetchRoundsPieChartData(),
                          ),
                        )
                      ],
                    ),
                  ),
                ])
              : DesktopDashboardCards(items: [
                  DesktopDashboardCardItem(
                      title: AppLocalizations.of(context)!.inspections,
                      count: inspHeader.length,
                      icon: const Icon(Icons.assignment, color: Colors.blue),
                      backgroundColor: Colors.white,
                      valueColor: Colors.blue,
                      detailsWidget: Column(
                        children: [
                          Expanded(
                            child: RoundsPieChart(
                            centerWidget: Text(
                              inspHeader.length.toString(),
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            data: ref
                                .read(inspectionHeaderListProvider.notifier)
                                .fetchRoundsPieChartData(selectedDate),
                          ),
                           )
                        ],
                      )),
                  DesktopDashboardCardItem(
                    title: AppLocalizations.of(context)!.cilt,
                    count: ciltHeader.length,
                    icon: const Icon(Icons.assignment, color: Colors.blue),
                    backgroundColor: Colors.white,
                    valueColor: Colors.blue,
                    detailsWidget: Column(
                      children: [
                        Expanded(
                          child: RoundsPieChart(
                            centerWidget: Text(
                              ciltHeader.length.toString(),
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            data: ref
                                .read(ciltHeaderListProvider.notifier)
                                .fetchRoundsPieChartData(selectedDate),
                          ),
                        ),
                      ],
                    ),
                  ),
                  DesktopDashboardCardItem(
                    title: AppLocalizations.of(context)!.faults,
                    count: faults.length,
                    icon: const Icon(Icons.assignment, color: Colors.blue),
                    backgroundColor: Colors.white,
                    valueColor: Colors.blue,
                    detailsWidget: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: RoundsPieChart(
                            centerWidget: Text(
                              faults.length.toString(),
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            data: ref
                                .read(faultHeaderListProvider.notifier)
                                .fetchRoundsPieChartData(),
                          ),
                        )
                      ],
                    ),
                  ),
                  DesktopDashboardCardItem(
                    title: AppLocalizations.of(context)!.jobs,
                    count: jobs.length,
                    icon: const Icon(Icons.assignment, color: Colors.blue),
                    backgroundColor: Colors.white,
                    detailsWidget: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: RoundsPieChart(
                            centerWidget: Text(
                              jobs.length.toString(),
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            data: ref
                                .read(jobHeaderListProvider.notifier)
                                .fetchRoundsPieChartData(),
                          ),
                        )
                      ],
                    ),
                  ),
                ]),
          UIHelper().getScreenType(context) != ScreenType.desktop
              ? const SizedBox(height: 20)
              : SizedBox.shrink(),
          UIHelper().getScreenType(context) != ScreenType.desktop
              ? Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 18),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          AppLocalizations.of(context)!.my_activities_for_today,
                          style: TextStyle(
                            fontSize: 16,
                            color: AppColors.titleTextColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              : SizedBox.shrink()
        ],
      ),
    );
  }

  int _tapCount = 0;
  DateTime? _lastTapTime;

  void _handleTap() {
    final now = DateTime.now();

    if (_lastTapTime == null ||
        now.difference(_lastTapTime!) > Duration(seconds: 2)) {
      _tapCount = 1;
    } else {
      _tapCount++;
    }

    _lastTapTime = now;

    if (_tapCount == 10) {
      _tapCount = 0;
      _navigateToSettingsPage();
    }
  }

  _navigateToSettingsPage() {
    Navigator.push(
        context,
        CupertinoPageRoute(
            builder: ((context) => Settings(
                  themeData: ThemeData(useMaterial3: false),
                )))).then((value) {});
  }

  _headerTitle() {
    String plantName = ref
            .watch(plantListProvider)
            .firstWhereOrNull(
                (plant) => plant.plant_id == ref.watch(plantProvider))
            ?.plant_name ??
        "";

    plantName = "$plantName (${ref.watch(plantProvider)})";
    return InkWell(
      onTap: () {
        _handleTap();
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.you_are_at,
            style: const TextStyle(color: Colors.black, fontSize: 12),
          ),
          SizedBox(
            width: MediaQuery.of(context).size.width * 0.36,
            child: Text(
              plantName,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                  fontSize: 15,
                  letterSpacing: 0.5),
            ),
          )
        ],
      ),
    );
  }

  List<BottomNavigationBarItem> buildBottomNavigationBarItems(
      int selectedIndex) {
    final items = [
      {'icon': Icons.assignment, 'label': 'Task'},
      {'icon': Icons.error_outline, 'label': 'Fault'},
      {'icon': Icons.home, 'label': 'Home'},
      {'icon': Icons.work_outline, 'label': 'Job'},
      {'icon': Icons.inventory, 'label': 'Tech Objects'},
    ];

    return items.asMap().entries.map((entry) {
      int idx = entry.key;
      var item = entry.value;
      return BottomNavigationBarItem(
        icon: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          decoration: BoxDecoration(
            color: selectedIndex == idx
                ? Colors.white.withOpacity(0.2)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.all(8),
          child: Icon(item['icon'] as IconData),
        ),
        label: item['label'] as String,
      );
    }).toList();
  }

  void getData() async {
    await _downloadAllData();
    _initNotifier();
  }

  Future<void> _downloadAllData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    List<Future> futures = [
      PAHelper.getLocationInAsyncMode(context),
      PAHelper.getAssetInAsyncMode(context),
      PAHelper.getKpiInAsyncMode(context),
      PAHelper.getInspectionPlanInAsyncMode(context),
      PAHelper.getCiltPlanInAsyncMode(context),
      PAHelper.getInspectionInAsyncMode(context),
      PAHelper.getCiltInAsyncMode(context),
      PAHelper.getFaultInAsyncMode(context),
      PAHelper.getJobInAsyncMode(context),
    ];
    try {
      await Future.wait(futures);
      // await PAHelper.requestDataDownload();
      setState(() {
        _isLoading = false;
        _errorMessage = null;
      });
    } catch (error) {
      setState(() {
        _isLoading = false;
        _errorMessage =
            "${AppLocalizations.of(context)!.failed_to_downalod_data} $error";
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _getPlantData() async {
    final plant = ref.read(plantProvider.notifier);
    final plantSection = ref.read(plantSectionProvider.notifier);
    final shift = ref.read(shiftProvider.notifier);
    List<APP_SETTING_HEADER> list = await DbHelper.getAppSettingHeader();
    if (list.isNotEmpty) {
      for (var item in list) {
        if (item.prop_name == 'plantId') {
          plant.setPlant(item.prop_value.toString());
        } else if (item.prop_name == 'sectionId') {
          List<dynamic> decodedList = jsonDecode(item.prop_value.toString());
          List<String> sectionIds =
              decodedList.map((e) => e.toString()).toList();
          plantSection.setPlantSection(sectionIds);
        } else if (item.prop_name == 'shiftCode') {
          shift.setShift(item.prop_value.toString());
        }
      }
    }
  }

  _initNotifier() {
    ref.read(progressProvider).setInboxCount();
/*    _initDataFromDb();*/

    _appNotifier.notifySystemError((data) async {
      if (mounted) {
        UIHelper.showSystemErrorDialog(context,
            description: data["data"]["error"]);
        Logger.logInfo('Home', '_initNotifier', data.toString());
      }
    });

    _appNotifier.notifyInfoMessages((data) async {
      if (mounted) {
        ref.read(progressProvider).addInboxCount(data);
        if (data[EventSyncStatusFieldType] ==
            EventSyncStatusTypeInboxProcessingComplete) {
          await _getPlantData();
          _initDataFromDb();
        }
      }
    });

    _appNotifier.notifySyncStatus((data) async {
      if (mounted) {
        ref.read(progressProvider).addInboxCount(data);
        if (data[EventSyncStatusFieldType] ==
            EventSyncStatusTypeInboxProcessingComplete) {
          // await _getPlantData();
          _initDataFromDb();
        }
      }
    });

    _appNotifier.notifyAttachmentStatus((data) async {
      if (mounted) {
        ref.read(progressProvider).setPendingAttachmentCount();
      }
    });

    if (kIsWeb) {
      _subscription = PushNotifications.onNotificationProcessed.listen((_) {
        //  React to notification queue processed
        debugPrint(
            "📦 Notification queue processed on Home, updating UI state");
        final plant = ref.watch(plantProvider);
        final plantSection = ref.watch(plantSectionProvider);
        final shift = ref.watch(shiftProvider);
        final searchProvider = ref.read(roundsSearchProvider.notifier).state;
        ref
            .read(filteredCiltProvider.notifier)
            .filter(plant, plantSection, shift, searchProvider, ref);
        ref
            .read(inspectionHeaderListProvider.notifier)
            .fetchInspectionListHeaders(plant, plantSection, shift, ref);
        ref.read(faultHeaderListProvider.notifier).fetchFaultHeaderList(plant);
        ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
        NotificationToastManager.showRefreshToast(context, onRefresh: () async {
          final plant = ref.watch(plantProvider);
          final plantSection = ref.watch(plantSectionProvider);
          final shift = ref.watch(shiftProvider);
          final searchProvider = ref.read(roundsSearchProvider.notifier).state;
          await ref
              .read(filteredCiltProvider.notifier)
              .filter(plant, plantSection, shift, searchProvider, ref);
          await ref
              .read(inspectionHeaderListProvider.notifier)
              .fetchInspectionListHeaders(plant, plantSection, shift, ref);
          await ref
              .read(faultHeaderListProvider.notifier)
              .fetchFaultHeaderList(plant);
          await ref
              .read(jobHeaderListProvider.notifier)
              .fetchJobHeaderList(plant);
        });
        // showRefreshToast(context);
        // TODO: e.g., refresh data, show snackbar, etc.
      });
    }
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    if (mounted) {
      if (kIsWeb) {
        _subscription.cancel();
      }
    }
  }

  void _initDataFromDb() async {
    if (mounted) {
      final plant = ref.read(plantProvider.notifier).state;
      final plantSection = ref.read(plantSectionProvider.notifier).state;
      final shift = ref.read(shiftProvider.notifier).state;

      await ref.read(progressProvider).setInboxCount();
      await ref.read(userProvider.notifier).getUser();
      if (kIsWeb) {
        String? imageString = await DbHelper()
            .getAttachmentFromIndexDbByUid(ref.read(userProvider)!.user_id!);
        if (imageString != null && imageString.isNotEmpty) {
          ref
              .read(profileImageProvider.notifier)
              .getImage(base64Decode(imageString));
        }
      }
      await ref.read(roleProvider.notifier).getRole();
      await ref
          .read(ciltHeaderListProvider.notifier)
          .fetchCiltListHeaders(plant, plantSection, shift, ref);
      await ref
          .read(ciltPlanListHeaderProvider.notifier)
          .fetchCiltPlanListHeaders();
      await ref
          .read(inspectionHeaderListProvider.notifier)
          .fetchInspectionListHeaders(plant, plantSection, shift, ref);
      await ref.read(assetHeaderProvider.notifier).getAssetHeaderList(plant);
      await ref
          .read(usersListProvider.notifier)
          .getUsers(plantId: plant, plantSec: plantSection);
      await ref
          .read(ciltHeaderListProvider.notifier)
          .fetchCiltListHeaders(plant, plantSection, shift, ref);
      await ref
          .read(ciltPlanListHeaderProvider.notifier)
          .fetchCiltPlanListHeaders();
      await ref
          .read(inspectionHeaderListProvider.notifier)
          .fetchInspectionListHeaders(plant, plantSection, shift, ref);
      await ref.read(assetHeaderProvider.notifier).getAssetHeaderList(plant);
      await ref.read(kpiHeaderProvider.notifier).getKPIHeaderList(plant);
      await ref.read(flocHeaderProvider.notifier).getLocHeaderList(plant);
      // if (ScreenType.mobile == UIHelper().getScreenType(context)) {
      //   final ciltHeaders = ref.read(ciltHeaderListProvider);
      //   final inspHeaders = ref.read(inspectionHeaderListProvider);
      //   ref
      //       .read(ciltTasksProvider.notifier)
      //       .fetchTasksForAllHeaders(ciltHeaders);
      //   ref
      //       .read(inspTasksProvider.notifier)
      //       .fetchTasksForAllHeaders(inspHeaders, plant);
      //   ref
      //       .read(ciltExecuteProvider.notifier)
      //       .getAllExecTasksForAllExecHeaders(ciltHeaders);
      //   ref
      //       .read(inspExecuteProvider.notifier)
      //       .getAllExecTasksForAllExecHeaders(inspHeaders);
      // } 
      await ref
          .read(inspectionPlanListHeaderProvider.notifier)
          .fetchInspectionPlanListHeaders();

      await ref.read(locationListProvider.notifier).fetchLocationList(plant);
      await ref.read(assetListProvider.notifier).fetchAssetList(plant);
      await ref
          .read(locationCategoryListProvider.notifier)
          .fetchLocationCategoryList();
      await ref
          .read(assetCategoryListProvider.notifier)
          .fetchAssetCategoryList();
      await ref.read(abcCategoryListProvider.notifier).fetchABCCategoryList();
      await ref
          .read(faultHeaderListProvider.notifier)
          .fetchFaultHeaderList(plant, initial: true);
      await ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant, initial: true);
      await ref.read(faultTypeListProvider.notifier).fetchFaultTypeList();
      await ref.read(faultModeHeaderListProvider.notifier).fetchFaultModeList();
      await ref.read(priorityListProvider.notifier).fetchPriorityList();
      await ref.read(skipReasonListProvider.notifier).fetchSkipReasonList();
      await ref
          .read(skipReasonListInspProvider.notifier)
          .fetchInspSkipReasonList();
      await ref.read(jobTypeListProvider.notifier).jobTypeList();
      await ref
          .read(pickListHeaderListProvider.notifier)
          .getPickListHeaderList();
      await ref.read(pickListCodeListProvider.notifier).getPickListCodeList();
      await ref.read(documentHeaderProvider.notifier).fetchDocumentHeaders();
      await ref
          .read(documentAttachmentProvider.notifier)
          .fetchDocumentAttachments();
      await ref.read(systemConditionProvider.notifier).getSystemConditions();
    }
  }

  Future<void> refreshData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await downloadTransactionData();
      await _getPlantData();
      await SyncEngine().receive();
      _initDataFromDb();
     // ref.invalidate(inspectionDataLoaderProvider);
    } catch (e) {
      _errorMessage =
          "${AppLocalizations.of(context)!.error_refreshing_data} $e";
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  downloadTransactionData() async {
    List<Future> futures = [
      PAHelper.getInspectionInAsyncMode(context),
      PAHelper.getCiltInAsyncMode(context),
      PAHelper.getFaultInAsyncMode(context),
      PAHelper.getJobInAsyncMode(context),
    ];
    try {
      await Future.wait(futures);
      setState(() {
        _isLoading = false;
        _errorMessage = null;
      });
    } catch (error) {
      setState(() {
        _isLoading = false;
        _errorMessage =
            "${AppLocalizations.of(context)!.failed_to_downalod_data} $error";
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
