import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:rounds/be/CILT_EXEC_ACTION.dart';
import 'package:rounds/be/FAULT_HEADER.dart';
import 'package:rounds/be/INSPECTION_PLAN_HEADER.dart';
import 'package:rounds/be/JOB_HEADER.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/helpers/pa_helper.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:intl/intl.dart';
import 'package:rounds/models/intractive_Item_Model.dart';
import 'package:rounds/providers/inspection/inspection_header_provider.dart';
import 'package:rounds/providers/user_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rounds/widgets/cilt_indicator.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import '../../be/CILT_EXEC_HEADER.dart';
import '../../be/CILT_PLAN_HEADER.dart';
import '../../be/DOCUMENT_HEADER.dart';
import '../../be/INSPECTION_SECTION.dart';
import '../../be/INSP_EXEC_ACTION.dart';
import '../../be/INSP_EXEC_HEADER.dart';
import '../../be/JOB_ACTION.dart';
import '../../be/JOB_DOCUMENT.dart';
import '../../helpers/db_helper.dart';
import '../../providers/cilt/cilt_header_provider.dart';
import '../../providers/cilt/cilt_plan_header_provider.dart';
import '../../providers/fault/fault_header_provider.dart';
import '../../providers/fault/fault_type_provider.dart';
import '../../providers/inspection/inspection_plan_header_provider.dart';
import '../../providers/job_creation/job_creation_filter_provider.dart';
import '../../providers/job_creation/job_header_provider.dart';
import '../../utils/app_constants.dart';
import '../../utils/constants.dart';
import '../../utils/utils.dart';
import '../assign_plan_screen.dart';
import '../cilt/cilt_detail_screen.dart';
import '../fault/fault_detail_screen.dart';
import '../fault/fault_filter_provider.dart';
import '../fault/fault_screen.dart';
import '../fault/tabs/edit_fault_field_provider.dart';
import '../inspection/inspection_detail_screen.dart';
import '../inspection/inspection_screen.dart';
import '../job_creation/job_creation_detail_screen.dart';
import '../job_creation/job_creation_screen.dart';
import '../job_creation/tabs/edit_job_creation_field_provider.dart';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';

class JobCard extends ConsumerStatefulWidget {
  final CILT_PLAN_HEADER? ciltPlanHeader;
  CILT_EXEC_HEADER? ciltHeader;
  final INSPECTION_PLAN_HEADER? inspectionPlanHeader;
  INSP_EXEC_HEADER? inspectionHeader;
  final FAULT_HEADER? faultHeader;
  final JOB_HEADER? jobHeader;
  final Color color;
  final int index;
  final Function(InteractiveItemModel) onTap;

  JobCard(
      {super.key,
      required this.index,
      this.color = Colors.white,
      this.ciltPlanHeader,
      this.ciltHeader,
      this.inspectionPlanHeader,
      this.inspectionHeader,
      this.faultHeader,
      required this.onTap,
      this.jobHeader});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _JobCardState();
}

class _JobCardState extends ConsumerState<JobCard> {
  USER_HEADER? user_header;
  String syncStatus = "";
  late SharedPreferences preferences;
  bool hasVisited = false;
  @override
  void initState() {
    // TODO: implement initState
    initSharedPreferences();
    super.initState();
  }

  late BuildContext _safeContext;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _safeContext = context; // Store a safe reference to the context
  }

  void _closeDialog() {
    if (mounted) {
      Navigator.pop(_safeContext);
    }
  }

  @override
  Widget build(BuildContext context) {
    user_header = ref.watch(userProvider);
    return _getSlidableCard(context);
  }

  double _getProgressValue(CILT_EXEC_HEADER? ciltHeader) {
    if (ciltHeader != null) {
      final numberOfTasks =
          ref.watch(ciltTasksProvider)[ciltHeader.plan_id] ?? [];
      final numberOfCompletedTasks =
          ref.watch(ciltExecuteProvider)[ciltHeader.cilt_id] ?? [];
      int totalTasks = numberOfTasks
          .where((element) =>
              element.plan_id.toString() == ciltHeader.plan_id.toString())
          .length;
      int completedTasks = numberOfCompletedTasks
          .where((element) =>
              element.cilt_id.toString() == ciltHeader.cilt_id.toString())
          .length;
      if (totalTasks == 0) {
        return 0.0;
      }
      double percentage = double.parse(
          ((completedTasks / totalTasks) * 100).toStringAsFixed(2));

      return percentage / 100;
    } else {
      return 0.0 / 100;
    }
  }

  double _getProgressValueInsp(INSP_EXEC_HEADER? inspHeader,
      {int recursionDepth = 0}) {
    if (inspHeader != null) {
      final numberOfTasks =
          ref.watch(inspTasksProvider)[inspHeader.plan_id] ?? [];

      final numberOfCompletedTasks =
          ref.watch(inspExecuteProvider)[inspHeader.insp_id] ?? [];
      int totalTasks = numberOfTasks
          .where((element) =>
              element.plan_id.toString() == inspHeader.plan_id.toString())
          .length;

      int completedTasks = numberOfCompletedTasks
          .where((element) =>
              element.insp_id.toString() == inspHeader.insp_id.toString())
          .length;

      if (totalTasks == 0) {
        return 0.0;
      }
      double percentage = double.parse(
          ((completedTasks / totalTasks) * 100).toStringAsFixed(2));
      double value = percentage / 100;

      // If value is not between 0.0 and 1.0, try recalculating (max 3 times to avoid infinite loop)
      if ((value < 0.0 || value > 1.0) && recursionDepth < 3) {
        return _getProgressValueInsp(inspHeader,
            recursionDepth: recursionDepth + 1);
      }

      return value;
    } else {
      return 0.0;
    }
  }

  double calculateCompletionPercentage(int totalTasks, int completedTasks) {
    if (totalTasks == 0) {
      return 0.0;
    }
    double percentage = (completedTasks / totalTasks) * 100;
    return percentage;
  }

  bool isAcceptVisible() {
    final user = ref.watch(userProvider);
    if (widget.ciltHeader != null) {
      return ((widget.ciltHeader?.status == AppConstants.STATE_ASSIGNED &&
                  widget.ciltHeader?.status != AppConstants.STATE_COMPLETED &&
                  widget.ciltHeader?.assigned_to == user?.user_id!) ||
              (widget.ciltHeader?.status == AppConstants.STATE_UNASSIGNED &&
                  widget.ciltHeader?.status != AppConstants.STATE_COMPLETED))
          ? true
          : false;
    } else {
      return true;
    }
  }

  bool isAcceptVisibleInsp() {
    final user = ref.watch(userProvider);
    if (widget.inspectionHeader != null) {
      return ((widget.inspectionHeader?.status == AppConstants.STATE_ASSIGNED &&
                  widget.inspectionHeader?.status !=
                      AppConstants.STATE_COMPLETED &&
                  widget.inspectionHeader?.assigned_to == user?.user_id!) ||
              (widget.inspectionHeader?.status ==
                      AppConstants.STATE_UNASSIGNED &&
                  widget.inspectionHeader?.status !=
                      AppConstants.STATE_COMPLETED))
          ? true
          : false;
    } else {
      return true;
    }
  }

  bool isAssignVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isAssign(role.cilt!)) {
        if (widget.ciltHeader != null) {
          return ((widget.ciltHeader?.status == AppConstants.STATE_UNASSIGNED &&
                      widget.ciltHeader?.status !=
                          AppConstants.STATE_COMPLETED) ||
                  widget.ciltHeader?.status == AppConstants.STATE_REJECTED)
              ? true
              : false;
        } else {
          return true;
        }
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isAssignVisibleInsp() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isAssign(role.inspection!)) {
        if (widget.inspectionHeader != null) {
          return ((widget.inspectionHeader?.status ==
                          AppConstants.STATE_UNASSIGNED &&
                      widget.inspectionHeader?.status !=
                          AppConstants.STATE_COMPLETED) ||
                  widget.inspectionHeader?.status ==
                      AppConstants.STATE_REJECTED)
              ? true
              : false;
        } else {
          return true;
        }
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  Map<String, bool> getButtonVisibility(bool isAcceptVisible,
      bool isAssignVisible, dynamic widget, dynamic user_header) {
    bool isProgressBelowOne = _getProgressValue(widget.ciltHeader) < 1;

    bool showRejectButton =
        widget.ciltHeader?.status != AppConstants.STATE_COMPLETED &&
            widget.ciltHeader?.status != AppConstants.STATE_REJECTED &&
            user_header?.user_id == widget.ciltHeader?.assigned_to &&
            isProgressBelowOne &&
            widget.ciltHeader!.assigned_to != widget.ciltHeader!.created_by;

    bool showAcceptButton = isAcceptVisible && isProgressBelowOne;

    bool showAssignButton = isAssignVisible && isProgressBelowOne;

    return {
      'Reject': showRejectButton,
      'Accept': showAcceptButton,
      'Assign': showAssignButton,
    };
  }

  Map<String, bool> getButtonVisibilityInsp(bool isAcceptVisible,
      bool isAssignVisible, dynamic widget, dynamic user_header) {
    bool isProgressBelowOne =
        _getProgressValueInsp(widget.inspectionHeader) < 1;

    bool showRejectButton =
        widget.inspectionHeader?.status != AppConstants.STATE_COMPLETED &&
            widget.inspectionHeader?.status != AppConstants.STATE_REJECTED &&
            user_header?.user_id == widget.inspectionHeader?.assigned_to &&
            isProgressBelowOne &&
            widget.inspectionHeader!.assigned_to !=
                widget.inspectionHeader!.created_by;

    bool showAcceptButton = isAcceptVisible && isProgressBelowOne;

    bool showAssignButton = isAssignVisible && isProgressBelowOne;

    return {
      'Reject': showRejectButton,
      'Accept': showAcceptButton,
      'Assign': showAssignButton,
    };
  }

  final Map<String, GlobalKey<_JobCardState>> _slidableKeys = {};
  GlobalKey<_JobCardState>? _activeSlidableKey;

  _getSlidableCard(BuildContext ctx) {
    return Slidable(
      key: ValueKey(widget.ciltPlanHeader ??
          widget.faultHeader ??
          widget.inspectionPlanHeader),
      groupTag: "cilt",
      endActionPane: widget.jobHeader != null
          ? (isJobExecutionVisible()
              ? getJobStatusLeftActionButtons(widget.jobHeader!)
              : [
                  SlidableAction(
                    onPressed: (context) {},
                    label: '',
                  ),
                ])
          : widget.ciltHeader != null
              ? getEndPanelOfCilt()
              : getEndPanelOfInsp(),
      startActionPane: widget.jobHeader != null
          ? (isJobExecutionVisible()
              ? getJobStatusRightActionButtons(widget.jobHeader!)
              : [
                  SlidableAction(
                    onPressed: (context) {},
                    label: '',
                  ),
                ])
          : widget.ciltHeader != null
              ? getStartPanelOfCilt()
              : getStartPanelOfInsp(),
      child: getJobCard(context),
    );
  }

  getEndPanelOfCilt() {
    final ciltHeaders = ref.watch(ciltHeaderListProvider.notifier);
    return ActionPane(
      motion: const ScrollMotion(),
      children: isCiltExecutionVisible()
          ? [
              if (widget.ciltHeader?.status != AppConstants.STATE_COMPLETED &&
                  widget.ciltHeader?.status != AppConstants.STATE_REJECTED &&
                  user_header?.user_id == widget.ciltHeader?.assigned_to &&
                  _getProgressValue(widget.ciltHeader) < 1 &&
                  widget.ciltHeader!.assigned_to !=
                      widget.ciltHeader!.created_by)
                SlidableAction(
                  onPressed: (context) => confirmDelete(context, ciltHeaders),
                  borderRadius: BorderRadius.circular(10),
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  icon: Icons.cancel,
                  label: AppLocalizations.of(context)!.reject,
                ),
            ]
          : [
              SlidableAction(
                onPressed: (context) {},
                label: '',
              )
            ],
    );
  }

  getEndPanelOfInsp() {
    final inspHeaders = ref.watch(inspectionHeaderListProvider.notifier);
    return ActionPane(
      motion: const ScrollMotion(),
      children: isInspExecutionVisible()
          ? [
              if (widget.inspectionHeader?.status !=
                      AppConstants.STATE_COMPLETED &&
                  widget.inspectionHeader?.status !=
                      AppConstants.STATE_REJECTED &&
                  user_header?.user_id ==
                      widget.inspectionHeader?.assigned_to &&
                  _getProgressValueInsp(widget.inspectionHeader) < 1 &&
                  widget.inspectionHeader!.assigned_to !=
                      widget.inspectionHeader!.created_by)
                SlidableAction(
                  onPressed: (context) =>
                      confirmDeleteInsp(context, inspHeaders),
                  borderRadius: BorderRadius.circular(10),
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  icon: Icons.cancel,
                  label: AppLocalizations.of(context)!.reject,
                ),
            ]
          : [
              SlidableAction(
                onPressed: (context) {},
                label: '',
              )
            ],
    );
  }

  getStartPanelOfCilt() {
    return ActionPane(
      motion: const ScrollMotion(),
      children: isCiltExecutionVisible()
          ? (_getProgressValue(widget.ciltHeader) < 1
              ? [
                  isAcceptVisible()
                      ? SlidableAction(
                          onPressed: (context) => onAccept(widget.ciltHeader!),
                          backgroundColor: AppColors.greenColor,
                          foregroundColor: AppColors.white,
                          borderRadius: BorderRadius.circular(10),
                          icon: Icons.check,
                          label: AppLocalizations.of(context)!.accept,
                        )
                      : const SizedBox(),
                  isAssignVisible()
                      ? SlidableAction(
                          onPressed: (context) => _onAssign(widget.ciltHeader!),
                          backgroundColor: AppColors.blue,
                          borderRadius: BorderRadius.circular(10),
                          foregroundColor: AppColors.white,
                          icon: Icons.assignment_turned_in,
                          label: AppLocalizations.of(context)!.assign,
                        )
                      : const SizedBox(),
                ]
              : [
                  SlidableAction(
                    onPressed: (context) {},
                    label: '',
                  )
                ])
          : [
              SlidableAction(
                onPressed: (context) {},
                label: '',
              )
            ],
    );
  }

  getStartPanelOfInsp() {
    return ActionPane(
      motion: const ScrollMotion(),
      children: isInspExecutionVisible()
          ? (_getProgressValueInsp(widget.inspectionHeader) < 1
              ? [
                  isAcceptVisibleInsp()
                      ? SlidableAction(
                          onPressed: (context) =>
                              onAcceptInsp(widget.inspectionHeader!),
                          backgroundColor: AppColors.greenColor,
                          foregroundColor: AppColors.white,
                          borderRadius: BorderRadius.circular(10),
                          icon: Icons.check,
                          label: AppLocalizations.of(context)!.accept,
                        )
                      : const SizedBox(),
                  isAssignVisibleInsp()
                      ? SlidableAction(
                          onPressed: (context) =>
                              _onAssignInsp(widget.inspectionHeader!),
                          backgroundColor: AppColors.blue,
                          borderRadius: BorderRadius.circular(10),
                          foregroundColor: AppColors.white,
                          icon: Icons.assignment_turned_in,
                          label: AppLocalizations.of(context)!.assign,
                        )
                      : const SizedBox(),
                ]
              : [
                  SlidableAction(
                    onPressed: (context) {},
                    label: '',
                  )
                ])
          : [
              SlidableAction(
                onPressed: (context) {},
                label: '',
              )
            ],
    );
  }

  getJobStatusRightActionButtons(JOB_HEADER jobHeader) {
    if (jobHeader.status == null || jobHeader.p_mode == AppConstants.add) {
      return [
        SlidableAction(
          onPressed: (context) => () {},
          label: '',
        ),
      ];
    } else if (jobHeader.status == Constants.JOB_STATE_ORAS) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? [Container()]
          : [
              SlidableAction(
                onPressed: (context) {
                  handleStatusActionButtonsFunctions('reject', jobHeader);
                },
                backgroundColor: Colors.red,
                foregroundColor: AppColors.white,
                borderRadius: BorderRadius.circular(10),
                icon: Icons.cancel,
                label: AppLocalizations.of(context)!.reject,
              )
            ];
    } else if (jobHeader.status == Constants.JOB_STATE_ACPT) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? [Container()]
          : [
              SlidableAction(
                onPressed: (context) {
                  handleStatusActionButtonsFunctions('reject', jobHeader);
                },
                backgroundColor: Colors.red,
                foregroundColor: AppColors.white,
                borderRadius: BorderRadius.circular(10),
                icon: Icons.cancel,
                label: AppLocalizations.of(context)!.reject,
              ),
              SlidableAction(
                onPressed: (context) {
                  handleStatusActionButtonsFunctions('complete', jobHeader);
                },
                backgroundColor: AppColors.greenColor,
                foregroundColor: AppColors.white,
                borderRadius: BorderRadius.circular(10),
                icon: Icons.flag,
                label: AppLocalizations.of(context)!.complete,
              )
            ];
    } else if (jobHeader.status == Constants.JOB_STATE_RJCT) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? [Container()]
          : isAssignVisible()
              ? [
                  SlidableAction(
                    onPressed: (context) {
                      handleStatusActionButtonsFunctions('assign', jobHeader);
                    },
                    backgroundColor: AppColors.blue,
                    foregroundColor: AppColors.white,
                    borderRadius: BorderRadius.circular(10),
                    icon: Icons.assignment_turned_in,
                    label: AppLocalizations.of(context)!.assign,
                  ),
                ]
              : [
                  SlidableAction(
                    onPressed: (context) {},
                    label: '',
                  )
                ];
    } else if (jobHeader.status == Constants.JOB_STATE_NOCO) {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        ),
      ];
    } else {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        ),
      ];
    }
  }

  getJobStatusLeftActionButtons(JOB_HEADER jobHeader) {
    if (jobHeader.status == null || jobHeader.p_mode == 'A') {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        ),
      ];
    } else if (jobHeader.status == Constants.JOB_STATE_OSNO) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? [Container()]
          : isAssignVisible()
              ? [
                  SlidableAction(
                    onPressed: (context) {
                      handleStatusActionButtonsFunctions('assign', jobHeader);
                    },
                    backgroundColor: AppColors.blue,
                    foregroundColor: AppColors.white,
                    borderRadius: BorderRadius.circular(10),
                    icon: Icons.assignment_turned_in,
                    label: AppLocalizations.of(context)!.assign,
                  ),
                ]
              : [
                  SlidableAction(
                    onPressed: (context) {},
                    label: '',
                  )
                ];
    } else if (jobHeader.status == Constants.JOB_STATE_ORAS) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? [Container()]
          : [
              SlidableAction(
                onPressed: (context) {
                  handleStatusActionButtonsFunctions('accept', jobHeader);
                },
                backgroundColor: AppColors.greenColor,
                foregroundColor: AppColors.white,
                borderRadius: BorderRadius.circular(10),
                icon: Icons.assignment_turned_in,
                label: AppLocalizations.of(context)!.accept,
              ),
            ];
    } else if (jobHeader.status == Constants.JOB_STATE_RJCT) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? [Container()]
          : isAssignVisible()
              ? [
                  SlidableAction(
                    onPressed: (context) {
                      handleStatusActionButtonsFunctions('assign', jobHeader);
                    },
                    backgroundColor: AppColors.blue,
                    foregroundColor: AppColors.white,
                    borderRadius: BorderRadius.circular(10),
                    icon: Icons.assignment_turned_in,
                    label: AppLocalizations.of(context)!.assign,
                  ),
                ]
              : [
                  SlidableAction(
                    onPressed: (context) {},
                    label: '',
                  )
                ];
    } else if (jobHeader.status == Constants.JOB_STATE_NOCO) {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        ),
      ];
    } else {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        ),
      ];
    }
  }

  Future<bool> confirmDelete(
    BuildContext context,
    CiltHeaderListNotifier ciltHeaders,
  ) async {
    bool connectionState = await Utils.hasInternetConnection();
    if (connectionState) {
      // ignore: use_build_context_synchronously
      return showConfirmationDialog(
            context,
            description:
                AppLocalizations.of(context)!.are_you_sure_want_reject_job,
            positiveButtonString: AppLocalizations.of(context)!.reject,
            positiveButtonOnTap: () async {
              if (widget.ciltHeader != null) {
                await onReject(widget.ciltHeader!);
              }
            },
            cancel: () => _closeDialog(),
          ) ??
          false;
    } else {
      // ignore: use_build_context_synchronously
      UIHelper.showSnackBar(context,
          message: AppLocalizations.of(context)!.offline_message);
      return false;
    }
  }

  Future<bool> confirmDeleteInsp(
    BuildContext context,
    InspectionHeaderListNotifier inspHeaders,
  ) async {
    bool connectionState = await Utils.hasInternetConnection();
    if (connectionState) {
      // ignore: use_build_context_synchronously
      return showConfirmationDialog(
            context,
            description:
                AppLocalizations.of(context)!.are_you_sure_want_reject_job,
            positiveButtonString: AppLocalizations.of(context)!.reject,
            positiveButtonOnTap: () async {
              if (widget.inspectionHeader != null) {
                await onRejectInsp(widget.inspectionHeader!);
              }
            },
            cancel: () => _closeDialog(),
          ) ??
          false;
    } else {
      // ignore: use_build_context_synchronously
      UIHelper.showSnackBar(context,
          message: AppLocalizations.of(context)!.offline_message);
      return false;
    }
  }

  String getTitle() {
    String title = '';
    if (widget.ciltPlanHeader != null) {
      title = widget.ciltPlanHeader!.title.toString();
    } else if (widget.inspectionPlanHeader != null) {
      title = widget.inspectionPlanHeader!.title.toString();
    } else if (widget.faultHeader != null) {
      title = widget.faultHeader!.description.toString();
    } else if (widget.jobHeader != null) {
      title = widget.jobHeader!.description.toString();
    }
    return title;
  }

  String getTime() {
    String time = '';
    if (widget.ciltHeader != null) {
      time =
          '${getTimeFormat(convertTimeString(widget.ciltHeader!.start_at!).substring(0, 5).toString())} - ${getTimeFormat(convertTimeString(widget.ciltHeader!.end_at!).substring(0, 5).toString())}';
    } else if (widget.inspectionHeader != null) {
      time =
          '${getTimeFormat(convertTimeString(widget.inspectionHeader!.start_at!).substring(0, 5).toString())} - ${getTimeFormat(convertTimeString(widget.inspectionHeader!.end_at!).substring(0, 5).toString())}';
    } else if (widget.faultHeader != null) {
      if (widget.faultHeader!.reported_on != null &&
          widget.faultHeader!.req_end != null) {
        time =
            '${UIHelper.formatDate(convertTimeString(widget.faultHeader!.reported_on))} - ${UIHelper.formatDate(convertTimeString(widget.faultHeader!.req_end))}';
      }
      if (widget.faultHeader!.reported_on != null &&
          widget.faultHeader!.req_end == null) {
        time = UIHelper.formatDate(
            convertTimeString(widget.faultHeader!.reported_on));
      }
      if (widget.faultHeader!.reported_on == null &&
          widget.faultHeader!.req_end != null) {
        time =
            UIHelper.formatDate(convertTimeString(widget.faultHeader!.req_end));
      }
    } else if (widget.jobHeader != null) {
      if (widget.jobHeader!.start_date != null &&
          widget.jobHeader!.end_date != null) {
        time =
            '${convertTimeString(widget.jobHeader!.start_date)} - ${convertTimeString(widget.jobHeader!.end_date)}';
      }
      if (widget.jobHeader!.start_date != null &&
          widget.jobHeader!.end_date == null) {
        time = convertTimeString(widget.jobHeader!.start_date);
      }
      if (widget.jobHeader!.start_date == null &&
          widget.jobHeader!.end_date != null) {
        time = convertTimeString(widget.jobHeader!.end_date);
      }
    }
    return time;
  }

  String getStatus() {
    String status = '';
    if (widget.ciltHeader != null) {
      switch (widget.ciltHeader!.status) {
        case AppConstants.STATE_COMPLETED:
          return "Completed";
        case AppConstants.STATE_UNASSIGNED:
          return "Open";
        case AppConstants.STATE_REJECTED:
          return "Rejected";
        case AppConstants.STATE_ASSIGNED:
          return "Assigned";
        case AppConstants.STATE_ACCEPTED:
          return "Accepted";
        default:
          return widget.ciltHeader!.status.toString();
      }
    } else if (widget.inspectionHeader != null) {
      switch (widget.inspectionHeader!.status) {
        case AppConstants.STATE_COMPLETED:
          return "Completed";
        case AppConstants.STATE_UNASSIGNED:
          return "Open";
        case AppConstants.STATE_REJECTED:
          return "Rejected";
        case AppConstants.STATE_ASSIGNED:
          return "Assigned";
        case AppConstants.STATE_ACCEPTED:
          return "Accepted";
        default:
          return widget.inspectionHeader!.status.toString();
      }
    } else if (widget.faultHeader != null) {
      status = UIHelper.getStatusString(widget.faultHeader!.status.toString());
    } else if (widget.jobHeader != null) {
      status = UIHelper.getStatusString(widget.jobHeader!.status.toString());
    }
    return status;
  }

  String getSyncStatus() {
    switch (widget.ciltHeader?.syncStatus.index ?? 0) {
      case 0:
        if (widget.ciltHeader?.objectStatus.index != 0) {
          return '';
        }
        if (widget.ciltHeader?.objectStatus.index == 2) {
          return AppLocalizations.of(context)!.sync_now;
        }
        return '';

      case 1:
        return AppLocalizations.of(context)!.queued;

      case 2:
        return AppLocalizations.of(context)!.sent;

      case 3:
        return AppLocalizations.of(context)!.error;

      default:
        return '';
    }
  }

  String getSyncStatusInsp() {
    switch (widget.inspectionHeader?.syncStatus.index ?? 0) {
      case 0:
        if (widget.inspectionHeader?.objectStatus.index != 0) {
          return '';
        }
        if (widget.inspectionHeader?.objectStatus.index == 2) {
          return AppLocalizations.of(context)!.sync_now;
        }
        return '';

      case 1:
        return AppLocalizations.of(context)!.queued;

      case 2:
        return AppLocalizations.of(context)!.sent;

      case 3:
        return AppLocalizations.of(context)!.error;

      default:
        return '';
    }
  }

  Color getSyncStatusColor() {
    if (widget.ciltHeader != null) {
      switch (widget.ciltHeader?.syncStatus.index ?? 0) {
        case 0:
          return Colors.transparent;
        case 1:
          return Colors.orange;
        case 2:
          return AppColors.primaryColor;
        case 3:
          return Colors.red;
        default:
          return Colors.transparent;
      }
    } else if (widget.inspectionHeader != null) {
      switch (widget.inspectionHeader?.syncStatus.index ?? 0) {
        case 0:
          return Colors.transparent;
        case 1:
          return Colors.orange;
        case 2:
          return AppColors.primaryColor;
        case 3:
          return Colors.red;
        default:
          return Colors.transparent;
      }
    } else {
      return Colors.transparent;
    }
  }

  Widget getJobCard(BuildContext context) {
    USER_HEADER? assignedTo;
    final ciltPlanSectionHeader =
        ref.watch(ciltPlanSectionListHeaderProvider.notifier);
    final userList = ref.watch(usersListProvider);

    final inspectionPlanSectionHeader =
        ref.watch(inspectionPlanSectionListHeaderProvider.notifier);
    final ciltExecListHeader = ref.watch(ciltExecuteTaskListProvider.notifier);
    final inspExecListHeader = ref.watch(inspExecuteTaskListProvider.notifier);
    var percentage = 0.0;
    if (widget.ciltHeader != null) {
      percentage = _getProgressValue(widget.ciltHeader);
    } else if (widget.inspectionHeader != null) {
      percentage = _getProgressValueInsp(widget.inspectionHeader);
      // Only consider percentage if it's between 0.0 and 1.0
      if (percentage < 0.0 || percentage > 1.0) {
        percentage = 0.0;
      }
    } else {
      percentage = 0.0;
    }
    final percentageClamped = percentage.clamp(0.0, 1.0);
    final percent = percentageClamped * 100;

    final todayDate = DateFormat('yyyyMMdd').format(DateTime.now());
    if (widget.ciltHeader != null) {
      if (widget.ciltHeader?.assigned_to != null) {
        assignedTo =
            DbHelper.getAssignUser(userList, widget.ciltHeader!.assigned_to!);
      } else {
        assignedTo = USER_HEADER(user_id: null);
      }
    } else if (widget.inspectionHeader != null) {
      if (widget.inspectionHeader?.assigned_to != null) {
        assignedTo = DbHelper.getAssignUser(
            userList, widget.inspectionHeader!.assigned_to!);
      } else {
        assignedTo = USER_HEADER(user_id: null);
      }
    }
    String title = getTitle();
    String time = getTime();
    String priority = getPriorityType();
    String status = getStatus();
    return InkWell(
      onTap: onCardTap(todayDate, ciltPlanSectionHeader, ciltExecListHeader,
          inspectionPlanSectionHeader, inspExecListHeader),
      child: Container(
        decoration: UIHelper.cardDecoration(cardColor: widget.color),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
          child: Column(
            children: [
              Row(
                children: [
                  widget.ciltHeader != null
                      ? Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: CiltIndicator(
                            boxSize: 18,
                            colors: CiltColor.fromCiltCode(15),
                          ),
                        )
                      : Row(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: SvgPicture.asset(
                                'assets/icon/task_icons/inspection.svg',
                                width: 30,
                                height: 30,
                              ),
                            ),
                            Text((widget.inspectionHeader?.insp_id ?? "")
                                .toString())
                          ],
                        ),
                  const Spacer(),
                  Expanded(
                    child: Container(
                      height: 20,
                      width: MediaQuery.of(context).size.width * 0.40,
                      child: Center(
                        child: LinearPercentIndicator(
                          progressColor: AppColors.greenColor,
                          lineHeight: 15,
                          backgroundColor: HexColor('#EEEEEE'),
                          percent: percentage,
                          center: Text(
                            "${percent.floor()}%",
                            style: const TextStyle(
                                fontSize: 11, fontWeight: FontWeight.bold),
                          ),
                          barRadius: const Radius.circular(8.0),
                        ),
                      ),
                    ),
                  )
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Padding(
                            padding:
                                const EdgeInsets.only(top: 8.0, bottom: 4.0),
                            child: Text(
                              title.length > 60
                                  ? title.substring(0, 60)
                                  : title,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: 13,
                                color: AppColors.titleTextColor,
                                fontWeight: FontWeight.w700,
                                letterSpacing: 0.1,
                              ),
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.centerRight,
                          child: Padding(
                            padding: const EdgeInsets.only(right: 0.0),
                            child: Container(
                              alignment: Alignment.centerRight,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 6, vertical: 4),
                              decoration: BoxDecoration(
                                color: UIHelper.getPriorityColor(priority
                                    .toString()), // Handle null priority
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(12),
                                  bottomLeft: Radius.circular(12),
                                ),
                              ),
                              child: Text(
                                priority,
                                style: TextStyle(
                                  color: AppColors.white,
                                  fontSize: 11,
                                  letterSpacing: 0.5,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                    Row(
                      children: [
                        const Icon(
                          Icons.access_time,
                          color: Colors.blue,
                          size: 12,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          time,
                          style: TextStyle(
                            color: AppColors.secondaryTextColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  if (widget.ciltHeader?.assigned_to != null ||
                      widget.inspectionHeader?.assigned_to != null)
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.account_circle,
                            color: Colors.blue,
                            size: 12,
                          ),
                          const SizedBox(width: 4),
                          Align(
                            alignment: Alignment.bottomLeft,
                            child: Text(
                              "${toCamelCase(assignedTo?.first_name ?? "")} ${toCamelCase(assignedTo?.last_name ?? "")}",
                              style: TextStyle(
                                color: AppColors.secondaryTextColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  const Spacer(),
                  Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: Align(
                      alignment: Alignment.bottomRight,
                      child: Text(
                        status,
                        style: TextStyle(
                          color: getStatusColor(),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              widget.ciltHeader != null
                  ? (widget.ciltHeader?.syncStatus.index == 1 ||
                          widget.ciltHeader?.syncStatus.index == 2 ||
                          widget.ciltHeader?.syncStatus.index == 3)
                      ? Center(
                          child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: getSyncStatusColor()),
                          onPressed: () async {
                            if (widget.ciltHeader?.objectStatus ==
                                    ObjectStatus.modify ||
                                widget.ciltHeader?.syncStatus == 3) {
                              CILT_EXEC_HEADER? ciltHeaderData =
                                  await DbHelper.getCiltExecHeader(
                                      widget.ciltHeader!);
                              if (ciltHeaderData != null) {
                                if (!kIsWeb) {
                                  await PAHelper.modifyCiltExecInAsyncMode(
                                      context, ciltHeaderData);
                                } else {
                                  await PAHelper.modifyCiltExecInSyncMode(
                                      context, ciltHeaderData);
                                }
                              }
                            }
                          },
                          child: Text(getSyncStatus()),
                        ))
                      : SizedBox()
                  : SizedBox(),
              widget.inspectionHeader != null
                  ? (widget.inspectionHeader?.syncStatus.index == 1 ||
                          widget.inspectionHeader?.syncStatus.index == 2 ||
                          widget.inspectionHeader?.syncStatus.index == 3)
                      ? Center(
                          child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: getSyncStatusColor()),
                          onPressed: () async {
                            if (widget.inspectionHeader?.objectStatus ==
                                    ObjectStatus.modify ||
                                widget.inspectionHeader?.syncStatus == 3) {
                              INSP_EXEC_HEADER? inspHeaderData =
                                  await DbHelper.getInspectionExecHeader(
                                      widget.inspectionHeader!);
                              if (inspHeaderData != null) {
                                if (!kIsWeb) {
                                  await PAHelper.modifyInspExecInAsyncMode(
                                      context, inspHeaderData);
                                } else {
                                  await PAHelper.modifyInspExecInSyncMode(
                                      context, inspHeaderData);
                                }
                              }
                            }
                          },
                          child: Text(getSyncStatusInsp()),
                        ))
                      : SizedBox()
                  : SizedBox()
            ],
          ),
        ),
      ),
    );
  }

  getTimeFormat(String time) {
    String appendedTime = time;
    return appendedTime;
  }

  getPriorityType() {
    if (widget.ciltPlanHeader != null && widget.ciltHeader != null) {
      if (widget.ciltPlanHeader!.plan_id == widget.ciltHeader!.plan_id) {
        if (widget.ciltHeader!.priority != null) {
          return ref
              .watch(priorityListProvider.notifier)
              .fetchPriorityCode(widget.ciltHeader!.priority.toString());
        } else {
          return '';
        }
      }
    } else if (widget.inspectionPlanHeader != null &&
        widget.inspectionHeader != null) {
      if (widget.inspectionPlanHeader!.plan_id ==
          widget.inspectionHeader!.plan_id) {
        if (widget.inspectionHeader!.priority != null) {
          return ref
              .watch(priorityListProvider.notifier)
              .fetchPriorityCode(widget.inspectionHeader!.priority.toString());
        } else {
          return '';
        }
      }
    } else if (widget.faultHeader != null) {
      if (widget.faultHeader!.priority != null) {
        return ref
            .watch(priorityListProvider.notifier)
            .fetchPriorityCode(widget.faultHeader!.priority.toString());
      } else {
        return '';
      }
    } else if (widget.jobHeader != null) {
      if (widget.jobHeader!.priority != null) {
        ref
            .watch(priorityListProvider.notifier)
            .fetchPriorityCode(widget.jobHeader!.priority.toString());
      } else {
        return '';
      }
    } else {
      return '';
    }
  }

/*  void _navigateToInspectionDetailScreen(BuildContext context) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation1, animation2) =>
        pageBuilder: (context, animation1, animation2) =>
            const InspectionDetailScreen(),
        transitionDuration: Duration.zero,
        reverseTransitionDuration: Duration.zero,
      ),
    );
  }*/

  Future<void> onReject(CILT_EXEC_HEADER ciltExecHeader, {bool? fromDialog}) async {
    final plant = ref.watch(plantProvider.notifier).state;
    final plantSection = ref.watch(plantSectionProvider.notifier).state;
    final shift = ref.read(shiftProvider.notifier).state;

    CILT_EXEC_ACTION rejectAction = CILT_EXEC_ACTION(
        cilt_id: ciltExecHeader.cilt_id, user_action: Constants.ACTION_REJECT);
    rejectAction.fid = ciltExecHeader.lid;
    await DbHelper().acceptCiltAction(rejectAction);

    await DbHelper.updateCilt(ciltExecHeader);

    Result result =
        await PAHelper.rejectCLITInSyncMode(context, ciltExecHeader);

    if(!kIsWeb && UIHelper().getScreenType(context) == ScreenType.mobile && fromDialog == true){
      _closeDialog();
    }
    
    bool connectionState = await Utils.hasInternetConnection();
    if (connectionState) {
      if (result.body['InfoMessage'] != null) {
        if (result.body['InfoMessage'][0]['category'] == 'FAILURE' ||
            result.body['InfoMessage'][0]['category'] == 'WARNING') {
          if (result.body['InfoMessage'][0]['message'] != null) {
            if (mounted) {
              UIHelper.showErrorDialog(
                context,
                description: result.body['InfoMessage'][0]['message'],
              );
            }
          }
        }
      } else {
        final searchKey = ref.read(roundsSearchProvider);
        if(!kIsWeb && UIHelper().getScreenType(context) == ScreenType.mobile){
          await ref.read(filteredCiltProvider.notifier).filter(plant, plantSection, shift, "", ref);
        }
        else{
          await ref.read(filteredCiltProvider.notifier).filter(plant, plantSection, shift, searchKey, ref);
        }

        await ref
            .read(ciltPlanListHeaderProvider.notifier)
            .fetchCiltPlanListHeaders();
        setState(() {});
      }
    } else {
      UIHelper.showSnackBar(context,
          message: AppLocalizations.of(context)!.offline_message);
    }
    
  }

  Future<void> onRejectInsp(INSP_EXEC_HEADER inspExecHeader, {bool? fromDialog}) async {
    final plant = ref.watch(plantProvider.notifier).state;
    final plantSection = ref.watch(plantSectionProvider.notifier).state;
    final shift = ref.read(shiftProvider.notifier).state;

    INSP_EXEC_ACTION rejectAction = INSP_EXEC_ACTION(
        insp_id: inspExecHeader.insp_id, user_action: Constants.ACTION_REJECT);
    rejectAction.fid = inspExecHeader.lid;
    await DbHelper().acceptInspAction(rejectAction);

    await DbHelper.updateInsp(inspExecHeader);

    Result result =
        await PAHelper.rejectINSPInSyncMode(context, inspExecHeader);

    if(!kIsWeb && UIHelper().getScreenType(context) == ScreenType.mobile && fromDialog == true){
      _closeDialog();
    }

    bool connectionState = await Utils.hasInternetConnection();
    if (connectionState) {
      if (result.body['InfoMessage'] != null) {
        if (result.body['InfoMessage'][0]['category'] == 'FAILURE' ||
            result.body['InfoMessage'][0]['category'] == 'WARNING') {
          if (result.body['InfoMessage'][0]['message'] != null) {
            if (mounted) {
              UIHelper.showErrorDialog(
                context,
                description: result.body['InfoMessage'][0]['message'],
              );
            }
          }
        }
      } else {
        final searchKey = ref.read(roundsSearchProvider);
        await ref.read(filterInspectionHeaderListProvider.notifier).filter(plant, plantSection, shift, searchKey, ref);
        await ref
            .read(inspectionPlanListHeaderProvider.notifier)
            .fetchInspectionPlanListHeaders();
        setState(() {});
      }
    } else {
      UIHelper.showSnackBar(context,
          message: AppLocalizations.of(context)!.offline_message);
    }
  }

  void onRejectData(CILT_EXEC_HEADER ciltExecHeader) async {
    final plant = ref.watch(plantProvider.notifier).state;
    final plantSection = ref.watch(plantSectionProvider.notifier).state;
    final shift = ref.read(shiftProvider.notifier).state;

    var result = await PAHelper.rejectCLITInSyncMode(context, ciltExecHeader);
    
    final searchKey = ref.read(roundsSearchProvider);
    if(!kIsWeb && UIHelper().getScreenType(context) == ScreenType.mobile){
     ref.read(roundsSearchProvider.notifier).state = '';
     await ref.read(filteredCiltProvider.notifier).filter(plant, plantSection, shift, searchKey, ref);
    } else{
     await ref.read(filteredCiltProvider.notifier).filter(plant, plantSection, shift, searchKey, ref);
    }

    await ref
        .read(ciltPlanListHeaderProvider.notifier)
        .fetchCiltPlanListHeaders();
    _closeDialog();
    String? message = result.body['InfoMessage'][0]['message'];
    if (message != null) {
      UIHelper.showErrorDialog(
        context,
        title: "Error",
        description: message,
      );
    }
  }

  void onRejectDataInsp(INSP_EXEC_HEADER inspExecHeader) async {
    final plant = ref.watch(plantProvider.notifier).state;
    final plantSection = ref.watch(plantSectionProvider.notifier).state;
    final shift = ref.read(shiftProvider.notifier).state;

    var result = await PAHelper.rejectINSPInSyncMode(context, inspExecHeader);
    
    final searchKey = ref.read(roundsSearchProvider);
    await ref.read(filterInspectionHeaderListProvider.notifier).filter(plant, plantSection, shift, searchKey, ref);

    await ref
        .read(inspectionPlanListHeaderProvider.notifier)
        .fetchInspectionPlanListHeaders();
    _closeDialog();
    String? message = result.body['InfoMessage'][0]['message'];
    if (message != null) {
      UIHelper.showErrorDialog(
        context,
        title: "Error",
        description: message,
      );
    }
  }

  Future<void> onAccept(CILT_EXEC_HEADER ciltExecHeader,
      {bool? navigate = false}) async {
    bool connectionState = await Utils.hasInternetConnection();
    if (connectionState) {
      final plant = ref.watch(plantProvider.notifier).state;
      final plantSection = ref.watch(plantSectionProvider.notifier).state;
      final shift = ref.read(shiftProvider.notifier).state;
      /*ciltExecHeader.assigned_on ??= DateTime.now().millisecondsSinceEpoch;
      String tempStatus = ciltExecHeader.status.toString();
      ciltExecHeader.status = AppConstants.STATE_ACCEPTED;
      ciltExecHeader.assigned_to = ref.read(userProvider)?.user_id;*/
      ref
          .read(ciltHeaderListProvider.notifier)
          .updateCiltExecHeader(ciltExecHeader);

      ref
          .read(filteredCiltProvider.notifier)
          .updateCiltExecHeader(ciltExecHeader);

      ref
          .watch(ciltHeaderProvider.notifier)
          .selectedCiltHeaders(ciltExecHeader);

      CILT_EXEC_ACTION acceptAction = CILT_EXEC_ACTION(
          cilt_id: ciltExecHeader.cilt_id,
          user_action: Constants.ACTION_ACCEPT);
      acceptAction.fid = ciltExecHeader.lid;
      await DbHelper().acceptCiltAction(acceptAction);

      await DbHelper.updateCilt(ciltExecHeader);

      Result? result = await PAHelper.acceptCLITExecInSyncMode(
          context, ciltExecHeader, ref.read(userProvider)?.user_id ?? "");

      final todayDate = DateFormat('yyyyMMdd').format(DateTime.now());

      // final ciltPlanSectionHeader =
      //     ref.watch(ciltPlanSectionListHeaderProvider.notifier);
      // final ciltExecListHeader =
      //     ref.watch(ciltExecuteTaskListProvider.notifier);
      // final inspectionPlanSectionHeader =
      //     ref.watch(inspectionPlanSectionListHeaderProvider.notifier);
      // final inspExecListHeader =
      //     ref.watch(inspExecuteTaskListProvider.notifier);

/*      onCardTap(todayDate, ciltPlanSectionHeader, ciltExecListHeader,
              inspectionPlanSectionHeader, inspExecListHeader)
          .call();*/

/*      if (result.body['InfoMessage'] != null) {
        if (result.body['InfoMessage'][0]['category'] == 'FAILURE' ||
            result.body['InfoMessage'][0]['category'] == 'WARNING') {
*/ /*          ciltExecHeader.status = tempStatus;
          ciltExecHeader.assigned_to = null;

          await DbHelper.updateCilt(ciltExecHeader);*/ /*

          if (result.body['InfoMessage'][0]['message'] != null) {
            if (mounted) {
              UIHelper.showErrorDialog(
                context,
                description: result.body['InfoMessage'][0]['message'],
              );
            }
          }
        }
      }*/

      if (result.body["CILT_EXEC"] != null) {
        if (result.body["CILT_EXEC"][0] != null && navigate == true) {
          await ref
              .read(ciltHeaderProvider.notifier)
              .selectedCiltHeaders(ciltExecHeader);
          widget.ciltHeader = ref.read(ciltHeaderProvider.notifier).state;
          final searchKey = ref.read(roundsSearchProvider);
          await ref.read(filteredCiltProvider.notifier).filter(plant, plantSection, shift, searchKey, ref);
          await ref
              .read(ciltPlanListHeaderProvider.notifier)
              .fetchCiltPlanListHeaders();
          setState(() {});
        }
      } else {
        await ref
            .read(ciltHeaderProvider.notifier)
            .selectedCiltHeaders(ciltExecHeader);
        widget.ciltHeader = ref.read(ciltHeaderProvider.notifier).state;
        final searchKey = ref.read(roundsSearchProvider);
        await ref.read(filteredCiltProvider.notifier).filter(plant, plantSection, shift, searchKey, ref);
        await ref
            .read(ciltPlanListHeaderProvider.notifier)
            .fetchCiltPlanListHeaders();
        setState(() {});
      }
    } else {
      UIHelper.showSnackBar(context,
          message: AppLocalizations.of(context)!.offline_message);
    }
  }

  Future<void> onAcceptInsp(INSP_EXEC_HEADER inspExecHeader,
      {bool? navigate = false}) async {
    bool connectionState = await Utils.hasInternetConnection();
    if (connectionState) {
      final plant = ref.watch(plantProvider.notifier).state;
      final plantSection = ref.watch(plantSectionProvider.notifier).state;
      final shift = ref.read(shiftProvider.notifier).state;
      /*ciltExecHeader.assigned_on ??= DateTime.now().millisecondsSinceEpoch;
      String tempStatus = ciltExecHeader.status.toString();
      ciltExecHeader.status = AppConstants.STATE_ACCEPTED;
      ciltExecHeader.assigned_to = ref.read(userProvider)?.user_id;*/
      ref
          .read(inspectionHeaderListProvider.notifier)
          .updateInspExecHeader(inspExecHeader);

      ref
          .read(filterInspectionHeaderListProvider.notifier)
          .updateInspExecHeader(inspExecHeader);

      await ref
          .watch(inspectionHeaderProvider.notifier)
          .fetchInspectionHeaders(inspExecHeader);

      INSP_EXEC_ACTION acceptAction = INSP_EXEC_ACTION(
          insp_id: inspExecHeader.insp_id,
          user_action: Constants.ACTION_ACCEPT);
      acceptAction.fid = inspExecHeader.lid;
      await DbHelper().acceptInspAction(acceptAction);

      await DbHelper.updateInsp(inspExecHeader);

      Result? result = await PAHelper.acceptINSPExecInSyncMode(
          context, inspExecHeader, ref.read(userProvider)?.user_id ?? "");

      if (result.body["INSP_EXEC"] != null) {
        if (result.body["INSP_EXEC"][0] != null && navigate == true) {
          await ref
              .read(inspectionHeaderProvider.notifier)
              .fetchInspectionHeaders(inspExecHeader);
          widget.inspectionHeader =
              ref.read(inspectionHeaderProvider.notifier).state;
          final searchKey = ref.read(roundsSearchProvider);
          await ref.read(filterInspectionHeaderListProvider.notifier).filter(plant, plantSection, shift, searchKey, ref);
          await ref
              .read(inspectionPlanListHeaderProvider.notifier)
              .fetchInspectionPlanListHeaders();
          setState(() {});
        }
      } else {
        await ref
            .read(inspectionHeaderProvider.notifier)
            .fetchInspectionHeaders(inspExecHeader);
        widget.inspectionHeader =
            ref.read(inspectionHeaderProvider.notifier).state;
         final searchKey = ref.read(roundsSearchProvider);
        await ref.read(filterInspectionHeaderListProvider.notifier).filter(plant, plantSection, shift, searchKey, ref);
        await ref
            .read(inspectionPlanListHeaderProvider.notifier)
            .fetchInspectionPlanListHeaders();
        setState(() {});
      }
    } else {
      UIHelper.showSnackBar(context,
          message: AppLocalizations.of(context)!.offline_message);
    }
  }

  getStatusColor() {
    if (widget.faultHeader != null) {
      return UIHelper.getFaultStatusColor(
          widget.faultHeader!.status.toString());
    }
    if (widget.jobHeader != null) {
      return UIHelper.getJobCreationStatusColor(
          widget.jobHeader!.status.toString());
    }
    if (widget.ciltHeader?.status == "UNASSIGNED") {
      return UIHelper.getJobCreationStatusColor(
          widget.ciltHeader?.status.toString() ?? "");
    }
    if (widget.ciltHeader?.status == "REJECTED") {
      return UIHelper.getJobCreationStatusColor(
          widget.ciltHeader?.status.toString() ?? "");
    }
    if (widget.inspectionHeader?.status == "UNASSIGNED") {
      return UIHelper.getJobCreationStatusColor(
          widget.inspectionHeader?.status.toString() ?? "");
    }
    if (widget.inspectionHeader?.status == "REJECTED") {
      return UIHelper.getJobCreationStatusColor(
          widget.inspectionHeader?.status.toString() ?? "");
    }
    return AppColors.greenColor;
  }

  Future<void> _navigateToCiltTaskDetailScreen(
      BuildContext context,
      CILT_PLAN_HEADER? ciltPlanHeader,
      CILT_EXEC_HEADER? cilt_header,
      CiltPlanSectionListHeaderNotifier ciltPlanSectionHeader,
      CiltExecuteTaskListNotifier ciltExecListHeader) async {
    await ciltPlanSectionHeader
        .fetchCiltPlanSectionListHeaders(ciltPlanHeader!.plan_id.toString());
    await ciltExecListHeader.getCiltExecuteTaskList();

    if (UIHelper().getScreenType(context) != ScreenType.desktop) {
      var result = await Navigator.push(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation1, animation2) => CiltDetailScreen(
            ciltPlanHeader: ciltPlanHeader,
            ciltHeader: cilt_header,
          ),
          transitionDuration: Duration.zero,
          reverseTransitionDuration: Duration.zero,
        ),
      );

      if (!kIsWeb && UIHelper().getScreenType(context) == ScreenType.mobile) {
        ref.read(roundsSearchProvider.notifier).state = '';
      }

      if (result is CILT_EXEC_HEADER) {
        setState(() {
          syncStatus;
        });
      }
    } else {
      await ref
          .watch(ciltTasksProvider.notifier)
          .getCiltTasks(ciltPlanHeader.plan_id!);
      await ref
          .watch(ciltExecuteProvider.notifier)
          .getCiltExecute(cilt_header!);
      widget.onTap(InteractiveItemModel(type: "CILT_EXEC_HEADER", data: {
        "CILT_PLAN_HEADER": ciltPlanHeader,
        "CILT_EXEC_HEADER": cilt_header,
        "index": widget.index
      }));
      roundDetailViewNotifier.value =
          InteractiveItemModel(type: "CILT_EXEC_HEADER", data: {
        "CILT_PLAN_HEADER": ciltPlanHeader,
        "CILT_EXEC_HEADER": cilt_header,
        "index": widget.index
      });
      setState(() {});
    }
  }

  Future<void> _navigateToInspectionTaskDetailScreen(
      BuildContext context,
      INSPECTION_PLAN_HEADER? inspectionPlanHeader,
      INSP_EXEC_HEADER? insp_header,
      InspectionPlanSectionListHeaderNotifier inspectionPlanSectionHeader,
      InspExecuteTaskListNotifier inspExecListHeader) async {
    await inspectionPlanSectionHeader.fetchInspectionPlanSectionListHeaders(
        inspectionPlanHeader!.plan_id.toString());
    await ref
        .read(inspectionExecSectionListHeaderProvider.notifier)
        .fetchInspectionExecSectionListHeaders(insp_header!.insp_id!);
    await inspExecListHeader.getInspExecuteTaskList();

    if (UIHelper().getScreenType(context) != ScreenType.desktop) {
      var result = await Navigator.push(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation1, animation2) =>
              InspectionDetailScreen(
            inspectionPlanHeader: inspectionPlanHeader,
            inspHeader: insp_header,
          ),
          transitionDuration: Duration.zero,
          reverseTransitionDuration: Duration.zero,
        ),
      );


      if (!kIsWeb && UIHelper().getScreenType(context) == ScreenType.mobile) {
        ref.read(roundsSearchProvider.notifier).state = '';
      }

      if (result is INSP_EXEC_HEADER) {
        setState(() {
          syncStatus;
        });
      }
    } else {
      INSPECTION_SECTION? section = await DbHelper.getInspectionSectionByPlanId(
          widget.inspectionHeader!.plan_id.toString());
      await ref.watch(inspTasksProvider.notifier).getInspTasks(
          widget.inspectionHeader!.plan_id!,
          header: widget.inspectionHeader!,
          section: section!,
          plantId: widget.inspectionHeader!.plant_id.toString());
      await ref
          .read(inspectionPlanTaskListHeaderProvider.notifier)
          .fetchInspectionPlanTaskListHeaders(
              plantId: widget.inspectionHeader!.plant_id.toString(),
              header: widget.inspectionHeader!,
              section: section);
      await ref
          .watch(inspExecuteProvider.notifier)
          .getInspExecute(insp_header!);

      widget.onTap(InteractiveItemModel(type: "INSP_EXEC_HEADER", data: {
        "INSPECTION_PLAN_HEADER": inspectionPlanHeader,
        "INSP_EXEC_HEADER": insp_header,
        "index": widget.index
      }));
      roundDetailViewNotifier.value =
          InteractiveItemModel(type: "INSP_EXEC_HEADER", data: {
        "INSPECTION_PLAN_HEADER": inspectionPlanHeader,
        "INSP_EXEC_HEADER": insp_header,
        "index": widget.index
      });
      setState(() {});
    }

    /* await inspectionPlanSectionHeader.fetchInspectionPlanSectionListHeaders(
        inspectionPlanHeader!.plan_id.toString());
    await inspExecListHeader.getInspExecuteTaskList();
    if (mounted) {
      widget.onTap(InteractiveItemModel(type: "INSPECTION_PLAN_HEADER", data: {
        "INSPECTION_PLAN_HEADER": inspectionPlanHeader,
        "index": widget.index
      }));
      if (UIHelper().getScreenType(context) != ScreenType.desktop) {
        Navigator.push(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation1, animation2) =>
                InspectionDetailScreen(
                    inspectionPlanHeader: inspectionPlanHeader),
            transitionDuration: Duration.zero,
            reverseTransitionDuration: Duration.zero,
          ),
        );
      }
    }*/
  }

  Future<void> _navigateToFaultScreen(
      BuildContext context, FAULT_HEADER? faultHeader) async {
    final plant = ref.watch(plantProvider.notifier).state;
    final faultHeaderList = ref.watch(faultHeaderListProvider.notifier);
    final faultHeaderProviderr = ref.watch(faultHeaderProvider.notifier);
    final editProviderr = ref.watch(editFaultFieldProvider.notifier);
    final faultAction = ref.watch(getFaultActionProvider.notifier);
    final faultDocument = ref.watch(getFaultDocumentProvider.notifier);
    final faultNoticedOn = ref.watch(faultNoticedOnProvider.notifier);
    final dueOn = ref.watch(faultDueOnProvider.notifier);
    editProviderr.getEditFaultFieldEnable(false);
    await faultHeaderProviderr.getFaultHeader(
        faultId: faultHeader!.fault_id.toString());

    if (faultHeaderProviderr.state.status != null) {
      if (faultHeaderProviderr.state.reported_on != null) {
        faultNoticedOn
            .getFaultNoticedOn(faultHeaderProviderr.state.reported_on!);
      }
      if (faultHeaderProviderr.state.req_end != null) {
        dueOn.getDueOn(faultHeaderProviderr.state.req_end!);
      }
    }
    await faultAction.getFaultAction(faultHeader.fault_id.toString());
    await faultDocument.getFaultDocuments(faultHeader.fault_id.toString());
    await Navigator.push(context, MaterialPageRoute(builder: (context) {
      return FaultDetailScreen(
        type: AppConstants.fault,
      );
    }));

    await faultHeaderList.fetchFaultHeaderList(plant);
  }

  Future<void> _navigateToJobScreen(
      BuildContext context, JOB_HEADER? jobHeader) async {
    final jobHeaderList = ref.watch(jobHeaderListProvider.notifier);
    final jobHeaderProviderr = ref.watch(jobHeaderProvider.notifier);
    final editProviderr = ref.watch(editJobCreationProvider.notifier);
    final plant = ref.watch(plantProvider.notifier).state;
    final jobAction = ref.watch(getJobActionProvider.notifier);
    final jobDocument = ref.watch(getJobDocumentProvider.notifier);
    final jobStartOn = ref.watch(jobStartOnProvider.notifier);
    final jobEndOn = ref.watch(jobEndOnProvider.notifier);

    editProviderr.getEditJobCreationEnable(false);
    await jobHeaderProviderr.getJobHeader(jobId: jobHeader!.job_id.toString());

    if (jobHeaderProviderr.state.status != null) {
      if (jobHeaderProviderr.state.start_date != null) {
        jobStartOn.getJobStartOn(jobHeaderProviderr.state.start_date!);
      }
      if (jobHeaderProviderr.state.end_date != null) {
        jobEndOn.getJobEndOn(jobHeaderProviderr.state.end_date!);
      }
    }
    await jobAction.getJobAction(jobHeader.job_id.toString());
    await jobDocument.getJobDocuments(jobHeader.job_id.toString());
    if (context.mounted) {
      await Navigator.push(context, MaterialPageRoute(builder: (context) {
        return JobCreationDetailScreen(
          fromFault: false,
        );
      }));
    }
    await jobHeaderList.fetchJobHeaderList(plant);
  }

  Future<void> _onAssign(CILT_EXEC_HEADER ciltExecHeader) async {
    bool connectionState = await Utils.hasInternetConnection();
    if (connectionState) {
      final plant = ref.watch(plantProvider.notifier).state;
      final plantSection = ref.watch(plantSectionProvider.notifier).state;
      final shift = ref.read(shiftProvider.notifier).state;
      USER_HEADER? asignee;
      if (!kIsWeb) {
       
        if (ScreenType.desktop == UIHelper().getScreenType(context)) {
         // if(mounted) {
            asignee = await showDialog(
              context: context,
              barrierDismissible: false,
              builder: (rootDialogContext) => Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxWidth: 420, // Slightly more than a mobile screen width
                      maxHeight: 800,
                    ),
                    child: const Padding(
                      padding: EdgeInsets.all(20.0),
                      child: AssignScreen(isSaveButtonForWeb: true,),
                    ),
                  )));
         // }
        } else{
           asignee = await Navigator.of(context)
            .push(MaterialPageRoute(builder: (context) => AssignScreen()));
        }
        
      } else {
        if (mounted) {
          asignee = await showDialog(
              context: context,
              barrierDismissible: false,
              builder: (rootDialogContext) => Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxWidth: 420, // Slightly more than a mobile screen width
                      maxHeight: 800,
                    ),
                    child: const Padding(
                      padding: EdgeInsets.all(20.0),
                      child: AssignScreen(isSaveButtonForWeb: true),
                    ),
                  )));
        }
      }
      if (asignee != null) {
        /*    ciltExecHeader.assigned_to = asignee.user_id;

        ciltExecHeader.assigned_on = DateTime.now().millisecondsSinceEpoch;*/
        /*  String tempStatus = cilt_exec_header.status.toString();*/
        ref
            .read(ciltHeaderListProvider.notifier)
            .updateCiltExecHeader(ciltExecHeader);
        /*   cilt_exec_header.status = "ASSIGNED";*/
        /*   await DbHelper.updateCilt(cilt_exec_header);*/
        CILT_EXEC_ACTION assignAction = CILT_EXEC_ACTION(
            cilt_id: ciltExecHeader.cilt_id,
            user_action: Constants.ACTION_ASSIGN,
            context1: asignee.user_id,
            context2: (DateTime.now().millisecondsSinceEpoch).toString());
        assignAction.fid = ciltExecHeader.lid;
        await DbHelper().acceptCiltAction(assignAction);

        await DbHelper.updateCilt(ciltExecHeader);
        Result result;
       // if (!kIsWeb) {
          result =
              await PAHelper.assignCLITExecInSyncMode(context, ciltExecHeader);
        // } else {
        //   result =
        //       await PAHelper.assignCLITExecInSyncMode(context, ciltExecHeader);
        // }

        if (result.body['InfoMessage'] != null) {
          if (result.body['InfoMessage'][0]['category'] == 'FAILURE' ||
              result.body['InfoMessage'][0]['category'] == 'WARNING') {
            /*        cilt_exec_header.assigned_to = null;
            cilt_exec_header.status = tempStatus;
            await DbHelper.updateCilt(cilt_exec_header);*/

            if (result.body['InfoMessage'][0]['message'] != null) {
              if (mounted) {
                UIHelper.showErrorDialog(
                  context,
                  description: result.body['InfoMessage'][0]['message'],
                );
              }
            }
          }
        }

        if (result.body["CILT_EXEC"] != null) {
          if (result.body["CILT_EXEC"][0] != null) {
            await ref
                .read(ciltHeaderProvider.notifier)
                .selectedCiltHeaders(ciltExecHeader);
            widget.ciltHeader = ref.read(ciltHeaderProvider.notifier).state;
            final searchKey = ref.read(roundsSearchProvider);
          await ref.read(filteredCiltProvider.notifier).filter(plant, plantSection, shift, searchKey, ref);

            await ref
                .read(ciltPlanListHeaderProvider.notifier)
                .fetchCiltPlanListHeaders();
            setState(() {});
          }
        } else {
          await ref
              .read(ciltHeaderProvider.notifier)
              .selectedCiltHeaders(ciltExecHeader);
          widget.ciltHeader = ref.read(ciltHeaderProvider.notifier).state;
          final searchKey = ref.read(roundsSearchProvider);
         await ref.read(filteredCiltProvider.notifier).filter(plant, plantSection, shift, searchKey, ref);

          await ref
              .read(ciltPlanListHeaderProvider.notifier)
              .fetchCiltPlanListHeaders();
          setState(() {});
        }
      }
    } else {
      UIHelper.showSnackBar(context,
          message: AppLocalizations.of(context)!.offline_message);
    }
  }

  Future<void> _onAssignInsp(INSP_EXEC_HEADER inspExecHeader) async {
    bool connectionState = await Utils.hasInternetConnection();
    if (connectionState) {
      final plant = ref.watch(plantProvider.notifier).state;
      final plantSection = ref.watch(plantSectionProvider.notifier).state;
      final shift = ref.read(shiftProvider.notifier).state;
      USER_HEADER? asignee;
      if (!kIsWeb) {
       
        if (ScreenType.desktop == UIHelper().getScreenType(context)) {
          if(mounted) {
            asignee = await showDialog(
              context: context,
              barrierDismissible: false,
              builder: (rootDialogContext) => Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxWidth: 420, // Slightly more than a mobile screen width
                      maxHeight: 800,
                    ),
                    child: const Padding(
                      padding: EdgeInsets.all(20.0),
                      child: AssignScreen(isSaveButtonForWeb: true,),
                    ),
                  )));
          }
        } else{
           asignee = await Navigator.of(context)
            .push(MaterialPageRoute(builder: (context) => AssignScreen()));
        }
        
      } else {
        if (mounted) {
          asignee = await showDialog(
              context: context,
              barrierDismissible: false,
              builder: (rootDialogContext) => Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxWidth: 420, // Slightly more than a mobile screen width
                      maxHeight: 800,
                    ),
                    child: const Padding(
                      padding: EdgeInsets.all(20.0),
                      child: AssignScreen(isSaveButtonForWeb: true),
                    ),
                  )));
        }
      }
      if (asignee != null) {
        /*    ciltExecHeader.assigned_to = asignee.user_id;

        ciltExecHeader.assigned_on = DateTime.now().millisecondsSinceEpoch;*/
        /*  String tempStatus = cilt_exec_header.status.toString();*/
        ref
            .read(inspectionHeaderListProvider.notifier)
            .updateInspExecHeader(inspExecHeader);
        /*   cilt_exec_header.status = "ASSIGNED";*/
        /*   await DbHelper.updateCilt(cilt_exec_header);*/
        INSP_EXEC_ACTION assignAction = INSP_EXEC_ACTION(
            insp_id: inspExecHeader.insp_id,
            user_action: Constants.ACTION_ASSIGN,
            context1: asignee.user_id,
            context2: (DateTime.now().millisecondsSinceEpoch).toString());
        assignAction.fid = inspExecHeader.lid;
        await DbHelper().acceptInspAction(assignAction);

        await DbHelper.updateInsp(inspExecHeader);
        Result result;
        //if (!kIsWeb) {
          result =
              await PAHelper.assignINSPExecInSyncMode(context, inspExecHeader);
        // } else {
        //   result =
        //       await PAHelper.assignINSPExecInSyncMode(context, inspExecHeader);
        // }

        if (result.body['InfoMessage'] != null) {
          if (result.body['InfoMessage'][0]['category'] == 'FAILURE' ||
              result.body['InfoMessage'][0]['category'] == 'WARNING') {
            /*        cilt_exec_header.assigned_to = null;
            cilt_exec_header.status = tempStatus;
            await DbHelper.updateCilt(cilt_exec_header);*/

            if (result.body['InfoMessage'][0]['message'] != null) {
              if (mounted) {
                UIHelper.showErrorDialog(
                  context,
                  description: result.body['InfoMessage'][0]['message'],
                );
              }
            }
          }
        }

        if (result.body["INSP_EXEC"] != null) {
          if (result.body["INSP_EXEC"][0] != null) {
            await ref
                .read(inspectionHeaderProvider.notifier)
                .fetchInspectionHeaders(inspExecHeader);
            widget.inspectionHeader =
                ref.read(inspectionHeaderProvider.notifier).state;
            final searchKey = ref.read(roundsSearchProvider);
          await ref.read(filterInspectionHeaderListProvider.notifier).filter(plant, plantSection, shift, searchKey, ref);

            await ref
                .read(inspectionPlanListHeaderProvider.notifier)
                .fetchInspectionPlanListHeaders();
            setState(() {});
          }
        } else {
          await ref
              .read(inspectionHeaderProvider.notifier)
              .fetchInspectionHeaders(inspExecHeader);
          widget.inspectionHeader =
              ref.read(inspectionHeaderProvider.notifier).state;
           final searchKey = ref.read(roundsSearchProvider);
          await ref.read(filterInspectionHeaderListProvider.notifier).filter(plant, plantSection, shift, searchKey, ref);

          await ref
              .read(inspectionPlanListHeaderProvider.notifier)
              .fetchInspectionPlanListHeaders();
          setState(() {});
        }
      }
    } else {
      UIHelper.showSnackBar(context,
          message: AppLocalizations.of(context)!.offline_message);
    }
  }

  getUser() async {
    user_header = await ref.watch(userProvider);
  }

  Future<void> resettingUI() async {
    final ciltPlanSectionHeader =
        ref.watch(ciltPlanSectionListHeaderProvider.notifier);
    final ciltExecListHeader = ref.watch(ciltExecuteTaskListProvider.notifier);
    if (widget.ciltHeader != null) {
      if (widget.ciltPlanHeader != null) {
        if (widget.ciltHeader!.status == AppConstants.STATE_ACCEPTED ||
            widget.ciltHeader!.status == AppConstants.STATE_COMPLETED) {
          DateTime now = DateTime.now();
          DateTime ciltDateAndTime =
              getTimeStamps(); // returns combined start_on + start_at

          String startAT = widget.ciltHeader!.start_at.toString();
          String startON = widget.ciltHeader!.start_on.toString();

          final int startYear = int.parse(startON.substring(0, 4));
          final int startMonth = int.parse(startON.substring(4, 6));
          final int startDay = int.parse(startON.substring(6, 8));
          final DateTime onlyStartDate =
              DateTime(startYear, startMonth, startDay);

          if (now.year == onlyStartDate.year &&
              now.month == onlyStartDate.month &&
              now.day == onlyStartDate.day &&
              now.isBefore(ciltDateAndTime) &&
              (widget.ciltHeader!.status != AppConstants.STATE_COMPLETED)) {
            await ref
                .watch(ciltHeaderProvider.notifier)
                .selectedCiltHeaders(widget.ciltHeader!);
            await ref
                .watch(ciltPlanHeaderProvider.notifier)
                .selectedCiltPlanHeaders(widget.ciltPlanHeader);
            await ref
                .read(ciltTaskNotifier.notifier)
                .reset(widget.ciltPlanHeader!.plan_id.toString(), ref);
            ref.read(ciltToggleStateProvider.notifier).setToggleState(false);
            await _navigateToCiltTaskDetailScreen(
                context,
                widget.ciltPlanHeader,
                ref.read(ciltHeaderProvider),
                ciltPlanSectionHeader,
                ciltExecListHeader);
          } else {
            await ref
                .watch(ciltHeaderProvider.notifier)
                .selectedCiltHeaders(widget.ciltHeader!);
            await ref
                .watch(ciltPlanHeaderProvider.notifier)
                .selectedCiltPlanHeaders(widget.ciltPlanHeader);
            await ref
                .read(ciltTaskNotifier.notifier)
                .reset(widget.ciltPlanHeader!.plan_id.toString(), ref);
            ref.read(ciltToggleStateProvider.notifier).setToggleState(false);
            await _navigateToCiltTaskDetailScreen(
                context,
                widget.ciltPlanHeader,
                ref.read(ciltHeaderProvider),
                ciltPlanSectionHeader,
                ciltExecListHeader);
          }
        } else {
          if (widget.ciltHeader!.assigned_to == null ||
              ((widget.ciltHeader?.status == AppConstants.STATE_ASSIGNED) &&
                  user_header?.user_id == widget.ciltHeader!.assigned_to)) {
            if (ScreenType.desktop == UIHelper().getScreenType(context)) {
              widget.onTap(
                InteractiveItemModel(
                  type: "ACCEPT_REJECT",
                  data: {
                    "type": "ACCEPT_REJECT",
                    "index": widget.index,
                    "isCilt": true,
                    "isInsp": false,
                    "acceptCondition": getButtonVisibility(isAcceptVisible(),
                            isAssignVisible(), widget, user_header)["Accept"] ??
                        true,
                    "assignCondition": getButtonVisibility(isAcceptVisible(),
                            isAssignVisible(), widget, user_header)["Assign"] ??
                        true,
                    "rejectCondition": getButtonVisibility(isAcceptVisible(),
                            isAssignVisible(), widget, user_header)["Reject"] ??
                        true,
                    "onAcceptPressed": () async {
                      await onAccept(widget.ciltHeader!, navigate: true);
                      await resettingUI();
                    },
                    "onAssignPressed": () async {
                      await _onAssign(widget.ciltHeader!);
                      await onAssigned(
                        widget.ciltHeader!,
                        ciltPlanSectionHeader,
                        ciltExecListHeader,
                      );
                    },
                    "onRejectPressed": () async {
                      await onReject(widget.ciltHeader!);
                      if (user_header?.user_id ==
                          widget.ciltHeader!.assigned_to) {
                        await onRejected(
                            ciltPlanSectionHeader, ciltExecListHeader);
                      }
                    },
                  },
                ),
              );
            } else {
              _showActioPicherSheet(context);
            }
          } else if (user_header?.user_id == widget.ciltHeader!.assigned_to) {
            if (widget.ciltHeader!.status != AppConstants.STATE_REJECTED) {
              await _navigateToCiltTaskDetailScreen(
                  context,
                  widget.ciltPlanHeader,
                  widget.ciltHeader,
                  ciltPlanSectionHeader,
                  ciltExecListHeader);
              await ref
                  .watch(ciltHeaderProvider.notifier)
                  .selectedCiltHeaders(widget.ciltHeader!);
              await ref
                  .watch(ciltPlanHeaderProvider.notifier)
                  .selectedCiltPlanHeaders(widget.ciltPlanHeader);
            } else {
              await onRejectedFromScreen(
                  ciltPlanSectionHeader, ciltExecListHeader);
            }
          } else {
            await ref
                .watch(ciltHeaderProvider.notifier)
                .selectedCiltHeaders(widget.ciltHeader!);
            await ref
                .watch(ciltPlanHeaderProvider.notifier)
                .selectedCiltPlanHeaders(widget.ciltPlanHeader);
            await ref
                .read(ciltTaskNotifier.notifier)
                .reset(widget.ciltPlanHeader!.plan_id.toString(), ref);
            ref.read(ciltToggleStateProvider.notifier).setToggleState(false);
            await _navigateToCiltTaskDetailScreen(
                context,
                widget.ciltPlanHeader,
                ref.read(ciltHeaderProvider),
                ciltPlanSectionHeader,
                ciltExecListHeader);
          }
        }
      }
    }
  }

  Future<void> resettingUIInsp() async {
    final inspPlanSectionHeader =
        ref.watch(inspectionPlanSectionListHeaderProvider.notifier);
    final inspExecListHeader = ref.watch(inspExecuteTaskListProvider.notifier);
    if (widget.inspectionHeader != null) {
      if (widget.inspectionPlanHeader != null) {
        if (widget.inspectionHeader!.status == AppConstants.STATE_ACCEPTED ||
            widget.inspectionHeader!.status == AppConstants.STATE_COMPLETED) {
          DateTime now = DateTime.now();
          DateTime inspDateAndTime =
              getTimeStampsInsp(); // returns combined start_on + start_at

          String startAT = widget.inspectionHeader!.start_at.toString();
          String startON = widget.inspectionHeader!.start_on.toString();

          final int startYear = int.parse(startON.substring(0, 4));
          final int startMonth = int.parse(startON.substring(4, 6));
          final int startDay = int.parse(startON.substring(6, 8));
          final DateTime onlyStartDate =
              DateTime(startYear, startMonth, startDay);

          if (now.year == onlyStartDate.year &&
              now.month == onlyStartDate.month &&
              now.day == onlyStartDate.day &&
              now.isBefore(inspDateAndTime) &&
              (widget.inspectionHeader!.status !=
                  AppConstants.STATE_COMPLETED)) {
            await ref
                .watch(inspectionHeaderProvider.notifier)
                .fetchInspectionHeaders(widget.inspectionHeader!);
            await ref
                .watch(inspectionPlanHeaderProvider.notifier)
                .selectedInspPlanHeaders(widget.inspectionPlanHeader);
            await ref
                .read(inspectionTaskNotifier.notifier)
                .reset(widget.inspectionPlanHeader!.plan_id.toString(), ref);
            ref
                .read(inspectionToggleStateProvider.notifier)
                .setToggleState(false);
            await _navigateToInspectionTaskDetailScreen(
                context,
                widget.inspectionPlanHeader,
                ref.read(inspectionHeaderProvider),
                inspPlanSectionHeader,
                inspExecListHeader);
          } else {
            await ref
                .watch(inspectionHeaderProvider.notifier)
                .fetchInspectionHeaders(widget.inspectionHeader!);
            await ref
                .watch(inspectionPlanHeaderProvider.notifier)
                .selectedInspPlanHeaders(widget.inspectionPlanHeader);
            await ref
                .read(inspectionTaskNotifier.notifier)
                .reset(widget.inspectionPlanHeader!.plan_id.toString(), ref);
            ref
                .read(inspectionToggleStateProvider.notifier)
                .setToggleState(false);
            await _navigateToInspectionTaskDetailScreen(
                context,
                widget.inspectionPlanHeader,
                ref.read(inspectionHeaderProvider),
                inspPlanSectionHeader,
                inspExecListHeader);
          }
        } else {
          if (widget.inspectionHeader!.assigned_to == null ||
              ((widget.inspectionHeader?.status ==
                      AppConstants.STATE_ASSIGNED) &&
                  user_header?.user_id ==
                      widget.inspectionHeader!.assigned_to)) {
            if (ScreenType.desktop == UIHelper().getScreenType(context)) {
              widget.onTap(
                InteractiveItemModel(
                  type: "ACCEPT_REJECT",
                  data: {
                    "type": "ACCEPT_REJECT",
                    "index": widget.index,
                    "isCilt": false,
                    "isInsp": true,
                    "acceptCondition": getButtonVisibilityInsp(
                            isAcceptVisibleInsp(),
                            isAssignVisibleInsp(),
                            widget,
                            user_header)["Accept"] ??
                        true,
                    "assignCondition": getButtonVisibilityInsp(
                            isAcceptVisibleInsp(),
                            isAssignVisibleInsp(),
                            widget,
                            user_header)["Assign"] ??
                        true,
                    "rejectCondition": getButtonVisibilityInsp(
                            isAcceptVisibleInsp(),
                            isAssignVisibleInsp(),
                            widget,
                            user_header)["Reject"] ??
                        true,
                    "onAcceptPressed": () async {
                      await onAcceptInsp(widget.inspectionHeader!,
                          navigate: true);
                      await resettingUIInsp();
                    },
                    "onAssignPressed": () async {
                      await _onAssignInsp(widget.inspectionHeader!);
                      await onAssignedInsp(
                        widget.inspectionHeader!,
                        inspPlanSectionHeader,
                        inspExecListHeader,
                      );
                    },
                    "onRejectPressed": () async {
                      await onRejectInsp(widget.inspectionHeader!);
                      if (user_header?.user_id ==
                          widget.inspectionHeader!.assigned_to) {
                        await onRejectedInsp(
                            inspPlanSectionHeader, inspExecListHeader);
                      }
                    },
                  },
                ),
              );
            } else {
              _showActioPicherSheetInsp(context);
            }
          } else if (user_header?.user_id ==
              widget.inspectionHeader!.assigned_to) {
            if (widget.inspectionHeader!.status !=
                AppConstants.STATE_REJECTED) {
              await _navigateToInspectionTaskDetailScreen(
                  context,
                  widget.inspectionPlanHeader,
                  widget.inspectionHeader,
                  inspPlanSectionHeader,
                  inspExecListHeader);
              await ref
                  .watch(inspectionHeaderProvider.notifier)
                  .fetchInspectionHeaders(widget.inspectionHeader!);
              await ref
                  .watch(inspectionPlanHeaderProvider.notifier)
                  .selectedInspPlanHeaders(widget.inspectionPlanHeader);
            } else {
              await onRejectedFromScreenInsp(
                  inspPlanSectionHeader, inspExecListHeader);
            }
          } else {
            await ref
                .watch(inspectionHeaderProvider.notifier)
                .fetchInspectionHeaders(widget.inspectionHeader!);
            await ref
                .watch(inspectionPlanHeaderProvider.notifier)
                .selectedInspPlanHeaders(widget.inspectionPlanHeader);
            await ref
                .read(inspectionTaskNotifier.notifier)
                .reset(widget.inspectionPlanHeader!.plan_id.toString(), ref);
            ref
                .read(inspectionToggleStateProvider.notifier)
                .setToggleState(false);
            await _navigateToInspectionTaskDetailScreen(
                context,
                widget.inspectionPlanHeader,
                ref.read(inspectionHeaderProvider),
                inspPlanSectionHeader,
                inspExecListHeader);
          }
        }
      }
    }
  }

  DateTime getTimeStamps() {
    final String startAT =
        widget.ciltHeader!.start_at.toString(); // e.g., "140000"
    final String startON =
        widget.ciltHeader!.start_on.toString(); // e.g., "20250323"

    final year = int.parse(startON.substring(0, 4));
    final month = int.parse(startON.substring(4, 6));
    final day = int.parse(startON.substring(6, 8));

    int time = int.parse(startAT);

    int hours = time ~/ 10000;
    int minutes = (time % 10000) ~/ 100;
    int seconds = time % 100; // Extract seconds by taking the remainder

    final DateTime combinedDateTime =
        DateTime(year, month, day, hours, minutes, seconds);
    return combinedDateTime;
  }

  DateTime getTimeStampsInsp() {
    final String startAT =
        widget.inspectionHeader!.start_at.toString(); // e.g., "140000"
    final String startON =
        widget.inspectionHeader!.start_on.toString(); // e.g., "20250323"

    final year = int.parse(startON.substring(0, 4));
    final month = int.parse(startON.substring(4, 6));
    final day = int.parse(startON.substring(6, 8));

    int time = int.parse(startAT);

    int hours = time ~/ 10000;
    int minutes = (time % 10000) ~/ 100;
    int seconds = time % 100; // Extract seconds by taking the remainder

    final DateTime combinedDateTime =
        DateTime(year, month, day, hours, minutes, seconds);
    return combinedDateTime;
  }

  GestureTapCallback onCardTap(
      todayDate,
      ciltPlanSectionHeader,
      ciltExecListHeader,
      inspectionPlanSectionHeader,
      InspExecuteTaskListNotifier inspExecListHeader) {
    if (widget.ciltHeader != null) {
      // if (widget.ciltHeader!.start_on.toString() != todayDate) {
      //   return () {};
      // } else {
      return !isCiltDisplayVisible()
          ? () {}
          : () async {
              if (widget.ciltPlanHeader != null) {
                if (widget.ciltHeader!.status == AppConstants.STATE_ACCEPTED ||
                    widget.ciltHeader!.status == AppConstants.STATE_COMPLETED) {
                  DateTime now = DateTime.now();
                  DateTime ciltDateAndTime =
                      getTimeStamps(); // returns combined start_on + start_at

                  String startAT = widget.ciltHeader!.start_at.toString();
                  String startON = widget.ciltHeader!.start_on.toString();

                  final int startYear = int.parse(startON.substring(0, 4));
                  final int startMonth = int.parse(startON.substring(4, 6));
                  final int startDay = int.parse(startON.substring(6, 8));
                  final DateTime onlyStartDate =
                      DateTime(startYear, startMonth, startDay);

                  if (now.year == onlyStartDate.year &&
                      now.month == onlyStartDate.month &&
                      now.day == onlyStartDate.day &&
                      now.isBefore(ciltDateAndTime) &&
                      (widget.ciltHeader!.status !=
                          AppConstants.STATE_COMPLETED)) {
                    await ref
                        .watch(ciltHeaderProvider.notifier)
                        .selectedCiltHeaders(widget.ciltHeader!);
                    await ref
                        .watch(ciltPlanHeaderProvider.notifier)
                        .selectedCiltPlanHeaders(widget.ciltPlanHeader);
                    await ref
                        .read(ciltTaskNotifier.notifier)
                        .reset(widget.ciltPlanHeader!.plan_id.toString(), ref);
                    ref
                        .read(ciltToggleStateProvider.notifier)
                        .setToggleState(false);
                    await _navigateToCiltTaskDetailScreen(
                        context,
                        widget.ciltPlanHeader,
                        ref.read(ciltHeaderProvider),
                        ciltPlanSectionHeader,
                        ciltExecListHeader);
                  } else {
                    await ref
                        .watch(ciltHeaderProvider.notifier)
                        .selectedCiltHeaders(widget.ciltHeader!);
                    await ref
                        .watch(ciltPlanHeaderProvider.notifier)
                        .selectedCiltPlanHeaders(widget.ciltPlanHeader);
                    await ref
                        .read(ciltTaskNotifier.notifier)
                        .reset(widget.ciltPlanHeader!.plan_id.toString(), ref);
                    ref
                        .read(ciltToggleStateProvider.notifier)
                        .setToggleState(false);
                    await _navigateToCiltTaskDetailScreen(
                        context,
                        widget.ciltPlanHeader,
                        ref.read(ciltHeaderProvider),
                        ciltPlanSectionHeader,
                        ciltExecListHeader);
                  }
                } else {
                  if (widget.ciltHeader!.assigned_to == null ||
                      ((widget.ciltHeader?.status == AppConstants.STATE_ASSIGNED
                          // &&
                          //     user_header!.user_id !=
                          //         widget.ciltHeader!.assigned_to
                          ) &&
                          user_header?.user_id ==
                              widget.ciltHeader!.assigned_to)) {
                    if (ScreenType.desktop ==
                        UIHelper().getScreenType(context)) {
                      widget.onTap(
                        InteractiveItemModel(
                          type: "ACCEPT_REJECT",
                          data: {
                            "type": "ACCEPT_REJECT",
                            "index": widget.index,
                            "isCilt": true,
                            "isInsp": false,
                            "acceptCondition": getButtonVisibility(
                                    isAcceptVisible(),
                                    isAssignVisible(),
                                    widget,
                                    user_header)["Accept"] ??
                                true,
                            "assignCondition": getButtonVisibility(
                                    isAcceptVisible(),
                                    isAssignVisible(),
                                    widget,
                                    user_header)["Assign"] ??
                                true,
                            "rejectCondition": getButtonVisibility(
                                    isAcceptVisible(),
                                    isAssignVisible(),
                                    widget,
                                    user_header)["Reject"] ??
                                true,
                            "onAcceptPressed": () async {
                              await onAccept(widget.ciltHeader!,
                                  navigate: true);
                              await resettingUI();
                            },
                            "onAssignPressed": () async {
                              await _onAssign(widget.ciltHeader!);
                              await onAssigned(
                                widget.ciltHeader!,
                                ciltPlanSectionHeader,
                                ciltExecListHeader,
                              );
                            },
                            "onRejectPressed": () async {
                              await onReject(widget.ciltHeader!);
                              if (user_header?.user_id ==
                                  widget.ciltHeader!.assigned_to) {
                                await onRejected(
                                    ciltPlanSectionHeader, ciltExecListHeader);
                              }
                            },
                          },
                        ),
                      );
                    } else {
                      _showActioPicherSheet(context);
                    }
                  } else if (user_header?.user_id ==
                      widget.ciltHeader!.assigned_to) {
                    if (widget.ciltHeader!.status !=
                        AppConstants.STATE_REJECTED) {
                      await _navigateToCiltTaskDetailScreen(
                          context,
                          widget.ciltPlanHeader,
                          widget.ciltHeader,
                          ciltPlanSectionHeader,
                          ciltExecListHeader);
                      await ref
                          .watch(ciltHeaderProvider.notifier)
                          .selectedCiltHeaders(widget.ciltHeader!);
                      await ref
                          .watch(ciltPlanHeaderProvider.notifier)
                          .selectedCiltPlanHeaders(widget.ciltPlanHeader);
                    } else {
                      await onRejectedFromScreen(
                          ciltPlanSectionHeader, ciltExecListHeader);
                    }
                  } else {
                    await ref
                        .watch(ciltHeaderProvider.notifier)
                        .selectedCiltHeaders(widget.ciltHeader!);
                    await ref
                        .watch(ciltPlanHeaderProvider.notifier)
                        .selectedCiltPlanHeaders(widget.ciltPlanHeader);
                    await ref
                        .read(ciltTaskNotifier.notifier)
                        .reset(widget.ciltPlanHeader!.plan_id.toString(), ref);
                    ref
                        .read(ciltToggleStateProvider.notifier)
                        .setToggleState(false);
                    await _navigateToCiltTaskDetailScreen(
                        context,
                        widget.ciltPlanHeader,
                        ref.read(ciltHeaderProvider),
                        ciltPlanSectionHeader,
                        ciltExecListHeader);
                  }
                }
              }
            };
      // }
    }
    if (widget.inspectionHeader != null) {
      Future.microtask(() {
        ref
            .read(numericInspectionHeaderProvider.notifier)
            .clearAllNumericValue();
      });
      // if (widget.ciltHeader!.start_on.toString() != todayDate) {
      //   return () {};
      // } else {
      return !isInspDisplayVisible()
          ? () {}
          : () async {
              if (widget.inspectionPlanHeader != null) {
                if (widget.inspectionHeader!.status ==
                        AppConstants.STATE_ACCEPTED ||
                    widget.inspectionHeader!.status ==
                        AppConstants.STATE_COMPLETED) {
                  DateTime now = DateTime.now();
                  DateTime ciltDateAndTime =
                      getTimeStampsInsp(); // returns combined start_on + start_at

                  String startAT = widget.inspectionHeader!.start_at.toString();
                  String startON = widget.inspectionHeader!.start_on.toString();

                  final int startYear = int.parse(startON.substring(0, 4));
                  final int startMonth = int.parse(startON.substring(4, 6));
                  final int startDay = int.parse(startON.substring(6, 8));
                  final DateTime onlyStartDate =
                      DateTime(startYear, startMonth, startDay);

                  if (now.year == onlyStartDate.year &&
                      now.month == onlyStartDate.month &&
                      now.day == onlyStartDate.day &&
                      now.isBefore(ciltDateAndTime) &&
                      (widget.inspectionHeader!.status !=
                          AppConstants.STATE_COMPLETED)) {
                    await ref
                        .watch(inspectionHeaderProvider.notifier)
                        .fetchInspectionHeaders(widget.inspectionHeader!);
                    await ref
                        .watch(inspectionPlanHeaderProvider.notifier)
                        .selectedInspPlanHeaders(widget.inspectionPlanHeader);
                    await ref.read(inspectionTaskNotifier.notifier).reset(
                        widget.inspectionPlanHeader!.plan_id.toString(), ref);
                    ref
                        .read(inspectionToggleStateProvider.notifier)
                        .setToggleState(false);
                    await _navigateToInspectionTaskDetailScreen(
                        context,
                        widget.inspectionPlanHeader,
                        ref.read(inspectionHeaderProvider),
                        inspectionPlanSectionHeader,
                        inspExecListHeader);
                  } else {
                    await ref
                        .watch(inspectionHeaderProvider.notifier)
                        .fetchInspectionHeaders(widget.inspectionHeader!);
                    await ref
                        .watch(inspectionPlanHeaderProvider.notifier)
                        .selectedInspPlanHeaders(widget.inspectionPlanHeader);
                    await ref.read(inspectionTaskNotifier.notifier).reset(
                        widget.inspectionPlanHeader!.plan_id.toString(), ref);
                    ref
                        .read(inspectionToggleStateProvider.notifier)
                        .setToggleState(false);
                    await _navigateToInspectionTaskDetailScreen(
                        context,
                        widget.inspectionPlanHeader,
                        ref.read(inspectionHeaderProvider),
                        inspectionPlanSectionHeader,
                        inspExecListHeader);
                  }
                } else {
                  if (widget.inspectionHeader!.assigned_to == null ||
                      ((widget.inspectionHeader?.status ==
                              AppConstants.STATE_ASSIGNED
                          // &&
                          //     user_header!.user_id !=
                          //         widget.ciltHeader!.assigned_to
                          ) &&
                          user_header?.user_id ==
                              widget.inspectionHeader!.assigned_to)) {
                    if (ScreenType.desktop ==
                        UIHelper().getScreenType(context)) {
                      widget.onTap(
                        InteractiveItemModel(
                          type: "ACCEPT_REJECT",
                          data: {
                            "type": "ACCEPT_REJECT",
                            "index": widget.index,
                            "isCilt": false,
                            "isInsp": true,
                            "acceptCondition": getButtonVisibilityInsp(
                                    isAcceptVisibleInsp(),
                                    isAssignVisibleInsp(),
                                    widget,
                                    user_header)["Accept"] ??
                                true,
                            "assignCondition": getButtonVisibilityInsp(
                                    isAcceptVisibleInsp(),
                                    isAssignVisibleInsp(),
                                    widget,
                                    user_header)["Assign"] ??
                                true,
                            "rejectCondition": getButtonVisibilityInsp(
                                    isAcceptVisibleInsp(),
                                    isAssignVisibleInsp(),
                                    widget,
                                    user_header)["Reject"] ??
                                true,
                            "onAcceptPressed": () async {
                              await onAcceptInsp(widget.inspectionHeader!,
                                  navigate: true);
                              await resettingUIInsp();
                            },
                            "onAssignPressed": () async {
                              await _onAssignInsp(widget.inspectionHeader!);
                              await onAssignedInsp(
                                widget.inspectionHeader!,
                                inspectionPlanSectionHeader,
                                inspExecListHeader,
                              );
                            },
                            "onRejectPressed": () async {
                              await onRejectInsp(widget.inspectionHeader!);
                              if (user_header?.user_id ==
                                  widget.inspectionHeader!.assigned_to) {
                                await onRejectedInsp(
                                    inspectionPlanSectionHeader,
                                    inspExecListHeader);
                              }
                            },
                          },
                        ),
                      );
                    } else {
                      _showActioPicherSheetInsp(context);
                    }
                  } else if (user_header?.user_id ==
                      widget.inspectionHeader!.assigned_to) {
                    if (widget.inspectionHeader!.status !=
                        AppConstants.STATE_REJECTED) {
                      await _navigateToInspectionTaskDetailScreen(
                          context,
                          widget.inspectionPlanHeader,
                          widget.inspectionHeader,
                          inspectionPlanSectionHeader,
                          inspExecListHeader);
                      await ref
                          .watch(inspectionHeaderProvider.notifier)
                          .fetchInspectionHeaders(widget.inspectionHeader!);
                      await ref
                          .watch(inspectionPlanHeaderProvider.notifier)
                          .selectedInspPlanHeaders(widget.inspectionPlanHeader);
                    } else {
                      await onRejectedFromScreenInsp(
                          inspectionPlanSectionHeader, inspExecListHeader);
                    }
                  } else {
                    await ref
                        .watch(inspectionHeaderProvider.notifier)
                        .fetchInspectionHeaders(widget.inspectionHeader!);
                    await ref
                        .watch(inspectionPlanHeaderProvider.notifier)
                        .selectedInspPlanHeaders(widget.inspectionPlanHeader);
                    await ref.read(inspectionTaskNotifier.notifier).reset(
                        widget.inspectionPlanHeader!.plan_id.toString(), ref);
                    ref
                        .read(inspectionToggleStateProvider.notifier)
                        .setToggleState(false);
                    await _navigateToInspectionTaskDetailScreen(
                        context,
                        widget.inspectionPlanHeader,
                        ref.read(inspectionHeaderProvider),
                        inspectionPlanSectionHeader,
                        inspExecListHeader);
                  }
                }
              }
            };
      // }
    }

/*    else if (widget.inspectionHeader != null) {
      if (widget.inspectionHeader!.start_on.toString() != todayDate) {
        return () {};
      } else {
        return !isInspDisplayVisible()
            ? () {}
            : () {
                if (widget.inspectionPlanHeader != null) {
                  DateTime now = DateTime.now();
                  String formattedTime = DateFormat('HHmmss').format(now);
                  String startAT = widget.inspectionHeader!.start_at.toString();
                  if (int.parse(formattedTime) < int.parse(startAT)) {
                    UIHelper.showErrorDialog(context,
                        description:
                            "${AppLocalizations.of(context)!.cannot_start_task_before} ${formatStartTimeAsClock(startAT)}");
                  } else {
                    _navigateToInspectionTaskDetailScreen(
                        context,
                        widget.inspectionPlanHeader,
                        widget.inspectionHeader,
                        inspectionPlanSectionHeader,
                        inspExecListHeader);
                  }
                }
              };
      }
    } */

    else if (widget.faultHeader != null) {
      return !isFaultDisplayVisible()
          ? () {}
          : () {
              _navigateToFaultScreen(context, widget.faultHeader);
            };
    } else if (widget.jobHeader != null) {
      return !isJobDisplayVisible()
          ? () {}
          : () {
              _navigateToJobScreen(context, widget.jobHeader);
            };
    }

    return () {};
  }

  showConfirmationDialog(
    BuildContext context, {
    String? title,
    required String description,
    final bool dismissible = false,
    required String positiveButtonString,
    VoidCallback? positiveButtonOnTap,
    VoidCallback? cancel,
  }) {
    if (!context.mounted) return;

    showDialog<bool>(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return dismissible;
          },
          child: AlertDialog(
            title: Text(AppLocalizations.of(context)!.alertString),
            content: Text(description),
            actions: [
              TextButton(
                onPressed: () async {
                  if (widget.ciltHeader != null) {
                    await onReject(widget.ciltHeader!, fromDialog: true);
                   // Navigator.of(context).pop();
                  } else if(widget.inspectionHeader != null){
                    await onRejectInsp(widget.inspectionHeader!, fromDialog: true);
                   // Navigator.of(context).pop();
                  }
                },
                child: Text(
                  positiveButtonString,
                  style: TextStyle(
                    color: AppColors.primaryColor,
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  _closeDialog();
                },
                child: Text(
                  AppLocalizations.of(context)!.cancel,
                  style: TextStyle(
                    color: AppColors.primaryColor,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showActioPicherSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              getButtonVisibility(isAcceptVisible(), isAssignVisible(), widget,
                          user_header)["Accept"] ??
                      true
                  ? ListTile(
                      leading:
                          const Icon(Icons.check, color: AppColors.greenColor),
                      title: Text(
                        AppLocalizations.of(context)!.accept,
                        style: const TextStyle(color: AppColors.greenColor),
                      ),
                      onTap: () async {
                        onAccept(widget.ciltHeader!, navigate: true);
                        await ref
                            .watch(ciltHeaderProvider.notifier)
                            .selectedCiltHeaders(widget.ciltHeader!);
                        await ref
                            .watch(ciltPlanHeaderProvider.notifier)
                            .selectedCiltPlanHeaders(widget.ciltPlanHeader);
                        Navigator.of(context).pop();
                      },
                    )
                  : SizedBox(),
              getButtonVisibility(isAcceptVisible(), isAssignVisible(), widget,
                          user_header)["Assign"] ??
                      true
                  ? ListTile(
                      leading: const Icon(Icons.assignment_turned_in_outlined,
                          color: AppColors.blue),
                      title: Text(
                        AppLocalizations.of(context)!.assign,
                        style: const TextStyle(color: AppColors.blue),
                      ),
                      onTap: () async {
                        await _onAssign(widget.ciltHeader!);
                        Navigator.of(context, rootNavigator: true).pop();
                      })
                  : const SizedBox(),
              getButtonVisibility(isAcceptVisible(), isAssignVisible(), widget,
                          user_header)["Reject"] ??
                      true
                  ? ListTile(
                      leading: const Icon(Icons.cancel,
                          color: AppColors.redAccentColor),
                      title: Text(
                        AppLocalizations.of(context)!.reject,
                        style: const TextStyle(color: AppColors.redAccentColor),
                      ),
                      onTap: () async {
                        await onReject(widget.ciltHeader!);
                        Navigator.of(context).pop();
                      },
                    )
                  : const SizedBox(),
            ],
          ),
        );
      },
    );
  }

  void _showActioPicherSheetInsp(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              getButtonVisibilityInsp(
                          isAcceptVisibleInsp(),
                          isAssignVisibleInsp(),
                          widget,
                          user_header)["Accept"] ??
                      true
                  ? ListTile(
                      leading:
                          const Icon(Icons.check, color: AppColors.greenColor),
                      title: Text(
                        AppLocalizations.of(context)!.accept,
                        style: const TextStyle(color: AppColors.greenColor),
                      ),
                      onTap: () async {
                        onAcceptInsp(widget.inspectionHeader!, navigate: true);
                        await ref
                            .watch(inspectionHeaderProvider.notifier)
                            .fetchInspectionHeaders(widget.inspectionHeader!);
                        await ref
                            .watch(inspectionPlanHeaderProvider.notifier)
                            .selectedInspPlanHeaders(
                                widget.inspectionPlanHeader);
                        Navigator.of(context).pop();
                      },
                    )
                  : SizedBox(),
              getButtonVisibilityInsp(
                          isAcceptVisibleInsp(),
                          isAssignVisibleInsp(),
                          widget,
                          user_header)["Assign"] ??
                      true
                  ? ListTile(
                      leading: const Icon(Icons.assignment_turned_in_outlined,
                          color: AppColors.blue),
                      title: Text(
                        AppLocalizations.of(context)!.assign,
                        style: const TextStyle(color: AppColors.blue),
                      ),
                      onTap: () async {
                        await _onAssignInsp(widget.inspectionHeader!);
                        Navigator.of(context, rootNavigator: true).pop();
                      })
                  : const SizedBox(),
              getButtonVisibilityInsp(
                          isAcceptVisibleInsp(),
                          isAssignVisibleInsp(),
                          widget,
                          user_header)["Reject"] ??
                      true
                  ? ListTile(
                      leading: const Icon(Icons.cancel,
                          color: AppColors.redAccentColor),
                      title: Text(
                        AppLocalizations.of(context)!.reject,
                        style: const TextStyle(color: AppColors.redAccentColor),
                      ),
                      onTap: () async {
                        await onRejectInsp(widget.inspectionHeader!);
                        Navigator.of(context).pop();
                      },
                    )
                  : const SizedBox(),
            ],
          ),
        );
      },
    );
  }

  initSharedPreferences() async {
    preferences = await SharedPreferences.getInstance();
  }

  String toCamelCase(String input) {
    if (input.isEmpty) return '';
    return input[0].toUpperCase() + input.substring(1).toLowerCase();
  }

  String convertTimeString(int? time) {
    if (time == null) {
      return "00:00:00";
    }

    int hours = time ~/ 10000;
    int minutes = (time % 10000) ~/ 100;
    int seconds = time % 100; // Extract seconds by taking the remainder

    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String formatStartTimeAsClock(String startAT) {
    if (startAT.length != 6) return "Invalid Time";

    String hours = startAT.substring(0, 2);
    String minutes = startAT.substring(2, 4);
    String seconds = startAT.substring(4, 6);

    return "$hours:$minutes:$seconds"; // Format as HH:mm:ss
  }

  bool isCiltDisplayVisible() {
    // final role = ref.watch(roleProvider);
    // if (role != null) {
    //   if (UIHelper.isDisplay(role.cilt!)) {
    //     return true;
    //   } else {
    //     return false;
    //   }
    // } else {
    //   return false;
    // }
    return true;
  }

  bool isInspDisplayVisible() {
    // final role = ref.watch(roleProvider);
    // if (role != null) {
    //   if (UIHelper.isDisplay(role.cilt!)) {
    //     return true;
    //   } else {
    //     return false;
    //   }
    // } else {
    //   return false;
    // }
    return true;
  }

  bool isCiltExecutionVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isExecute(role.cilt!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isInspExecutionVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isExecute(role.inspection!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isFaultDisplayVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isDisplay(role.fault!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isFaultExecutionVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isExecute(role.fault!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isJobDisplayVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isDisplay(role.task!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isJobExecutionVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isExecute(role.task!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  handleStatusActionButtonsFunctions(String type, JOB_HEADER jobHeader) {
    if (type == 'assign') {
      UIHelper.showConfirmationDialogWithYesOrNo(context,
          description: AppLocalizations.of(context)!.do_you_want_to_assign_job,
          yes: () {
        Navigator.pop(context);
        onAssign(type, jobHeader, Constants.JOB_STATE_ORAS, context);
      }, no: () {
        Navigator.pop(context);
      });
    }
    if (type == 'accept') {
      UIHelper.showConfirmationDialogWithYesOrNo(context,
          description: AppLocalizations.of(context)!.do_you_want_to_accept_job,
          yes: () {
        onYes(type, jobHeader, Constants.JOB_STATE_ACPT, context);
      }, no: () {
        Navigator.pop(context);
      });
    }
    if (type == 'reject') {
      UIHelper.showConfirmationDialogWithYesOrNo(context,
          description: AppLocalizations.of(context)!.do_you_want_to_reject_job,
          yes: () {
        onYes(type, jobHeader, Constants.JOB_STATE_RJCT, context);
      }, no: () {
        Navigator.pop(context);
      });
    } else if (type == 'complete') {
      UIHelper.showConfirmationDialogWithYesOrNo(context,
          description: AppLocalizations.of(context)!
              .do_you_want_to_complete_job, yes: () {
        onYes(type, jobHeader, Constants.JOB_STATE_NOCO, context);
      }, no: () {
        Navigator.pop(context);
      });
    }
  }

  void onAssign(
    String type,
    JOB_HEADER jobHeader,
    String jobStatus,
    BuildContext context,
  ) async {
    var result;
    if (!kIsWeb) {
     if(ScreenType.desktop == UIHelper().getScreenType(context)){
        result = await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (rootDialogContext) => Dialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              child: ConstrainedBox(
                constraints: const BoxConstraints(
                  maxWidth: 600,
                  maxHeight: 800,
                ),
                child: const AssignScreen(
                  isSaveButtonForWeb: true,
                ),
              )));
      }else {
        result =
          await Navigator.push(context, MaterialPageRoute(builder: (context) {
        return const AssignScreen();
      }));
      }
    } else {
      result = await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (rootDialogContext) => Dialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              child: ConstrainedBox(
                constraints: const BoxConstraints(
                  maxWidth: 600,
                  maxHeight: 800,
                ),
                child: const AssignScreen(
                  isSaveButtonForWeb: true,
                ),
              )));
    }
    if (result != null) {
      USER_HEADER user = result as USER_HEADER;
      jobHeader.assigned_to = user.user_id;
      await AppDatabaseManager()
          .update(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));
      if (mounted) {
        onYes(type, jobHeader, jobStatus, context);
      }
    }
  }

  onYes(String type, JOB_HEADER jobHeader, String statusType,
      BuildContext context) async {
    final jobHeaderData = ref.watch(jobHeaderProvider.notifier);
    final documents = ref.watch(getFaultDocumentHeaderProvider.notifier).state;
    JOB_HEADER header = jobHeader;
    JOB_HEADER? headerData =
        await DbHelper.getJobHeaderById(jobHeader.job_id.toString());
    if (headerData != null) {
      await AppDatabaseManager()
          .update(DBInputEntity(JOB_HEADER.TABLE_NAME, header.toJson()));
    } else {
      await AppDatabaseManager()
          .update(DBInputEntity(JOB_HEADER.TABLE_NAME, header.toJson()));
    }
    await jobHeaderData.getJobHeader(
        jobId: jobHeaderData.state.job_id.toString());
    JOB_ACTION? action =
        await DbHelper.getJobActionByJobId(header.job_id.toString());
    if (action != null) {
      action.user_action = statusType;
      await AppDatabaseManager()
          .update(DBInputEntity(JOB_ACTION.TABLE_NAME, action.toJson()));
    } else {
      JOB_ACTION? action;

      action = JOB_ACTION(job_id: header.job_id, user_action: statusType);
      action.fid = header.lid;
      await AppDatabaseManager()
          .insert(DBInputEntity(JOB_ACTION.TABLE_NAME, action.toJson()));
    }
    for (var data in documents) {
      JOB_DOCUMENT newDocument = JOB_DOCUMENT(
          job_id: jobHeader.job_id!.toInt(),
          doc_id: data.doc_id,
          p_mode: AppConstants.add);
      newDocument.fid = jobHeader.lid;
      JOB_DOCUMENT? doc = await DbHelper.getJobSingleDocumentsByJobId(
          jobHeader.job_id.toString(), data.doc_id.toString());
      if (doc != null) {
      } else {
        if (doc != null) {
          DOCUMENT_HEADER? header =
              await DbHelper.getDocumentHeadersByDocsId(doc.doc_id.toString());
          if (header != null) {
            await AppDatabaseManager().insert(
                DBInputEntity(JOB_DOCUMENT.TABLE_NAME, newDocument.toJson()));
          }
        }
      }
    }
    if (mounted) {
      Navigator.pop(context);
      await sendToServer(type, jobHeader, context);
    }
  }

  sendToServer(String type, JOB_HEADER jobHeader, BuildContext context) async {
    if (type == AppConstants.assign) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.assigning_job);
    } else if (type == AppConstants.accept) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.accepting_job);
    } else if (type == AppConstants.reject) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.rejecting_job);
    } else if (type == AppConstants.complete) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.completing_job);
    }
    if (type == AppConstants.complete) {
      Result? result;
      if (!kIsWeb) {
        result = await PAHelper.addOrModifyJobInAsyncMode(context, jobHeader);
      } else {
        result = await PAHelper.addOrModifyJobInSyncMode(context, jobHeader);
      }
      jobHeader.status = Constants.JOB_STATE_NOCO;
      await AppDatabaseManager()
          .update(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));
      final plant = ref.read(plantProvider.notifier).state;
      final plantSection = ref.read(plantSectionProvider.notifier).state;
      final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
      await faultHeaderList.fetchFaultHeaderList(plant);
      final filteredFaultType =
          ref.read(filteredFaultHeaderListProvider.notifier);

      final filterOfFaultType =
          ref.read(filterOfFaultTypeProvider.notifier).state;
      final filterOfFaultCode =
          ref.read(filterOfFaultCodeProvider.notifier).state;
      final filterOfFaultPriorityCode =
          ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
      final filterOfFaultPriority =
          ref.read(filterOfFaultPriorityProvider.notifier).state;
      final statusTypeFaultFilter =
          ref.read(statusTypeFaultFilterProvider.notifier).state;
      final statusFaultFilter =
          ref.read(statusFaultFilterProvider.notifier).state;
      final selectedDateFault = ref.read(selectedCalendarDateProvider);
      final searchFault = ref.read(searchTextProvider.notifier).state;

      if ((searchFault != '') ||
          filterOfFaultType.isNotEmpty ||
          filterOfFaultCode.isNotEmpty ||
          filterOfFaultPriorityCode.isNotEmpty ||
          filterOfFaultPriority.isNotEmpty ||
          statusTypeFaultFilter.isNotEmpty ||
          statusFaultFilter.isNotEmpty ||
          selectedDateFault != null) {
        await filteredFaultType.filteredFaultHeaderList(
            faulttypeList: filterOfFaultCode,
            priorityList: filterOfFaultPriorityCode,
            statusList: statusTypeFaultFilter,
            type: (searchFault != '')
                ? AppConstants.search
                : AppConstants.faultType,
            plantId: plant,
            plantSec: plantSection,
            search: searchFault);
      } else {
        await filteredFaultType.filteredFaultHeaderList(
            type: 'Initial',
            faultList: faultHeaderList.state,
            plantId: plant,
            plantSec: plantSection);
      }
/*      await filteredFaultType.filteredFaultHeaderList(
          type: 'Initial',
          faultList: faultHeaderList.state,
          plantId: plant,
          plantSec: plantSection);*/
      if (result != null) {
        if (result.body['InfoMessage'] != null) {
          if (mounted) {
            Navigator.pop(context);
          }
          if (result.body['InfoMessage'][0]['category'] == 'FAILURE') {
            if (result.body['InfoMessage'][0]['message'] != null) {
              if (context.mounted) {
                UIHelper.showErrorDialog(
                  context,
                  description: result.body['InfoMessage'][0]['message'],
                );
              }
            }
          } else {
            if (result.body['InfoMessage'][0]['message'] != null) {
              if (context.mounted) {
                UIHelper.showErrorDialog(context,
                    description: result.body['InfoMessage'][0]['message']);
              }
            }
          }
        } else {
          Navigator.pop(context);
          // //TODO
          Navigator.pop(context);
        }
      }
    } else {
      if (!(await URLService.isInternetConnected())) {
        if (mounted) {
          return UIHelper.showErrorDialog(
            context,
            description:
                AppLocalizations.of(context)!.noInternetConnectivityString,
          );
        }
      } else {
        if (mounted) {
          Result? result =
              await PAHelper.addOrModifyJobInSyncMode(context, jobHeader);
          if (result != null) {
            if (result.body['InfoMessage'] != null) {
              if (mounted) {
                Navigator.pop(context);
              }
              if (result.body['InfoMessage'][0]['category'] == 'FAILURE') {
                if (result.body['InfoMessage'][0]['message'] != null) {
                  if (context.mounted) {
                    UIHelper.showErrorDialog(
                      context,
                      description: result.body['InfoMessage'][0]['message'],
                    );
                  }
                }
              } else {
                if (result.body['InfoMessage'][0]['message'] != null) {
                  if (context.mounted) {
                    UIHelper.showErrorDialog(context,
                        description: result.body['InfoMessage'][0]['message']);
                  }
                }
              }
            } else {
              if (mounted) {
                Navigator.pop(context);
                // //TODO
                Navigator.pop(context);
              }
              final plant = ref.read(plantProvider.notifier).state;
              final plantSection =
                  ref.read(plantSectionProvider.notifier).state;
              await ref
                  .read(jobHeaderListProvider.notifier)
                  .fetchJobHeaderList(plant);
              final jobHeaderDatata = ref.read(jobHeaderListProvider);

              final filteredJobTypeNotifier =
                  ref.read(filteredJobCreationHeaderListProvider.notifier);

              final searchJob = ref.read(searchTextJobProvider.notifier).state;
              final filterOfJobType =
                  ref.read(filterOfJobTypeProvider.notifier).state;
              final filterOfJobTypeCode =
                  ref.read(filterOfJobTypeCodeProvider.notifier).state;
              final filterOfJobPriority =
                  ref.read(filterOfPriorityProvider.notifier).state;
              final filterOfJobPriorityCode =
                  ref.read(filterOfPriorityCodeProvider.notifier).state;
              final statusJobFilter =
                  ref.read(statusFilterProvider.notifier).state;
              final statusTypeJobFilter =
                  ref.read(statusTypeFilterProvider.notifier).state;
              final selectedDateJob = ref.read(selectedCalendarDateJobProvider);
              if ((searchJob != '') ||
                  filterOfJobType.isNotEmpty ||
                  filterOfJobTypeCode.isNotEmpty ||
                  filterOfJobPriorityCode.isNotEmpty ||
                  filterOfJobPriority.isNotEmpty ||
                  statusTypeJobFilter.isNotEmpty ||
                  statusJobFilter.isNotEmpty ||
                  selectedDateJob != null) {
                await filteredJobTypeNotifier.filteredJobHeaderList(
                  typeList: filterOfJobTypeCode,
                  priorityList: filterOfJobPriorityCode,
                  statusList: statusTypeJobFilter,
                  type: (searchJob != '')
                      ? AppConstants.search
                      : AppConstants.jobType,
                  plantId: plant,
                  plantSec: plantSection,
                  search: searchJob,
                );
              } else {
                await filteredJobTypeNotifier.filteredJobHeaderList(
                    type: 'Initial',
                    jobList: jobHeaderDatata,
                    plantId: plant,
                    plantSec: plantSection);
              }

/*              await filteredjobTypeNotifier.filteredJobHeaderList(
                  type: 'Initial',
                  jobList: jobHeaderDatata,
                  plantId: plant,
                  plantSec: plantSection);*/
              setState(() {});
            }
          }
        }
      }
    }
  }

  Future<void> onAssigned(CILT_EXEC_HEADER ciltHeader, ciltPlanSectionHeader,
      ciltExecListHeader) async {
    if (ciltHeader.status == AppConstants.STATE_ASSIGNED &&
        user_header?.user_id == widget.ciltHeader!.assigned_to) {
      widget.onTap(
        InteractiveItemModel(
          type: "ACCEPT_REJECT",
          data: {
            "type": "ACCEPT_REJECT",
            "index": widget.index,
            "isCilt": true,
            "isInsp": false,
            "acceptCondition": getButtonVisibility(isAcceptVisible(),
                    isAssignVisible(), widget, user_header)["Accept"] ??
                true,
            "assignCondition": getButtonVisibility(isAcceptVisible(),
                    isAssignVisible(), widget, user_header)["Assign"] ??
                true,
            "rejectCondition": getButtonVisibility(isAcceptVisible(),
                    isAssignVisible(), widget, user_header)["Reject"] ??
                true,
            "onAcceptPressed": () async {
              await onAccept(widget.ciltHeader!, navigate: true);
              await resettingUI();
            },
            "onAssignPressed": () async {
              await _onAssign(widget.ciltHeader!);
              await onAssigned(widget.ciltHeader!, ciltPlanSectionHeader,
                  ciltExecListHeader);
            },
            "onRejectPressed": () async {
              await onReject(widget.ciltHeader!);
              if (user_header?.user_id == widget.ciltHeader!.assigned_to) {
                await onRejected(ciltPlanSectionHeader, ciltExecListHeader);
/*                await _navigateToCiltTaskDetailScreen(
                    context,
                    widget.ciltPlanHeader,
                    widget.ciltHeader,
                    ciltPlanSectionHeader,
                    ciltExecListHeader);
                await ref
                    .watch(ciltHeaderProvider.notifier)
                    .selectedCiltHeaders(widget.ciltHeader!);
                await ref
                    .watch(ciltPlanHeaderProvider.notifier)
                    .selectedCiltPlanHeaders(widget.ciltPlanHeader);*/
              }
            },
          },
        ),
      );
    } else {
      await ref
          .watch(ciltHeaderProvider.notifier)
          .selectedCiltHeaders(widget.ciltHeader!);
      await ref
          .watch(ciltPlanHeaderProvider.notifier)
          .selectedCiltPlanHeaders(widget.ciltPlanHeader);
      ref
          .read(ciltTaskNotifier.notifier)
          .reset(widget.ciltPlanHeader!.plan_id.toString(), ref);
      ref.read(ciltToggleStateProvider.notifier).setToggleState(false);
      await _navigateToCiltTaskDetailScreen(
          context,
          widget.ciltPlanHeader,
          ref.read(ciltHeaderProvider),
          ciltPlanSectionHeader,
          ciltExecListHeader);
    }
  }

  Future<void> onAssignedInsp(INSP_EXEC_HEADER inspHeader,
      inspPlanSectionHeader, inspExecListHeader) async {
    if (inspHeader.status == AppConstants.STATE_ASSIGNED &&
        user_header?.user_id == widget.inspectionHeader!.assigned_to) {
      widget.onTap(
        InteractiveItemModel(
          type: "ACCEPT_REJECT",
          data: {
            "type": "ACCEPT_REJECT",
            "index": widget.index,
            "isCilt": false,
            "isInsp": true,
            "acceptCondition": getButtonVisibilityInsp(isAcceptVisibleInsp(),
                    isAssignVisibleInsp(), widget, user_header)["Accept"] ??
                true,
            "assignCondition": getButtonVisibilityInsp(isAcceptVisibleInsp(),
                    isAssignVisibleInsp(), widget, user_header)["Assign"] ??
                true,
            "rejectCondition": getButtonVisibilityInsp(isAcceptVisibleInsp(),
                    isAssignVisibleInsp(), widget, user_header)["Reject"] ??
                true,
            "onAcceptPressed": () async {
              await onAcceptInsp(widget.inspectionHeader!, navigate: true);
              await resettingUIInsp();
            },
            "onAssignPressed": () async {
              await _onAssignInsp(widget.inspectionHeader!);
              await onAssignedInsp(widget.inspectionHeader!,
                  inspPlanSectionHeader, inspExecListHeader);
            },
            "onRejectPressed": () async {
              await onRejectInsp(widget.inspectionHeader!);
              if (user_header?.user_id ==
                  widget.inspectionHeader!.assigned_to) {
                await onRejectedInsp(inspPlanSectionHeader, inspExecListHeader);
              }
            },
          },
        ),
      );
    } else {
      await ref
          .watch(inspectionHeaderProvider.notifier)
          .fetchInspectionHeaders(widget.inspectionHeader!);
      await ref
          .watch(inspectionPlanHeaderProvider.notifier)
          .selectedInspPlanHeaders(widget.inspectionPlanHeader);
      ref
          .read(inspectionTaskNotifier.notifier)
          .reset(widget.inspectionPlanHeader!.plan_id.toString(), ref);
      ref.read(inspectionToggleStateProvider.notifier).setToggleState(false);
      await _navigateToInspectionTaskDetailScreen(
          context,
          widget.inspectionPlanHeader,
          ref.read(inspectionHeaderProvider),
          inspPlanSectionHeader,
          inspExecListHeader);
    }
  }

  Future<void> onRejected(ciltPlanSectionHeader, ciltExecListHeader) async {
    if (user_header?.user_id == widget.ciltHeader!.assigned_to) {
      /*  if (widget.ciltHeader!.assigned_to == null ||
          ((widget.ciltHeader?.status == AppConstants.STATE_ASSIGNED) &&
              user_header?.user_id == widget.ciltHeader!.assigned_to)) {
        if (ScreenType.desktop == UIHelper().getScreenType(context)) {
          widget.onTap(
            InteractiveItemModel(
              type: "ACCEPT_REJECT",
              data: {
                "type": "ACCEPT_REJECT",
                "index": widget.index,
                "acceptCondition": getButtonVisibility(isAcceptVisible(),
                        isAssignVisible(), widget, user_header)["Accept"] ??
                    true,
                "assignCondition": getButtonVisibility(isAcceptVisible(),
                        isAssignVisible(), widget, user_header)["Assign"] ??
                    true,
                "rejectCondition": getButtonVisibility(isAcceptVisible(),
                        isAssignVisible(), widget, user_header)["Reject"] ??
                    true,
                "onAcceptPressed": () async {
                  await onAccept(widget.ciltHeader!, navigate: true);
                  await ref
                      .watch(ciltHeaderProvider.notifier)
                      .selectedCiltHeaders(widget.ciltHeader!);
                  await ref
                      .watch(ciltPlanHeaderProvider.notifier)
                      .selectedCiltPlanHeaders(widget.ciltPlanHeader);
                },
                "onAssignPressed": () async {
                  await _onAssign(widget.ciltHeader!);
                  await onAssigned(
                    ciltPlanSectionHeader,
                    ciltExecListHeader,
                  );
                },
                "onRejectPressed": () async {
                  await onReject(widget.ciltHeader!);
                  if (user_header?.user_id == widget.ciltHeader!.assigned_to) {
                    await onRejected(ciltPlanSectionHeader, ciltExecListHeader);
                    */ /*         await _navigateToCiltTaskDetailScreen(
                                      context,
                                      widget.ciltPlanHeader,
                                      widget.ciltHeader,
                                      ciltPlanSectionHeader,
                                      ciltExecListHeader);
                                  await ref
                                      .watch(ciltHeaderProvider.notifier)
                                      .selectedCiltHeaders(widget.ciltHeader!);
                                  await ref
                                      .watch(ciltPlanHeaderProvider.notifier)
                                      .selectedCiltPlanHeaders(
                                          widget.ciltPlanHeader);*/ /*
                  }
                },
              },
            ),
          );
        } else {
          _showActioPicherSheet(context);
        }
      }*/

      widget.onTap(InteractiveItemModel(type: "", data: {}));
    } else {
      await ref
          .watch(ciltHeaderProvider.notifier)
          .selectedCiltHeaders(widget.ciltHeader!);
      await ref
          .watch(ciltPlanHeaderProvider.notifier)
          .selectedCiltPlanHeaders(widget.ciltPlanHeader);
      ref
          .read(ciltTaskNotifier.notifier)
          .reset(widget.ciltPlanHeader!.plan_id.toString(), ref);
      ref.read(ciltToggleStateProvider.notifier).setToggleState(false);
      await _navigateToCiltTaskDetailScreen(
          context,
          widget.ciltPlanHeader,
          ref.read(ciltHeaderProvider),
          ciltPlanSectionHeader,
          ciltExecListHeader);
    }
  }

  Future<void> onRejectedInsp(inspPlanSectionHeader, inspExecListHeader) async {
    if (user_header?.user_id == widget.inspectionHeader!.assigned_to) {
      widget.onTap(InteractiveItemModel(type: "", data: {}));
    } else {
      await ref
          .watch(inspectionHeaderProvider.notifier)
          .fetchInspectionHeaders(widget.inspectionHeader!);
      await ref
          .watch(inspectionPlanHeaderProvider.notifier)
          .selectedInspPlanHeaders(widget.inspectionPlanHeader);
      ref
          .read(inspectionTaskNotifier.notifier)
          .reset(widget.inspectionPlanHeader!.plan_id.toString(), ref);
      ref.read(inspectionToggleStateProvider.notifier).setToggleState(false);
      await _navigateToInspectionTaskDetailScreen(
          context,
          widget.inspectionPlanHeader,
          ref.read(inspectionHeaderProvider),
          inspPlanSectionHeader,
          inspExecListHeader);
    }
  }

  Future<void> onRejectedFromScreen(
      ciltPlanSectionHeader, ciltExecListHeader) async {
    if (user_header?.user_id == widget.ciltHeader!.assigned_to) {
     if(ScreenType.desktop == UIHelper().getScreenType(context)){ widget.onTap(
        InteractiveItemModel(
          type: "ACCEPT_REJECT",
          data: {
            "type": "ACCEPT_REJECT",
            "index": widget.index,
            "isCilt": true,
            "isInsp": false,
            "acceptCondition": getButtonVisibility(isAcceptVisible(),
                    isAssignVisible(), widget, user_header)["Accept"] ??
                true,
            "assignCondition": getButtonVisibility(isAcceptVisible(),
                    isAssignVisible(), widget, user_header)["Assign"] ??
                true,
            "rejectCondition": getButtonVisibility(isAcceptVisible(),
                    isAssignVisible(), widget, user_header)["Reject"] ??
                true,
            "onAcceptPressed": () async {
              await onAccept(widget.ciltHeader!, navigate: true);
              await resettingUI();
              /*     await ref
                  .watch(ciltHeaderProvider.notifier)
                  .selectedCiltHeaders(widget.ciltHeader!);
              await ref
                  .watch(ciltPlanHeaderProvider.notifier)
                  .selectedCiltPlanHeaders(
                  widget.ciltPlanHeader);
              await _navigateToCiltTaskDetailScreen(
                  context,
                  widget.ciltPlanHeader,
                  ref.read(ciltHeaderProvider),
                  ciltPlanSectionHeader,
                  ciltExecListHeader);*/
            },
            "onAssignPressed": () async {
              await _onAssign(widget.ciltHeader!);
              await onAssigned(
                widget.ciltHeader!,
                ciltPlanSectionHeader,
                ciltExecListHeader,
              );
            },
            "onRejectPressed": () async {
              await onReject(widget.ciltHeader!);
              if (user_header?.user_id == widget.ciltHeader!.assigned_to) {
                await onRejected(ciltPlanSectionHeader, ciltExecListHeader);
              }
            },
          },
        )
      );} else{
        _showActioPicherSheet(context);
        }
    } else {
      await ref
          .watch(ciltHeaderProvider.notifier)
          .selectedCiltHeaders(widget.ciltHeader!);
      await ref
          .watch(ciltPlanHeaderProvider.notifier)
          .selectedCiltPlanHeaders(widget.ciltPlanHeader);
      ref
          .read(ciltTaskNotifier.notifier)
          .reset(widget.ciltPlanHeader!.plan_id.toString(), ref);
      ref.read(ciltToggleStateProvider.notifier).setToggleState(false);
      await _navigateToCiltTaskDetailScreen(
          context,
          widget.ciltPlanHeader,
          ref.read(ciltHeaderProvider),
          ciltPlanSectionHeader,
          ciltExecListHeader);
    }
  }

  Future<void> onRejectedFromScreenInsp(
      inspPlanSectionHeader, inspExecListHeader) async {
    if (user_header?.user_id == widget.inspectionHeader!.assigned_to) {
      if(ScreenType.desktop == UIHelper().getScreenType(context)){widget.onTap(
        InteractiveItemModel(
          type: "ACCEPT_REJECT",
          data: {
            "type": "ACCEPT_REJECT",
            "index": widget.index,
            "isCilt": false,
            "isInsp": true,
            "acceptCondition": getButtonVisibilityInsp(isAcceptVisibleInsp(),
                    isAssignVisibleInsp(), widget, user_header)["Accept"] ??
                true,
            "assignCondition": getButtonVisibilityInsp(isAcceptVisibleInsp(),
                    isAssignVisibleInsp(), widget, user_header)["Assign"] ??
                true,
            "rejectCondition": getButtonVisibilityInsp(isAcceptVisibleInsp(),
                    isAssignVisibleInsp(), widget, user_header)["Reject"] ??
                true,
            "onAcceptPressed": () async {
              await onAcceptInsp(widget.inspectionHeader!, navigate: true);
              await resettingUIInsp();
            },
            "onAssignPressed": () async {
              await _onAssignInsp(widget.inspectionHeader!);
              await onAssignedInsp(
                widget.inspectionHeader!,
                inspPlanSectionHeader,
                inspExecListHeader,
              );
            },
            "onRejectPressed": () async {
              await onRejectInsp(widget.inspectionHeader!);
              if (user_header?.user_id ==
                  widget.inspectionHeader!.assigned_to) {
                await onRejectedInsp(inspPlanSectionHeader, inspExecListHeader);
              }
            },
          },
        ),
      );}else{
        _showActioPicherSheetInsp(context);
      }
    } else {
      await ref
          .watch(inspectionHeaderProvider.notifier)
          .fetchInspectionHeaders(widget.inspectionHeader!);
      await ref
          .watch(inspectionPlanHeaderProvider.notifier)
          .selectedInspPlanHeaders(widget.inspectionPlanHeader);
      await ref
          .read(inspectionTaskNotifier.notifier)
          .reset(widget.inspectionPlanHeader!.plan_id.toString(), ref);
      ref.read(inspectionToggleStateProvider.notifier).setToggleState(false);
      await _navigateToInspectionTaskDetailScreen(
          context,
          widget.inspectionPlanHeader,
          ref.read(inspectionHeaderProvider),
          inspPlanSectionHeader,
          inspExecListHeader);
    }
  }
}

class JobItem {
  final String type;
  final String title;
  final String status;
  final String priority;
  final String time;

  JobItem({
    required this.type,
    required this.title,
    required this.status,
    required this.priority,
    required this.time,
  });
}
