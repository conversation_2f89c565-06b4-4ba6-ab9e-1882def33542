import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../utils/app_colors.dart';
import 'package:flutter/foundation.dart';

class AcceptRejectPage extends ConsumerStatefulWidget {
  final bool acceptCondtion;
  final bool assignCondition;
  final bool rejectCondition;
  final bool isCilt;
  final bool isInsp;
  final VoidCallback? onAcceptPressed;
  final VoidCallback? onAssignPressed;
  final VoidCallback? onRejectPressed;

  const AcceptRejectPage({
    required this.acceptCondtion,
    required this.assignCondition,
    required this.rejectCondition,
    required this.isCilt,
    required this.isInsp,
    required this.onAcceptPressed,
    required this.onAssignPressed,
    required this.onRejectPressed,
    super.key,
  });

  @override
  _AcceptRejectPageState createState() => _AcceptRejectPageState();
}

class _AcceptRejectPageState extends ConsumerState<AcceptRejectPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Expanded(
            child: Center(
              child: SizedBox(
                width: 320,
                child: Card(
                  elevation: 8,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        (widget.acceptCondtion ||
                                widget.assignCondition ||
                                widget.rejectCondition)
                            ? Padding(
                                padding: const EdgeInsets.only(bottom: 20.0),
                                child: Center(
                                  child: Text(
                                    widget.isCilt
                                        ? 'To perform this CILT you need to accept or reject the CILT'
                                        : widget.isInsp
                                            ? 'To perform this inspection you need to accept or reject the inspection'
                                            : 'To perform this action you need to accept or reject',
                                    style:
                                        Theme.of(context).textTheme.titleMedium,
                                  ),
                                ),
                              )
                            : Center(
                                child: Text(
                                  widget.isCilt
                                      ? 'This CILT has been rejected !'
                                      : widget.isInsp
                                          ? 'This inspection has been rejected !'
                                          : 'This action has been rejected !',
                                  style:
                                      Theme.of(context).textTheme.titleMedium,
                                ),
                              ),
                        widget.acceptCondtion
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                    Expanded(
                                      child: Padding(
                                        padding: kIsWeb
                                            ? const EdgeInsets.symmetric(
                                                vertical: 4.0)
                                            : const EdgeInsets.symmetric(
                                              vertical: 4.0),
                                        child: ElevatedButton(
                                          style: ElevatedButton.styleFrom(
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(20),
                                              ),
                                              backgroundColor: AppColors.grey,
                                              side: BorderSide(
                                                  color: AppColors.grey)),
                                          onPressed: widget.onAcceptPressed,
                                          child: Text(
                                            AppLocalizations.of(context)!
                                                .accept,
                                            style: const TextStyle(
                                                color: AppColors.greenColor),
                                          ),
                                        ),
                                      ),
                                    )
                                  ])
                            : const SizedBox.shrink(),
                        widget.assignCondition
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Padding(
                                      padding: kIsWeb
                                          ? const EdgeInsets.symmetric(
                                              vertical: 4.0)
                                          : const EdgeInsets.symmetric(
                                              vertical: 4.0),
                                      child: ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: AppColors.grey,
                                          side:
                                              BorderSide(color: AppColors.grey),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(20),
                                          ),
                                        ),
                                        onPressed: widget.onAssignPressed,
                                        child: Text(
                                          AppLocalizations.of(context)!.assign,
                                          style: const TextStyle(
                                              color: AppColors.blue),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              )
                            : SizedBox(),
                        const SizedBox.shrink(),
                        widget.rejectCondition
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Padding(
                                      padding: kIsWeb
                                          ? const EdgeInsets.symmetric(
                                              vertical: 4.0)
                                          : const EdgeInsets.symmetric(
                                              vertical: 4.0),
                                      child: ElevatedButton(
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: AppColors.grey,
                                            side: BorderSide(
                                                color: AppColors.grey),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                          ),
                                          onPressed: widget.onRejectPressed,
                                          child: Text(
                                            AppLocalizations.of(context)!
                                                .reject,
                                            style: const TextStyle(
                                                color:
                                                    AppColors.redAccentColor),
                                          )),
                                    ),
                                  ),
                                ],
                              )
                            : SizedBox(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
