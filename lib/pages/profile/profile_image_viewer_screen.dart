import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../utils/app_colors.dart';

class ProfileImageViewerScreen extends ConsumerStatefulWidget {
  final Uint8List? image;
  const ProfileImageViewerScreen({this.image});

  @override
  _ProfileImageViewerScreenWidgetState createState() =>
      _ProfileImageViewerScreenWidgetState();
}

class _ProfileImageViewerScreenWidgetState
    extends ConsumerState<ProfileImageViewerScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Safe<PERSON><PERSON>(
        child: Center(
            child: InteractiveViewer(
          child: widget.image != null
              ? Image.memory(
                  widget.image!,
                  fit: BoxFit.cover,
                )
              : Image.asset("assets/images/empty_profile.png"),
        )),
      ),
    );
  }
}
