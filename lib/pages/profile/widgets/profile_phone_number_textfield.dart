import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:rounds/pages/profile/profile_general_tab.dart';
import 'package:rounds/providers/register_provider.dart';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:rounds/utils/utils.dart';

import '../../../helpers/ui_helper.dart';
import '../../../utils/app_colors.dart';

class ProfilePhoneNumberTextField extends ConsumerStatefulWidget {
  ProfilePhoneNumberTextField(
      {Key? key, required this.controller, this.readOnly = false})
      : super(key: key);

  final TextEditingController controller;
  bool readOnly;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ProfilePhoneNumberTextFieldState();
}

class _ProfilePhoneNumberTextFieldState
    extends ConsumerState<ProfilePhoneNumberTextField> {
  String initialCountry = 'IN';

  @override
  void initState() {
    loadFromJson();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      absorbing: widget.readOnly,
      child: InternationalPhoneNumberInput(
        selectorConfig: SelectorConfig(
          selectorType: UIHelper().getScreenType(context) == ScreenType.desktop
              ? PhoneInputSelectorType.DROPDOWN
              : PhoneInputSelectorType.BOTTOM_SHEET,
          showFlags: true,
          trailingSpace: false,
        ),
        textFieldController: widget.controller,
        keyboardType: const TextInputType.numberWithOptions(
          signed: false,
          decimal: false,
        ),
        onInputChanged: (phone) {
          initialCountry = phone.dialCode ?? 'IN';
          ref
              .read(editPhoneNumberProvider.notifier)
              .getPhoneNumber(widget.controller.text);
        },
        searchBoxDecoration: InputDecoration(
          label: Text(
            "Select Country",
            style: UIHelper.labelStyle(),
          ),
          labelStyle: const TextStyle(
              color: AppColors.blackTitleText,
              fontSize: 18,
              fontWeight: FontWeight.w700),
          hintStyle: UIHelper.valueStyle14(),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide(
              color: AppColors.grey,
              width: 1,
            ),
          ),
        ),
        validator: (number) {
          if ((number ?? "").isNotEmpty) {
            String enteredNumber = number!;
            String digitsOnly = enteredNumber.replaceAll(RegExp(r'\D'), '');
            int requiredLength = countryPhoneLengths[initialCountry] ?? 0;
            if (requiredLength > 0 && digitsOnly.length != requiredLength) {
              return "Phone number must be $requiredLength digits for $initialCountry";
            }
            return null;
          }
        },
        textStyle: UIHelper.valueStyle14(),
        initialValue: PhoneNumber(
          isoCode: initialCountry,
          dialCode: "+91",
        ),
        inputBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide(
            color: AppColors.grey,
            width: 1,
          ),
        ),
        inputDecoration: InputDecoration(
          fillColor: AppColors.backgroundGrey,
          hintText: AppLocalizations.of(context)!.phone_number,
          hintStyle: UIHelper.valueStyle14(),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5),
            borderSide: BorderSide(
              color: AppColors.grey,
              width: 1,
            ),
          ),
        ),
        autoFocusSearch: true,
      ),
    );
  }

  Future<String> loadFromJson() async {
    return await rootBundle.loadString('assets/json/country_list.json');
  }

  final Map<String, int> countryPhoneLengths = {
    'AF': 9,
    'AX': 7,
    'AL': 9,
    'DZ': 9,
    'AS': 10,
    'AD': 6,
    'AO': 9,
    'AI': 10,
    'AQ': 0,
    'AG': 7,
    'AR': 10,
    'AM': 8,
    'AW': 7,
    'AU': 9,
    'AT': 10,
    'AZ': 9,
    'BS': 10,
    'BH': 8,
    'BD': 10,
    'BB': 7,
    'BY': 9,
    'BE': 9,
    'BZ': 7,
    'BJ': 8,
    'BM': 7,
    'BT': 8,
    'BO': 8,
    'BA': 8,
    'BW': 8,
    'BR': 10,
    'IO': 6,
    'BN': 7,
    'BG': 8,
    'BF': 8,
    'BI': 7,
    'KH': 9,
    'CM': 9,
    'CA': 10,
    'CV': 7,
    'KY': 7,
    'CF': 8,
    'TD': 8,
    'CL': 9,
    'CN': 11,
    'CX': 8,
    'CC': 8,
    'CO': 10,
    'KM': 7,
    'CG': 9,
    'CD': 9,
    'CK': 8,
    'CR': 8,
    'CI': 8,
    'HR': 9,
    'CU': 8,
    'CY': 8,
    'CZ': 9,
    'DK': 8,
    'DJ': 7,
    'DM': 7,
    'DO': 10,
    'EC': 9,
    'EG': 10,
    'SV': 8,
    'GQ': 8,
    'ER': 7,
    'EE': 8,
    'ET': 9,
    'FK': 7,
    'FO': 6,
    'FJ': 7,
    'FI': 10,
    'FR': 9,
    'GF': 9,
    'PF': 8,
    'GA': 8,
    'GM': 7,
    'GE': 9,
    'DE': 10,
    'GH': 9,
    'GI': 7,
    'GR': 10,
    'GL': 6,
    'GD': 7,
    'GP': 9,
    'GU': 10,
    'GT': 8,
    'GG': 10,
    'GN': 9,
    'GW': 7,
    'GY': 7,
    'HT': 9,
    'VA': 6,
    'HN': 8,
    'HK': 8,
    'HU': 9,
    'IS': 7,
    'IN': 10,
    'ID': 10,
    'IR': 10,
    'IQ': 10,
    'IE': 9,
    'IM': 8,
    'IL': 9,
    'IT': 10,
    'JM': 10,
    'JP': 10,
    'JE': 9,
    'JO': 9,
    'KZ': 10,
    'KE': 9,
    'KI': 7,
    'KP': 10,
    'KR': 10,
    'KW': 8,
    'KG': 9,
    'LA': 8,
    'LV': 8,
    'LB': 7,
    'LS': 8,
    'LR': 7,
    'LY': 8,
    'LI': 8,
    'LT': 8,
    'LU': 8,
    'MO': 8,
    'MK': 8,
    'MG': 8,
    'MW': 8,
    'MY': 10,
    'MV': 7,
    'ML': 8,
    'MT': 8,
    'MH': 7,
    'MQ': 9,
    'MR': 8,
    'MU': 8,
    'YT': 8,
    'MX': 10,
    'FM': 7,
    'MD': 8,
    'MC': 8,
    'MN': 8,
    'ME': 8,
    'MS': 7,
    'MA': 9,
    'MZ': 9,
    'MM': 9,
    'NA': 8,
    'NR': 7,
    'NP': 10,
    'NL': 9,
    'AN': 8,
    'NC': 8,
    'NZ': 8,
    'NI': 8,
    'NE': 8,
    'NG': 10,
    'NU': 7,
    'NF': 7,
    'MP': 8,
    'NO': 8,
    'OM': 8,
    'PK': 10,
    'PW': 7,
    'PS': 8,
    'PA': 8,
    'PG': 8,
    'PY': 8,
    'PE': 9,
    'PH': 10,
    'PN': 7,
    'PL': 9,
    'PT': 9,
    'PR': 10,
    'QA': 8,
    'RO': 10,
    'RU': 10,
    'RW': 9,
    'RE': 8,
    'BL': 8,
    'SH': 8,
    'KN': 10,
    'LC': 10,
    'MF': 8,
    'PM': 8,
    'VC': 10,
    'WS': 8,
    'SM': 8,
    'ST': 8,
    'SA': 9,
    'SN': 9,
    'RS': 9,
    'SC': 7,
    'SL': 8,
    'SG': 8,
    'SK': 9,
    'SI': 8,
    'SB': 7,
    'SO': 8,
    'ZA': 9,
    'SS': 8,
    'GS': 8,
    'ES': 9,
    'LK': 9,
    'SD': 9,
    'SR': 8,
    'SJ': 7,
    'SZ': 8,
    'SE': 9,
    'CH': 9,
    'SY': 9,
    'TW': 9,
    'TJ': 9,
    'TZ': 9,
    'TH': 9,
    'TL': 8,
    'TG': 8,
    'TK': 7,
    'TO': 8,
    'TT': 10,
    'TN': 8,
    'TR': 10,
    'TM': 9,
    'TC': 10,
    'TV': 7,
    'UG': 9,
    'UA': 9,
    'AE': 9,
    'GB': 10,
    'US': 10,
    'UY': 8,
    'UZ': 9,
    'VU': 7,
    'VE': 10,
    'VN': 10,
    'VG': 10,
    'VI': 10,
    'WF': 8,
    'YE': 9,
    'ZM': 9,
    'ZW': 9,
  };
}
