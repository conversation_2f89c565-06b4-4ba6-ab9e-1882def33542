import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/ASSET_DOCUMENT.dart';
import 'package:rounds/be/CILT_EXEC_ACTION.dart';
import 'package:rounds/be/CILT_EXEC_DOC.dart';
import 'package:rounds/be/CILT_EXEC_SEC.dart';
import 'package:rounds/be/CILT_EXEC_TASK.dart';
import 'package:rounds/be/CILT_PLAN_DOC.dart';
import 'package:rounds/be/CILT_PLAN_HEADER.dart';
import 'package:rounds/be/CILT_SECTION.dart';
import 'package:rounds/be/CILT_TASK.dart';
import 'package:rounds/be/CILT_TASK_DOC.dart';
import 'package:rounds/be/DOCUMENT_ATTACHMENT.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:rounds/be/FAULT_ACTION.dart';
import 'package:rounds/be/FAULT_DOCUMENT.dart';
import 'package:rounds/be/INSPECTION_PLAN_DOC.dart';
import 'package:rounds/be/INSPECTION_PLAN_HEADER.dart';
import 'package:rounds/be/INSPECTION_SECTION.dart';
import 'package:rounds/be/INSPECTION_TASK.dart';
import 'package:rounds/be/INSPECTION_TASK_DOC.dart';
import 'package:rounds/be/INSP_EXEC_ACTION.dart';
import 'package:rounds/be/INSP_EXEC_DOC.dart';
import 'package:rounds/be/INSP_EXEC_HEADER.dart';
import 'package:rounds/be/INSP_EXEC_SEC.dart';
import 'package:rounds/be/INSP_EXEC_TASK.dart';
import 'package:rounds/be/JOB_ACTION.dart';
import 'package:rounds/be/JOB_DOCUMENT.dart';
import 'package:rounds/be/LOCATION_DOCUMENT.dart';
import 'package:rounds/be/LOCATION_HEADER.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/pages/downloadDataPage.dart';
import 'package:rounds/pages/login/login.dart';
import 'package:rounds/pages/profile/profile_general_tab.dart';
import 'package:rounds/pages/profile/profile_image_viewer_screen.dart';
import 'package:rounds/pages/profile/profile_user_preference_tab.dart';
import 'package:rounds/utils/utils.dart';
import 'package:rounds/widgets/version_info.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../be/APP_SETTING_HEADER.dart';
import '../../be/ASSET_HEADER.dart';
import '../../be/CILT_EXEC_HEADER.dart';
import '../../be/FAULT_HEADER.dart';
import '../../be/JOB_HEADER.dart';
import '../../be/KPI_HEADER.dart';
import '../../be/SHIFT_HEADER.dart';
import '../../helpers/db_helper.dart';
import '../../helpers/pa_helper.dart';
import '../../helpers/ui_helper.dart';
import '../../models/intractive_Item_Model.dart';
import '../../providers/fault/fault_header_provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/app_colors.dart';
import '../assets/assets.dart';
import '../fault/fault_screen.dart';
import '../inspection/inspection_screen.dart';
import '../job_creation/job_creation_screen.dart';
import '../login/login_state/login_state.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';

final editProfileScreenProvider =
    StateNotifierProvider<EditProfileScreenNotifier, bool>((ref) {
  return EditProfileScreenNotifier();
});

class EditProfileScreenNotifier extends StateNotifier<bool> {
  EditProfileScreenNotifier() : super(true);

  void getEditProfileScreenEnable(bool data) async {
    try {
      bool enableProfileFields = data;
      state = enableProfileFields;
    } catch (e) {
      Logger.logError('EditProfileScreenNotifier', 'getEditProfileScreenEnable',
          e.toString());
    }
  }
}

final editProfileDetailScreenProvider =
    StateNotifierProvider<EditProfileDetailScreenNotifier, bool>((ref) {
  return EditProfileDetailScreenNotifier();
});

class EditProfileDetailScreenNotifier extends StateNotifier<bool> {
  EditProfileDetailScreenNotifier() : super(true);

  void getEditProfileDetailScreenEnable(bool data) async {
    try {
      bool enableProfileFields = data;
      state = enableProfileFields;
    } catch (e) {
      Logger.logError('EditProfileDetailScreenNotifier',
          'getEditProfileDetailScreenEnable', e.toString());
    }
  }
}

final profileImageProvider =
    StateNotifierProvider<ProfileImageNotifier, Uint8List?>((ref) {
  return ProfileImageNotifier();
});

class ProfileImageNotifier extends StateNotifier<Uint8List?> {
  ProfileImageNotifier() : super(null);

  void getImage(Uint8List data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError('ProfileImageNotifier', 'getImage', e.toString());
    }
  }
}

class ProfileScreen extends ConsumerStatefulWidget {
  final USER_HEADER userHeader;
  const ProfileScreen({super.key, required this.userHeader});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen>
    with SingleTickerProviderStateMixin {
  Future? fetchInitialPlantDetails;
  Uint8List? image;

  late TabController _tabController;
  String plantData = '';
  List<String> plantSectionsData = [];
  String shiftData = '';

  @override
  void initState() {
    // TODO: implement initState
    Future.delayed((Duration.zero), () async {
      List<APP_SETTING_HEADER> list = await DbHelper.getAppSettingHeader();
      if (list.isNotEmpty) {
        for (var item in list) {
          if (item.prop_name == 'plantId') {
            plantData = item.prop_value.toString();
          } else if (item.prop_name == 'sectionId') {
            List<dynamic> decodedList = jsonDecode(item.prop_value.toString());
            List<String> sectionIds =
                decodedList.map((e) => e.toString()).toList();
            plantSectionsData = sectionIds;
          } else if (item.prop_name == 'shiftCode') {
            shiftData = ' ${item.prop_value.toString()}';
          }
        }
      }
    });

    if (widget.userHeader.phone != null) {
      ref
          .read(editPhoneNumberProvider.notifier)
          .getPhoneNumber(widget.userHeader.phone!);
    }
    if (ref.read(profileImageProvider) != null) {
      Future.delayed((const Duration(milliseconds: 100)), () async {
        if(kIsWeb){
          String? imageString = await DbHelper()
            .getAttachmentFromIndexDbByUid(widget.userHeader.user_id!);
        if (imageString != null) {
          ref
              .read(profileImageProvider.notifier)
              .getImage(base64Decode(imageString));
          image = base64Decode(imageString);
        }
        }
      });
    }
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    _tabController.addListener(() async {
      if (!_tabController.indexIsChanging) {
        if (_tabController.index == 1) {
          bool validate = validateData(context);
          if (validate) {
            bool data = await onClickBack();
            if (data) {
              if (mounted) {
                UIHelper.showConfirmationDialogWithYesOrNo(context,
                    description: AppLocalizations.of(context)!
                        .change_of_user_preference_save_confirmation, yes: () {
                  Navigator.of(context, rootNavigator: true).pop();
                  sendData(context);
                }, no: () async {
                  Navigator.of(context, rootNavigator: true).pop();
                  List<APP_SETTING_HEADER> list =
                      await DbHelper.getAppSettingHeader();
                  if (list.isNotEmpty) {
                    for (var item in list) {
                      if (item.prop_name == 'plantId') {
                        ref
                            .read(plantProvider.notifier)
                            .setPlant(item.prop_value.toString());
                      } else if (item.prop_name == 'sectionId') {
                        List<dynamic> decodedList =
                            jsonDecode(item.prop_value.toString());
                        List<String> sectionIds =
                            decodedList.map((e) => e.toString()).toList();
                        ref
                            .read(plantSectionProvider.notifier)
                            .setPlantSection(sectionIds);
                      } else if (item.prop_name == 'shiftCode') {
                        ref
                            .read(shiftProvider.notifier)
                            .setShift(item.prop_value.toString());
                      }
                    }
                  }
                  final plant = ref.watch(plantProvider.notifier).state;
                  ref
                      .read(plantSectionListProvider.notifier)
                      .fetchPlantsSectionList(plant);
                  ref.read(shiftListProvider.notifier).fetchShiftList(plant);

                  ref
                      .read(editProfileScreenProvider.notifier)
                      .getEditProfileScreenEnable(true);
                });
              }
            }
            ref
                .read(editProfileScreenProvider.notifier)
                .getEditProfileScreenEnable(true);
          }
          ref
              .read(editProfileScreenProvider.notifier)
              .getEditProfileScreenEnable(true);
        }
        if (_tabController.index == 0) {
          final phoneNumber = ref.watch(editPhoneNumberProvider.notifier);
          final imageData = ref.watch(profileImageProvider.notifier);
          final userHeader = ref.watch(userProvider);
          if (userHeader != null) {
            if (userHeader.phone.toString() !=
                phoneNumber.state.trim().toString()) {
              if (mounted) {
                UIHelper.showConfirmationDialogWithYesOrNo(context,
                    description: AppLocalizations.of(context)!
                        .you_have_unsaved_phone_number, yes: () async {
                  Navigator.pop(context);
                  await savePhoneNumber();
                }, no: () async {
                  Navigator.pop(context);
                  phoneNumber.getPhoneNumber(userHeader.phone.toString());
                  if(kIsWeb){
                    String? imageString = await DbHelper()
                      .getAttachmentFromIndexDbByUid(
                          ref.read(userProvider)!.user_id!);
                  imageData.getImage(base64Decode(imageString!));
                  }
                  ref
                      .read(editProfileDetailScreenProvider.notifier)
                      .getEditProfileDetailScreenEnable(true);
                });
              }
            } else {
              ref
                  .read(editProfileDetailScreenProvider.notifier)
                  .getEditProfileDetailScreenEnable(true);
            }
          }
        }
        setState(() {});
      }
    });
    final plant = ref.read(plantProvider);
    fetchInitialPlantDetails = Future.wait([
      ref.read(shiftListProvider.notifier).fetchShiftList(plant),
      ref.read(plantListProvider.notifier).fetchPlantsList(),
      ref.read(plantSectionListProvider.notifier).fetchPlantsSectionList(plant),
    ]);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<bool> onClickBack() async {
    final plantOfProvider = ref.watch(plantProvider.notifier).state;
    final plantSectionOfProvider =
        ref.watch(plantSectionProvider.notifier).state;
    final shiftOfProvider = ref.watch(shiftProvider.notifier).state;
    String plant = '';
    List<String> plantSections = [];
    String shift = '';
    List<APP_SETTING_HEADER> list = await DbHelper.getAppSettingHeader();
    if (list.isNotEmpty) {
      for (var item in list) {
        if (item.prop_name == 'plantId') {
          plant = item.prop_value.toString();
        } else if (item.prop_name == 'sectionId') {
          List<dynamic> decodedList = jsonDecode(item.prop_value.toString());
          List<String> sectionIds =
              decodedList.map((e) => e.toString()).toList();
          plantSections = sectionIds;
        } else if (item.prop_name == 'shiftCode') {
          shift = ' ${item.prop_value.toString()}';
        }
      }
    }
    bool plantSectionListEqual =
        plantSectionOfProvider.length == plantSections.length &&
            plantSectionOfProvider
                .every((element) => plantSections.contains(element)) &&
            plantSections
                .every((element) => plantSectionOfProvider.contains(element));
    if (plantOfProvider != plant.trim() ||
        !plantSectionListEqual ||
        shiftOfProvider != shift.trim()) {
      return true;
    }
    return false;
  }

  PreferredSizeWidget buildAppBar() {
    if (_tabController.index == 0) {
      final isEdit = ref.watch(editProfileScreenProvider);
      return AppBar(
        elevation: 0,
        centerTitle: false,
        backgroundColor: AppColors.white,
        leading: IconButton(
            onPressed: () {
              onClickBackArrowButton();
            },
            icon: Icon(
              Icons.arrow_back_ios,
              color: AppColors.titleTextColor,
              size: 20,
            )),
        title: Padding(
          padding: const EdgeInsets.only(left: 0),
          child: Text(
            AppLocalizations.of(context)!.profile_screen,
            style: UIHelper.titleStyle14(),
          ),
        ),
        actions: [
          Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: isEdit
                  ? Row(
                      children: [
                        kIsWeb
                            ? ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        AppColors.greySubtitleText),
                                onPressed: () {
                                  ref
                                      .read(editProfileScreenProvider.notifier)
                                      .getEditProfileScreenEnable(false);
                                },
                                child: Text('Edit',
                                    style: TextStyle(color: AppColors.white)),
                              )
                            : InkWell(
                                onTap: () {
                                  ref
                                      .read(editProfileScreenProvider.notifier)
                                      .getEditProfileScreenEnable(false);
                                },
                                child: const Icon(
                                  Icons.edit,
                                  size: 28,
                                  color: AppColors.greySubtitleText,
                                ),
                              ),
                      ],
                    )
                  : Row(
                      children: [
                        kIsWeb
                            ? ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.greenColor),
                                onPressed: () {
                                  onCheck();
                                  ref
                                      .read(editProfileScreenProvider.notifier)
                                      .getEditProfileScreenEnable(true);
                                  /*  final plant = ref.watch(plantProvider);
                                  final plantSection =
                                      ref.watch(plantSectionProvider);
                                  final shift = ref.watch(shiftProvider);
                                  ref.invalidate(inspectionDataLoaderProvider);

                                  await ref
                                      .read(filteredCiltProvider.notifier)
                                      .filter(
                                          plant, plantSection, shift, "", ref);

                                  await ref
                                      .read(filterInspectionHeaderListProvider
                                          .notifier)
                                      .filter(
                                          plant, plantSection, shift, "", ref);*/
                                },
                                child: Text('Save',
                                    style: TextStyle(color: AppColors.white)),
                              )
                            : InkWell(
                                onTap: () {
                                  onCheck();
                                  ref
                                      .read(editProfileScreenProvider.notifier)
                                      .getEditProfileScreenEnable(true);
                                },
                                child: const Icon(
                                  Icons.check,
                                  size: 30,
                                  color: AppColors.greenColor,
                                )),
                        const SizedBox(
                          width: 20,
                        ),
                        kIsWeb
                            ? ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.redAccentColor),
                                onPressed: () async {
                                  bool validate = validateData(context);
                                  if (validate) {
                                    bool data = await onClickBack();
                                    if (data) {
                                      if (mounted) {
                                        return UIHelper
                                            .showConfirmationDialogWithYesOrNo(
                                                context,
                                                description: AppLocalizations
                                                        .of(context)!
                                                    .change_of_user_preference_save_confirmation,
                                                yes: () {
                                          Navigator.of(context,
                                                  rootNavigator: true)
                                              .pop();
                                          sendData(context);
                                          ref
                                              .read(editProfileScreenProvider
                                                  .notifier)
                                              .getEditProfileScreenEnable(true);
                                        }, no: () async {
                                          Navigator.of(context,
                                                  rootNavigator: true)
                                              .pop();
                                          List<APP_SETTING_HEADER> list =
                                              await DbHelper
                                                  .getAppSettingHeader();
                                          if (list.isNotEmpty) {
                                            for (var item in list) {
                                              if (item.prop_name == 'plantId') {
                                                ref
                                                    .read(
                                                        plantProvider.notifier)
                                                    .setPlant(item.prop_value
                                                        .toString());
                                              } else if (item.prop_name ==
                                                  'sectionId') {
                                                List<dynamic> decodedList =
                                                    jsonDecode(item.prop_value
                                                        .toString());
                                                List<String> sectionIds =
                                                    decodedList
                                                        .map(
                                                            (e) => e.toString())
                                                        .toList();
                                                ref
                                                    .read(plantSectionProvider
                                                        .notifier)
                                                    .setPlantSection(
                                                        sectionIds);
                                              } else if (item.prop_name ==
                                                  'shiftCode') {
                                                ref
                                                    .read(
                                                        shiftProvider.notifier)
                                                    .setShift(item.prop_value
                                                        .toString());
                                              }
                                            }
                                          }
                                          final plant = ref
                                              .watch(plantProvider.notifier)
                                              .state;
                                          ref
                                              .read(plantSectionListProvider
                                                  .notifier)
                                              .fetchPlantsSectionList(plant);
                                          ref
                                              .read(shiftListProvider.notifier)
                                              .fetchShiftList(plant);

                                          ref
                                              .read(editProfileScreenProvider
                                                  .notifier)
                                              .getEditProfileScreenEnable(true);
                                        });
                                      }
                                    }

                                    List<APP_SETTING_HEADER> list =
                                        await DbHelper.getAppSettingHeader();
                                    if (list.isNotEmpty) {
                                      for (var item in list) {
                                        if (item.prop_name == 'plantId') {
                                          ref
                                              .read(plantProvider.notifier)
                                              .setPlant(
                                                  item.prop_value.toString());
                                        } else if (item.prop_name ==
                                            'sectionId') {
                                          List<dynamic> decodedList =
                                              jsonDecode(
                                                  item.prop_value.toString());
                                          List<String> sectionIds = decodedList
                                              .map((e) => e.toString())
                                              .toList();
                                          ref
                                              .read(
                                                  plantSectionProvider.notifier)
                                              .setPlantSection(sectionIds);
                                        } else if (item.prop_name ==
                                            'shiftCode') {
                                          ref
                                              .read(shiftProvider.notifier)
                                              .setShift(
                                                  item.prop_value.toString());
                                        }
                                      }
                                    }
                                    final plant =
                                        ref.watch(plantProvider.notifier).state;
                                    ref
                                        .read(plantSectionListProvider.notifier)
                                        .fetchPlantsSectionList(plant);
                                    ref
                                        .read(shiftListProvider.notifier)
                                        .fetchShiftList(plant);

                                    ref
                                        .read(
                                            editProfileScreenProvider.notifier)
                                        .getEditProfileScreenEnable(true);
                                  }
                                },
                                child: Text('Cancel',
                                    style: TextStyle(color: AppColors.white)),
                              )
                            : InkWell(
                                onTap: () async {
                                  bool validate = validateData(context);
                                  if (validate) {
                                    bool data = await onClickBack();
                                    if (data) {
                                      if (mounted) {
                                        return UIHelper
                                            .showConfirmationDialogWithYesOrNo(
                                                context,
                                                description: AppLocalizations
                                                        .of(context)!
                                                    .change_of_user_preference_save_confirmation,
                                                yes: () {
                                          Navigator.of(context,
                                                  rootNavigator: true)
                                              .pop();
                                          sendData(context);
                                          ref
                                              .read(editProfileScreenProvider
                                                  .notifier)
                                              .getEditProfileScreenEnable(true);
                                        }, no: () async {
                                          Navigator.of(context,
                                                  rootNavigator: true)
                                              .pop();
                                          List<APP_SETTING_HEADER> list =
                                              await DbHelper
                                                  .getAppSettingHeader();
                                          if (list.isNotEmpty) {
                                            for (var item in list) {
                                              if (item.prop_name == 'plantId') {
                                                ref
                                                    .read(
                                                        plantProvider.notifier)
                                                    .setPlant(item.prop_value
                                                        .toString());
                                              } else if (item.prop_name ==
                                                  'sectionId') {
                                                List<dynamic> decodedList =
                                                    jsonDecode(item.prop_value
                                                        .toString());
                                                List<String> sectionIds =
                                                    decodedList
                                                        .map(
                                                            (e) => e.toString())
                                                        .toList();
                                                ref
                                                    .read(plantSectionProvider
                                                        .notifier)
                                                    .setPlantSection(
                                                        sectionIds);
                                              } else if (item.prop_name ==
                                                  'shiftCode') {
                                                ref
                                                    .read(
                                                        shiftProvider.notifier)
                                                    .setShift(item.prop_value
                                                        .toString());
                                              }
                                            }
                                          }
                                          final plant = ref
                                              .watch(plantProvider.notifier)
                                              .state;
                                          ref
                                              .read(plantSectionListProvider
                                                  .notifier)
                                              .fetchPlantsSectionList(plant);
                                          ref
                                              .read(shiftListProvider.notifier)
                                              .fetchShiftList(plant);

                                          ref
                                              .read(editProfileScreenProvider
                                                  .notifier)
                                              .getEditProfileScreenEnable(true);
                                        });
                                      }
                                    }

                                    List<APP_SETTING_HEADER> list =
                                        await DbHelper.getAppSettingHeader();
                                    if (list.isNotEmpty) {
                                      for (var item in list) {
                                        if (item.prop_name == 'plantId') {
                                          ref
                                              .read(plantProvider.notifier)
                                              .setPlant(
                                                  item.prop_value.toString());
                                        } else if (item.prop_name ==
                                            'sectionId') {
                                          List<dynamic> decodedList =
                                              jsonDecode(
                                                  item.prop_value.toString());
                                          List<String> sectionIds = decodedList
                                              .map((e) => e.toString())
                                              .toList();
                                          ref
                                              .read(
                                                  plantSectionProvider.notifier)
                                              .setPlantSection(sectionIds);
                                        } else if (item.prop_name ==
                                            'shiftCode') {
                                          ref
                                              .read(shiftProvider.notifier)
                                              .setShift(
                                                  item.prop_value.toString());
                                        }
                                      }
                                    }
                                    final plant =
                                        ref.watch(plantProvider.notifier).state;
                                    ref
                                        .read(plantSectionListProvider.notifier)
                                        .fetchPlantsSectionList(plant);
                                    ref
                                        .read(shiftListProvider.notifier)
                                        .fetchShiftList(plant);

                                    ref
                                        .read(
                                            editProfileScreenProvider.notifier)
                                        .getEditProfileScreenEnable(true);
                                  }
                                },
                                child: const Icon(
                                  Icons.cancel,
                                  color: AppColors.redAccentColor,
                                  size: 30,
                                ),
                              ),
                      ],
                    )),
          isEdit
              ? Padding(
                  padding: const EdgeInsets.only(right: 8.0, left: 8.0),
                  child: kIsWeb
                      ? Row(
                          children: [
                            ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.greySubtitleText),
                              onPressed: () {
                                onLogOut();
                              },
                              child: Text('Logout',
                                  style: TextStyle(color: AppColors.white)),
                            ),
                          ],
                        )
                      : InkWell(
                          onTap: () {
                            onLogOut();
                          },
                          child: const Icon(
                            Icons.logout,
                            size: 26,
                            color: AppColors.greySubtitleText,
                          ),
                        ))
              : SizedBox()
        ],
      );
    } else {
      final isEdit = ref.watch(editProfileDetailScreenProvider);
      return AppBar(
        elevation: 0,
        centerTitle: false,
        backgroundColor: AppColors.white,
        leading: IconButton(
            onPressed: () {
              onClickBackArrowButton();
            },
            icon: Icon(
              Icons.arrow_back_ios,
              color: AppColors.titleTextColor,
              size: 20,
            )),
        title: Padding(
          padding: const EdgeInsets.only(left: 18),
          child: Text(
            AppLocalizations.of(context)!.profile_screen,
            style: UIHelper.titleStyle14(),
          ),
        ),
        actions: [
          Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: isEdit
                  ? Row(
                      children: [
                        kIsWeb
                            ? ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        AppColors.greySubtitleText),
                                onPressed: () {
                                  ref
                                      .read(editProfileDetailScreenProvider
                                          .notifier)
                                      .getEditProfileDetailScreenEnable(false);
                                },
                                child: Text('Edit',
                                    style: TextStyle(color: AppColors.white)),
                              )
                            : InkWell(
                                onTap: () {
                                  ref
                                      .read(editProfileDetailScreenProvider
                                          .notifier)
                                      .getEditProfileDetailScreenEnable(false);
                                },
                                child: const Icon(
                                  Icons.edit,
                                  size: 28,
                                  color: AppColors.greySubtitleText,
                                ),
                              ),
                      ],
                    )
                  : Row(
                      children: [
                        kIsWeb
                            ? ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.greenColor),
                                onPressed: () {
                                  submitPhoneNumber();
                                },
                                child: Text('Save',
                                    style: TextStyle(color: AppColors.white)),
                              )
                            : InkWell(
                                onTap: () {
                                  submitPhoneNumber();
                                },
                                child: const Icon(
                                  Icons.check,
                                  size: 30,
                                  color: AppColors.greenColor,
                                )),
                        const SizedBox(
                          width: 20,
                        ),
                        kIsWeb
                            ? ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.redAccentColor),
                                onPressed: () {
                                  final phoneNumber = ref
                                      .watch(editPhoneNumberProvider.notifier);
                                  final imageData =
                                      ref.watch(profileImageProvider.notifier);
                                  final userHeader = ref.watch(userProvider);
                                  String base64String = '';
                                  if (userHeader != null) {
                                    if (imageData.state != null) {
                                      base64String =
                                          base64Encode(imageData.state!);
                                    }
                                    bool phone = userHeader.phone! !=
                                        phoneNumber.state.trim();
                                    bool imageData1 =
                                        userHeader.thumbnail! != base64String &&
                                            imageData.state != null;
                                    if (phone || imageData1) {
                                      UIHelper.showConfirmationDialogWithYesOrNo(
                                          context,
                                          description: AppLocalizations.of(
                                                  context)!
                                              .you_have_unsaved_phone_number,
                                          yes: () async {
                                        Navigator.pop(context);
                                        await savePhoneNumber();
                                      }, no: () {
                                        Navigator.pop(context);
                                        phoneNumber.getPhoneNumber(
                                            userHeader.phone.toString());
                                        ref
                                            .read(
                                                editProfileDetailScreenProvider
                                                    .notifier)
                                            .getEditProfileDetailScreenEnable(
                                                true);
                                      });
                                    }
                                  }
                                  ref
                                      .read(editProfileDetailScreenProvider
                                          .notifier)
                                      .getEditProfileDetailScreenEnable(true);
                                },
                                child: Text('Cancel',
                                    style: TextStyle(color: AppColors.white)),
                              )
                            : InkWell(
                                onTap: () {
                                  final phoneNumber = ref
                                      .watch(editPhoneNumberProvider.notifier);
                                  final imageData =
                                      ref.watch(profileImageProvider.notifier);
                                  final userHeader = ref.watch(userProvider);
                                  if (userHeader != null) {
                                    if (userHeader.phone.toString() !=
                                            phoneNumber.state
                                                .trim()
                                                .toString() ||
                                        base64Decode(userHeader.thumbnail!) !=
                                            imageData.state ||
                                        imageData.state != null) {
                                      UIHelper.showConfirmationDialogWithYesOrNo(
                                          context,
                                          description: AppLocalizations.of(
                                                  context)!
                                              .you_have_unsaved_phone_number,
                                          yes: () async {
                                        Navigator.pop(context);
                                        await savePhoneNumber();
                                      }, no: () {
                                        Navigator.pop(context);
                                        phoneNumber.getPhoneNumber(
                                            userHeader.phone.toString());
                                        ref
                                            .read(
                                                editProfileDetailScreenProvider
                                                    .notifier)
                                            .getEditProfileDetailScreenEnable(
                                                true);
                                      });
                                    }
                                  }
                                  ref
                                      .read(editProfileDetailScreenProvider
                                          .notifier)
                                      .getEditProfileDetailScreenEnable(true);
                                },
                                child: const Icon(
                                  Icons.cancel,
                                  color: AppColors.redAccentColor,
                                  size: 30,
                                ),
                              ),
                      ],
                    ))
        ],
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final edit = ref.watch(editProfileDetailScreenProvider);
    final phoneNumber = ref.watch(editPhoneNumberProvider.notifier);
    final imageData = ref.watch(profileImageProvider.notifier);
    final userHeader = ref.watch(userProvider);
    String? imageString;
    return WillPopScope(
      onWillPop: () async {
        if(kIsWeb){
          imageString = await DbHelper()
            .getAttachmentFromIndexDbByUid(ref.read(userProvider)!.user_id!);
        }
        bool validate = validateData(context);
        if (validate) {
          bool data = await onClickBack();
          if (data) {
            if (mounted) {
              return UIHelper.showConfirmationDialogWithYesOrNo(context,
                  description: AppLocalizations.of(context)!
                      .change_of_user_preference_save_confirmation, yes: () {
                Navigator.of(context, rootNavigator: true).pop();
                sendData(context);
                ref
                    .read(editProfileScreenProvider.notifier)
                    .getEditProfileScreenEnable(true);
              }, no: () async {
                Navigator.of(context, rootNavigator: true).pop();
                List<APP_SETTING_HEADER> list =
                    await DbHelper.getAppSettingHeader();
                if (list.isNotEmpty) {
                  for (var item in list) {
                    if (item.prop_name == 'plantId') {
                      ref
                          .read(plantProvider.notifier)
                          .setPlant(item.prop_value.toString());
                    } else if (item.prop_name == 'sectionId') {
                      List<dynamic> decodedList =
                          jsonDecode(item.prop_value.toString());
                      List<String> sectionIds =
                          decodedList.map((e) => e.toString()).toList();
                      ref
                          .read(plantSectionProvider.notifier)
                          .setPlantSection(sectionIds);
                    } else if (item.prop_name == 'shiftCode') {
                      ref
                          .read(shiftProvider.notifier)
                          .setShift(item.prop_value.toString());
                    }
                  }
                }
                final plant = ref.watch(plantProvider.notifier).state;
                ref
                    .read(plantSectionListProvider.notifier)
                    .fetchPlantsSectionList(plant);
                ref.read(shiftListProvider.notifier).fetchShiftList(plant);

                ref
                    .read(editProfileScreenProvider.notifier)
                    .getEditProfileScreenEnable(true);
                Navigator.pop(context);
              });
            }
          }
          ref
              .read(editProfileScreenProvider.notifier)
              .getEditProfileScreenEnable(true);

          if (userHeader != null) {
            if (userHeader.phone.toString() !=
                    phoneNumber.state.replaceAll('', '') ||
                base64Decode(imageString!) != imageData.state) {
              if (mounted) {
                UIHelper.showConfirmationDialogWithYesOrNo(context,
                    description: AppLocalizations.of(context)!
                        .you_have_unsaved_phone_number, yes: () async {
                  Navigator.pop(context);
                  await savePhoneNumber();
                  Navigator.pop(context);
                }, no: () {
                  Navigator.pop(context);
                  phoneNumber.getPhoneNumber(userHeader.phone.toString());
                  imageData.getImage(base64Decode(imageString!));
                  ref
                      .read(editProfileDetailScreenProvider.notifier)
                      .getEditProfileDetailScreenEnable(true);
                  Navigator.pop(context);
                });
              }
            } else {
              ref
                  .read(editProfileDetailScreenProvider.notifier)
                  .getEditProfileDetailScreenEnable(true);
              Navigator.pop(context);
            }
          }
          return true;
        }
        ref
            .read(editProfileScreenProvider.notifier)
            .getEditProfileScreenEnable(false);

        if (userHeader != null) {
          if (userHeader.phone.toString() !=
                  phoneNumber.state.trim().toString() ||
              base64Decode(imageString!) != imageData.state) {
            UIHelper.showConfirmationDialogWithYesOrNo(context,
                description: AppLocalizations.of(context)!
                    .you_have_unsaved_phone_number, yes: () async {
              Navigator.pop(context);
              await savePhoneNumber();
            }, no: () {
              Navigator.pop(context);
              phoneNumber.getPhoneNumber(userHeader.phone.toString());
              imageData.getImage(base64Decode(imageString!));
              ref
                  .read(editProfileDetailScreenProvider.notifier)
                  .getEditProfileDetailScreenEnable(true);
            });
          } else {
            ref
                .read(editProfileDetailScreenProvider.notifier)
                .getEditProfileDetailScreenEnable(true);
          }
        }

        return false;
      },
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: buildAppBar(),
        body: SafeArea(
            child: Padding(
          padding: UIHelper.columnFieldOnlhorizontalPadding6(),
          child: Column(children: [
            Stack(
              children: [
                InkWell(
                    onTap: () async {
                      if(kIsWeb){
                      String? imageString = await DbHelper()
                          .getAttachmentFromIndexDbByUid(
                              ref.read(userProvider)!.user_id!);
                      if (imageString != null && imageString!.isNotEmpty) {
                        if (image == null) {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (context) => ProfileImageViewerScreen(
                                  image: base64Decode(imageString!))));
                        } else {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (context) =>
                                  ProfileImageViewerScreen(image: image)));
                        }
                      }
                      } else if (image != null) {
                        Navigator.of(context).push(MaterialPageRoute(
                            builder: (context) =>
                                ProfileImageViewerScreen(image: image)));
                      }
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppColors.grey,
                          width: 1.0,
                        ),
                      ),
                      child: Hero(
                        tag: "profileImage",
                        child: CircleAvatar(
                          backgroundColor: AppColors.white,
                          radius: 70,
                          backgroundImage: ref.watch(profileImageProvider) !=
                                      null &&
                                  ref.watch(profileImageProvider)!.isNotEmpty
                              ? image == null
                                  ? MemoryImage(ref
                                      .watch(profileImageProvider.notifier)
                                      .state!)
                                  : MemoryImage(image!)
                              : const AssetImage(
                                      'assets/images/empty_profile.png')
                                  as ImageProvider,
                        ),
                      ),
                    )),
                edit
                    ? SizedBox()
                    : Positioned(
                        bottom: 4,
                        right: 6,
                        child: InkWell(
                          onTap: () {
                            imagePicker(context);
                          },
                          child: Container(
                            padding: EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.grey.shade200,
                            ),
                            child: const Icon(
                              Icons.camera_alt,
                              color: Colors.black,
                              size: 24,
                            ),
                          ),
                        ),
                      ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(top: 5.0),
              child: Text(
                widget.userHeader.first_name != null &&
                        widget.userHeader.last_name != null
                    ? '${UIHelper().toCamelCase(widget.userHeader.first_name!)} ${UIHelper().toCamelCase(widget.userHeader.last_name!)}'
                    : '',
                style: TextStyle(
                    color: AppColors.black,
                    fontSize: 16,
                    fontWeight: FontWeight.bold),
              ),
            ),
            Text(
              widget.userHeader.email != null ? widget.userHeader.email! : '',
              style: TextStyle(
                  color: AppColors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.w500),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 5.0),
              child: TabBar(
                controller: _tabController,
                tabs: <Widget>[
                  Tab(
                    icon: FittedBox(
                      child: Text(
                        AppLocalizations.of(context)!.user_preference,
                        style: const TextStyle(color: AppColors.blackTitleText),
                      ),
                    ),
                  ),
                  Tab(
                    icon: FittedBox(
                      child: Text(
                        AppLocalizations.of(context)!.user_detail,
                        style: const TextStyle(color: AppColors.blackTitleText),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: <Widget>[
                  FutureBuilder(
                    future: fetchInitialPlantDetails,
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.done) {
                        return SingleChildScrollView(
                            child: ProfileUserPreferenceTab(
                                userHeader: widget.userHeader));
                      } else {
                        return const Center(child: CircularProgressIndicator());
                      }
                    },
                  ),
                  ProfileGeneralTab(
                    userHeader: widget.userHeader,
                  ),
                ],
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Center(child: VersionInfo()),
            )
          ]),
        )),
      ),
      //),
    );
  }

  void onCheck() {
    bool validate = validateData(context);
    if (validate) {
      if (noSaveData()) {
        UIHelper.showConfirmationDialogWithYesOrNo(
          context,
          description: alertMessage(),
          yes: () {
            Navigator.of(context, rootNavigator: true).pop();
            sendData(context);
          },
          no: () {
            Navigator.pop(context);
          },
        );
      }
    }
  }

  bool noSaveData() {
    final plantOfProvider = ref.watch(plantProvider.notifier).state;
    final plantSectionOfProvider =
        ref.watch(plantSectionProvider.notifier).state;
    final shiftOfProvider = ref.watch(shiftProvider.notifier).state;
    bool plantSectionListEqual =
        plantSectionOfProvider.length == plantSectionsData.length &&
            plantSectionOfProvider
                .every((element) => plantSectionsData.contains(element)) &&
            plantSectionsData
                .every((element) => plantSectionOfProvider.contains(element));
    if (plantOfProvider != plantData.trim()) {
      return true;
    } else if (!plantSectionListEqual) {
      return true;
    } else if (shiftOfProvider != shiftData.trim()) {
      return true;
    } else if (!plantSectionListEqual && shiftOfProvider != shiftData.trim()) {
      return true;
    } else if (plantOfProvider != plantData.trim() &&
        !plantSectionListEqual &&
        shiftOfProvider != shiftData.trim()) {
      return true;
    } else {
      return false;
    }
  }

  String alertMessage() {
    final plantOfProvider = ref.watch(plantProvider.notifier).state;
    final plantSectionOfProvider =
        ref.watch(plantSectionProvider.notifier).state;
    final shiftOfProvider = ref.watch(shiftProvider.notifier).state;
    bool plantSectionListEqual =
        plantSectionOfProvider.length == plantSectionsData.length &&
            plantSectionOfProvider
                .every((element) => plantSectionsData.contains(element)) &&
            plantSectionsData
                .every((element) => plantSectionOfProvider.contains(element));
    if (plantOfProvider != plantData.trim()) {
      return AppLocalizations.of(context)!.are_you_sure_want_change_plant;
    } else if (!plantSectionListEqual) {
      return AppLocalizations.of(context)!
          .are_you_sure_want_change_plant_section;
    } else if (shiftOfProvider != shiftData.trim()) {
      return AppLocalizations.of(context)!.are_you_sure_want_change_shift;
    } else if (!plantSectionListEqual && shiftOfProvider != shiftData.trim()) {
      return AppLocalizations.of(context)!
          .are_you_sure_want_change_section_and_shift;
    } else if (plantOfProvider != plantData.trim() &&
        !plantSectionListEqual &&
        shiftOfProvider != shiftData.trim()) {
      return AppLocalizations.of(context)!.are_you_sure_want_change_plant;
    } else {
      return '';
    }
  }

  String loadingAlertMessage() {
    final plantOfProvider = ref.watch(plantProvider.notifier).state;
    final plantSectionOfProvider =
        ref.watch(plantSectionProvider.notifier).state;
    final shiftOfProvider = ref.watch(shiftProvider.notifier).state;
    bool plantSectionListEqual =
        plantSectionOfProvider.length == plantSectionsData.length &&
            plantSectionOfProvider
                .every((element) => plantSectionsData.contains(element)) &&
            plantSectionsData
                .every((element) => plantSectionOfProvider.contains(element));
    if (plantOfProvider != plantData.trim()) {
      return 'Switching to selected plant';
    } else if (!plantSectionListEqual) {
      return 'Switching to selected plant section';
    } else if (shiftOfProvider != shiftData.trim()) {
      return 'Switching to selected shift';
    } else if (!plantSectionListEqual && shiftOfProvider != shiftData.trim()) {
      return 'Switching to selected plant section & shift';
    } else if (plantOfProvider != plantData.trim() &&
        !plantSectionListEqual &&
        shiftOfProvider != shiftData.trim()) {
      return 'Switching to selected plant';
    } else {
      return '';
    }
  }

  validateData(BuildContext context) {
    bool data = false;
    final plant = ref.read(plantProvider);
    final plantSection = ref.read(plantSectionProvider);
    final shift = ref.read(shiftProvider);

    if (plant == '') {
      if (mounted) {
        ref.watch(plantValidationsProvider.notifier).getPlantValidation(true);
      }
      return data;
    } else if (plantSection.isEmpty) {
      if (mounted) {
        ref
            .read(plantSectionValidationsProvider.notifier)
            .getPlantSectionValidation(true);
      }
      return data;
    } else if (shift == '') {
      if (mounted) {
        ref.read(shiftValidationsProvider.notifier).getShiftValidation(true);
      }
      return data;
    } else {
      if (mounted) {
        ref.read(plantValidationsProvider.notifier).clearPlantValidation();
        ref
            .read(plantSectionValidationsProvider.notifier)
            .clearPlantSectionValidation();
        ref.read(shiftValidationsProvider.notifier).clearShiftValidation();
      }
      return true;
    }
  }

  sendData(BuildContext context) async {
    await clearLocalDb();
    final plant = ref.read(plantProvider);
    final plantSection = ref.read(plantSectionProvider);
    final shift = ref.read(shiftProvider);
    SHIFT_HEADER? shiftHeader = await DbHelper.getShiftHeader(shift, plant);
    String shiftValue = '';
    if (shiftHeader != null) {
      shiftValue = shiftHeader.shift_code.toString();
    }

    Map<String, dynamic> setData = {
      "plantId": plant,
      "sectionId": plantSection,
      "shiftCode": shiftValue
    };
    try {
      ref.read(plantProvider.notifier).clearPlant();
      ref.read(shiftProvider.notifier).clearShift();
      ref.read(plantSectionProvider.notifier).clearPlantSection();
      SharedPreferences preferences = await SharedPreferences.getInstance();
      preferences.setString("Plant", plant);
      preferences.setStringList("PlantSection", plantSection);
      preferences.setString("Shift", shiftValue);
      preferences.setBool("isUserPreferenceSet", true);

      ref.read(plantProvider.notifier).setPlant(plant);
      ref.read(shiftProvider.notifier).setShift(shiftValue);
      ref.read(plantSectionProvider.notifier).setPlantSection(plantSection);

      UIHelper()
          .progressDialog(context: context, message: loadingAlertMessage());

      await PAHelper.setUserPreferenceLogin(context, setData);
      Result result = await PAHelper.getUserPreferenceLogin(context);

      List<APP_SETTING_HEADER> settingsList =
          result.body.entries.map<APP_SETTING_HEADER>((entry) {
        return APP_SETTING_HEADER(
          prop_name: entry.key,
          prop_value: entry.value is List
              ? jsonEncode(entry.value.map((e) => e.toString()).toList())
              : entry.value.toString(),
        );
      }).toList();
      await AppDatabaseManager()
          .execute("DELETE FROM ${APP_SETTING_HEADER.TABLE_NAME}");
      List results = await AppDatabaseManager()
          .select(DBInputEntity(APP_SETTING_HEADER.TABLE_NAME, {}));
      for (var setting in settingsList) {
        await AppDatabaseManager().insert(
            DBInputEntity(APP_SETTING_HEADER.TABLE_NAME, setting.toJson()));
      }
    } catch (e) {
      Logger.logError('ProfileScreen', 'validateData', e.toString());
    }
    List<APP_SETTING_HEADER> list = await DbHelper.getAppSettingHeader();
    if (list.isNotEmpty) {
      for (var item in list) {
        if (item.prop_name == 'plantId') {
          ref.read(plantProvider.notifier).setPlant(item.prop_value.toString());
        } else if (item.prop_name == 'sectionId') {
          List<dynamic> decodedList = jsonDecode(item.prop_value.toString());
          List<String> sectionIds =
              decodedList.map((e) => e.toString()).toList();
          ref.read(plantSectionProvider.notifier).setPlantSection(sectionIds);
        } else if (item.prop_name == 'shiftCode') {
          ref.read(shiftProvider.notifier).setShift(item.prop_value.toString());
        }
      }
    }
    if (!kIsWeb) {
      await SettingsHelper().setFetchInterval(15);
    }

    if (mounted) {
      Navigator.of(context, rootNavigator: true).pop();
      Navigator.pop(context);
      try {
        /* String query =
            'DELETE FROM ${LOCATION_HEADER.TABLE_NAME};DELETE FROM ${LOCATION_DOCUMENT.TABLE_NAME};DELETE FROM ${ASSET_HEADER.TABLE_NAME};DELETE FROM ${ASSET_DOCUMENT.TABLE_NAME};DELETE FROM ${KPI_HEADER.TABLE_NAME};DELETE FROM ${INSPECTION_PLAN_HEADER.TABLE_NAME};DELETE FROM ${INSPECTION_SECTION.TABLE_NAME};DELETE FROM ${INSPECTION_TASK.TABLE_NAME};DELETE FROM ${INSPECTION_PLAN_DOC.TABLE_NAME};DELETE FROM ${INSPECTION_TASK_DOC.TABLE_NAME};DELETE FROM ${INSP_EXEC_HEADER.TABLE_NAME};DELETE FROM ${INSP_EXEC_SEC.TABLE_NAME};DELETE FROM ${INSP_EXEC_TASK.TABLE_NAME};DELETE FROM ${INSP_EXEC_ACTION.TABLE_NAME};DELETE FROM ${INSP_EXEC_DOC.TABLE_NAME};DELETE FROM ${CILT_PLAN_HEADER.TABLE_NAME};DELETE FROM ${CILT_SECTION.TABLE_NAME};DELETE FROM ${CILT_TASK.TABLE_NAME};DELETE FROM ${CILT_PLAN_DOC.TABLE_NAME}DELETE FROM ${CILT_TASK_DOC.TABLE_NAME};DELETE FROM ${CILT_EXEC_HEADER.TABLE_NAME};DELETE FROM ${CILT_EXEC_SEC.TABLE_NAME};DELETE FROM ${CILT_EXEC_TASK.TABLE_NAME};DELETE FROM ${CILT_EXEC_ACTION.TABLE_NAME};DELETE FROM ${CILT_EXEC_DOC.TABLE_NAME};DELETE FROM ${FAULT_HEADER.TABLE_NAME};DELETE FROM ${FAULT_ACTION.TABLE_NAME};DELETE FROM ${FAULT_DOCUMENT.TABLE_NAME};DELETE FROM ${DOCUMENT_HEADER.TABLE_NAME};DELETE FROM ${DOCUMENT_ATTACHMENT.TABLE_NAME};DELETE FROM ${JOB_HEADER.TABLE_NAME};DELETE FROM ${JOB_ACTION.TABLE_NAME};DELETE FROM ${JOB_DOCUMENT.TABLE_NAME};';
*/
        /* String query =
            'DELETE FROM ${LOCATION_HEADER.TABLE_NAME};DELETE FROM ${ASSET_HEADER.TABLE_NAME};DELETE FROM ${KPI_HEADER.TABLE_NAME};DELETE FROM ${INSPECTION_PLAN_HEADER.TABLE_NAME};DELETE FROM ${INSP_EXEC_HEADER.TABLE_NAME};DELETE FROM ${CILT_PLAN_HEADER.TABLE_NAME};DELETE FROM ${CILT_EXEC_HEADER.TABLE_NAME};DELETE FROM ${FAULT_HEADER.TABLE_NAME};DELETE FROM ${JOB_HEADER.TABLE_NAME};DELETE FROM ${DOCUMENT_HEADER.TABLE_NAME}';
*/

        String baseQuery =
            'DELETE FROM ${LOCATION_DOCUMENT.TABLE_NAME};DELETE FROM ${LOCATION_HEADER.TABLE_NAME};DELETE FROM ${ASSET_DOCUMENT.TABLE_NAME};DELETE FROM ${ASSET_HEADER.TABLE_NAME};DELETE FROM ${KPI_HEADER.TABLE_NAME};DELETE FROM ${INSPECTION_SECTION.TABLE_NAME};DELETE FROM ${INSPECTION_TASK.TABLE_NAME};DELETE FROM ${INSPECTION_PLAN_DOC.TABLE_NAME};DELETE FROM ${INSPECTION_TASK_DOC.TABLE_NAME};DELETE FROM ${INSPECTION_PLAN_HEADER.TABLE_NAME};DELETE FROM ${INSP_EXEC_SEC.TABLE_NAME};DELETE FROM ${INSP_EXEC_TASK.TABLE_NAME};DELETE FROM ${INSP_EXEC_ACTION.TABLE_NAME};DELETE FROM ${INSP_EXEC_DOC.TABLE_NAME};DELETE FROM ${INSP_EXEC_HEADER.TABLE_NAME};DELETE FROM ${CILT_SECTION.TABLE_NAME};DELETE FROM ${CILT_TASK.TABLE_NAME};DELETE FROM ${CILT_PLAN_DOC.TABLE_NAME};DELETE FROM ${CILT_PLAN_HEADER.TABLE_NAME};DELETE FROM ${CILT_TASK_DOC.TABLE_NAME};DELETE FROM ${CILT_EXEC_SEC.TABLE_NAME};DELETE FROM ${CILT_EXEC_TASK.TABLE_NAME};DELETE FROM ${CILT_EXEC_ACTION.TABLE_NAME};DELETE FROM ${CILT_EXEC_DOC.TABLE_NAME};DELETE FROM ${CILT_EXEC_HEADER.TABLE_NAME};DELETE FROM ${FAULT_ACTION.TABLE_NAME};DELETE FROM ${FAULT_DOCUMENT.TABLE_NAME};DELETE FROM ${FAULT_HEADER.TABLE_NAME};DELETE FROM ${DOCUMENT_ATTACHMENT.TABLE_NAME};DELETE FROM ${DOCUMENT_HEADER.TABLE_NAME};DELETE FROM ${JOB_ACTION.TABLE_NAME};DELETE FROM ${JOB_DOCUMENT.TABLE_NAME};DELETE FROM ${JOB_HEADER.TABLE_NAME};';
        List<String> queryList = baseQuery.split(';');
        for (String query in queryList) {
          if (query.trim().isNotEmpty) {
            await AppDatabaseManager().execute(query);
          }
        }
      } catch (e) {
        Logger.logError('ProfileScreen', 'sendData', e.toString());
      }
      roundDetailViewNotifier.value = InteractiveItemModel(
        type: "",
        data: {"type": "", "index": null},
      );
      faultDetailViewNotifier.value = InteractiveItemModel(
        type: "",
        data: {"type": "", "index": null},
      );
      jobDetailViewNotifier.value = InteractiveItemModel(
        type: "",
        data: {"type": "", "index": null},
      );
      assetDetailViewNotifier.value = InteractiveItemModel(
        type: "",
        data: {"type": "", "index": null},
      );
      Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) {
        return const DownloadDataPage();
      }));
    }
  }

  Future<void> submitPhoneNumber() async {
    final phoneNumber = ref.watch(editPhoneNumberProvider.notifier);
    final imageData = ref.watch(profileImageProvider.notifier);
    final userHeader = ref.watch(userProvider);
    String base64String = '';
    if (imageData.state != null) {
      base64String = base64Encode(imageData.state!);
    }
    bool phone = false;
    bool imageData1 = false;
    if (userHeader != null) {
      phone = userHeader.phone! != phoneNumber.state.trim();

      imageData1 =
          userHeader.thumbnail! != base64String && imageData.state != null;
    }
    if (phone || imageData1) {
      UIHelper.showConfirmationDialogWithYesOrNo(context,
          description: AppLocalizations.of(context)!
              .are_you_sure_you_want_to_update_phone_number, yes: () async {
        Navigator.pop(context);
        await savePhoneNumber();
      }, no: () {
        Navigator.pop(context);
        ref
            .read(editProfileDetailScreenProvider.notifier)
            .getEditProfileDetailScreenEnable(true);
      });
    }
    ref
        .read(editProfileDetailScreenProvider.notifier)
        .getEditProfileDetailScreenEnable(true);
  }

  Future<void> savePhoneNumber() async {
    final phoneNumber = ref.read(editPhoneNumberProvider);

    if (phoneNumber != '') {
      if (!(await URLService.isInternetConnected())) {
        if (mounted) {
          UIHelper.showErrorDialog(
            context,
            description:
                AppLocalizations.of(context)!.noInternetConnectivityString,
          );
        }
      } else {
        UIHelper().progressDialog(
            context: context,
            message: AppLocalizations.of(context)!.updating_phone_number);
        USER_HEADER userHeader = widget.userHeader;
        String phone = phoneNumber.replaceAll(' ', '');
        userHeader.phone = phone;
        USER_HEADER userHeaderMap = userHeader;
        if (image != null) {
          await DbHelper().saveAttachmentinIndexDbByUid(
              userHeader.user_id ?? "", base64Encode(image!));
          if (!kIsWeb) {
            userHeader.thumbnail = base64Encode(image!);
            userHeaderMap.thumbnail = widget.userHeader.thumbnail!;
          } else {
            if (kIsWeb) {
              userHeader.thumbnail = await DbHelper()
                  .getAttachmentFromIndexDbByUid(widget.userHeader.user_id!);
            }
            // userHeaderMap.thumbnail = await DbHelper()
            //     .getAttachmentFromIndexDbByUid(widget.userHeader.user_id!);
          }
        }
        await DbHelper.updateUserHeader(userHeaderMap);
        Result? result;
        result = await PAHelper.updateUser(context, userHeader);

        ref.read(userProvider.notifier).getUser();
        if(kIsWeb){
          String? imageString = await DbHelper()
            .getAttachmentFromIndexDbByUid(ref.read(userProvider)!.user_id!);
        if (imageString != null && imageString.isNotEmpty) {
          ref
              .read(profileImageProvider.notifier)
              .getImage(base64Decode(imageString));
        }
        }
        if (result != null) {
          Navigator.pop(context);
        }
      }
    }
    ref
        .read(editProfileDetailScreenProvider.notifier)
        .getEditProfileDetailScreenEnable(true);
  }

  void imagePicker(BuildContext context) {
    final imageProvider = ref.watch(profileImageProvider.notifier);
    if (UIHelper().getScreenType(context) == ScreenType.desktop) {
      showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            contentPadding: const EdgeInsets.all(20),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.camera_alt, color: Colors.blue),
                  title: const Text("Camera"),
                  onTap: () async {
                    Navigator.pop(context); // Close the dialog
                    final file = await ImagePicker()
                        .pickImage(source: ImageSource.camera);
                    if (file != null) {
                      Uint8List imageByte;
                      if (!kIsWeb) {
                        final imageData = File(file.path);
                        imageByte = await imageData.readAsBytes();
                      } else {
                        imageByte = await file.readAsBytes();
                      }
                      setState(() {
                        image = imageByte;
                      });
                      imageProvider.getImage(image!);
                    }
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.file_present_rounded,
                      color: Colors.green),
                  title: const Text("Browse"),
                  onTap: () async {
                    Navigator.pop(context); // Close the dialog
                    final file = await ImagePicker().pickImage(
                        source: ImageSource
                            .gallery); // Changed to gallery for browse
                    if (file != null) {
                      Uint8List imageByte;
                      if (!kIsWeb) {
                        final imageData = File(file.path);
                        imageByte = await imageData.readAsBytes();
                      } else {
                        imageByte = await file.readAsBytes();
                      }
                      setState(() {
                        image = imageByte;
                      });
                      imageProvider.getImage(image!);
                    }
                  },
                ),
              ],
            ),
          );
        },
      );
    } else {
      showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) {
          return Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.camera_alt, color: Colors.blue),
                  title: const Text("Camera"),
                  onTap: () async {
                    Navigator.pop(context);
                    final file = await ImagePicker()
                        .pickImage(source: ImageSource.camera);
                    if (file != null) {
                      if (!kIsWeb) {
                        final imageData = File(file.path);
                        Uint8List imageByte = await imageData.readAsBytes();
                        setState(() {
                          image = imageByte;
                        });
                        imageProvider.getImage(image!);
                      } else {
                        Uint8List imageByte = await file.readAsBytes();
                        setState(() {
                          image = imageByte;
                        });

                        imageProvider.getImage(image!);
                      }
                    }
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.file_present_rounded,
                      color: Colors.green),
                  title: const Text("Browse"),
                  onTap: () async {
                    Navigator.pop(context);
                    final file = await ImagePicker()
                        .pickImage(source: ImageSource.camera);
                    if (file != null) {
                      if (!kIsWeb) {
                        final imageData = File(file.path);
                        Uint8List imageByte = await imageData.readAsBytes();
                        setState(() {
                          image = imageByte;
                        });
                        imageProvider.getImage(image!);
                      } else {
                        Uint8List imageByte = await file.readAsBytes();
                        setState(() {
                          image = imageByte;
                        });

                        imageProvider.getImage(image!);
                      }
                    }
                  },
                ),
              ],
            ),
          );
        },
      );
    }
  }

  Future<void> clearLocalDb() async {
    await AppDatabaseManager()
        .execute("DELETE FROM ${LOCATION_HEADER.TABLE_NAME}");
    await AppDatabaseManager()
        .execute("DELETE FROM ${ASSET_HEADER.TABLE_NAME}");
    await AppDatabaseManager().execute("DELETE FROM ${KPI_HEADER.TABLE_NAME}");
    await AppDatabaseManager()
        .execute("DELETE FROM ${INSPECTION_PLAN_HEADER.TABLE_NAME}");
    await AppDatabaseManager()
        .execute("DELETE FROM ${CILT_PLAN_HEADER.TABLE_NAME}");
    await AppDatabaseManager()
        .execute("DELETE FROM ${INSP_EXEC_HEADER.TABLE_NAME}");
    await AppDatabaseManager()
        .execute("DELETE FROM ${CILT_EXEC_HEADER.TABLE_NAME}");
    await AppDatabaseManager()
        .execute("DELETE FROM ${FAULT_HEADER.TABLE_NAME}");
    await AppDatabaseManager().execute("DELETE FROM ${JOB_HEADER.TABLE_NAME}");
  }

  void onLogOut() {
    UIHelper.showConfirmationDialogWithYesOrNo(context,
        description: AppLocalizations.of(context)!.alert_msg_to_clear_data,
        yes: () {
      Navigator.pop(context);
      clearData();
    }, no: () {
      Navigator.pop(context);
    });
  }

  clearData() async {
    try {
      await SettingsHelper().clearData();
    } catch (e) {
      Logger.logError('ProfileScreen', 'clearData', e.toString());
    }
    final prefs = await SharedPreferences.getInstance();
    // Preserve userName and userEmail before clearing
    final userName = prefs.getString('userName');
    final userEmail = prefs.getString('userEmail');
    await prefs.clear();
    if (kIsWeb) {
      if (userName != null) {
        await prefs.setString('userName', userName);
      }
      if (userEmail != null) {
        await prefs.setString('userEmail', userEmail);
      }
    }
    ref.read(loginStateProvider.notifier).setLoginState(LoginState.domain);
    Future.delayed(Duration(milliseconds: 300), () {
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (Route<dynamic> route) => false,
      );
    });
  }

  void onClickBackArrowButton() async {
    final phoneNumber = ref.watch(editPhoneNumberProvider.notifier);
    final imageData = ref.watch(profileImageProvider.notifier);
    final userHeader = ref.watch(userProvider);
    bool validate = validateData(context);
    if (validate) {
      bool data = await onClickBack();
      if (data) {
        if (mounted) {
          return UIHelper.showConfirmationDialogWithYesOrNo(context,
              description: AppLocalizations.of(context)!
                  .change_of_user_preference_save_confirmation, yes: () {
            Navigator.of(context, rootNavigator: true).pop();
            sendData(context);
            ref
                .read(editProfileScreenProvider.notifier)
                .getEditProfileScreenEnable(true);
          }, no: () async {
            Navigator.of(context, rootNavigator: true).pop();
            List<APP_SETTING_HEADER> list =
                await DbHelper.getAppSettingHeader();
            if (list.isNotEmpty) {
              for (var item in list) {
                if (item.prop_name == 'plantId') {
                  ref
                      .read(plantProvider.notifier)
                      .setPlant(item.prop_value.toString());
                } else if (item.prop_name == 'sectionId') {
                  List<dynamic> decodedList =
                      jsonDecode(item.prop_value.toString());
                  List<String> sectionIds =
                      decodedList.map((e) => e.toString()).toList();
                  ref
                      .read(plantSectionProvider.notifier)
                      .setPlantSection(sectionIds);
                } else if (item.prop_name == 'shiftCode') {
                  ref
                      .read(shiftProvider.notifier)
                      .setShift(item.prop_value.toString());
                }
              }
            }
            final plant = ref.watch(plantProvider.notifier).state;
            ref
                .read(plantSectionListProvider.notifier)
                .fetchPlantsSectionList(plant);
            ref.read(shiftListProvider.notifier).fetchShiftList(plant);

            ref
                .read(editProfileScreenProvider.notifier)
                .getEditProfileScreenEnable(true);
            Navigator.pop(context);
          });
        }
      }
      ref
          .read(editProfileScreenProvider.notifier)
          .getEditProfileScreenEnable(true);

      if (userHeader != null) {
        String base64String = '';
        if (imageData.state != null) {
          base64String = base64Encode(imageData.state!);
        }
        bool dataPhone = userHeader.phone != null && userHeader.phone != ''
            ? userHeader.phone.toString() !=
                phoneNumber.state.replaceAll('', '')
            : false;
            if(kIsWeb){
        String? imageString = await DbHelper()
            .getAttachmentFromIndexDbByUid(ref.read(userProvider)!.user_id!);
        bool dataThumbnail =
            imageString != null ? imageString != base64String : false;
        if (dataPhone || dataThumbnail) {
          if (mounted) {
            return UIHelper.showConfirmationDialogWithYesOrNo(context,
                description: AppLocalizations.of(context)!
                    .you_have_unsaved_phone_number, yes: () async {
              Navigator.pop(context);
              await savePhoneNumber();
              Navigator.pop(context);
            }, no: () {
              Navigator.pop(context);
              phoneNumber.getPhoneNumber(userHeader.phone.toString());
              imageData.getImage(base64Decode(imageString!));
              ref
                  .read(editProfileDetailScreenProvider.notifier)
                  .getEditProfileDetailScreenEnable(true);
              Navigator.pop(context);
            });
          }
        } else {
          ref
              .read(editProfileDetailScreenProvider.notifier)
              .getEditProfileDetailScreenEnable(true);
          // Navigator.pop(context);
        }
            }
      }
      // Navigator.pop(context);
      Navigator.pop(context);
      // ref
      //     .read(editProfileScreenProvider.notifier)
      //     .getEditProfileScreenEnable(false);
    } else {
      if (userHeader != null && mounted) {
        String base64String = '';
        String? imageString;
        if (imageData.state != null) {
          base64String = base64Encode(imageData.state!);
        }
        if(kIsWeb){
          imageString = await DbHelper()
            .getAttachmentFromIndexDbByUid(ref.read(userProvider)!.user_id!);
        }
        if (userHeader.phone.toString() !=
                phoneNumber.state.trim().toString() ||
            imageString! != base64String) {
          return UIHelper.showConfirmationDialogWithYesOrNo(context,
              description: AppLocalizations.of(context)!
                  .you_have_unsaved_phone_number, yes: () async {
            Navigator.pop(context);
            await savePhoneNumber();
          }, no: () {
            Navigator.pop(context);
            phoneNumber.getPhoneNumber(userHeader.phone.toString());
            imageData.getImage(base64Decode(imageString!));
            ref
                .read(editProfileDetailScreenProvider.notifier)
                .getEditProfileDetailScreenEnable(true);
          });
        } else {
          ref
              .read(editProfileDetailScreenProvider.notifier)
              .getEditProfileDetailScreenEnable(true);
        }
      }
      ref
          .read(editProfileScreenProvider.notifier)
          .getEditProfileScreenEnable(false);
    }
  }
}
