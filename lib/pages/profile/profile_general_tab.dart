import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/pages/profile/widgets/profile_phone_number_textfield.dart';
import 'package:rounds/pages/profile/profile_screen.dart';
import 'package:rounds/pages/profile/reset_password_screen.dart';
import 'package:rounds/utils/utils.dart';

import '../../../be/USER_HEADER.dart';
import '../../../helpers/ui_helper.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../utils/app_colors.dart';
import '../../widgets/phone_number_textfield.dart';

final editPhoneNumberProvider =
    StateNotifierProvider<EditPhoneNumberNotifier, String>((ref) {
  return EditPhoneNumberNotifier();
});

class EditPhoneNumberNotifier extends StateNotifier<String> {
  EditPhoneNumberNotifier() : super('');

  void getPhoneNumber(String phone) {
    try {
      state = phone;
    } catch (e) {
      Logger.logError(
          'EditPhoneNumberNotifier', 'getPhoneNumber', e.toString());
    }
  }
}

class ProfileGeneralTab extends ConsumerStatefulWidget {
  final USER_HEADER userHeader;
  ProfileGeneralTab({required this.userHeader, super.key});

  @override
  _ProfileGeneralTabState createState() => _ProfileGeneralTabState();
}

class _ProfileGeneralTabState extends ConsumerState<ProfileGeneralTab> {
  TextEditingController phoneNumberController = TextEditingController();
  TextEditingController passwordController = TextEditingController();

  bool isExpanded = true;

  @override
  void initState() {
    phoneNumberController.text = widget.userHeader.phone.toString();
    super.initState();
    Future.microtask(() {
      ref
          .read(editPhoneNumberProvider.notifier)
          .getPhoneNumber(phoneNumberController.text);
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        child: Column(
          children: [
            UIHelper.sizedBox8(),
            getDetails(),
          ],
        ),
      ),
    );
  }

  Widget getDetails() {
    return Padding(
      padding: UIHelper.columnFieldPadding(),
      child: Column(
        children: [
          Container(
            decoration: UIHelper.cardDecoration(),
            child: Padding(
              padding: UIHelper.allPaddingOf10(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  getUserDetailField(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget getUserDetailField() {
    final isEdit = ref.watch(editProfileDetailScreenProvider.notifier).state;
    final phone = ref.watch(editPhoneNumberProvider.notifier);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: UIHelper.columnFieldOnlyVerticalPadding10(),
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(
              AppLocalizations.of(context)!.phone_number,
              style: UIHelper.labelStyle(),
            ),
            ProfilePhoneNumberTextField(
              controller: phoneNumberController,
              readOnly: isEdit ? true : false,
            ),
          ]),
        ),

/*        UIHelper.buildLabelAndValueAsWidgetOfFault(
            label: AppLocalizations.of(context)!.phone_number,
            value: widget.userHeader.phone.toString(),
            controller: phoneNumberController,
            onChanged: (v) {
              phoneNumberController.text = v;
              phone.getPhoneNumber(phoneNumberController.text);
            },
            readOnly: isEdit ? true : false,
            isTextFieldRequiredAsValueWidget: true),*/
        const SizedBox(
          height: 6,
        ),
        ElevatedButton(
            style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                    side: BorderSide(color: AppColors.primaryColor),
                    borderRadius: BorderRadius.circular(8.0)),
                backgroundColor: AppColors.primaryColor,
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 16)),
            onPressed: () {
              if (UIHelper().getScreenType(context) == ScreenType.desktop) {
                showDialog(
                    context: context,
                    builder: (context) {
                      return Dialog(
                        backgroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20.0),
                        ),
                        child: ConstrainedBox(
                            constraints: const BoxConstraints(
                              maxWidth:
                                  420, // Slightly more than a mobile screen width
                              maxHeight: 800,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: ResetPasswordScreen(),
                            )),
                      );
                    });
              } else {
                Navigator.push(context, MaterialPageRoute(builder: (context) {
                  return ResetPasswordScreen();
                }));
              }
            },
            child: Row(
              children: [
                Text(AppLocalizations.of(context)!.resetPassword),
                const Spacer(),
                const Icon(Icons.lock_reset)
              ],
            ))
      ],
    );
  }
}
