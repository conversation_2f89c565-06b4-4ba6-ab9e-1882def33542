import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/pages/downloadDataPage.dart';
import 'package:rounds/pages/login/forgot_password_screen.dart';
import 'package:rounds/utils/app_extensions.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../be/APP_SETTING_HEADER.dart';
import '../../be/SHIFT_HEADER.dart';
import '../../helpers/db_helper.dart';
import '../../helpers/pa_helper.dart';
import '../../helpers/ui_helper.dart';
import '../../providers/fault/fault_header_provider.dart';
import '../../utils/app_colors.dart';
import 'package:http/http.dart' as http;

class ResetPasswordScreen extends ConsumerStatefulWidget {
  ResetPasswordScreen({super.key});

  @override
  ConsumerState<ResetPasswordScreen> createState() =>
      _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends ConsumerState<ResetPasswordScreen> {
  bool isEdit = true;
  TextEditingController oldPasswordController = TextEditingController();
  TextEditingController newPasswordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  bool showTextOfOldPassword = false;
  bool showTextOfNewPassword = false;
  bool showTextOfConfirmNewPassword = false;
  String oldPasswordErrorMsg = '';
  String newPasswordErrorMsg = '';
  String confirmNewPasswordErrorMsg = '';
  String errorMsg = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: AppColors.whiteColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.white,
        leadingWidth: 10,
        leading: IconButton(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: Icon(
              Icons.arrow_back_ios,
              color: AppColors.titleTextColor,
              size: 20,
            )),
        title: Text(
          context.locale.resetPassword,
          style: const TextStyle(
            fontSize: 23,
            fontWeight: FontWeight.w900,
            color: AppColors.blackTitleText,
          ),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: UIHelper.columnFieldPadding(),
          child: SizedBox(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            child: Padding(
              padding: UIHelper.allPaddingOf10(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 10,
                  ),
                  passwordFields()
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget passwordFields() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UIHelper.buildLabelAndValueAsWidgetOfProfileScreen(
            label: AppLocalizations.of(context)!.oldPassword,
            value: '',
            controller: oldPasswordController,
            onChanged: (v) {},
            isPassword: true,
            showText: showTextOfOldPassword,
            customSuffixIcon: IconButton(
              onPressed: () {
                setState(() {
                  showTextOfOldPassword = !showTextOfOldPassword;
                });
              },
              icon: Icon(
                showTextOfOldPassword ? Icons.visibility : Icons.visibility_off,
                color: AppColors.buttonColor,
              ),
            )),
        oldPasswordErrorMsg != ''
            ? Container(
                padding: const EdgeInsets.only(left: 5.0, bottom: 5.0),
                width: double.infinity,
                child: Text(
                  oldPasswordErrorMsg,
                  style: const TextStyle(color: Colors.redAccent),
                ),
              )
            : SizedBox(),
        UIHelper.buildLabelAndValueAsWidgetOfProfileScreen(
            label: AppLocalizations.of(context)!.newPassword,
            value: '',
            controller: newPasswordController,
            onChanged: (v) {
              final cursorPosition = newPasswordController.selection.baseOffset;

              String filteredText =
                  v.replaceAll(RegExp(r"[^A-Za-z\d!@#\$%^&*()-_=+<>?]"), '');

              newPasswordController.value = TextEditingValue(
                text: filteredText,
                selection: TextSelection.collapsed(
                  offset: cursorPosition > filteredText.length
                      ? filteredText.length
                      : cursorPosition,
                ),
              );

              if (newPasswordController.text.isNotEmpty) {
                setState(() {
                  newPasswordErrorMsg =
                      UIHelper().isValidPassword(newPasswordController.text)
                          ? ''
                          : AppLocalizations.of(context)!.password_validation;
                });
              }
            },
            isPassword: true,
            showText: showTextOfNewPassword,
            customSuffixIcon: IconButton(
              onPressed: () {
                setState(() {
                  showTextOfNewPassword = !showTextOfNewPassword;
                });
              },
              icon: Icon(
                showTextOfNewPassword ? Icons.visibility : Icons.visibility_off,
                color: AppColors.buttonColor,
              ),
            )),
        newPasswordErrorMsg != ''
            ? Container(
                padding: const EdgeInsets.only(left: 5.0, bottom: 5.0),
                width: double.infinity,
                child: Text(
                  newPasswordErrorMsg,
                  style: const TextStyle(color: Colors.redAccent),
                ),
              )
            : SizedBox(),
        UIHelper.buildLabelAndValueAsWidgetOfProfileScreen(
            label: AppLocalizations.of(context)!.confirmPassword,
            value: '',
            controller: confirmPasswordController,
            onChanged: (v) {
              final cursorPosition =
                  confirmPasswordController.selection.baseOffset;

              String filteredText =
                  v.replaceAll(RegExp(r"[^A-Za-z\d!@#\$%^&*()-_=+<>?]"), '');

              confirmPasswordController.value = TextEditingValue(
                text: filteredText,
                selection: TextSelection.collapsed(
                  offset: cursorPosition > filteredText.length
                      ? filteredText.length
                      : cursorPosition,
                ),
              );

              if (confirmPasswordController.text.isNotEmpty) {
                setState(() {
                  confirmNewPasswordErrorMsg =
                      UIHelper().isValidPassword(confirmPasswordController.text)
                          ? ''
                          : AppLocalizations.of(context)!.password_validation;
                });
              }
            },
            isPassword: true,
            showText: showTextOfConfirmNewPassword,
            customSuffixIcon: IconButton(
              onPressed: () {
                setState(() {
                  showTextOfConfirmNewPassword = !showTextOfConfirmNewPassword;
                });
              },
              icon: Icon(
                showTextOfConfirmNewPassword
                    ? Icons.visibility
                    : Icons.visibility_off,
                color: AppColors.buttonColor,
              ),
            )),
        confirmNewPasswordErrorMsg != ''
            ? Container(
                padding: const EdgeInsets.only(left: 5.0, bottom: 5.0),
                width: double.infinity,
                child: Text(
                  confirmNewPasswordErrorMsg,
                  style: const TextStyle(color: Colors.redAccent),
                ),
              )
            : SizedBox(),
        Padding(
          padding: const EdgeInsets.only(right: 2.0),
          child: Align(
            alignment: Alignment.centerRight,
            child: InkWell(
              onTap: () {
                Navigator.push(context, MaterialPageRoute(builder: (context) {
                  return ForgotPasswordScreen();
                }));
              },
              child: Text(
                AppLocalizations.of(context)!.forgot_password,
                style: TextStyle(color: AppColors.primaryColor, fontSize: 14),
              ),
            ),
          ),
        ),
        SizedBox(height: 20),
        errorMsg != ''
            ? Align(
                alignment: Alignment.center,
                child: Text(
                  errorMsg,
                  style: const TextStyle(color: Colors.redAccent),
                ))
            : SizedBox(),
        errorMsg != '' ? SizedBox(height: 10) : SizedBox(),
        Row(
          children: [
            Expanded(
              child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                          side: BorderSide(color: AppColors.primaryColor),
                          borderRadius: BorderRadius.circular(8.0)),
                      backgroundColor: AppColors.primaryColor,
                      padding:
                          EdgeInsets.symmetric(vertical: 12, horizontal: 16)),
                  onPressed: () async {
                    if (validate()) {
                    } else {
                      await submit(
                          oldPasswordData: oldPasswordController.text,
                          newPasswordData: newPasswordController.text);
                    }
                  },
                  child: Text(AppLocalizations.of(context)!.submit)),
            ),
          ],
        ),
      ],
    );
  }

  bool validate() {
    if (oldPasswordController.text.isEmpty) {
      setState(() {
        oldPasswordErrorMsg =
            AppLocalizations.of(context)!.please_enter_old_password;
      });
      return true;
    } else if (newPasswordController.text.isEmpty) {
      setState(() {
        newPasswordErrorMsg =
            AppLocalizations.of(context)!.please_enter_new_password;
      });
      return true;
    } else if (confirmPasswordController.text.isEmpty) {
      setState(() {
        confirmNewPasswordErrorMsg =
            AppLocalizations.of(context)!.please_enter_confirm_password;
      });
      return true;
    } else if (newPasswordController.text != confirmPasswordController.text) {
      setState(() {
        confirmNewPasswordErrorMsg =
            AppLocalizations.of(context)!.password_doesnot_match;
      });
      return true;
    }
    return false;
  }

  Future<void> submit(
      {required String oldPasswordData,
      required String newPasswordData}) async {
    try {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.resetting_password);
      String user = await SettingsHelper().getUserName();
      String company = await SettingsHelper().getCompany();
      String application = SettingsHelper().getApplicationName();
      String umpUrl = await SettingsHelper().getUrl();
      String currentPassword = oldPasswordData;
      String newPassword = newPasswordData;

      String authHeader = base64Encode(utf8.encode('$user:$currentPassword'));

      var headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Basic $authHeader'
      };

      var url = Uri.parse(
          '$umpUrl/API/v3/companies/$company/users/$user/changepassword?application=$application&newPassword=$newPassword');

      var request = http.Request('PUT', url);
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();
      Navigator.pop(context);
      if (response.statusCode == 204) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text("Password changed successfully!"),
            backgroundColor: AppColors.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.all(20),
            duration: Duration(seconds: 30),
          ),
        );
        Navigator.pop(context);
      } else {
        String responseBody = await response.stream.bytesToString();
        Map<String, dynamic> responseData = jsonDecode(responseBody);
        setState(() {
          errorMsg = responseData['error'] ?? "Unknown error";
        });
      }
    } catch (e) {
      Logger.logError('ResetPasswordScreen', 'changePassword', e.toString());
    }
  }

  void onCheck() {
    bool validate = validateData(context);
    if (validate) {
      UIHelper.showConfirmationDialogWithYesOrNo(
        context,
        description:
            AppLocalizations.of(context)!.are_you_sure_want_change_plant,
        yes: () {
          Navigator.pop(context);
          sendData(context);
        },
        no: () {
          Navigator.pop(context);
        },
      );
    }
  }

  validateData(BuildContext context) async {
    bool data = false;
    final plant = ref.read(plantProvider);
    final plantSection = ref.read(plantSectionProvider);
    final shift = ref.read(shiftProvider);

    if (plant == '') {
      if (mounted) {
        ref.watch(plantValidationsProvider.notifier).getPlantValidation(true);
      }
      return data;
    } else if (plantSection.isEmpty) {
      if (mounted) {
        ref
            .read(plantSectionValidationsProvider.notifier)
            .getPlantSectionValidation(true);
      }
      return data;
    } else if (shift == '') {
      if (mounted) {
        ref.read(shiftValidationsProvider.notifier).getShiftValidation(true);
      }
      return data;
    } else {
      if (mounted) {
        ref.read(plantValidationsProvider.notifier).clearPlantValidation();
        ref
            .read(plantSectionValidationsProvider.notifier)
            .clearPlantSectionValidation();
        ref.read(shiftValidationsProvider.notifier).clearShiftValidation();
      }
      return true;
    }
  }

  sendData(BuildContext context) async {
    final plant = ref.read(plantProvider);
    final plantSection = ref.read(plantSectionProvider);
    final shift = ref.read(shiftProvider);
    SHIFT_HEADER? shiftHeader = await DbHelper.getShiftHeader(shift, plant);
    String shiftValue = '';
    if (shiftHeader != null) {
      shiftValue = shiftHeader.shift_code.toString();
    }

    Map<String, dynamic> setData = {
      "plantId": plant,
      "sectionId": plantSection,
      "shiftCode": shiftValue
    };
    try {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.switching_to_selected_plant);
      await PAHelper.setUserPreferenceLogin(context, setData);
      Result result = await PAHelper.getUserPreferenceLogin(context);

      List<APP_SETTING_HEADER> settingsList =
          result.body.entries.map<APP_SETTING_HEADER>((entry) {
        return APP_SETTING_HEADER(
          prop_name: entry.key,
          prop_value: entry.value is List
              ? jsonEncode(entry.value.map((e) => e.toString()).toList())
              : entry.value.toString(),
        );
      }).toList();
      await AppDatabaseManager()
          .execute("DELETE FROM ${APP_SETTING_HEADER.TABLE_NAME}");
      for (var setting in settingsList) {
        await AppDatabaseManager().update(
            DBInputEntity(APP_SETTING_HEADER.TABLE_NAME, setting.toJson()));
      }
    } catch (e) {
      Logger.logError('ResetPasswordScreen', 'validateData', e.toString());
    }
    List<APP_SETTING_HEADER> list = await DbHelper.getAppSettingHeader();
    if (list.isNotEmpty) {
      for (var item in list) {
        if (item.prop_name == 'plantId') {
          ref.read(plantProvider.notifier).setPlant(item.prop_value.toString());
        } else if (item.prop_name == 'sectionId') {
          List<dynamic> decodedList = jsonDecode(item.prop_value.toString());
          List<String> sectionIds =
              decodedList.map((e) => e.toString()).toList();
          ref.read(plantSectionProvider.notifier).setPlantSection(sectionIds);
        } else if (item.prop_name == 'shiftCode') {
          ref.read(shiftProvider.notifier).setShift(item.prop_value.toString());
        }
      }
    }
    if (!kIsWeb) {
      await SettingsHelper().setFetchInterval(15);
    }
    Navigator.pop(context);
    Navigator.pop(context);
    Navigator.push(context, MaterialPageRoute(builder: (context) {
      return const DownloadDataPage();
    }));
  }
}
