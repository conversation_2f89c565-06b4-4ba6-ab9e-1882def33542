import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/PLANT_SECTION_HEADER.dart';
import 'package:rounds/pages/profile/profile_screen.dart';
import '../../../be/USER_HEADER.dart';
import '../../../helpers/ui_helper.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../../providers/fault/fault_header_provider.dart';
import '../../../utils/app_colors.dart';

class ProfileUserPreferenceTab extends ConsumerStatefulWidget {
  final USER_HEADER userHeader;
  ProfileUserPreferenceTab({required this.userHeader, super.key});

  @override
  _ProfileUserPreferenceTabState createState() =>
      _ProfileUserPreferenceTabState();
}

class _ProfileUserPreferenceTabState
    extends ConsumerState<ProfileUserPreferenceTab> {
  String selectedPlant = '';
  String selectedShift = '';
  late Future<bool> selected;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    selectedPlant = ref.read(plantProvider);
    selectedShift = ref.read(shiftProvider);
    selectedPlantSection = ref.read(plantSectionProvider);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          UIHelper.sizedBox8(),
          getDetails(),
        ],
      ),
    );
  }

  Widget getDetails() {
    return Padding(
      padding: UIHelper.columnFieldPadding(),
      child: Column(
        children: [
          Container(
            decoration: UIHelper.cardDecoration(),
            child: Padding(
              padding: UIHelper.allPaddingOf10(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  getUserDetailField(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget getUserDetailField() {
    final plantValidation = ref.watch(plantValidationsProvider);
    final plantSectionValidation = ref.watch(plantSectionValidationsProvider);
    final shiftValidation = ref.watch(shiftValidationsProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UIHelper.buildLabelAndValueAsWidgetOfUserPreferenceDropDown(
            label: AppLocalizations.of(context)!.plant,
            value: getPlantDropdown(),
            isTextFieldRequiredAsValueWidget: true),
        plantValidation ? getError('plant') : Container(),
        const SizedBox(height: 16),
        UIHelper.buildLabelAndValueAsWidgetOfUserPreferenceDropDown(
            label: AppLocalizations.of(context)!.plantSection,
            value: getPlantSectionDropdown(),
            isTextFieldRequiredAsValueWidget: true),
        plantSectionValidation ? getError('plant sections') : Container(),
        const SizedBox(height: 16),
        UIHelper.buildLabelAndValueAsWidgetOfUserPreferenceDropDown(
            label: AppLocalizations.of(context)!.shift,
            value: getShiftDropdown(),
            isTextFieldRequiredAsValueWidget: true),
        shiftValidation ? getError('shift') : Container(),
      ],
    );
  }

  Widget getPlantDropdown() {
    final isEdit = ref.watch(editProfileScreenProvider.notifier).state;
    final plants = ref.watch(plantListProvider.notifier).state;
    final plant = ref.watch(plantProvider);
    if (plant != '') {
      setState(() {
        selectedPlant = plant;
      });
    }

    final dropdownItems = plants.map((option) {
      return DropdownMenuItem<String>(
        value: option.plant_id,
        child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(option.plant_id!, style: UIHelper.valueBoldStyle()),
                Padding(
                  padding: const EdgeInsets.only(top: 1.0),
                  child: Text(option.plant_name!,
                      style: UIHelper.descriptionStyle()),
                ),
              ],
            )),
      );
    }).toList();

    if (dropdownItems.where((item) => item.value == '').isEmpty) {
      dropdownItems.insert(
        0,
        DropdownMenuItem<String>(
          value: '',
          child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(AppLocalizations.of(context)!.select,
                style: UIHelper.valueStyle()),
          ),
        ),
      );
    }
    return Container(
      decoration: UIHelper.fieldDecoration(),
      child: DropdownButton<String>(
        elevation: 0,
        isExpanded: true,
        underline: const SizedBox(),
        padding: const EdgeInsets.only(right: 10),
        value: selectedPlant.isNotEmpty ? selectedPlant : '',
        items: dropdownItems,
        onChanged: isEdit
            ? null
            : (newValue) {
                onChangePlant(newValue!);
              },
        disabledHint: Padding(
          padding: const EdgeInsets.only(left: 5.0),
          child: Text(
            selectedPlant.isNotEmpty
                ? selectedPlant
                : AppLocalizations.of(context)!.select,
            style: UIHelper.valueStyle(),
          ),
        ),
      ),
    );
  }

  void onChangePlant(String newValue) async {
    final plantValidation = ref.read(plantValidationsProvider.notifier);
    if (mounted) {
      setState(() {
        selectedPlant = newValue;
        if (selectedPlant == '' ||
            selectedPlant == AppLocalizations.of(context)!.select) {
          plantValidation.getPlantValidation(true);
        } else {
          plantValidation.clearPlantValidation();
        }
      });
    }
    if (selectedPlant.isNotEmpty &&
        selectedPlant != AppLocalizations.of(context)!.select) {
      ref.read(plantProvider.notifier).setPlant(selectedPlant);
      try {
        await ref
            .read(plantSectionListProvider.notifier)
            .fetchPlantsSectionList(selectedPlant);
        await ref
            .read(shiftListProvider.notifier)
            .fetchShiftList(selectedPlant);
        selectedPlantSection = [];
        ref.read(plantSectionProvider.notifier).clearPlantSection();
        selectedShift = '';
        ref.read(shiftProvider.notifier).clearShift();
      } catch (e) {
        Logger.logError(
            'ProfileUserPreferenceTab', 'onChangePlant', e.toString());
      }
    }
  }

  List<String> selectedPlantSection = [];
  bool isPlantSectionDropdownOpen = false;

  Widget getPlantSectionDropdown() {
    final isEdit = ref.watch(editProfileScreenProvider.notifier).state;

    final plant = ref.watch(plantProvider.notifier).state;

    final plantSections = ref
        .watch(plantSectionListProvider.notifier)
        .state
        .where((element) => element.plant_id == plant)
        .toList();

    final plantSectionValidation =
        ref.watch(plantSectionValidationsProvider.notifier);
    final plantSection = ref.watch(plantSectionProvider);

    if (plantSection.isNotEmpty) {
      selectedPlantSection = plantSection;
    }

    return Container(
      decoration: UIHelper.fieldDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: isEdit
                ? null
                : () {
                    setState(() {
                      isPlantSectionDropdownOpen = !isPlantSectionDropdownOpen;
                    });
                  },
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 10.0, vertical: 12.0),
              child: Row(
                children: [
                  Expanded(
                    child: selectedPlantSection.isNotEmpty
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: selectedPlantSection.map((sectionId) {
                              final section = plantSections.firstWhere(
                                (e) => e.section_id == sectionId,
                                orElse: () => PLANT_SECTION_HEADER(
                                    plant_id: '',
                                    section_id: ''), // create dummy if needed
                              );
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(section.section_id ?? '',
                                      style: UIHelper.valueBoldStyle()),
                                  Text(section.description ?? '',
                                      style: UIHelper.descriptionStyle()),
                                ],
                              );
                            }).toList(),
                          )
                        : Text(AppLocalizations.of(context)!.select,
                            style: UIHelper.valueStyle()),
                  ),
                  isEdit
                      ? Icon(
                          Icons.arrow_drop_down,
                          color: AppColors.grey,
                        )
                      : Icon(
                          isPlantSectionDropdownOpen
                              ? Icons.arrow_drop_up
                              : Icons.arrow_drop_down,
                        ),
                ],
              ),
            ),
          ),
          if (isPlantSectionDropdownOpen)
            Container(
              color: AppColors.white,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: plantSections.length,
                itemBuilder: (context, index) {
                  final option = plantSections[index];
                  final isSelected =
                      selectedPlantSection.contains(option.section_id);

                  return CheckboxListTile(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 5.0),
                    activeColor: AppColors.primaryColor,
                    title: Padding(
                      padding: const EdgeInsets.only(left: 5.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(option.section_id ?? '',
                              style: UIHelper.valueBoldStyle()),
                          Padding(
                            padding: const EdgeInsets.only(top: 1.0),
                            child: Text(option.description ?? '',
                                style: UIHelper.descriptionStyle()),
                          ),
                        ],
                      ),
                    ),
                    value: isSelected,
                    onChanged: isEdit
                        ? null
                        : (checked) {
                            setState(() {
                              if (checked == true) {
                                selectedPlantSection.add(option.section_id!);
                              } else {
                                selectedPlantSection.remove(option.section_id!);
                              }

                              if (selectedPlantSection.isEmpty) {
                                plantSectionValidation
                                    .getPlantSectionValidation(true);
                              } else {
                                plantSectionValidation
                                    .clearPlantSectionValidation();
                              }

                              ref
                                  .read(plantSectionProvider.notifier)
                                  .setPlantSection(selectedPlantSection);
                            });
                          },
                  );
                },
              ),
            ),
        ],
      ),
    );
  }
  /*Widget getPlantSectionDropdown() {
    final isEdit = ref.watch(editProfileScreenProvider.notifier).state;

    final plant = ref.watch(plantProvider.notifier).state;
    final plantSections = ref
        .watch(plantSectionListProvider.notifier)
        .state
        .where((element) => element.plant_id == plant)
        .toList();
    final plantSectionValidation =
        ref.watch(plantSectionValidationsProvider.notifier);

    final dropdownItems = plantSections.map((option) {
      return MultiSelectItem<String>(
        option.section_id!,
        "${option.section_id!} - ${option.description!}",
      );
    }).toList();

    List<PLANT_SECTION_HEADER> data =
        getMatchingPlantSections(selectedPlantSection, plantSections);

    final dropdownItems1 = data.map((option) {
      return MultiSelectItem<String>(
        option.section_id!,
        "${option.section_id!} - ${option.description!}",
      );
    }).toList();

    return MultiSelectDropDown(
      allOptions: isEdit
          ? selectedPlantSection.isNotEmpty
              ? dropdownItems1
              : []
          : ((plant == "" ? [] : dropdownItems)),
      edit: isEdit,
      selected: selectedPlantSection,
      buttonText: plant == "" ? "" : AppLocalizations.of(context)!.select,
      absorbing: isEdit ? true : (dropdownItems.isNotEmpty ? false : true),
      onConfirm: plant == ""
          ? (v) {}
          : (v) {
              selectedPlantSection = v;
              if (selectedPlantSection.isEmpty ||
                  selectedPlantSection
                      .contains(AppLocalizations.of(context)!.select)) {
                plantSectionValidation.getPlantSectionValidation(true);
              }
              ref
                  .read(plantSectionProvider.notifier)
                  .setPlantSection(selectedPlantSection);
            },
    );
  }

  List<PLANT_SECTION_HEADER> getMatchingPlantSections(
      List<String> selectedPlantSection,
      List<PLANT_SECTION_HEADER> plantSectionList) {
    return selectedPlantSection
        .map((sectionId) {
          return plantSectionList
              .where(
                (plant) => plant.section_id == sectionId,
              )
              .toList();
        })
        .expand((item) => item)
        .toList();
  }*/

  Widget getShiftDropdown() {
    final isEdit = ref.watch(editProfileScreenProvider.notifier).state;
    final plant = ref.watch(plantProvider.notifier).state;
    final shifts = ref
        .read(shiftListProvider)
        .where((element) => element.plant_id == plant);

    final shiftValidation = ref.read(shiftValidationsProvider.notifier);
    final shift = ref.watch(shiftProvider);
    if (shift != '') {
      selectedShift = shift;
    }
    final dropdownItems = shifts.map((option) {
      return DropdownMenuItem<String>(
        value: option.shift_code,
        child: Padding(
          padding: const EdgeInsets.only(left: 5.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(option.shift_code!, style: UIHelper.valueBoldStyle()),
              Padding(
                padding: const EdgeInsets.only(top: 1.0),
                child: Text(option.shift_name!,
                    style: UIHelper.descriptionStyle()),
              ),
            ],
          ),
        ),
      );
    }).toList();

    if (dropdownItems.where((item) => item.value == '').isEmpty) {
      dropdownItems.insert(
        0,
        DropdownMenuItem<String>(
          value: '',
          child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(AppLocalizations.of(context)!.select,
                style: UIHelper.valueStyle()),
          ),
        ),
      );
    }
    return Container(
        decoration: UIHelper.fieldDecoration(),
        child: DropdownButton<String>(
            elevation: 0,
            isExpanded: true,
            underline: const SizedBox(),
            padding: EdgeInsets.only(right: 10),
            value: selectedShift.isNotEmpty ? selectedShift : '',
            items: dropdownItems,
            onChanged: isEdit
                ? null
                : (newValue) {
                    if (mounted) {
                      setState(() {
                        selectedShift = newValue!;
                        if (selectedShift == '' ||
                            selectedShift ==
                                AppLocalizations.of(context)!.select) {
                          shiftValidation.getShiftValidation(true);
                        } else {
                          shiftValidation.clearShiftValidation();
                        }
                      });
                    }
                    if (selectedPlant.isNotEmpty &&
                        selectedPlant != AppLocalizations.of(context)!.select) {
                      ref.read(shiftProvider.notifier).setShift(selectedShift);
                    }
                  },
            disabledHint: Padding(
              padding: const EdgeInsets.only(left: 5.0),
              child: Text(
                selectedShift.isNotEmpty
                    ? selectedShift
                    : AppLocalizations.of(context)!.select,
                style: UIHelper.valueStyle(),
              ),
            )));
  }

  getError(String errorText) {
    return Text(
      '${AppLocalizations.of(context)!.please_select_mandatory} $errorText',
      style: TextStyle(color: AppColors.redColor),
    );
  }
}
