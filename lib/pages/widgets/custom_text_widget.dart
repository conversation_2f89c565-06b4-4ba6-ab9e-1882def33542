import 'package:flutter/material.dart';
import 'package:rounds/utils/app_colors.dart';

class CustomHeadingText extends StatelessWidget {
  final String text;

  const CustomHeadingText({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final fontSize = screenWidth > 600 ? 46 : 36; // Adjust thresholds as needed

    return Text(
      text,
      style: TextStyle(
        fontSize: fontSize.toDouble(),
        fontWeight: FontWeight.w900,
        color: AppColors.blackTitleText,
      ),
    );
  }
}

class CustomSubTitleText extends StatelessWidget {
  final String text;const CustomSubTitleText({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final fontSize = screenWidth > 600 ? 17 : 14; // Adjust thresholds as needed

    return Text(
      text,
      style: TextStyle(
        fontSize: fontSize.toDouble(),
        fontWeight: FontWeight.w500,
        color: AppColors.subTitleTextColor,
      ),
    );
  }
}