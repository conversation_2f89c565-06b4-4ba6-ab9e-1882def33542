import 'package:flutter/material.dart';

class TabItemModel {
  final String title;
  final int? count;

  TabItemModel({
    required this.title,
    this.count,
  });
}

class _TabItem extends StatelessWidget {
  final String title;
  final bool isSelected;
  final int? count;

  const _TabItem({
    Key? key,
    required this.title,
    required this.isSelected,
    this.count,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: 30,
      padding: EdgeInsets.symmetric(horizontal: isSelected ? 10 : 8),
      decoration: BoxDecoration(
        color: isSelected ? Color(0xFFDDE6B0) : Color(0xffF6F6F7),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isSelected ? Color(0xFFCBD699) : Color(0xffE5E8EA),
        ),
      ),
      child: Center(
        child: Row(
          children: [
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 300),
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
                color: isSelected ? Colors.black : Colors.grey,
              ),
              child: Text(title),
            ),
            if (count != null && count! > 0) ...[
              const SizedBox(width: 4),
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: isSelected ? Color(0xFFCBD699) : Colors.white,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: AnimatedDefaultTextStyle(
                    duration: const Duration(milliseconds: 300),
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 10,
                      color: isSelected ? Colors.black : Colors.grey,
                    ),
                    child: Text(count.toString()),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class EamTabs extends StatefulWidget {
  final List<TabItemModel> items;
  final Function(int, String)? onChanged;

  const EamTabs({
    Key? key,
    required this.items,
    this.onChanged,
  }) : super(key: key);

  @override
  State<EamTabs> createState() => _EamTabsState();
}

class _EamTabsState extends State<EamTabs> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      physics: const BouncingScrollPhysics(),
      child: Row(
        children: List.generate(
          widget.items.length,
          (index) => Row(
            children: [
              InkWell(
                customBorder: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                onTap: () {
                  setState(() {
                    widget.onChanged?.call(index, widget.items[index].title);
                    _selectedIndex = index;
                  });
                },
                child: _TabItem(
                  title: widget.items[index].title,
                  count: widget.items[index].count,
                  isSelected: index == _selectedIndex,
                ),
              ),
              const SizedBox(width: 10),
            ],
          ),
        ),
      ),
    );
  }
}
