import 'package:flutter/material.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:rounds/models/menu_action_item.dart';
import 'package:rounds/pages/widgets/tooltip_shape.dart';

import '../../utils/app_colors.dart';

double _height = 30.0;

class MenuActionButton extends StatelessWidget {
  final IconData? icon;
  final Color? iconColor;
  final List<MenuActionItem> options;
  final Function(MenuActionItem) onOptionItemSelected;
  final bool? showborder;
  const MenuActionButton(
      {Key? key,
      this.icon,
      required this.options,
      required this.onOptionItemSelected,
      this.showborder,
      this.iconColor})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: _height,
      width: _height,
      decoration: showborder ?? false
          ? BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: HexColor("#D5DADD"),
              ))
          : null,
      child: PopupMenuButton<MenuActionItem>(
        offset: const Offset(0, 50),
        shape: const TooltipShape(),
        icon: Icon(
          Icons.more_vert,
          color: AppColors.titleTextColor,
        ),
        onSelected: (MenuActionItem value) {
          onOptionItemSelected(value);
        },
        itemBuilder: (BuildContext context) {
          return options.map((MenuActionItem menuOptionItem) {
            return PopupMenuItem<MenuActionItem>(
              value: menuOptionItem,
              child: Container(
                  width: double.infinity, child: Text(menuOptionItem.name)),
            );
          }).toList();
        },
      ),
    );
  }
}
