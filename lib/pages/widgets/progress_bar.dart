import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../providers/cilt/cilt_plan_header_provider.dart';
import '../../providers/progress_provider.dart';

import '../../utils/app_styles.dart';

class ProgressIndicators extends ConsumerStatefulWidget {
  const ProgressIndicators({super.key});

  @override
  ConsumerState<ProgressIndicators> createState() => _ProgressIndicatorState();
}

class _ProgressIndicatorState extends ConsumerState<ProgressIndicators>
    with TickerProviderStateMixin {
  late AnimationController controller;
  bool determinate = false;

  @override
  void initState() {
    controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    )..addListener(() {
        setState(() {});
      });
    controller.repeat();
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final progress = ref.watch(progressProvider);
    return progress.inboxCount == 0 || kIsWeb
        ? const SizedBox()
        : Column(
            children: <Widget>[
              const SizedBox(height: 20),
              LinearProgressIndicator(
                value: controller.value,
              ),
              const SizedBox(height: 10),
              Text(
                "${AppLocalizations.of(context)!.data_sync_in_progress}${progress.inboxCount} ${AppLocalizations.of(context)!.packets_remaining}",
                style: AppStyles.textStyle_14_600w,
              ),
            ],
          );
  }
}
