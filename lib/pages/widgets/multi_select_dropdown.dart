import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:multi_select_flutter/chip_display/multi_select_chip_display.dart';
import 'package:multi_select_flutter/dialog/multi_select_dialog_field.dart';
import 'package:multi_select_flutter/util/multi_select_item.dart';

import '../../utils/app_colors.dart';

class MultiSelectDropDown extends StatefulWidget {
  final List<MultiSelectItem<String>> allOptions;
  final List<String> selected;
  final Function(List<String>) onConfirm;
  String? buttonText;
  bool edit;
  final bool absorbing;

  MultiSelectDropDown({
    super.key,
    required this.allOptions,
    required this.onConfirm,
    required this.selected,
    required this.absorbing,
    this.buttonText,
    this.edit = false,
  });

  @override
  _MultiSelectDropDownState createState() => _MultiSelectDropDownState();
}

class _MultiSelectDropDownState extends State<MultiSelectDropDown> {
  List<String> _selectedOptions = [];

  @override
  Widget build(BuildContext context) {
    _selectedOptions = widget.selected;
    return AbsorbPointer(
        absorbing: widget.absorbing,
        child: MultiSelectDialogField<String>(
            items: widget.allOptions,
            initialValue: _selectedOptions,
            dialogHeight: MediaQuery.of(context).size.height *
                0.12 *
                widget.allOptions.length /
                1.5,
            decoration: BoxDecoration(
              border: Border.all(
                color: AppColors.cardBorderGrey,
                width: 1, // Border width
              ),
              borderRadius: BorderRadius.circular(5.0),
            ),
            buttonText: Text(widget.buttonText ?? ""),
            buttonIcon: (widget.allOptions.isEmpty
                ? Icon(Icons.arrow_drop_down, color: AppColors.grey)
                : (widget.edit
                    ? Icon(Icons.arrow_drop_down, color: AppColors.grey)
                    : Icon(Icons.arrow_drop_down))),
            onConfirm: (selected) {
              if (widget.onConfirm != null) {
                try {
                  widget.onConfirm(List<String>.from(selected));
                } catch (e) {
                  Logger.logInfo("MultiSelectDropDown", "build",
                      "Error while onConfirm selected items");
                }
              } else {
                Logger.logInfo("MultiSelectDropDown", "build",
                    "Error while onConfirm selected items");
              }
            },
            chipDisplay: MultiSelectChipDisplay(
              height: 80,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
              items: _selectedOptions.isNotEmpty
                  ? _selectedOptions
                      .map((e) => MultiSelectItem<String>(e, e))
                      .toList()
                  : [
                      MultiSelectItem<String>(
                          "No items selected", "No items selected"),
                    ],
            )));
  }
}
