import 'package:flutter/material.dart';

class CustomActionMenu extends StatelessWidget {
  final List<ActionMenuItem> menuItems;

  const CustomActionMenu({Key? key, required this.menuItems}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: menuItems.map((item) {
        return ListTile(
          leading: Icon(item.icon),
          title: Text(item.title),
          onTap: () {
            Navigator.pop(context); // Close the menu
            item.onTap(); // Execute the provided action
          },
        );
      }).toList(),
    );
  }
}

class ActionMenuItem {
  final String title;
  final IconData icon;
  final VoidCallback onTap;

  ActionMenuItem({required this.title, required this.icon, required this.onTap});
}
