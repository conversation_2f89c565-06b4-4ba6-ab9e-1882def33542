import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:rounds/utils/app_extensions.dart';
import 'package:rounds/utils/app_styles.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

enum TextDisplayType { error, longText, warning, msg }

class MobileErrorAndLongTextview extends StatelessWidget {
  final String text;
  final String? title;
  final TextDisplayType type;

  final TextStyle? titleStyle;
  final TextStyle? bodyStyle;
  final Color? backgroundColor;
  final Color? borderColor;
  final EdgeInsetsGeometry padding;
  final int previewLines;
  final String? readMoreText;
  final TextStyle? readMoreStyle;

  const MobileErrorAndLongTextview({
    super.key,
    required this.text,
    this.title,
    this.type = TextDisplayType.longText,
    this.titleStyle,
    this.bodyStyle,
    this.backgroundColor,
    this.borderColor,
    this.padding = const EdgeInsets.all(8),
    this.previewLines = 2,
    this.readMoreText,
    this.readMoreStyle,
  });

  Color? getBackgroundColor(TextDisplayType? type) {
    switch (type) {
      case TextDisplayType.error:
        return const Color(0xFFFCE4EC);
      case TextDisplayType.warning:
        return const Color(0xFFFFE0B2);
      case TextDisplayType.msg:
        return const Color(0xFFE8F5E9);
      default:
        return Colors.grey[100];
    }
  }

  Color getBorderColor(TextDisplayType? type) {
    switch (type) {
      case TextDisplayType.error:
        return Colors.red;
      case TextDisplayType.warning:
        return Colors.orange;
      case TextDisplayType.msg:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  TextStyle getTitleStyle(TextDisplayType? type) {
    switch (type) {
      case TextDisplayType.error:
        return AppStyles.headLine15_600.copyWith(color: Colors.red);
      case TextDisplayType.warning:
        return AppStyles.headLine15_600.copyWith(color: Colors.orange);
      case TextDisplayType.msg:
        return AppStyles.headLine15_600.copyWith(color: Colors.green);
      default:
        return AppStyles.headLine15_600;
    }
  }

  @override
  Widget build(BuildContext context) {
    final defaultBgColor = getBackgroundColor(type);

    final defaultBorderColor = getBorderColor(type);

    final defaultTitleStyle = getTitleStyle(type);

    final effectiveBgColor = backgroundColor ?? defaultBgColor;
    final effectiveBorderColor = borderColor ?? defaultBorderColor;
    final effectiveBodyStyle =
        bodyStyle ?? AppStyles.headLine15_600.copyWith(color: Colors.black);
    final effectiveReadMoreStyle = readMoreStyle ??
        AppStyles.headLine13_600
            .copyWith(color: Theme.of(context).primaryColor);
    final effectiveReadMoreText =
        readMoreText ?? AppLocalizations.of(context)!.read_more;

    final textSpan = TextSpan(
      text: text,
      style: effectiveBodyStyle,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      maxLines: previewLines,
    );

    textPainter.layout(maxWidth: MediaQuery.of(context).size.width - 40);

    final exceedsMaxLines = textPainter.didExceedMaxLines;

    return Container(
      width: double.maxFinite,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: effectiveBgColor,
        border: Border.all(color: effectiveBorderColor),
      ),
      child: Padding(
        padding: padding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            if (title != null && title!.isNotEmpty)
              Text(
                title!,
                style: titleStyle ?? defaultTitleStyle,
              ),
            if (title != null && title!.isNotEmpty) const SizedBox(height: 4),
            if (text.isNotEmpty)
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: exceedsMaxLines
                          ? _getTruncatedText(
                              text, previewLines, context, effectiveBodyStyle)
                          : text,
                      style: effectiveBodyStyle,
                    ),
                    if (exceedsMaxLines)
                      TextSpan(
                        text: " $effectiveReadMoreText",
                        style: effectiveReadMoreStyle,
                        recognizer: TapGestureRecognizer()
                          ..onTap = () => _showBottomSheet(context),
                      ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _getTruncatedText(
      String text, int maxLines, BuildContext context, TextStyle style) {
    final textSpan = TextSpan(text: text, style: style);
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      maxLines: maxLines,
    );
    textPainter.layout(maxWidth: MediaQuery.of(context).size.width - 60);

    final endPosition = textPainter.getPositionForOffset(Offset(
      textPainter.width,
      textPainter.height,
    ));

    final endOffset = endPosition.offset;

    if (endOffset < text.length) {
      return text.substring(0, max(0, endOffset - 10)) + '...';
    }
    return text;
  }

  int max(int a, int b) => a > b ? a : b;

  _showBottomSheet(BuildContext context) {
    final bottomSheetTitle = type == TextDisplayType.error
        ? AppLocalizations.of(context)!.error
        : AppLocalizations.of(context)!.long_text;

    final titleColor = type == TextDisplayType.error ? Colors.red : null;

    showModalBottomSheet<void>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
      ),
      builder: (BuildContext context) {
        return Container(
          color: backgroundColor,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 13),
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  3.0.spaceY,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        title ?? bottomSheetTitle,
                        style: titleStyle ??
                            TextStyle(
                              fontSize: 18,
                              color: titleColor,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      InkWell(
                        onTap: () => Navigator.pop(context),
                        child: const Padding(
                          padding: EdgeInsets.all(8.0),
                          child: Icon(Icons.close),
                        ),
                      )
                    ],
                  ),
                  if (text.isNotEmpty)
                    Text(
                      text,
                      style: bodyStyle ?? AppStyles.headLine15_600,
                    ),
                  13.0.spaceY,
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
