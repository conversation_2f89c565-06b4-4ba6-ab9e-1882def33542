import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../helpers/ui_helper.dart';
import '../widgets/job_creation_image_upload.dart';

class JobCreationAttachmentTabFault extends ConsumerStatefulWidget {
  JobCreationAttachmentTabFault({super.key});

  @override
  _JobCreationAttachmentTabFaultState createState() =>
      _JobCreationAttachmentTabFaultState();
}

class _JobCreationAttachmentTabFaultState
    extends ConsumerState<JobCreationAttachmentTabFault> {
  bool isExpandedForAttachment = false;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        child: Padding(
          padding: UIHelper.columnFieldOnlhorizontalPadding(),
          child: Column(
            children: [
              UIHelper.sizedBox8(),
              getAttachments(),
            ],
          ),
        ),
      ),
    );
  }

  Widget getAttachments() {
    return Padding(
      padding: UIHelper.columnFieldPadding(),
      child: Column(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [JobCreationFileOrImageUploadWidget()],
          ),
        ],
      ),
    );
  }
}
