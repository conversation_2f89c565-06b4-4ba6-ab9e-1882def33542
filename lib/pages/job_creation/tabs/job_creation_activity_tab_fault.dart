import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../helpers/ui_helper.dart';
import 'package:intl/intl.dart';

import '../../../providers/job_creation/job_header_provider.dart';
import 'edit_job_creation_field_provider.dart';

class JobCreationActivityTabFault extends ConsumerStatefulWidget {
  JobCreationActivityTabFault({super.key});

  @override
  _JobCreationActivityTabFaultState createState() => _JobCreationActivityTabFaultState();
}

class _JobCreationActivityTabFaultState extends ConsumerState<JobCreationActivityTabFault> {
  TextEditingController inputController = TextEditingController();
  String readOnlyText = "";

  @override
  void initState() {
    super.initState();
    inputController = TextEditingController();
    final jobHeader = ref.read(jobHeaderProvider.notifier).state;
    if (jobHeader.details != null) {
      final currentDateTime =
          DateFormat('dd-MM-yyyy hh:mm a').format(DateTime.now());
      readOnlyText = '$currentDateTime \n ${jobHeader.details} \n';
    }
  }

  @override
  Widget build(BuildContext context) {
    final editProvider = ref.watch(editJobCreationProvider.notifier).state;
    return SafeArea(
      child: SingleChildScrollView(
        child: Padding(
          padding: UIHelper.columnFieldPadding(),
          child: Column(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  UIHelper.buildLabelAndValueAsWidgetOfFaultActivity(
                    readOnlyText: readOnlyText,
                    controller: inputController,
                    isTextFieldRequiredAsValueWidget: !editProvider,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
