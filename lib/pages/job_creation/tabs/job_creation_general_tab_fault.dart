import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/helpers/db_helper.dart';
import 'package:rounds/pages/job_creation/tabs/edit_job_creation_field_provider.dart';
import 'package:rounds/providers/job_creation/job_header_provider.dart';
import 'package:rounds/utils/utils.dart';

import '../../../be/USER_HEADER.dart';
import '../../../helpers/ui_helper.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../../providers/fault/fault_type_provider.dart';
import '../../../providers/user_provider.dart';
import '../../../utils/app_colors.dart';
import 'package:intl/intl.dart';

import '../../../utils/app_constants.dart';
import '../../assign_plan_screen.dart';

class JobCreationGeneralTabFault extends ConsumerStatefulWidget {
  JobCreationGeneralTabFault({super.key});

  @override
  _JobCreationGeneralTabFaultState createState() =>
      _JobCreationGeneralTabFaultState();
}

class _JobCreationGeneralTabFaultState
    extends ConsumerState<JobCreationGeneralTabFault> {
  String selectedStatus = '';
  String selectedJobType = '';
  String selectedFailureMode = '';
  String selectedPriority = '';
  TextEditingController startDateController = TextEditingController();
  TextEditingController endDateController = TextEditingController();
  TextEditingController estimationTimeController = TextEditingController();
  TextEditingController actualTimeController = TextEditingController();
  TextEditingController assignedToController = TextEditingController();
  TextEditingController assignedByController = TextEditingController();
  TextEditingController createdByController = TextEditingController();
  TextEditingController createdOnController = TextEditingController();

  @override
  void initState() {
    final jobHeader = ref.read(jobHeaderProvider);
    final jobStartOn = ref.read(jobStartOnProvider.notifier);
    final jonEndOn = ref.read(jobEndOnProvider.notifier);
    if (jobHeader.status == null || jobHeader.status == '') {
      if (jobStartOn.state != 0) {
        int? dateAsInt = jobHeader.start_date;
        String dateString = dateAsInt.toString();
        DateTime dateTime = DateTime.parse(dateString);
        String formattedDate = DateFormat("dd MMM yyyy").format(dateTime);
        startDateController.text = formattedDate;
      }
      if (jonEndOn.state != 0) {
        int? dateAsInt = jonEndOn.state;
        String dateString = dateAsInt.toString();
        DateTime dateTime = DateTime.parse(dateString);
        String formattedDate = DateFormat("dd MMM yyyy").format(dateTime);
        endDateController.text = formattedDate;
      }

    }
    super.initState();
  }

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();

    final jobHeader = ref.read(jobHeaderProvider);

    if (ScreenType.desktop != UIHelper().getScreenType(context)) {
      if (jobHeader.description?.isNotEmpty ?? false) {
        descriptionController.text = jobHeader.description!;
      }

      if (jobHeader.details?.isNotEmpty ?? false) {
        longTextController.text = jobHeader.details!;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        child: Column(
          children: [
            UIHelper.sizedBox8(),
            getGeneralDetails(context),
          ],
        ),
      ),
    );
  }

  Widget getGeneralDetails(BuildContext context) {
    return Padding(
      padding: UIHelper.columnFieldPadding(),
      child: Column(
        children: [
          Container(
            decoration: UIHelper.cardDecoration(),
            child: Padding(
              padding: UIHelper.allPaddingOf10(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ScreenType.desktop != UIHelper().getScreenType(context)
                      ? getJobInfoField()
                      : getJobInfoFieldWeb(),
                  Divider(thickness: 1, color: Colors.grey[300]),
                  UIHelper.sizedBox8(),
                  ScreenType.desktop != UIHelper().getScreenType(context)
                      ? getTechnicalObjects()
                      : getTechnicalObjectsWeb(),
                  Divider(thickness: 1, color: Colors.grey[300]),
                  UIHelper.sizedBox8(),
                  ScreenType.desktop != UIHelper().getScreenType(context)
                      ? getDates(context)
                      : getDatesWeb(context),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget getPriorityChoiceChip() {
    final jobHeader = ref.read(jobHeaderProvider.notifier).state;
    final priorityList = ref.watch(priorityListProvider.notifier).state;
    final priority = ref.watch(jobPriorityProvider.notifier);

    if (selectedPriority.isEmpty || selectedPriority == '') {
      if (jobHeader.priority != null || jobHeader.priority != '') {
        selectedPriority = ref
            .watch(priorityListProvider.notifier)
            .fetchPriorityCode(jobHeader.priority.toString());
        priority.getJobPriority(selectedPriority);
      }
    }
    if (priority.state.priority_code != null &&
        priority.state.priority_code!.isNotEmpty) {
      selectedPriority = ref
          .watch(priorityListProvider.notifier)
          .fetchPriorityCode(priority.state.priority_code.toString());
    }
    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: UIHelper.fieldDecoration(),
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
            child: LayoutBuilder(
              builder: (context, constraints) {
                return Wrap(
                  spacing: 12,
                  children: priorityList.map((option) {
                    final isSelected = selectedPriority == option.description;
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: ChoiceChip(
                        labelPadding: const EdgeInsets.symmetric(
                            horizontal: 15, vertical: 8),
                        label: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            option.description.toString(),
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColors.titleTextColor,
                              letterSpacing: 0.1,
                            ),
                          ),
                        ),
                        selected: isSelected,
                        selectedColor:
                            UIHelper.getPriorityColor(selectedPriority),
                        backgroundColor: Colors.grey[200],
                        onSelected: (selected) {
                          setState(() {
                            selectedPriority = option.description!;
                            priority.getJobPriority(selectedPriority);
                          });
                        },
                      ),
                    );
                  }).toList(),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  TextEditingController descriptionController = TextEditingController();
  TextEditingController longTextController = TextEditingController();
  bool isExpandedForLocationDetail = false;

  Widget getTechnicalObjects() {
    final jobHeader = ref.read(jobHeaderProvider.notifier).state;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.technical_objects,
          style: UIHelper.faultHeaderStyle(),
        ),
        jobHeader.location_id != null
            ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                label: AppLocalizations.of(context)!.functionalLocation,
                value: jobHeader.location_id.toString(),
                controller: TextEditingController(),
                isTextFieldRequiredAsValueWidget: false)
            : Container(),
        jobHeader.asset_no != null
            ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                label: AppLocalizations.of(context)!.asset,
                value: jobHeader.asset_no.toString(),
                controller: TextEditingController(),
                isTextFieldRequiredAsValueWidget: false)
            : Container(),
      ],
    );
  }

  Widget getTechnicalObjectsWeb() {
    final jobHeader = ref.read(jobHeaderProvider.notifier).state;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.technical_objects,
          style: UIHelper.faultHeaderStyle(),
        ),
        Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: jobHeader.location_id != null
                    ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                        label: AppLocalizations.of(context)!.functionalLocation,
                        value: jobHeader.location_id.toString(),
                        controller: TextEditingController(),
                        isTextFieldRequiredAsValueWidget: false)
                    : Container(),
              ),
              Expanded(
                child: jobHeader.asset_no != null
                    ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                        label: AppLocalizations.of(context)!.asset,
                        value: jobHeader.asset_no.toString(),
                        controller: TextEditingController(),
                        isTextFieldRequiredAsValueWidget: false)
                    : Container(),
              ),
            ])
      ],
    );
  }

  Widget getDates(BuildContext context) {
    final jobHeader = ref.read(jobHeaderProvider.notifier).state;
    final editProvider = ref.watch(editJobCreationProvider.notifier).state;
    final jobStartOn = ref.watch(jobStartOnProvider.notifier);
    final jonEndOn = ref.watch(jobEndOnProvider.notifier);
    if (jobStartOn.state != 0) {
      int? dateAsInt = jobStartOn.state;
      String dateString = dateAsInt.toString();
      DateTime dateTime = DateTime.parse(dateString);
      String formattedDate = DateFormat("dd MMM yyyy").format(dateTime);
      startDateController.text = formattedDate;
    }
    if (jonEndOn.state != 0) {
      int? dateAsInt = jonEndOn.state;
      String dateString = dateAsInt.toString();
      DateTime dateTime = DateTime.parse(dateString);
      String formattedDate = DateFormat("dd MMM yyyy").format(dateTime);
      endDateController.text = formattedDate;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.dates,
          style: UIHelper.faultHeaderStyle(),
        ),
        (jobHeader.status == null || jobHeader.status == '')
            ? Padding(
                padding: UIHelper.columnFieldOnlyVerticalPadding10(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.start_date,
                      style: UIHelper.labelStyle(),
                    ),
                    editProvider
                        ? TextFormField(
                            style: UIHelper.valueStyle14(),
                            controller: startDateController,
                            onChanged: (v) {},
                            enabled: true,
                            onTap: () {},
                            readOnly: true,
                            decoration: InputDecoration(
                                contentPadding: const EdgeInsets.only(left: 5),
                                border: OutlineInputBorder(
                                    gapPadding: 2,
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(5)),
                                    borderSide: BorderSide(
                                        color: AppColors.cardBorderGrey,
                                        width: 1)),
                                enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: AppColors.cardBorderGrey,
                                        width: 1)),
                                focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: AppColors.cardBorderGrey,
                                        width: 1)),
                                suffixIcon: InkWell(
                                    onTap: () {
                                      selectStartDate(
                                        context: context,
                                        type: AppConstants.startDate,
                                        initialDate: convertedToDateTime(
                                            startDateController.text),
                                        firstDate: DateTime(2000),
                                        // lastDate: DateTime.now()
                                      );
                                    },
                                    child: Icon(Icons.date_range))),
                          )
                        : Text(
                            jobHeader.start_date == null
                                ? ""
                                : !editProvider
                                    ? UIHelper.formatDate(
                                        jobHeader.start_date.toString())
                                    : '',
                            style: UIHelper.valueStyle14(),
                          )
                  ],
                ),
              )
            : (jobHeader.start_date != null)
                ? Padding(
                    padding: UIHelper.columnFieldOnlyVerticalPadding10(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context)!.start_date,
                          style: UIHelper.labelStyle(),
                        ),
                        editProvider
                            ? TextFormField(
                                style: UIHelper.valueStyle14(),
                                controller: startDateController,
                                onChanged: (v) {},
                                enabled: true,
                                onTap: () {},
                                readOnly: true,
                                decoration: InputDecoration(
                                    contentPadding:
                                        const EdgeInsets.only(left: 5),
                                    border: OutlineInputBorder(
                                        gapPadding: 2,
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(5)),
                                        borderSide: BorderSide(
                                            color: AppColors.cardBorderGrey,
                                            width: 1)),
                                    enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: AppColors.cardBorderGrey,
                                            width: 1)),
                                    focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: AppColors.cardBorderGrey,
                                            width: 1)),
                                    suffixIcon: InkWell(
                                        onTap: () {
                                          selectStartDate(
                                            context: context,
                                            type: AppConstants.startDate,
                                            initialDate: convertedToDateTime(
                                                startDateController.text),
                                            firstDate: DateTime(2000),
                                            // lastDate: DateTime.now()
                                          );
                                        },
                                        child: Icon(Icons.date_range))),
                              )
                            : Text(
                                UIHelper.formatDate(
                                    jobHeader.start_date.toString()),
                                style: UIHelper.valueStyle14(),
                              )
                      ],
                    ),
                  )
                : Container(),
        (jobHeader.status == null || jobHeader.status == '')
            ? Padding(
                padding: UIHelper.columnFieldOnlyVerticalPadding10(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.end_date,
                      style: UIHelper.labelStyle(),
                    ),
                    editProvider
                        ? TextFormField(
                            style: UIHelper.valueStyle14(),
                            controller: endDateController,
                            onChanged: (v) {},
                            enabled: true,
                            onTap: () {},
                            readOnly: true,
                            decoration: InputDecoration(
                                contentPadding: const EdgeInsets.only(left: 5),
                                border: OutlineInputBorder(
                                    gapPadding: 2,
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(5)),
                                    borderSide: BorderSide(
                                        color: AppColors.cardBorderGrey,
                                        width: 1)),
                                enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: AppColors.cardBorderGrey,
                                        width: 1)),
                                focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: AppColors.cardBorderGrey,
                                        width: 1)),
                                suffixIcon: InkWell(
                                    onTap: () {
                                      selectEndDate(
                                          context: context,
                                          type: AppConstants.endDate,
                                          initialDate: DateTime.now(),
                                          firstDate: DateTime.now(),
                                          lastDate: DateTime(2100));
                                    },
                                    child: const Icon(Icons.date_range))),
                          )
                        : Text(
                            jobHeader.end_date == null
                                ? ""
                                : !editProvider
                                    ? UIHelper.formatDate(
                                        jobHeader.end_date.toString())
                                    : '',
                            style: UIHelper.valueStyle14(),
                          )
                  ],
                ),
              )
            : (jobHeader.end_date != null)
                ? Padding(
                    padding: UIHelper.columnFieldOnlyVerticalPadding10(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context)!.end_date,
                          style: UIHelper.labelStyle(),
                        ),
                        editProvider
                            ? TextFormField(
                                style: UIHelper.valueStyle14(),
                                controller: endDateController,
                                onChanged: (v) {},
                                enabled: true,
                                onTap: () {},
                                readOnly: true,
                                decoration: InputDecoration(
                                    contentPadding:
                                        const EdgeInsets.only(left: 5),
                                    border: OutlineInputBorder(
                                        gapPadding: 2,
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(5)),
                                        borderSide: BorderSide(
                                            color: AppColors.cardBorderGrey,
                                            width: 1)),
                                    enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: AppColors.cardBorderGrey,
                                            width: 1)),
                                    focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: AppColors.cardBorderGrey,
                                            width: 1)),
                                    suffixIcon: InkWell(
                                        onTap: () {
                                          selectEndDate(
                                              context: context,
                                              type: AppConstants.endDate,
                                              initialDate: DateTime.now(),
                                              firstDate: DateTime.now(),
                                              lastDate: DateTime(2100));
                                        },
                                        child: const Icon(Icons.date_range))),
                              )
                            : Text(
                                UIHelper.formatDate(
                                    jobHeader.end_date.toString()),
                                style: UIHelper.valueStyle14(),
                              )
                      ],
                    ),
                  )
                : Container(),
      ],
    );
  }

  Widget getDatesWeb(BuildContext context) {
    final jobHeader = ref.read(jobHeaderProvider.notifier).state;
    final editProvider = ref.watch(editJobCreationProvider.notifier).state;
    final jobStartOn = ref.watch(jobStartOnProvider.notifier);
    final jonEndOn = ref.watch(jobEndOnProvider.notifier);
    if (jobStartOn.state != 0) {
      int? dateAsInt = jobStartOn.state;
      String dateString = dateAsInt.toString();
      DateTime dateTime = DateTime.parse(dateString);
      String formattedDate = DateFormat("dd MMM yyyy").format(dateTime);
      startDateController.text = formattedDate;
    }
    if (jonEndOn.state != 0) {
      int? dateAsInt = jonEndOn.state;
      String dateString = dateAsInt.toString();
      DateTime dateTime = DateTime.parse(dateString);
      String formattedDate = DateFormat("dd MMM yyyy").format(dateTime);
      endDateController.text = formattedDate;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.dates,
          style: UIHelper.faultHeaderStyle(),
        ),
        Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: (jobHeader.status == null || jobHeader.status == '')
                    ? Padding(
                        padding: UIHelper.columnFieldOnlyVerticalPadding10(),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context)!.start_date,
                              style: UIHelper.labelStyle(),
                            ),
                            editProvider
                                ? TextFormField(
                                    style: UIHelper.valueStyle14(),
                                    controller: startDateController,
                                    onChanged: (v) {},
                                    enabled: true,
                                    onTap: () {},
                                    readOnly: true,
                                    decoration: InputDecoration(
                                        contentPadding:
                                            const EdgeInsets.only(left: 5),
                                        border: OutlineInputBorder(
                                            gapPadding: 2,
                                            borderRadius:
                                                const BorderRadius.all(
                                                    Radius.circular(5)),
                                            borderSide: BorderSide(
                                                color: AppColors.cardBorderGrey,
                                                width: 1)),
                                        enabledBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                                color: AppColors.cardBorderGrey,
                                                width: 1)),
                                        focusedBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                                color: AppColors.cardBorderGrey,
                                                width: 1)),
                                        suffixIcon: InkWell(
                                            onTap: () {
                                              selectStartDate(
                                                context: context,
                                                type: AppConstants.startDate,
                                                initialDate:
                                                    convertedToDateTime(
                                                        startDateController
                                                            .text),
                                                firstDate: DateTime(2000),
                                                // lastDate: DateTime.now()
                                              );
                                            },
                                            child: Icon(Icons.date_range))),
                                  )
                                : Text(
                                    jobHeader.start_date == null
                                        ? ""
                                        : !editProvider
                                            ? UIHelper.formatDate(
                                                jobHeader.start_date.toString())
                                            : '',
                                    style: UIHelper.valueStyle14(),
                                  )
                          ],
                        ),
                      )
                    : (jobHeader.start_date != null)
                        ? Padding(
                            padding:
                                UIHelper.columnFieldOnlyVerticalPadding10(),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppLocalizations.of(context)!.start_date,
                                  style: UIHelper.labelStyle(),
                                ),
                                editProvider
                                    ? TextFormField(
                                        style: UIHelper.valueStyle14(),
                                        controller: startDateController,
                                        onChanged: (v) {},
                                        enabled: true,
                                        onTap: () {},
                                        readOnly: true,
                                        decoration: InputDecoration(
                                            contentPadding:
                                                const EdgeInsets.only(left: 5),
                                            border: OutlineInputBorder(
                                                gapPadding: 2,
                                                borderRadius:
                                                    const BorderRadius.all(
                                                        Radius.circular(5)),
                                                borderSide: BorderSide(
                                                    color: AppColors
                                                        .cardBorderGrey,
                                                    width: 1)),
                                            enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    color: AppColors
                                                        .cardBorderGrey,
                                                    width: 1)),
                                            focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    color: AppColors
                                                        .cardBorderGrey,
                                                    width: 1)),
                                            suffixIcon: InkWell(
                                                onTap: () {
                                                  selectStartDate(
                                                    context: context,
                                                    type:
                                                        AppConstants.startDate,
                                                    initialDate:
                                                        convertedToDateTime(
                                                            startDateController
                                                                .text),
                                                    firstDate: DateTime(2000),
                                                    // lastDate: DateTime.now()
                                                  );
                                                },
                                                child: Icon(Icons.date_range))),
                                      )
                                    : Text(
                                        UIHelper.formatDate(
                                            jobHeader.start_date.toString()),
                                        style: UIHelper.valueStyle14(),
                                      )
                              ],
                            ),
                          )
                        : Container(),
              ),
              SizedBox(width: 10),
              Expanded(
                child: (jobHeader.status == null || jobHeader.status == '')
                    ? Padding(
                        padding: UIHelper.columnFieldOnlyVerticalPadding10(),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context)!.end_date,
                              style: UIHelper.labelStyle(),
                            ),
                            editProvider
                                ? TextFormField(
                                    style: UIHelper.valueStyle14(),
                                    controller: endDateController,
                                    onChanged: (v) {},
                                    enabled: true,
                                    onTap: () {},
                                    readOnly: true,
                                    decoration: InputDecoration(
                                        contentPadding:
                                            const EdgeInsets.only(left: 5),
                                        border: OutlineInputBorder(
                                            gapPadding: 2,
                                            borderRadius:
                                                const BorderRadius.all(
                                                    Radius.circular(5)),
                                            borderSide: BorderSide(
                                                color: AppColors.cardBorderGrey,
                                                width: 1)),
                                        enabledBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                                color: AppColors.cardBorderGrey,
                                                width: 1)),
                                        focusedBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                                color: AppColors.cardBorderGrey,
                                                width: 1)),
                                        suffixIcon: InkWell(
                                            onTap: () {
                                              selectEndDate(
                                                  context: context,
                                                  type: AppConstants.endDate,
                                                  initialDate: DateTime.now(),
                                                  firstDate: DateTime.now(),
                                                  lastDate: DateTime(2100));
                                            },
                                            child:
                                                const Icon(Icons.date_range))),
                                  )
                                : Text(
                                    jobHeader.end_date == null
                                        ? ""
                                        : !editProvider
                                            ? UIHelper.formatDate(
                                                jobHeader.end_date.toString())
                                            : '',
                                    style: UIHelper.valueStyle14(),
                                  )
                          ],
                        ),
                      )
                    : (jobHeader.end_date != null)
                        ? Padding(
                            padding:
                                UIHelper.columnFieldOnlyVerticalPadding10(),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppLocalizations.of(context)!.end_date,
                                  style: UIHelper.labelStyle(),
                                ),
                                editProvider
                                    ? TextFormField(
                                        style: UIHelper.valueStyle14(),
                                        controller: endDateController,
                                        onChanged: (v) {},
                                        enabled: true,
                                        onTap: () {},
                                        readOnly: true,
                                        decoration: InputDecoration(
                                            contentPadding:
                                                const EdgeInsets.only(left: 5),
                                            border: OutlineInputBorder(
                                                gapPadding: 2,
                                                borderRadius:
                                                    const BorderRadius.all(
                                                        Radius.circular(5)),
                                                borderSide: BorderSide(
                                                    color: AppColors
                                                        .cardBorderGrey,
                                                    width: 1)),
                                            enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    color: AppColors
                                                        .cardBorderGrey,
                                                    width: 1)),
                                            focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    color: AppColors
                                                        .cardBorderGrey,
                                                    width: 1)),
                                            suffixIcon: InkWell(
                                                onTap: () {
                                                  selectEndDate(
                                                      context: context,
                                                      type:
                                                          AppConstants.endDate,
                                                      initialDate:
                                                          DateTime.now(),
                                                      firstDate: DateTime.now(),
                                                      lastDate: DateTime(2100));
                                                },
                                                child: const Icon(
                                                    Icons.date_range))),
                                      )
                                    : Text(
                                        UIHelper.formatDate(
                                            jobHeader.end_date.toString()),
                                        style: UIHelper.valueStyle14(),
                                      )
                              ],
                            ),
                          )
                        : Container(),
              )
            ])
      ],
    );
  }

  DateTime? _selectedStartDate;
  Future<void> selectStartDate(
      {required BuildContext context,
      required String type,
      DateTime? firstDate,
      DateTime? lastDate,
      required DateTime initialDate}) async {
    final startOn = ref.read(jobStartOnProvider.notifier);
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedStartDate ?? initialDate,
      firstDate: firstDate ?? DateTime(2000),
      lastDate: lastDate ?? DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Colors.blue,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      setState(() {
        _selectedStartDate = pickedDate;
        if (type == 'startDate') {
          startDateController.text =
              DateFormat('d MMMM yyyy').format(_selectedStartDate!);
          DateTime dateTime =
              DateFormat("d MMMM yyyy").parse(startDateController.text);
          String formattedDate = DateFormat("yyyyMMdd").format(dateTime);
          int dateAsInt = int.parse(formattedDate);
          startOn.getJobStartOn(dateAsInt);
        }
      });
    }
  }

  DateTime? _selectedEndDate;
  Future<void> selectEndDate(
      {required BuildContext context,
      required String type,
      DateTime? firstDate,
      DateTime? lastDate,
      required DateTime initialDate}) async {
    final endOn = ref.read(jobEndOnProvider.notifier);
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedEndDate ?? initialDate,
      firstDate: firstDate ?? DateTime(2000),
      lastDate: lastDate ?? DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Colors.blue,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      setState(() {
        _selectedEndDate = pickedDate;

        if (type == 'endDate') {
          endDateController.text =
              DateFormat('d MMMM yyyy').format(_selectedEndDate!);
          DateTime dateTime =
              DateFormat("d MMMM yyyy").parse(endDateController.text);
          String formattedDate = DateFormat("yyyyMMdd").format(dateTime);
          int dateAsInt = int.parse(formattedDate);
          endOn.getJobEndOn(dateAsInt);
        }
      });
    }
  }

  Widget getJobInfoField() {
    final editProvider = ref.watch(editJobCreationProvider.notifier).state;
    final description = ref.watch(jobDescriptionProvider.notifier);
    final assignedTo = ref.watch(jobAssignedToProvider.notifier);
    final jobHeader = ref.read(jobHeaderProvider.notifier).state;
    final userList = ref.watch(usersListProvider);
    final longText = ref.watch(jobLongTextProvider.notifier);
    USER_HEADER? assignedToData;
    USER_HEADER? assignedBy;
    if (jobHeader.description != null && jobHeader.description != '' && ScreenType.desktop==UIHelper().getScreenType(context)) {
      descriptionController.text = jobHeader.description.toString();
      description.getJobDescription(descriptionController.text);
    }

    if (jobHeader.assigned_by != null && jobHeader.assigned_by != '') {
      assignedBy = DbHelper.getAssignUser(userList, jobHeader.assigned_by!);

      if (assignedBy != null) {
        assignedByController.text = UIHelper()
            .toCamelCase('${assignedBy.first_name} ${assignedBy.last_name}');
      }
    }
    if (jobHeader.assigned_to != null && jobHeader.assigned_to != '') {
      assignedToData = DbHelper.getAssignUser(userList, jobHeader.assigned_to!);

      if (assignedToData != null) {
        if (assignedToData.first_name != null &&
            assignedToData.last_name != null) {
          assignedToController.text = UIHelper().toCamelCase(
              '${assignedToData.first_name.toString()} ${assignedToData.last_name.toString()}');
        }
        assignedTo.getJobAssignedTo(assignedToData.user_id!);
      }
    }
    if (description.state.isNotEmpty) {
      descriptionController.text = description.state;
    }

    if (assignedTo.state.isNotEmpty) {
      assignedToData = DbHelper.getAssignUser(userList, assignedTo.state);
      if (assignedToData != null) {
        if (assignedToData.first_name != null &&
            assignedToData.last_name != null) {
          assignedToController.text = UIHelper().toCamelCase(
              '${assignedToData.first_name.toString()} ${assignedToData.last_name.toString()}');
        }
        assignedTo.getJobAssignedTo(assignedToData.user_id!);
      }
    }

    if (jobHeader.details != null && jobHeader.details != '' && ScreenType.desktop==UIHelper().getScreenType(context)) {
      longTextController.text = jobHeader.details.toString();
      longText.getJobLongText(longTextController.text);
    }
    if (longText.state.isNotEmpty) {
      longTextController.text = longText.state;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.job_info,
          style: UIHelper.faultHeaderStyle(),
        ),
        (jobHeader.status == null || jobHeader.status == '')
            ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                label: AppLocalizations.of(context)!.description,
                value: jobHeader.description.toString(),
                controller: descriptionController,
                inputFormatters: [
                  LengthLimitingTextInputFormatter(40),
                ],
                onChanged: (v) {
                  if(ScreenType.desktop == UIHelper().getScreenType(context)){
                        TextSelection previousSelection =
                          descriptionController.selection;
                      descriptionController.text = v;
                      descriptionController.selection = previousSelection;
                      description.getJobDescription(v);
                      } else{
                        description.getJobDescription(v);
                      }
                },
                isTextFieldRequiredAsValueWidget: editProvider)
            : (jobHeader.description != null || jobHeader.description != '')
                ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                    label: AppLocalizations.of(context)!.description,
                    value: jobHeader.description.toString(),
                    controller: descriptionController,
                    onChanged: (v) {
                      if(ScreenType.desktop == UIHelper().getScreenType(context)){
                        TextSelection previousSelection =
                          descriptionController.selection;
                      descriptionController.text = v;
                      descriptionController.selection = previousSelection;
                      description.getJobDescription(v);
                      } else{
                        description.getJobDescription(v);
                      }
                      
                    },
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(40),
                    ],
                    isTextFieldRequiredAsValueWidget: editProvider)
                : Container(),
        (jobHeader.status == null || jobHeader.status == '')
            ? Container()
            : UIHelper.buildLabelAndValueAsWidgetOfFault(
                label: AppLocalizations.of(context)!.status,
                value: UIHelper.getJobStatusString(jobHeader.status.toString()),
                controller: TextEditingController(),
                isTextFieldRequiredAsValueWidget: false),
        (jobHeader.status == null || jobHeader.status == '')
            ? UIHelper.buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                label: AppLocalizations.of(context)!.job_type,
                value: getJobTypeDropdown(),
                isTextFieldRequiredAsValueWidget: editProvider)
            : editProvider
                ? jobHeader.p_mode == AppConstants.add
                    ? UIHelper
                        .buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                            label: AppLocalizations.of(context)!.job_type,
                            value: getJobTypeDropdown(),
                            isTextFieldRequiredAsValueWidget: editProvider)
                    : SizedBox()
                : SizedBox(),
        (jobHeader.status == null || jobHeader.status == '')
            ? UIHelper.buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                label: AppLocalizations.of(context)!.priority,
                value: getPriorityChoiceChip(),
                isTextFieldRequiredAsValueWidget: editProvider)
            : editProvider
                ? UIHelper.buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                    label: AppLocalizations.of(context)!.priority,
                    value: getPriorityChoiceChip(),
                    isTextFieldRequiredAsValueWidget: editProvider)
                : Container(),
        (jobHeader.status == null || jobHeader.status == '')
            ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                label: AppLocalizations.of(context)!.long_text,
                value: jobHeader.details.toString(),
                controller: longTextController,
                onChanged: (v) {
                  if (ScreenType.desktop == UIHelper().getScreenType(context)) {
                    TextSelection previousSelection =
                        longTextController.selection;
                    longTextController.text = v;
                    longTextController.selection = previousSelection;

                    // jobHeader.details = longTextController.text;
                    longText.getJobLongText(v);
                  } else {
                    longText.getJobLongText(v);
                  }
                },
                isTextFieldRequiredAsValueWidget: editProvider)
            : (jobHeader.details != null || jobHeader.details != '')
                ? editProvider
                    ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                        label: AppLocalizations.of(context)!.long_text,
                        value: jobHeader.details.toString(),
                        controller: longTextController,
                        onChanged: (v) {
                          if (ScreenType.desktop ==
                              UIHelper().getScreenType(context)) {
                            TextSelection previousSelection =
                                longTextController.selection;
                            longTextController.text = v;
                            longTextController.selection = previousSelection;

                            // jobHeader.details = longTextController.text;
                            longText.getJobLongText(v);
                          } else {
                            longText.getJobLongText(v);
                          }
                        },
                        isTextFieldRequiredAsValueWidget: editProvider)
                    : Container()
                : Container(),
        (jobHeader.status == null || jobHeader.status == '')
            ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                label: AppLocalizations.of(context)!.assignedto,
                value: assignedToController.text,
                isTextFieldRequiredAsValueWidget: editProvider,
                controller: assignedToController,
                readOnly: true,
                onTap: () {
                  onAssignTo();
                },
                suffixIcon: false)
            : jobHeader.assigned_to != null && jobHeader.assigned_to != ''
                ? editProvider
                    ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                        label: AppLocalizations.of(context)!.assignedto,
                        value: assignedToController.text,
                        isTextFieldRequiredAsValueWidget: editProvider,
                        controller: assignedToController,
                        readOnly: true,
                        onTap: () {
                          onAssignTo();
                        },
                        suffixIcon: false)
                    : UIHelper.buildLabelAndValueAsWidgetOfFault(
                        label: AppLocalizations.of(context)!.assignedto,
                        value: assignedToController.text,
                        isTextFieldRequiredAsValueWidget: editProvider,
                        controller: assignedToController,
                        readOnly: true,
                        onTap: () {
                          onAssignTo();
                        },
                        suffixIcon: false)
                : Container(),
        (jobHeader.status == null || jobHeader.status == '')
            ? Container()
            : jobHeader.assigned_by != null && jobHeader.assigned_by != ''
                ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                    label: AppLocalizations.of(context)!.assigned_by,
                    value: assignedByController.text,
                    isTextFieldRequiredAsValueWidget: false,
                    controller: assignedByController,
                    onChanged: (v) {},
                    suffixIcon: false)
                : Container(),
      ],
    );
  }

  Widget getJobInfoFieldWeb() {
    final editProvider = ref.watch(editJobCreationProvider.notifier).state;
    final description = ref.watch(jobDescriptionProvider.notifier);
    final assignedTo = ref.watch(jobAssignedToProvider.notifier);
    final jobHeader = ref.read(jobHeaderProvider.notifier).state;
    final userList = ref.watch(usersListProvider);
    final longText = ref.watch(jobLongTextProvider.notifier);
    USER_HEADER? assignedToData;
    USER_HEADER? assignedBy;
    if (jobHeader.assigned_by != null && jobHeader.assigned_by != '') {
      assignedBy = DbHelper.getAssignUser(userList, jobHeader.assigned_by!);

      if (assignedBy != null) {
        assignedByController.text = UIHelper()
            .toCamelCase('${assignedBy.first_name} ${assignedBy.last_name}');
      }
    }
    if (jobHeader.assigned_to != null && jobHeader.assigned_to != '') {
      assignedToData = DbHelper.getAssignUser(userList, jobHeader.assigned_to!);

      if (assignedToData != null) {
        if (assignedToData.first_name != null &&
            assignedToData.last_name != null) {
          assignedToController.text = UIHelper().toCamelCase(
              '${assignedToData.first_name.toString()} ${assignedToData.last_name.toString()}');
        }
        assignedTo.getJobAssignedTo(assignedToData.user_id!);
      }
    }

    if (assignedTo.state.isNotEmpty) {
      assignedToData = DbHelper.getAssignUser(userList, assignedTo.state);
      if (assignedToData != null) {
        if (assignedToData.first_name != null &&
            assignedToData.last_name != null) {
          assignedToController.text = UIHelper().toCamelCase(
              '${assignedToData.first_name.toString()} ${assignedToData.last_name.toString()}');
        }
        assignedTo.getJobAssignedTo(assignedToData.user_id!);
      }
    }

    if (description.state.isNotEmpty) {
      descriptionController.text = description.state;
    }

    if (longText.state.isNotEmpty) {
      longTextController.text = longText.state;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.job_info,
          style: UIHelper.faultHeaderStyle(),
        ),
        (jobHeader.status == null || jobHeader.status == '')
            ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                label: AppLocalizations.of(context)!.description,
                value: descriptionController.text,
                controller: descriptionController,
                inputFormatters: [
                  LengthLimitingTextInputFormatter(40),
                ],
                onChanged: (v) {
                  TextSelection previousSelection =
                      descriptionController.selection;
                  descriptionController.text = v;
                  descriptionController.selection = previousSelection;
                  description.getJobDescription(v);
                },
                isTextFieldRequiredAsValueWidget: editProvider)
            : (jobHeader.description != null || jobHeader.description != '')
                ? (editProvider
                    ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                        label: AppLocalizations.of(context)!.description,
                        value: descriptionController.text,
                        controller: descriptionController,
                        onChanged: (v) {
                          TextSelection previousSelection =
                              descriptionController.selection;
                          descriptionController.text = v;
                          descriptionController.selection = previousSelection;
                          description.getJobDescription(v);
                        },
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(40),
                        ],
                        isTextFieldRequiredAsValueWidget: editProvider)
                    : Container())
                : Container(),
        (jobHeader.status == null || jobHeader.status == '')
            ? UIHelper.buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                label: AppLocalizations.of(context)!.job_type,
                value: getJobTypeDropdown(),
                isTextFieldRequiredAsValueWidget: editProvider)
            : editProvider
                ? jobHeader.p_mode == AppConstants.add
                    ? UIHelper
                        .buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                            label: AppLocalizations.of(context)!.job_type,
                            value: getJobTypeDropdown(),
                            isTextFieldRequiredAsValueWidget: editProvider)
                    : UIHelper
                        .buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                            label: AppLocalizations.of(context)!.job_type,
                            value: getJobTypeDropdown(),
                            isTextFieldRequiredAsValueWidget: editProvider)
                : SizedBox(),
        (jobHeader.status == null || jobHeader.status == '')
            ? UIHelper.buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                label: AppLocalizations.of(context)!.priority,
                value: getPriorityChoiceChip(),
                isTextFieldRequiredAsValueWidget: editProvider)
            : editProvider
                ? UIHelper.buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                    label: AppLocalizations.of(context)!.priority,
                    value: getPriorityChoiceChip(),
                    isTextFieldRequiredAsValueWidget: editProvider)
                : Container(),
        (jobHeader.status == null)
            ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                label: AppLocalizations.of(context)!.long_text,
                value: longTextController.text,
                controller: longTextController,
                onChanged: (v) {
                  TextSelection previousSelection =
                      longTextController.selection;
                  longTextController.text = v;
                  longTextController.selection = previousSelection;

                  // jobHeader.details = longTextController.text;
                  longText.getJobLongText(v);
                },
                isTextFieldRequiredAsValueWidget: editProvider)
            : (jobHeader.details != null || jobHeader.details != '')
                ? editProvider
                    ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                        label: AppLocalizations.of(context)!.long_text,
                        value: longTextController.text,
                        controller: longTextController,
                        onChanged: (v) {
                          TextSelection previousSelection =
                              longTextController.selection;
                          longTextController.text = v;
                          longTextController.selection = previousSelection;

                          // jobHeader.details = longTextController.text;
                          longText.getJobLongText(v);
                        },
                        isTextFieldRequiredAsValueWidget: editProvider)
                    : Container()
                : Container(),
        (jobHeader.status == null || jobHeader.status == '')
            ? Container()
            : UIHelper.buildLabelAndValueAsWidgetOfFault(
                label: AppLocalizations.of(context)!.status,
                value: UIHelper.getJobStatusString(jobHeader.status.toString()),
                controller: TextEditingController(),
                isTextFieldRequiredAsValueWidget: false),
        (jobHeader.status == null || jobHeader.status == '')
            ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                label: AppLocalizations.of(context)!.assignedto,
                value: assignedToController.text,
                isTextFieldRequiredAsValueWidget: editProvider,
                controller: assignedToController,
                readOnly: true,
                onTap: () {
                  onAssignTo();
                },
                suffixIcon: false)
            : jobHeader.assigned_to != null && jobHeader.assigned_to != ''
                ? editProvider
                    ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                        label: AppLocalizations.of(context)!.assignedto,
                        value: assignedToController.text,
                        isTextFieldRequiredAsValueWidget: editProvider,
                        controller: assignedToController,
                        readOnly: true,
                        onTap: () {
                          onAssignTo();
                        },
                        suffixIcon: false)
                    : UIHelper.buildLabelAndValueAsWidgetOfFault(
                        label: AppLocalizations.of(context)!.assignedto,
                        value: assignedToController.text,
                        isTextFieldRequiredAsValueWidget: editProvider,
                        controller: assignedToController,
                        readOnly: true,
                        onTap: () {
                          onAssignTo();
                        },
                        suffixIcon: false)
                : Container(),
        (jobHeader.status == null || jobHeader.status == '')
            ? Container()
            : jobHeader.assigned_by != null && jobHeader.assigned_by != ''
                ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                    label: AppLocalizations.of(context)!.assigned_by,
                    value: assignedByController.text,
                    isTextFieldRequiredAsValueWidget: false,
                    controller: assignedByController,
                    onChanged: (v) {},
                    suffixIcon: false)
                : Container(),
      ],
    );
  }

  Widget getJobTypeDropdown() {
    final jobTypeHeader = ref.watch(jobTypeListProvider.notifier).state;
    final jobType = ref.watch(jobTypeProvider.notifier);
    final jobHeader = ref.read(jobHeaderProvider.notifier).state;

    if (selectedJobType.isEmpty) {
      selectedJobType =
          (jobHeader.job_type != null && jobHeader.job_type!.isNotEmpty)
              ? ref
                  .read(jobTypeListProvider.notifier)
                  .fetchjobTypeFromCode(jobHeader.job_type.toString())
              : '';
      jobType.getJobType(selectedJobType);
    }

    if (jobType.state.job_type != null && jobType.state.job_type!.isNotEmpty) {
      selectedJobType = ref
          .read(jobTypeListProvider.notifier)
          .fetchjobTypeFromCode(jobType.state.job_type.toString());
    }

    final dropdownItems = jobTypeHeader.map((option) {
      return DropdownMenuItem<String>(
        value: option.description,
        child: Padding(
          padding: const EdgeInsets.only(left: 5.0),
          child: Text(option.description!, style: UIHelper.valueStyle()),
        ),
      );
    }).toList();

    if (!dropdownItems.any((item) => item.value == '')) {
      dropdownItems.insert(
        0,
        DropdownMenuItem<String>(
          value: '',
          child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(AppLocalizations.of(context)!.select,
                style: UIHelper.valueStyle()),
          ),
        ),
      );
    }

    return Container(
      decoration: UIHelper.fieldDecoration(),
      child: DropdownButton<String>(
        elevation: 0,
        isExpanded: true,
        underline: const SizedBox(),
        value: dropdownItems.any((item) => item.value == selectedJobType)
            ? selectedJobType
            : '',
        items: (jobHeader.job_type != null && jobHeader.job_type != '')
            ? []
            : dropdownItems,
        disabledHint: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(
              selectedJobType.isNotEmpty
                  ? selectedJobType
                  : AppLocalizations.of(context)!.select,
              style: UIHelper.valueStyle(),
            )),
        onChanged: (newValue) {
          if (newValue != null) {
            setState(() {
              selectedJobType = newValue;
              jobType.getJobType(selectedJobType);
            });
          }
        },
      ),
    );
  }

  void onAssignTo() async {
    final assignedTo = ref.watch(jobAssignedToProvider.notifier);

    var result;
    if (!kIsWeb) {
    if (ScreenType.desktop == UIHelper().getScreenType(context)) {
        result = await showDialog(
            context: context,
            barrierDismissible: false,
            builder: (rootDialogContext) => Dialog(
                backgroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0),
                ),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxWidth: 420,
                    maxHeight: 800,
                  ),
                  child: const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: AssignScreen(
                      isSaveButtonForWeb: true,
                    ),
                  ),
                )));
      } else {
        result =
            await Navigator.push(context, MaterialPageRoute(builder: (context) {
          return const AssignScreen();
        }));
      }
    } else {
      result = await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (rootDialogContext) => Dialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              child: ConstrainedBox(
                constraints: const BoxConstraints(
                  maxWidth: 420,
                  maxHeight: 800,
                ),
                child: const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: AssignScreen(
                    isSaveButtonForWeb: true,
                  ),
                ),
              )));
    }
    if (result != null) {
      USER_HEADER user = result as USER_HEADER;
      assignedToController.text = UIHelper().toCamelCase(
          '${user.first_name.toString()} ${user.last_name.toString()}');
      assignedTo.getJobAssignedTo(user.user_id.toString());
    }
  }

  String toCamelCase(String input) {
    if (input.isEmpty) return '';
    return input[0].toUpperCase() + input.substring(1).toLowerCase();
  }

  static DateTime convertedToDateTime(String dateAsInt) {
    String dateString = dateAsInt.toString();
    DateTime parsedDate = DateFormat("dd MMM yyyy").parse(dateString);
    return parsedDate;
  }
}
