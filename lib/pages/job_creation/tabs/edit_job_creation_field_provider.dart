import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';

final editJobCreationProvider =
StateNotifierProvider<EditJobCreationNotifier, bool>((ref) {
  return EditJobCreationNotifier();
});

class EditJobCreationNotifier extends StateNotifier<bool> {
  EditJobCreationNotifier() : super(false);

  Future<void> getEditJobCreationEnable(bool data) async {
    try {
      bool enableJobCreationFields = data;
        state = enableJobCreationFields;

    } catch (e) {
      Logger.logError('EditJobCreationNotifier', 'getEditJobCreationEnable', e.toString());
    }
  }
}