import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:rounds/helpers/pushNotification_helper.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/models/intractive_Item_Model.dart';

import 'package:rounds/pages/dashboard/widgets/top_header.dart';
import 'package:rounds/pages/job_creation/job_creation_detail_screen.dart';
import 'package:rounds/pages/widgets/search_bar.dart';
import 'package:rounds/providers/job_creation/job_creation_filter_provider.dart';
import 'package:rounds/utils/notification_toast.dart';
import 'package:rounds/utils/utils.dart';
import 'package:rounds/widgets/overlay_toast.dart';
import 'package:rounds/widgets/task_calender.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../be/DOCUMENT_HEADER.dart';
import '../../be/FAULT_HEADER.dart';
import '../../be/JOB_ACTION.dart';
import '../../be/JOB_HEADER.dart';
import '../../helpers/db_helper.dart';
import '../../helpers/pa_helper.dart';
import '../../providers/attachments/attachment_provider.dart';
import '../../providers/fault/fault_header_provider.dart';
import '../../providers/fault/fault_type_provider.dart';
import '../../providers/job_creation/job_header_provider.dart';
import '../../services/app_notifier.dart';
import '../../utils/app_colors.dart';
import '../../utils/app_constants.dart';
import '../../utils/constants.dart';
import '../dashboard/dashboard.dart';
import '../fault/fault_filter_provider.dart';
import '../fault/fault_screen.dart';
import '../job_creation/job_creation_list.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

final filterOfJobTypeProvider = StateProvider<List<String>>((ref) => []);
final filterOfJobTypeCodeProvider = StateProvider<List<String>>((ref) => []);
final filterOfPriorityProvider = StateProvider<List<String>>((ref) => []);
final filterOfPriorityCodeProvider = StateProvider<List<String>>((ref) => []);
final statusFilterProvider = StateProvider<List<String>>((ref) => []);
final statusTypeFilterProvider = StateProvider<List<String>>((ref) => []);
final selectedCalendarDateJobProvider = StateProvider<DateTime?>((ref) => null);
final searchTextJobProvider = StateProvider<String>((ref) => '');
ValueNotifier<InteractiveItemModel?> jobDetailViewNotifier =
    ValueNotifier<InteractiveItemModel?>(null);

class JobCreationScreen extends ConsumerStatefulWidget {
  const JobCreationScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _TaskState();
}

class _TaskState extends ConsumerState<JobCreationScreen> {
  TextEditingController searchController = TextEditingController();
  ScrollController scrollController = ScrollController();
  AppNotifier appNotifier = AppNotifier();
  ValueNotifier<DateTime> currentGroupNotifier =
      ValueNotifier<DateTime>(DateTime.now());
  // ValueNotifier<InteractiveItemModel?> jobDetailViewNotifier =
  //     ValueNotifier<InteractiveItemModel?>(null);
  late final StreamSubscription<void> _subscription;

  @override
  void initState() {
    super.initState();
    _initNotifier();
    scrollController.addListener(_onScroll);
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final jobHeader = ref.read(jobHeaderListProvider);
    Future.delayed(Duration.zero).then((value) async {
      ref.read(searchTextJobProvider.notifier).state = "";
      final filteredJobTypeNotifier =
          ref.read(filteredJobCreationHeaderListProvider.notifier);
      final searchJob = ref.read(searchTextJobProvider.notifier).state;
      final filterOfJobType = ref.read(filterOfJobTypeProvider.notifier).state;
      final filterOfJobTypeCode =
          ref.read(filterOfJobTypeCodeProvider.notifier).state;
      final filterOfJobPriority =
          ref.read(filterOfPriorityProvider.notifier).state;
      final filterOfJobPriorityCode =
          ref.read(filterOfPriorityCodeProvider.notifier).state;
      final statusJobFilter = ref.read(statusFilterProvider.notifier).state;
      final statusTypeJobFilter =
          ref.read(statusTypeFilterProvider.notifier).state;
      final selectedDateJob = ref.read(selectedCalendarDateJobProvider);
      if ((searchJob != '') ||
          filterOfJobType.isNotEmpty ||
          filterOfJobTypeCode.isNotEmpty ||
          filterOfJobPriorityCode.isNotEmpty ||
          filterOfJobPriority.isNotEmpty ||
          statusTypeJobFilter.isNotEmpty ||
          statusJobFilter.isNotEmpty ||
          selectedDateJob != null) {
        await filteredJobTypeNotifier.filteredJobHeaderList(
          typeList: filterOfJobTypeCode,
          priorityList: filterOfJobPriorityCode,
          statusList: statusTypeJobFilter,
          type: (searchJob != '') ? AppConstants.search : AppConstants.jobType,
          plantId: plant,
          plantSec: plantSection,
          search: searchJob,
        );
      } else {
        await filteredJobTypeNotifier.filteredJobHeaderList(
            type: 'Initial',
            jobList: jobHeader,
            plantId: plant,
            plantSec: plantSection);
      }

      if (kIsWeb) {
        _subscription =
            PushNotifications.onNotificationProcessed.listen((_) async {
          // 🚀 React to notification queue processed
          debugPrint("📦 Notification queue processed, updating UI");
          final plant = ref.read(plantProvider);
          final plantSection = ref.read(plantSectionProvider);
          final shift = ref.read(shiftProvider);

          await filteredJobTypeNotifier.filteredJobHeaderList(
            typeList: filterOfJobTypeCode,
            priorityList: filterOfJobPriorityCode,
            statusList: statusTypeJobFilter,
            type: AppConstants.search,
            plantId: plant,
            plantSec: plantSection,
            search: ref.read(searchTextJobProvider),
          );

          ref.read(faultTypeListProvider.notifier).fetchFaultTypeList();
          ref.read(faultModeHeaderListProvider.notifier).fetchFaultModeList();
          NotificationToastManager.showRefreshToast(
            context,
            onRefresh: () {
              final plant = ref.watch(plantProvider);
              final plantSection = ref.watch(plantSectionProvider);
              final shift = ref.watch(shiftProvider);
              ref
                  .read(jobHeaderListProvider.notifier)
                  .fetchJobHeaderList(plant);
            },
          );

          // showRefreshToast(context);
          // TODO: e.g., refresh data, show snackbar, etc.
        });
      }

/*      await filteredJobTypeNotifier.filteredJobHeaderList(
          type: 'Initial',
          jobList: jobHeader,
          plantId: plant,
          plantSec: plantSection);*/
    });
  }

  _initNotifier() {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    appNotifier.notifyAttachmentStatus(
      (data) async {
        if (data[EventAttachmentStatusFieldStatus] ==
            EventAttachmentStatusSuccess) {
          if (mounted) {
            await ref
                .read(documentHeaderProvider.notifier)
                .fetchDocumentHeaders();
            await ref
                .read(documentAttachmentProvider.notifier)
                .fetchDocumentAttachments();
          }
        } else if (data[EventAttachmentStatusFieldStatus] ==
            EventAttachmentStatusError) {
          if (mounted) {}
        }
      },
    );
    appNotifier.notifySyncStatus(
      (data) async {
        final filterOfJobType =
            ref.read(filterOfJobTypeProvider.notifier).state;
        final filterOfJobTypeCode =
            ref.read(filterOfJobTypeCodeProvider.notifier).state;
        final filterOfPriority =
            ref.read(filterOfPriorityProvider.notifier).state;
        final filterOfPriorityCode =
            ref.read(filterOfPriorityCodeProvider.notifier).state;
        final statusFilter = ref.read(statusFilterProvider.notifier).state;
        final statusTypeFilter =
            ref.read(statusTypeFilterProvider.notifier).state;
        final selectedDate = ref.read(selectedCalendarDateJobProvider);
        if (data[EventSyncStatusFieldType] ==
            EventSyncStatusTypeInboxProcessingComplete) {
          if (mounted) {
            final jobHeader = ref.read(jobHeaderListProvider);
            final filteredJobTypeNotifier =
                ref.read(filteredJobCreationHeaderListProvider.notifier);
            final search = ref.read(searchTextJobProvider.notifier).state;
            /*         if (searchController.text.isNotEmpty) {
              await filteredJobTypeNotifier.filteredJobHeaderList(
                  search: searchController.text,
                  type: AppConstants.search,
                  plantId: plant,
                  plantSec: plantSection,
              );
            }*/

            if ((searchController.text.isNotEmpty && search != '') ||
                filterOfJobType.isNotEmpty ||
                filterOfJobTypeCode.isNotEmpty ||
                filterOfPriorityCode.isNotEmpty ||
                filterOfPriority.isNotEmpty ||
                statusTypeFilter.isNotEmpty ||
                statusFilter.isNotEmpty ||
                selectedDate != null) {
              await filteredJobTypeNotifier.filteredJobHeaderList(
                typeList: filterOfJobTypeCode,
                priorityList: filterOfPriorityCode,
                statusList: statusTypeFilter,
                type: (searchController.text.isNotEmpty && search != '')
                    ? AppConstants.search
                    : AppConstants.jobType,
                plantId: plant,
                plantSec: plantSection,
                search: searchController.text,
              );
            }
            /*           if (selectedDate != null) {
              */ /*          await filteredFaultTypeNotifier.filteredFaultHeaderList(
                  faulttypeList: filterOfFaultCode,
                  priorityList: filterOfPriorityCode,
                  statusList: statusTypeFilter,
                  type: AppConstants.faultType,
                  plantId: plant,
                  plantSec: plantSection);*/ /*
            }*/
            else {
              await filteredJobTypeNotifier.filteredJobHeaderList(
                  type: 'Initial',
                  jobList: jobHeader,
                  plantId: plant,
                  plantSec: plantSection);
            }
          }
        }
      },
    );
    if (kIsWeb) {
      _subscription = PushNotifications.onNotificationProcessed.listen((_) {
        // 🚀 React to notification queue processed
        debugPrint("📦 Notification queue processed, updating UI");
        final plant = ref.watch(plantProvider);
        final plantSection = ref.watch(plantSectionProvider);
        final shift = ref.watch(shiftProvider);
        ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
        NotificationToastManager.showRefreshToast(context, onRefresh: () {
          final plant = ref.watch(plantProvider);
          final plantSection = ref.watch(plantSectionProvider);
          final shift = ref.watch(shiftProvider);
          ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
        });
        // showRefreshToast(context);
        // TODO: e.g., refresh data, show snackbar, etc.
      });
    }
  }

  // void showRefreshToast(BuildContext context) {
  //   final overlay = Overlay.of(context);

  //   late OverlayEntry entry; // ✅ Declare as late

  //   entry = OverlayEntry(
  //     builder: (context) => Positioned(
  //       bottom: 40,
  //       right: 20,
  //       child: OverlayToast(
  //         entry: entry,
  //         onRefresh: () {
  //           entry.remove();
  //           final plant = ref.watch(plantProvider);
  //           final plantSection = ref.watch(plantSectionProvider);
  //           final shift = ref.watch(shiftProvider);
  //           ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
  //         },
  //         onCancel: () {
  //           entry.remove();
  //         },
  //       ),
  //     ),
  //   );

  //   overlay.insert(entry);

  //   // Auto-remove after 5 seconds
  //   Future.delayed(const Duration(seconds: 5), () {
  //     if (entry.mounted) entry.remove();
  //   });
  // }

  @override
  void dispose() {
    searchController.dispose();
    if (mounted) {
      if (kIsWeb) {
        _subscription.cancel();
      }

      appNotifier.unSubscribeNotifySyncStatus();
      appNotifier.unSubscribeNotifyAttachmentStatus();
    }
    super.dispose();
  }

  Future<void> refreshData() async {
    await downloadTransactionData();
  }

  downloadTransactionData() async {
    List<Future> futures = [SyncEngine().receive()];
    try {
      await Future.wait(futures);
      UIHelper.showSnackBar(context,
          message: AppLocalizations.of(context)!.refreshing);
    } catch (error) {
      UIHelper.showSnackBar(context, message: error.toString());
    }
  }

  void scrollToTargetDate(DateTime targetDate) {
    int index = findTargetGroupIndex(targetDate);
    if (index != -1) {
      scrollController.animateTo(
        index * 120.0,
        duration: Duration(seconds: 1),
        curve: Curves.easeInOut,
      );
    }
  }

  int findTargetGroupIndex(DateTime targetDate) {
    final jobHeader = ref.watch(jobHeaderListProvider);
    final filteredJobHeader = ref.watch(filteredJobCreationHeaderListProvider);
    final isFiltering = filteredJobHeader != jobHeader;
    final displayedList = isFiltering ? filteredJobHeader : jobHeader;
    displayedList.sort((a, b) {
      DateTime dateA = convertToDateTime(a.start_date.toString());
      DateTime dateB = convertToDateTime(b.start_date.toString());
      return dateA.compareTo(dateB);
    });
    for (int i = 0; i < displayedList.length; i++) {
      DateTime reportedDate =
          convertToDateTime(displayedList[i].start_date.toString());
      if (formatDate(reportedDate) == formatDate(targetDate)) {
        return i;
      }
    }
    return -1;
  }

  void _onScroll() {
    double offset = scrollController.offset;
    final jobHeader = ref.watch(jobHeaderListProvider);
    final filteredJobHeader = ref.watch(filteredJobCreationHeaderListProvider);
    final isFiltering = filteredJobHeader != jobHeader;
    final displayedList = isFiltering ? filteredJobHeader : jobHeader;

    for (int i = 0; i < displayedList.length; i++) {
      DateTime reportedDate =
          convertToDateTime(displayedList[i].start_date.toString());

      double groupPosition = i * 120.0;

      if (offset >= groupPosition && offset < (groupPosition + 35)) {
        currentGroupNotifier.value = reportedDate;
        break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final filteredJobType =
        ref.watch(filteredJobCreationHeaderListProvider.notifier);
    final filterOfJobType = ref.watch(filterOfJobTypeProvider.notifier).state;

    final filterOfPriority = ref.watch(filterOfPriorityProvider.notifier).state;
    final filterOfPriorityCode =
        ref.watch(filterOfPriorityCodeProvider.notifier).state;
    final statusFilter = ref.watch(statusFilterProvider.notifier).state;
    final statusTypeFilter = ref.watch(statusTypeFilterProvider.notifier).state;
    final searchJob = ref.watch(searchTextJobProvider.notifier).state;

    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        body: LayoutBuilder(
          builder: (context, constraints) {
            final screenSize = UIHelper().getScreenType(context);
            switch (screenSize) {
              case ScreenType.mobile:
                return Column(
                  children: [
                    SizedBox(height: 3),
                    getAppBar(),
                    getSearchBar(filteredJobType, context),
                    // const SizedBox(height: 10),
                    getFilters(
                        filterOfJobType,
                        filterOfPriority,
                        filterOfPriorityCode,
                        statusFilter,
                        statusTypeFilter,
                        filteredJobType),
                    Expanded(
                      child: Container(
                        color: AppColors.white,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 18),
                          child: RefreshIndicator(
                              onRefresh: refreshData,
                              child: JobCreationList(
                                scrollController: scrollController,
                                jobDetailViewNotifier: jobDetailViewNotifier,
                                onItemTap: (value) {
                                  onTapBackJob();
                                  jobDetailViewNotifier.value = value;
                                },
                              )),
                        ),
                      ),
                    ),
                  ],
                );
              case ScreenType.tablet:
                return Column(
                  children: [
                    SizedBox(height: 3),
                    getAppBar(),
                    getSearchBar(filteredJobType, context),
                    // const SizedBox(height: 10),
                    getFilters(
                        filterOfJobType,
                        filterOfPriority,
                        filterOfPriorityCode,
                        statusFilter,
                        statusTypeFilter,
                        filteredJobType),
                    Expanded(
                      child: Container(
                        color: AppColors.white,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 18),
                          child: RefreshIndicator(
                              onRefresh: refreshData,
                              child: JobCreationList(
                                scrollController: scrollController,
                                jobDetailViewNotifier: jobDetailViewNotifier,
                                onItemTap: (value) {
                                  jobDetailViewNotifier.value = value;
                                },
                              )),
                        ),
                      ),
                    ),
                  ],
                );
              case ScreenType.desktop:
                return Row(
                  children: [
                    ConstrainedBox(
                      constraints:
                          const BoxConstraints(minWidth: 410, maxWidth: 410),
                      child: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: getAppBar(),
                          ),
                          getSearchBar(filteredJobType, context),
                          const SizedBox(height: 10),
                          getFilters(
                              filterOfJobType,
                              filterOfPriority,
                              filterOfPriorityCode,
                              statusFilter,
                              statusTypeFilter,
                              filteredJobType),
                          Expanded(
                            child: Container(
                              color: AppColors.white,
                              child: Padding(
                                padding: UIHelper().getScreenType(context) ==
                                        ScreenType.desktop
                                    ? const EdgeInsets.symmetric(horizontal: 18)
                                    : const EdgeInsets.symmetric(horizontal: 0),
                                child: RefreshIndicator(
                                    onRefresh: refreshData,
                                    child: JobCreationList(
                                      scrollController: scrollController,
                                      jobDetailViewNotifier:
                                          jobDetailViewNotifier,
                                      onItemTap: (value) {
                                        jobDetailViewNotifier.value = value;
                                      },
                                    )),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const VerticalDivider(
                      thickness: 3,
                    ),
                    Expanded(
                        flex: 6,
                        child: ValueListenableBuilder(
                          valueListenable: jobDetailViewNotifier,
                          builder: (context, value, child) {
                            if (value == null) {
                              return Container(
                                color: Colors.white,
                              );
                            } else {
                              if (value.type == "JOB_HEADER") {
                                return JobCreationDetailScreen(
                                    fromFault: value.getValue("fromFault"));
                              } else {
                                return Container(
                                  color: Colors.white,
                                );
                              }
                            }
                          },
                        ))
                  ],
                );
            }
          },
        ),
      ),
    );
  }

  Container getSearchBar(FilteredJobCreationHeaderListNotifier filteredJobType,
      BuildContext context) {
    return Container(
      padding: UIHelper().getScreenType(context) == ScreenType.desktop
          ? const EdgeInsets.symmetric(horizontal: 8)
          : const EdgeInsets.symmetric(horizontal: 18, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: CustomSearchBar(
                onChanged: (v) {
                  // Reset job detail view immediately when user starts typing
                  jobDetailViewNotifier.value = InteractiveItemModel(
                    type: "",
                    data: {"type": "", "index": null},
                  );

                  ref.read(searchTextJobProvider.notifier).state = v;
                  if (!kIsWeb) {
                    searchController.text = v;
                    ref.read(searchTextJobProvider.notifier).state = v;
                  }
                  onSearchJob(filteredJobType, v);
                },
                controller: searchController,
                onCancel: () {
                  // Reset job detail view IMMEDIATELY when clearing search
                  jobDetailViewNotifier.value = InteractiveItemModel(
                    type: "",
                    data: {"type": "", "index": null},
                  );

                  ref.read(searchTextJobProvider.notifier).state = "";
                  setState(() {
                    onClearSearchJob(filteredJobType);
                    if (scrollController.hasClients) {
                      scrollController.animateTo(
                        0.0,
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeOut,
                      );
                    }
                  });
                }),
          ),
          const SizedBox(width: 10),
          InkWell(
            radius: 50,
            onTap: () async {
              var selectedDate = await showDialog(
                  context: context,
                  builder: (context) {
                    return Dialog(
                        child: Container(
                      padding: const EdgeInsets.all(5.0),
                      height: MediaQuery.of(context).size.height * 0.5,
                      child: TaskCalender(
                          taskDataSource: TaskDataSource(ref
                              .watch(jobHeaderListProvider)
                              .map((e) => CalenderTask.fromJobHeader(e))
                              .toList())),
                    ));
                  });
              if (selectedDate != null) {
                ref.read(selectedCalendarDateJobProvider.notifier).state
                    // = selectedDate
                    ;
                scrollToTargetDate(selectedDate);
              }
            },
            focusColor: Colors.grey,
            hoverColor: Colors.grey,
            splashColor: Colors.grey,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Positioned(
                  bottom: 1,
                  child: ValueListenableBuilder(
                    valueListenable: currentGroupNotifier,
                    builder: (context, value, child) => Text(
                      value.day.toString(),
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryColor),
                    ),
                  ),
                ),
                Icon(
                  Icons.calendar_today,
                  size: 35,
                  color: AppColors.primaryColor,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Container getFilters(
      List<String> filterOfJobType,
      List<String> filterOfPriority,
      List<String> filterOfPriorityCode,
      List<String> statusFilter,
      List<String> statusTypeFilter,
      FilteredJobCreationHeaderListNotifier filteredJobType) {
    return Container(
      padding: UIHelper().getScreenType(context) == ScreenType.desktop
          ? const EdgeInsets.symmetric(horizontal: 8)
          : const EdgeInsets.symmetric(horizontal: 18, vertical: 6),
      child: Row(
        children: [
          Expanded(child: _jobType()),
          const SizedBox(width: 7),
          Expanded(child: _priorityType()),
          const SizedBox(width: 7),
          Expanded(child: _status()),
          const SizedBox(width: 7),
          if (filterOfJobType.isNotEmpty ||
              filterOfPriority.isNotEmpty ||
              filterOfPriorityCode.isNotEmpty ||
              statusFilter.isNotEmpty ||
              filterOfPriority.isNotEmpty ||
              statusTypeFilter.isNotEmpty)
            _clearFilter(filteredJobType)
        ],
      ),
    );
  }

  Container getAppBar() {
    return Container(
      padding: UIHelper().getScreenType(context) == ScreenType.desktop
          ? const EdgeInsets.symmetric(horizontal: 8)
          : const EdgeInsets.symmetric(horizontal: 18),
      child: const TopHeader(),
    );
  }

  _filterRow({
    required String title,
    required List<PopupMenuEntry<Object?>> Function(BuildContext) itemBuilder,
    required Color boxColor,
    required Color titleColor,
  }) {
    return Container(
      height: 46,
      decoration: BoxDecoration(
        color: boxColor,
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: HexColor("#D5DADD")),
      ),
      child: PopupMenuButton(
        padding: const EdgeInsets.only(right: 200),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        offset: const Offset(0, 50),
        itemBuilder: itemBuilder,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            const SizedBox(width: 10),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: titleColor,
              ),
            ),
            Spacer(),
            Icon(
              Icons.keyboard_arrow_down,
              color: titleColor,
            ),
            const SizedBox(width: 5),
          ],
        ),
        onSelected: (v) {
          // applyFilter();
        },
      ),
    );
  }

/*  List<String> filterOfJobType = [];
  List<String> filterOfJobTypeCode = [];
  List<String> filterOfPriority = [];
  List<String> filterOfPriorityCode = [];
  List<String> statusFilter = [];
  List<String> statusTypeFilter = [];*/

  _jobType() {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final jobType = ref.watch(jobTypeListProvider.notifier);
    final filteredJobTypes =
        ref.watch(filteredJobCreationHeaderListProvider.notifier);

    final filterOfJobType = ref.watch(filterOfJobTypeProvider.notifier).state;
    final filterOfJobTypeCode =
        ref.watch(filterOfJobTypeCodeProvider.notifier).state;

    final filterOfPriorityCode =
        ref.watch(filterOfPriorityCodeProvider.notifier).state;

    final statusTypeFilter = ref.watch(statusTypeFilterProvider.notifier).state;

    return _filterRow(
      itemBuilder: (context) {
        return List.generate(jobType.state.length, (index) {
          String type = jobType.state[index].description!;
          String typecode = jobType.state[index].job_type!;
          return PopupMenuItem(
            value: type,
            child: SizedBox(
              width: double.infinity,
              child: CheckboxListTile(
                controlAffinity: ListTileControlAffinity.leading,
                activeColor: filterOfJobType.contains(type)
                    ? AppColors.primaryColor
                    : AppColors.transparent,
                title: Text(
                  type,
                ),
                value: filterOfJobType.contains(type),
                onChanged: (value) async {
                  if (value != null) {
                    if (value) {
                      if (!filterOfJobType.contains(type)) {
                        filterOfJobType.add(type);
                        filterOfJobTypeCode.add(typecode);
                        Navigator.pop(context, filterOfJobType);
                        await filteredJobTypes.filteredJobHeaderList(
                            typeList: filterOfJobTypeCode,
                            priorityList: filterOfPriorityCode,
                            statusList: statusTypeFilter,
                            type: AppConstants.jobType,
                            plantId: plant,
                            plantSec: plantSection);
                        setState(() {});
                      }
                    } else {
                      jobDetailViewNotifier.value = InteractiveItemModel(
                        type: "",
                        data: {"type": "", "index": null},
                      );
                      filterOfJobType.remove(type);
                      filterOfJobTypeCode.remove(typecode);
                      Navigator.pop(context, filterOfJobType);

                      if (filterOfJobTypeCode.isNotEmpty ||
                          filterOfPriorityCode.isNotEmpty ||
                          statusTypeFilter.isNotEmpty) {
                        await filteredJobTypes.filteredJobHeaderList(
                            typeList: filterOfJobTypeCode,
                            priorityList: filterOfPriorityCode,
                            statusList: statusTypeFilter,
                            type: AppConstants.jobType,
                            plantId: plant,
                            plantSec: plantSection);
                      } else {
                        await filteredJobTypes.filteredJobHeaderList(
                            type: 'Initial',
                            jobList: ref.read(jobHeaderListProvider),
                            plantId: plant,
                            plantSec: plantSection);
                      }
                    }
                    jobDetailViewNotifier.value = InteractiveItemModel(
                      type: "",
                      data: {"type": "", "index": null},
                    );
                    setState(() {});
                    if (scrollController.hasClients) {
                      scrollController.animateTo(
                        0.0,
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeOut,
                      );
                    }
                  }
                },
              ),
            ),
          );
        });
      },
      boxColor: filterOfJobType.isEmpty
          ? AppColors.white
          : AppColors.filterButtonColor,
      titleColor: Colors.black,
      title: AppLocalizations.of(context)!.job_type,
    );
  }

  _priorityType() {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final priority = ref.watch(priorityListProvider.notifier).state;
    final filteredJobType =
        ref.watch(filteredJobCreationHeaderListProvider.notifier);

    final filterOfJobTypeCode =
        ref.watch(filterOfJobTypeCodeProvider.notifier).state;
    final filterOfPriority = ref.watch(filterOfPriorityProvider.notifier).state;
    final filterOfPriorityCode =
        ref.watch(filterOfPriorityCodeProvider.notifier).state;

    final statusTypeFilter = ref.watch(statusTypeFilterProvider.notifier).state;

    return _filterRow(
      itemBuilder: (context) {
        return List.generate(priority.length, (index) {
          String type = priority[index].description!;
          String typeCode = priority[index].priority_code!;
          return PopupMenuItem(
            value: type,
            child: Container(
              width: double.infinity,
              child: CheckboxListTile(
                controlAffinity: ListTileControlAffinity.leading,
                activeColor: filterOfPriority.contains(type)
                    ? AppColors.primaryColor
                    : AppColors.transparent,
                title: Text(
                  type,
                ),
                value: filterOfPriority.contains(type),
                onChanged: (value) async {
                  if (value != null) {
                    if (value) {
                      if (!filterOfPriority.contains(type)) {
                        filterOfPriority.add(type);
                        filterOfPriorityCode.add(typeCode);
                        Navigator.pop(context, filterOfPriority);
                        await filteredJobType.filteredJobHeaderList(
                            typeList: filterOfJobTypeCode,
                            priorityList: filterOfPriorityCode,
                            statusList: statusTypeFilter,
                            type: AppConstants.priority,
                            plantId: plant,
                            plantSec: plantSection);
                        setState(() {});
                      }
                    } else {
                      filterOfPriority.remove(type);
                      filterOfPriorityCode.remove(typeCode);
                      Navigator.pop(context, filterOfPriority);
                      if (filterOfJobTypeCode.isNotEmpty ||
                          filterOfPriorityCode.isNotEmpty ||
                          statusTypeFilter.isNotEmpty) {
                        await filteredJobType.filteredJobHeaderList(
                            typeList: filterOfJobTypeCode,
                            priorityList: filterOfPriorityCode,
                            statusList: statusTypeFilter,
                            type: AppConstants.priority,
                            plantId: plant,
                            plantSec: plantSection);
                      } else {
                        await filteredJobType.filteredJobHeaderList(
                            type: 'Initial',
                            jobList: ref.read(jobHeaderListProvider),
                            plantId: plant,
                            plantSec: plantSection);
                      }
                    }
                    jobDetailViewNotifier.value = InteractiveItemModel(
                      type: "",
                      data: {"type": "", "index": null},
                    );
                    setState(() {});
                    if (scrollController.hasClients) {
                      scrollController.animateTo(
                        0.0,
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeOut,
                      );
                    }
                  }
                },
              ),
            ),
          );
        });
      },
      boxColor: filterOfPriorityCode.isEmpty
          ? AppColors.white
          : AppColors.filterButtonColor,
      titleColor: Colors.black,
      title: AppLocalizations.of(context)!.priority,
    );
  }

  _status() {
    List<String> status = [
      "Open",
      "User Assigned",
      "Accepted",
      "Rejected",
      "Completed",
    ];
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final filteredJobsType =
        ref.watch(filteredJobCreationHeaderListProvider.notifier);
    final filterOfJobType = ref.watch(filterOfJobTypeProvider.notifier).state;
    final filterOfJobTypeCode =
        ref.watch(filterOfJobTypeCodeProvider.notifier).state;
    final filterOfPriorityCode =
        ref.watch(filterOfPriorityCodeProvider.notifier).state;
    final statusFilter = ref.watch(statusFilterProvider.notifier).state;
    final statusTypeFilter = ref.watch(statusTypeFilterProvider.notifier).state;

    return _filterRow(
      itemBuilder: (context) {
        return List.generate(status.length, (index) {
          String data = status[index];
          return PopupMenuItem(
            value: data,
            child: SizedBox(
              width: double.infinity,
              child: CheckboxListTile(
                controlAffinity: ListTileControlAffinity.leading,
                activeColor: statusFilter.contains(data)
                    ? AppColors.primaryColor
                    : AppColors.transparent,
                title: Text(
                  data,
                ),
                value: statusFilter.contains(data),
                onChanged: (value) async {
                  if (value != null) {
                    if (value) {
                      if (!statusFilter.contains(data)) {
                        statusFilter.add(data);

                        String dataa = getStatusType(data);
                        statusTypeFilter.add(dataa);
                        Navigator.pop(context, statusFilter);
                        await filteredJobsType.filteredJobHeaderList(
                            typeList: filterOfJobTypeCode,
                            priorityList: filterOfPriorityCode,
                            statusList: statusTypeFilter,
                            type: AppConstants.status,
                            plantId: plant,
                            plantSec: plantSection);
                        setState(() {});
                      }
                    } else {
                      statusFilter.remove(data);
                      String dataa = getStatusType(data);
                      statusTypeFilter.remove(dataa);
                      Navigator.pop(context, statusFilter);
                      if (filterOfJobTypeCode.isNotEmpty ||
                          filterOfPriorityCode.isNotEmpty ||
                          statusTypeFilter.isNotEmpty) {
                        await filteredJobsType.filteredJobHeaderList(
                            typeList: filterOfJobTypeCode,
                            priorityList: filterOfPriorityCode,
                            statusList: statusTypeFilter,
                            type: AppConstants.status,
                            plantId: plant,
                            plantSec: plantSection);
                      } else {
                        await filteredJobsType.filteredJobHeaderList(
                            type: 'Initial',
                            jobList: ref.read(jobHeaderListProvider),
                            plantId: plant,
                            plantSec: plantSection);
                      }
                    }
                    jobDetailViewNotifier.value = InteractiveItemModel(
                      type: "",
                      data: {"type": "", "index": null},
                    );
                    setState(() {});
                    if (scrollController.hasClients) {
                      scrollController.animateTo(
                        0.0,
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeOut,
                      );
                    }
                  }
                },
              ),
            ),
          );
        });
      },
      boxColor: statusTypeFilter.isEmpty
          ? AppColors.white
          : AppColors.filterButtonColor,
      titleColor: Colors.black,
      title: AppLocalizations.of(context)!.status,
    );
  }

  getStatusType(status) {
    switch (status) {
      case "Open":
        return "CREATED";
      case "User Assigned":
        return "ASSIGNED";
      case "Accepted":
        return "ACCEPTED";
      case "Completed":
        return "COMPLETED";
      case "Rejected":
        return "REJECTED";
      default:
        return "";
    }
  }

  void onSearchJob(
      FilteredJobCreationHeaderListNotifier filteredJobType, String v) async {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final filterOfJobTypeCode =
        ref.read(filterOfJobTypeCodeProvider.notifier).state;
    final filterOfPriorityCode =
        ref.read(filterOfPriorityCodeProvider.notifier).state;
    final statusTypeFilter = ref.read(statusTypeFilterProvider.notifier).state;
    await filteredJobType.filteredJobHeaderList(
        typeList: filterOfJobTypeCode,
        priorityList: filterOfPriorityCode,
        statusList: statusTypeFilter,
        search: v,
        type: AppConstants.search,
        plantId: plant,
        plantSec: plantSection);
    setState(() {});
    if (scrollController.hasClients) {
      scrollController.animateTo(
        0.0,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void onClearSearchJob(
      FilteredJobCreationHeaderListNotifier filteredJobType) async {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final jobHeader = ref.read(jobHeaderListProvider.notifier).state;
    await filteredJobType.filteredJobHeaderList(
        type: 'Initial',
        jobList: jobHeader,
        plantId: plant,
        plantSec: plantSection);
    setState(() {
      searchController.clear();
      ref.read(searchTextJobProvider.notifier).state = '';
    });
  }

  _clearFilter(FilteredJobCreationHeaderListNotifier filteredFaultType) {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    return InkWell(
      onTap: () async {
        final jobHeader = ref.read(jobHeaderListProvider);
        await filteredFaultType.filteredJobHeaderList(
            type: 'Initial',
            jobList: jobHeader,
            plantId: plant,
            plantSec: plantSection);
        setState(() {
          ref.read(filterOfJobTypeProvider.notifier).state = [];
          ref.read(filterOfJobTypeCodeProvider.notifier).state = [];
          ref.read(filterOfPriorityProvider.notifier).state = [];
          ref.read(filterOfPriorityCodeProvider.notifier).state = [];
          ref.read(statusTypeFilterProvider.notifier).state = [];
          ref.read(statusFilterProvider.notifier).state = [];
          jobDetailViewNotifier.value = InteractiveItemModel(
            type: "",
            data: {"type": "", "index": null},
          );
        });
        if (scrollController.hasClients) {
          scrollController.animateTo(
            0.0,
            duration: Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      },
      child: const Icon(Icons.cancel),
    );
  }

  onTapBackJob() async {
    final jobHeader = ref.read(jobHeaderProvider.notifier).state;
    bool data = await checkIfModifiedJobDataThere();
    if (!data) {
      if (mounted) {
        UIHelper.showConfirmationDialogWithYesOrNo(context,
            description: AppLocalizations.of(context)!
                .unsave_data_do_you_want_save_it, no: () {
          onUnSaveFault(jobHeader);
        }, yes: () {
          Navigator.pop(context);
          onCheckback(null, onPressback: true);
        });
      }
      return false;
    } else {
      await AppDatabaseManager()
          .delete(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));
      return true;
    }
  }

  Future<bool> checkIfModifiedJobDataThere() async {
    final jobHeaderr = ref.read(jobHeaderProvider.notifier).state;
    final originalJobHeader = ref.read(jobHeaderProvider);
    final jobDescription = ref.watch(jobDescriptionProvider.notifier).state;
    final priority = ref.watch(jobPriorityProvider.notifier).state;
    final jobType = ref.watch(jobTypeProvider.notifier).state;
    final startDate = ref.watch(jobStartOnProvider.notifier).state;
    final endDate = ref.watch(jobEndOnProvider.notifier).state;
    final longText = ref.read(jobLongTextProvider.notifier).state;
    JOB_HEADER jobHeader = JOB_HEADER.fromJson(jobHeaderr.toJson());
    if (startDate != 0) {
      jobHeader.start_date = startDate;
    }
    if (endDate != 0) {
      jobHeader.end_date = endDate;
    }
    if (jobDescription.isNotEmpty && jobDescription != '') {
      jobHeader.description = jobDescription;
    }
    if (longText.isNotEmpty && longText != '') {
      jobHeader.details = longText;
    }
    if (priority.priority_code != null && priority.priority_code != '') {
      jobHeader.priority = priority.priority_code;
    }
    if (jobType.job_type != null && jobType.job_type != '') {
      jobHeader.job_type = jobType.job_type;
    }
    bool edited = true;
    edited = areJsonEqual(originalJobHeader.toJson(), jobHeader.toJson());
    return edited;
  }

  bool areJsonEqual(Map<String, dynamic> json1, Map<String, dynamic> json2) {
    return const DeepCollectionEquality().equals(json1, json2);
  }

  void onUnSaveFault(JOB_HEADER jobHeader) async {
    Navigator.pop(context);
    await AppDatabaseManager()
        .delete(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));
    if (mounted) {
      Navigator.pop(context, null);
    }
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    await ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
    final jobHeaderDatata = ref.read(jobHeaderListProvider);
    final filteredJobTypeNotifier =
        ref.read(filteredJobCreationHeaderListProvider.notifier);

    final search = ref.read(searchTextJobProvider.notifier).state;
    final filterOfJobType = ref.read(filterOfJobTypeProvider.notifier).state;
    final filterOfJobTypeCode =
        ref.read(filterOfJobTypeCodeProvider.notifier).state;
    final filterOfPriority = ref.read(filterOfPriorityProvider.notifier).state;
    final filterOfPriorityCode =
        ref.read(filterOfPriorityCodeProvider.notifier).state;
    final statusFilter = ref.read(statusFilterProvider.notifier).state;
    final statusTypeFilter = ref.read(statusTypeFilterProvider.notifier).state;
    final selectedDate = ref.read(selectedCalendarDateJobProvider);
    if ((search != '') ||
        filterOfJobType.isNotEmpty ||
        filterOfJobTypeCode.isNotEmpty ||
        filterOfPriorityCode.isNotEmpty ||
        filterOfPriority.isNotEmpty ||
        statusTypeFilter.isNotEmpty ||
        statusFilter.isNotEmpty ||
        selectedDate != null) {
      // Reset job detail view when any filter is applied
      jobDetailViewNotifier.value = InteractiveItemModel(
        type: "",
        data: {"type": "", "index": null},
      );
      await filteredJobTypeNotifier.filteredJobHeaderList(
        typeList: filterOfJobTypeCode,
        priorityList: filterOfPriorityCode,
        statusList: statusTypeFilter,
        type: (search != '') ? AppConstants.search : AppConstants.jobType,
        plantId: plant,
        plantSec: plantSection,
        search: search,
      );
    } else {
      await filteredJobTypeNotifier.filteredJobHeaderList(
          type: 'Initial',
          jobList: jobHeaderDatata,
          plantId: plant,
          plantSec: plantSection);
    }
  }

  void onCheckback(String? status, {bool onPressback = false}) {
    if (!validate()) {
      return;
    }
    if (onPressback) {
      onSave();
    } else {
      UIHelper.showConfirmationDialog(
        context,
        description: AppLocalizations.of(context)!.save_job_confirmation,
        positiveButtonString: AppLocalizations.of(context)!.ok,
        positiveButtonOnTap: () {
          Navigator.pop(context);
          onSave();
        },
      );
    }
  }

  bool validate() {
    final attachment = ref.watch(getJobDocumentProvider.notifier).state;
    final selectedDescription =
        ref.watch(jobDescriptionProvider.notifier).state;
    final selectedLongText = ref.watch(jobLongTextProvider.notifier).state;
    final selectedJobType = ref.watch(jobTypeProvider.notifier).state;
    final selectedPriority = ref.watch(priorityProvider.notifier).state;
    final endDate = ref.watch(jobEndOnProvider.notifier).state;

    bool data = false;
    if (selectedDescription.isEmpty) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_enter_description);
      return data;
    }
    if (selectedLongText.isEmpty) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_enter_long_text);
      return data;
    } else if (selectedJobType.description == null) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_jobType);
      return data;
    } else if (selectedPriority.priority_code == null) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_priority);
      return data;
    } else if (endDate == 0) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_endDate);
      return data;
    } else if (attachment.isEmpty) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!
              .please_upload_atleast_one_attachment);
      return data;
    }
    return true;
  }

  void onSave() async {
    UIHelper().progressDialog(
        context: context, message: AppLocalizations.of(context)!.saving_job);
    final headerData = ref.watch(getJobDocumentHeaderProvider.notifier).state;

    List<DOCUMENT_HEADER> header = headerData
        .where((element) => element.objectStatus == ObjectStatus.add)
        .toList();
    if(kIsWeb){
      await PAHelper.addDocumentInSyncMode(context, header);
    }
    else{
      await PAHelper.addDocumentInAsyncMode(context, header);
    }


    await newCreationOfJob();
  }

  Future<void> newCreationOfJob() async {
    final faultHeaderData = ref.watch(faultHeaderProvider.notifier);
    FAULT_HEADER faultHeader = faultHeaderData.state;
    final jobHeader = ref.watch(jobHeaderProvider.notifier).state;
    final jobDescription = ref.watch(jobDescriptionProvider.notifier).state;
    final jobLongText = ref.watch(jobLongTextProvider.notifier).state;
    final priority = ref.watch(jobPriorityProvider.notifier).state;
    final jobType = ref.watch(jobTypeProvider.notifier).state;
    final jobStartOn = ref.watch(jobStartOnProvider.notifier).state;
    final jobEndOn = ref.watch(jobEndOnProvider.notifier).state;

    final jobAssignedTo = ref.watch(jobAssignedToProvider.notifier).state;

    jobHeader.description = jobDescription;
    jobHeader.details = jobLongText;
    jobHeader.priority = priority.priority_code;
    jobHeader.job_type = jobType.job_type;
    jobHeader.start_date = jobStartOn;
    if (jobEndOn != 0) {
      jobHeader.end_date = jobEndOn;
    }
    if (jobAssignedTo.isNotEmpty) {
      jobHeader.assigned_to = jobAssignedTo;
    }
    await AppDatabaseManager()
        .update(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));
    JOB_ACTION? jobAction =
        await DbHelper.getJobActionByJobId(jobHeader.job_id.toString());
    if (jobAction != null) {
      await AppDatabaseManager()
          .update(DBInputEntity(JOB_ACTION.TABLE_NAME, jobAction.toJson()));
    } else {
      JOB_ACTION newAction = JOB_ACTION(
          job_id: jobHeader.job_id, user_action: Constants.JOB_STATE_OSNO);
      newAction.fid = jobHeader.lid;
      await AppDatabaseManager()
          .insert(DBInputEntity(JOB_ACTION.TABLE_NAME, newAction.toJson()));
      if (jobHeader.assigned_to != null && jobHeader.assigned_to != '') {
        JOB_ACTION? jobAction =
            await DbHelper.getJobActionByJobId(jobHeader.job_id.toString());
        if (jobAction != null) {
          jobAction.user_action = Constants.JOB_STATE_ORAS;
          await AppDatabaseManager()
              .update(DBInputEntity(JOB_ACTION.TABLE_NAME, jobAction.toJson()));
        }
      }
    }

    if (!kIsWeb) {
      await PAHelper.addOrModifyJobInAsyncMode(context, jobHeader);
    } else {
      await PAHelper.addOrModifyJobInSyncMode(context, jobHeader);
    }
    faultHeader.job_id = jobHeader.job_id;
    await AppDatabaseManager()
        .update(DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));

    Navigator.pop(context);
    Navigator.pop(context, jobHeader.job_id);
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
/*    jobHeader.status = Constants.JOB_STATE_OSNO;
    await AppDatabaseManager()
        .update(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));*/
    JOB_HEADER? jobsData =
        await DbHelper.getJobHeaderByFautlId(faultHeader.fault_id.toString());
    if (jobsData != null) {
      faultHeader.status = Constants.FAULT_STATE_ORAS;
      await AppDatabaseManager()
          .update(DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));
    }
    List jobist = await AppDatabaseManager()
        .select(DBInputEntity(JOB_HEADER.TABLE_NAME, {}));
    if (jobist.isNotEmpty) {}
    await ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
    final jobHeaderDatata = ref.read(jobHeaderListProvider);
    final filteredJobTypeNotifier =
        ref.read(filteredJobCreationHeaderListProvider.notifier);
    final search = ref.read(searchTextJobProvider.notifier).state;
    final filterOfJobType = ref.read(filterOfJobTypeProvider.notifier).state;
    final filterOfJobTypeCode =
        ref.read(filterOfJobTypeCodeProvider.notifier).state;
    final filterOfPriority = ref.read(filterOfPriorityProvider.notifier).state;
    final filterOfPriorityCode =
        ref.read(filterOfPriorityCodeProvider.notifier).state;
    final statusFilter = ref.read(statusFilterProvider.notifier).state;
    final statusTypeFilter = ref.read(statusTypeFilterProvider.notifier).state;
    final selectedDate = ref.read(selectedCalendarDateJobProvider);
    if ((search != '') ||
        filterOfJobType.isNotEmpty ||
        filterOfJobTypeCode.isNotEmpty ||
        filterOfPriorityCode.isNotEmpty ||
        filterOfPriority.isNotEmpty ||
        statusTypeFilter.isNotEmpty ||
        statusFilter.isNotEmpty ||
        selectedDate != null) {
      // Reset job detail view when any filter is applied
      jobDetailViewNotifier.value = InteractiveItemModel(
        type: "",
        data: {"type": "", "index": null},
      );
      await filteredJobTypeNotifier.filteredJobHeaderList(
        typeList: filterOfJobTypeCode,
        priorityList: filterOfPriorityCode,
        statusList: statusTypeFilter,
        type: (search != '') ? AppConstants.search : AppConstants.jobType,
        plantId: plant,
        plantSec: plantSection,
        search: search,
      );
    } else {
      await filteredJobTypeNotifier.filteredJobHeaderList(
          type: 'Initial',
          jobList: jobHeaderDatata,
          plantId: plant,
          plantSec: plantSection);
    }

    final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
    await faultHeaderList.fetchFaultHeaderList(plant);
    final filteredFaultType =
        ref.read(filteredFaultHeaderListProvider.notifier);

    final filterOfFaultType =
        ref.read(filterOfFaultTypeProvider.notifier).state;
    final filterOfFaultCode =
        ref.read(filterOfFaultCodeProvider.notifier).state;
    final filterOfFaultPriorityCode =
        ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
    final filterOfFaultPriority =
        ref.read(filterOfFaultPriorityProvider.notifier).state;
    final statusTypeFaultFilter =
        ref.read(statusTypeFaultFilterProvider.notifier).state;
    final statusFaultFilter =
        ref.read(statusFaultFilterProvider.notifier).state;
    final selectedDateFault = ref.read(selectedCalendarDateProvider);
    final searchFault = ref.read(searchTextProvider.notifier).state;

    if ((searchFault != '') ||
        filterOfFaultType.isNotEmpty ||
        filterOfFaultCode.isNotEmpty ||
        filterOfFaultPriorityCode.isNotEmpty ||
        filterOfFaultPriority.isNotEmpty ||
        statusTypeFaultFilter.isNotEmpty ||
        statusFaultFilter.isNotEmpty ||
        selectedDateFault != null) {
      await filteredFaultType.filteredFaultHeaderList(
          faulttypeList: filterOfFaultCode,
          priorityList: filterOfPriorityCode,
          statusList: statusTypeFilter,
          type: (searchFault != '')
              ? AppConstants.search
              : AppConstants.faultType,
          plantId: plant,
          plantSec: plantSection,
          search: searchFault);
    } else {
      await filteredFaultType.filteredFaultHeaderList(
          type: 'Initial',
          faultList: faultHeaderList.state,
          // faultList: faultHeaderDatas,
          plantId: plant,
          plantSec: plantSection);
    }

    ///TODO FAULT
/*    if (widget.fromFault) {
      Navigator.pop(context);
    }*/
    ref.read(bottomNavIndexProvider.notifier).state = 3;
  }
}
