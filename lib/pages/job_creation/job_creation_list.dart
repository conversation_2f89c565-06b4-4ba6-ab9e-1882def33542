import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/pages/job_creation/widgets/job_creation_card.dart';
import 'package:rounds/models/intractive_Item_Model.dart';
import 'package:rounds/providers/job_creation/job_creation_filter_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/utils.dart';

import '../../providers/job_creation/job_header_provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class JobCreationList extends ConsumerStatefulWidget {
  const JobCreationList(
      {super.key,
      required this.scrollController,
      required this.jobDetailViewNotifier,
      required this.onItemTap});
  final ScrollController scrollController;
  final Function(InteractiveItemModel) onItemTap;
  final ValueNotifier<InteractiveItemModel?> jobDetailViewNotifier;

  @override
  ConsumerState<JobCreationList> createState() => _JobCreationListState();
}

class _JobCreationListState extends ConsumerState<JobCreationList> {
/*
  late ValueNotifier<InteractiveItemModel?> jobDetailViewNotifier;

  @override
  void initState() {
    super.initState();
    jobDetailViewNotifier = ValueNotifier<InteractiveItemModel?>(null);
  }
*/

  @override
  Widget build(BuildContext context) {
    final jobHeader = ref.watch(jobHeaderListProvider);
    final filteredJobHeader = ref.watch(filteredJobCreationHeaderListProvider);
    //final isFiltering = filteredJobHeader != jobHeader;
   // final displayedList = isFiltering ? filteredJobHeader : jobHeader;

   final displayedList = filteredJobHeader.isNotEmpty ? filteredJobHeader : jobHeader;

    displayedList.sort((a, b) {
      DateTime dateA = convertToDateTime(a.start_date.toString());
      DateTime dateB = convertToDateTime(b.start_date.toString());
      return dateA.compareTo(dateB);
    });

    return displayedList.isEmpty
        ? Center(child: Text(AppLocalizations.of(context)!.no_results_found))
        : SlidableAutoCloseBehavior(
            child: GroupedListView(
              controller: widget.scrollController,
              sort: false,
              shrinkWrap: true,
              physics: const AlwaysScrollableScrollPhysics(),
              stickyHeaderBackgroundColor: Colors.transparent,
              elements: displayedList,
              groupBy: (element) {
                DateTime reportedDate =
                    convertToDateTime(element.start_date.toString());
                return formatDate(reportedDate);
              },
              groupHeaderBuilder: (element) => SizedBox(
                height: 35,
                child: Center(
                  child: Card(
                    elevation: 0,
                    color: AppColors.primaryColor.withOpacity(0.3),
                    child: Padding(
                      padding: const EdgeInsets.all(5.0),
                      child: Text(
                        formatDate(
                            convertToDateTime(element.start_date.toString())),
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                            fontSize: 11, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ),
              ),
              indexedItemBuilder: (context, element, index) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: Row(
                  children: [
                    ValueListenableBuilder(
                      valueListenable: widget.jobDetailViewNotifier,
                      builder: (context, value, child) {
                        return Expanded(
                          child: JobCreationCard(
                            index: index,
                            color: (UIHelper().getScreenType(context) ==
                                        ScreenType.desktop &&
                                    value?.getValue("index") == index)
                                ? AppColors.primaryColor
                                    .withOpacity(0.2)
                                    .withRed(10)
                                : Colors.white,
                            item: element,
                            onTap: (value) {
                              widget.jobDetailViewNotifier.value = value;
                              widget.onItemTap(value);
                            },
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
  }
}
