import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/be/JOB_HEADER.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/pages/job_creation/tabs/edit_job_creation_field_provider.dart';
import 'package:rounds/pages/job_creation/tabs/job_creation_activity_tab_fault.dart';
import 'package:rounds/pages/job_creation/tabs/job_creation_attachment_tab_fault.dart';
import 'package:rounds/pages/job_creation/tabs/job_creation_general_tab_fault.dart';
import 'package:rounds/providers/job_creation/job_header_provider.dart';
import 'package:rounds/providers/user_provider.dart';
import 'package:rounds/utils/app_extensions.dart';
import 'package:rounds/utils/utils.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import '../../be/DOCUMENT_HEADER.dart';
import '../../be/FAULT_HEADER.dart';
import '../../../providers/fault/fault_header_provider.dart';
import '../../../providers/fault/fault_type_provider.dart';
import '../../../utils/app_colors.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../be/JOB_ACTION.dart';
import '../../be/JOB_DOCUMENT.dart';
import '../../helpers/db_helper.dart';
import '../../helpers/pa_helper.dart';

import '../../models/intractive_Item_Model.dart';
import '../../providers/attachments/attachment_provider.dart';
import '../../providers/job_creation/job_creation_filter_provider.dart';
import '../../utils/app_constants.dart';
import '../../utils/app_styles.dart';
import '../../utils/constants.dart';
import '../assign_plan_screen.dart';
import '../dashboard/dashboard.dart';
import '../fault/fault_detail_screen.dart';
import '../fault/fault_filter_provider.dart';
import '../fault/fault_screen.dart';
import '../fault/tabs/edit_fault_field_provider.dart';

import 'package:flutter/cupertino.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';

import '../widgets/error_longtext_view.dart';
import 'job_creation_screen.dart';

class JobCreationDetailScreen extends ConsumerStatefulWidget {
  bool fromFault;
  final bool isBackButtonRequire;
  final bool display;
  JobCreationDetailScreen(
      {required this.fromFault,
      super.key,
      this.isBackButtonRequire = false,
      this.display = false});

  @override
  _JobCreationDetailScreenState createState() =>
      _JobCreationDetailScreenState();
}

class _JobCreationDetailScreenState
    extends ConsumerState<JobCreationDetailScreen> {
  static const sourceClass = 'JobCreationScreen';
  String selectedStatus = "";
  TextEditingController commentController = TextEditingController();
  bool isExpandedForLocationDetail = false;
  bool isExpandedForAttachment = false;
  bool error = false;
  TextEditingController longTextController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();
  bool viewMore = false;
  Future<List<InfoMessageData?>>? fetchInfoMessage;

  @override
  void initState() {
    super.initState();
    final jobHeader = ref.read(jobHeaderProvider.notifier).state;
    final description = ref.read(jobDescriptionProvider.notifier);
    final longText = ref.read(jobLongTextProvider.notifier);
    final jobStartOn = ref.read(jobStartOnProvider.notifier);
    final jobEndOn = ref.read(jobEndOnProvider.notifier);
    final jobPriority = ref.read(jobPriorityProvider.notifier);
    final jobType = ref.read(jobTypeProvider.notifier);
    if (jobHeader.priority != null) {
      jobPriority.getJobPriority(ref
          .read(priorityListProvider.notifier)
          .fetchPriorityCode(jobHeader.priority.toString()));
    }
    if (jobHeader.job_type != null) {
      jobType.getJobType(ref
          .read(jobTypeListProvider.notifier)
          .fetchjobTypeFromCode(jobHeader.job_type.toString()));
    }

    if (jobHeader.start_date != null) {
      jobStartOn.getJobStartOn(jobHeader.start_date!);
    }
    if (jobHeader.end_date != null) {
      jobEndOn.getJobEndOn(jobHeader.end_date!);
    }
    if (jobHeader.description != null && jobHeader.description != '') {
      descriptionController.text = jobHeader.description.toString();
      description.getJobDescription(descriptionController.text);
    }
    if (jobHeader.details != null && jobHeader.details != '') {
      longTextController.text = jobHeader.details.toString();
      longText.getJobLongText(longTextController.text);
    }
    fetchInfoMessage = InfoMessageHelper().getInfoMessageByBeLid(jobHeader.lid);
  }

  bool isEditVisible(JOB_HEADER jobHeader) {
    if (jobHeader.status == null ||
        jobHeader.status == Constants.FAULT_STATE_OSNO) {
      return jobHeader.syncStatus == SyncStatus.none ||
          jobHeader.syncStatus == SyncStatus.error;
    } else if (jobHeader.status == Constants.JOB_STATE_RJCT) {
      // Only allow edit if user can create or assign
      return (isJobCreateVisible() || isAssignVisible()) &&
          (jobHeader.syncStatus == SyncStatus.none ||
              jobHeader.syncStatus == SyncStatus.error);
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    final editProvider = ref.watch(editJobCreationProvider.notifier);
    final jobHeader = ref.read(jobHeaderProvider.notifier).state;
    final longText = ref.read(jobLongTextProvider.notifier);
    if (jobHeader.details != null && jobHeader.details != '' && ScreenType.desktop == UIHelper().getScreenType(context)) {
      longTextController.text = jobHeader.details.toString();
      longText.getJobLongText(longTextController.text);
    }
    return WillPopScope(
      onWillPop: () async {
        bool data = await checkIfModifiedJobDataThere();
        if (!data) {
          if (mounted) {
            UIHelper.showConfirmationDialogWithYesOrNo(context,
                description: AppLocalizations.of(context)!
                    .unsave_data_do_you_want_save_it, no: () {
              onUnSaveJob(jobHeader);
            }, yes: () {
              Navigator.pop(context);
              onCheckback(null, onPressback: true);
            });
          }
          return false;
        } else {
          String deleteQuery =
              'DELETE FROM ${JOB_HEADER.TABLE_NAME} WHERE ${JOB_HEADER.FIELD_JOB_ID} = ${jobHeader.job_id}';
          await AppDatabaseManager().execute(deleteQuery);
          return true;
        }
      },
      child: DefaultTabController(
        initialIndex: 0,
        length: 2,
        child: Scaffold(
          backgroundColor: AppColors.white,
          appBar: AppBar(
            elevation: 0,
            backgroundColor: AppColors.white,
            leadingWidth: widget.isBackButtonRequire
                ? 40
                : UIHelper().getScreenType(context) != ScreenType.desktop
                    ? 40
                    : jobHeader.p_mode == AppConstants.add
                        ? 40
                        : 0,
            leading: widget.isBackButtonRequire
                ? IconButton(
                    onPressed: () async {
                      bool data = await checkIfModifiedJobDataThere();
                      if (!data) {
                        if (mounted) {
                          UIHelper.showConfirmationDialogWithYesOrNo(context,
                              description: AppLocalizations.of(context)!
                                  .unsave_data_do_you_want_save_it, no: () {
                            onUnSaveJob(jobHeader);
                          }, yes: () {
                            Navigator.pop(context);
                            onCheckback(null, onPressback: true);
                          });
                        }
                      } else {
                        if(jobHeader.p_mode == AppConstants.add){
                          if (widget.display) {
                          Navigator.of(context)
                              .popUntil((route) => route.isFirst);
                        } else {
                          if (jobHeader.p_mode == AppConstants.add) {
                            String deleteQuery =
                                'DELETE FROM ${JOB_HEADER.TABLE_NAME} WHERE ${JOB_HEADER.FIELD_JOB_ID} = ${jobHeader.job_id}';
                            await AppDatabaseManager().execute(deleteQuery);
                          }
                          Navigator.pop(context);
                        }
                        }
                        
                      }
                    },
                    icon: Icon(
                      Icons.arrow_back_ios,
                      color: AppColors.titleTextColor,
                      size: 20,
                    ))
                : UIHelper().getScreenType(context) != ScreenType.desktop
                    ? IconButton(
                        onPressed: () async {
                          bool data = await checkIfModifiedJobDataThere();
                          if (!data) {
                            if (mounted) {
                              UIHelper.showConfirmationDialogWithYesOrNo(
                                  context,
                                  description: AppLocalizations.of(context)!
                                      .unsave_data_do_you_want_save_it, no: () {
                                onUnSaveJob(jobHeader);
                              }, yes: () {
                                Navigator.pop(context);
                                onCheckback(null, onPressback: true);
                              });
                            }
                          } else {
                            if(jobHeader.p_mode == AppConstants.add && jobHeader.p_mode != null){
                              String deleteQuery =
                                'DELETE FROM ${JOB_HEADER.TABLE_NAME} WHERE ${JOB_HEADER.FIELD_JOB_ID} = ${jobHeader.job_id}';
                            await AppDatabaseManager().execute(deleteQuery);
                            }
                            
                            Navigator.pop(context);
                          }
                        },
                        icon: Icon(
                          Icons.arrow_back_ios,
                          color: AppColors.titleTextColor,
                          size: 20,
                        ))
                    : jobHeader.p_mode == AppConstants.add
                        ? IconButton(
                            onPressed: () async {
                              bool data = await checkIfModifiedJobDataThere();
                              if (!data) {
                                if (mounted) {
                                  UIHelper.showConfirmationDialogWithYesOrNo(
                                      context,
                                      description: AppLocalizations.of(context)!
                                          .unsave_data_do_you_want_save_it,
                                      no: () {
                                    onUnSaveJob(jobHeader);
                                  }, yes: () {
                                    Navigator.pop(context);
                                    onCheckback(null, onPressback: true);
                                  });
                                }
                              } else {
                                if(jobHeader.p_mode == AppConstants.add){
                                  String deleteQuery =
                                    'DELETE FROM ${JOB_HEADER.TABLE_NAME} WHERE ${JOB_HEADER.FIELD_JOB_ID} = ${jobHeader.job_id}';
                                await AppDatabaseManager().execute(deleteQuery);
                                }
                                
                                Navigator.pop(context);
                              }
                            },
                            icon: Icon(
                              Icons.arrow_back_ios,
                              color: AppColors.titleTextColor,
                              size: 20,
                            ))
                        : const SizedBox.shrink(),
            title: jobHeader.status == null
                ? Padding(
                    padding:
                        UIHelper().getScreenType(context) == ScreenType.desktop
                            ? EdgeInsets.zero
                            : const EdgeInsets.only(left: 8.0),
                    child: Text(
                      AppLocalizations.of(context)!.new_string,
                      style: UIHelper.titleStyle14(),
                    ),
                  )
                : Padding(
                    padding:
                        UIHelper().getScreenType(context) == ScreenType.desktop
                            ? EdgeInsets.zero
                            : const EdgeInsets.only(left: 8.0),
                    child: Text(
                      jobHeader.job_id.toString(),
                      style: UIHelper.titleStyle16(),
                    ),
                  ),
            actions: [
              editProvider.state
                  ? Row(
                      children: [
                        (kIsWeb ||
                                UIHelper().getScreenType(context) ==
                                    ScreenType.desktop)
                            ? ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.greenColor),
                                onPressed: () {
                                  onCheck(jobHeader.status);
                                },
                                child: Text('Save',
                                    style: TextStyle(color: AppColors.white)),
                              )
                            : InkWell(
                                onTap: () {
                                  onCheck(jobHeader.status);
                                },
                                child: const Icon(
                                  Icons.check,
                                  size: 30,
                                  color: AppColors.greenColor,
                                )),
                        const SizedBox(
                          width: 20,
                        ),
                        jobHeader.status == null
                            ? SizedBox()
                            : (kIsWeb ||
                                    UIHelper().getScreenType(context) ==
                                        ScreenType.desktop)
                                ? ElevatedButton(
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor:
                                            AppColors.redAccentColor),
                                    onPressed: () {
                                      editProvider.getEditJobCreationEnable(
                                          !editProvider.state);
                                      retainOriginalData();
                                      setState(() {});
                                    },
                                    child: Text('Cancel',
                                        style:
                                            TextStyle(color: AppColors.white)),
                                  )
                                : InkWell(
                                    onTap: () {
                                      editProvider.getEditJobCreationEnable(
                                          !editProvider.state);
                                      setState(() {});
                                    },
                                    child: const Icon(
                                      Icons.cancel,
                                      color: AppColors.redAccentColor,
                                      size: 30,
                                    ),
                                  ),
                      ],
                    )
                  : Row(
                      children: [
                        Visibility(
                            visible: isExecutionVisible(),
                            child: (kIsWeb ||
                                    UIHelper().getScreenType(context) ==
                                        ScreenType.desktop)
                                ? getStatusActionButtonsWeb(jobHeader)
                                : getStatusActionButtons(jobHeader)),
                        // (jobHeader.status == null ||
                        //         jobHeader.status ==
                        //             Constants.FAULT_STATE_OSNO)
                        //     ? const SizedBox(
                        //         width: 20,
                        //       )
                        //     : const SizedBox(
                        //         width: 20,
                        //       ),
                        Visibility(
                          visible: isExecutionVisible() || isJobCreateVisible(),
                          child: isEditVisible(jobHeader)
                              ? (kIsWeb ||
                                      UIHelper().getScreenType(context) ==
                                          ScreenType.desktop)
                                  ? Padding(
                                      padding: const EdgeInsets.only(left: 20),
                                      child: ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                AppColors.greySubtitleText),
                                        onPressed: () {
                                          editProvider.getEditJobCreationEnable(
                                              !editProvider.state);
                                          setState(() {});
                                        },
                                        child: Text('Edit',
                                            style: TextStyle(
                                                color: AppColors.white)),
                                      ),
                                    )
                                  : Padding(
                                      padding: const EdgeInsets.only(left: 20),
                                      child: InkWell(
                                        onTap: () {
                                          editProvider.getEditJobCreationEnable(
                                              !editProvider.state);
                                          setState(() {});
                                        },
                                        child: const Icon(
                                          Icons.edit,
                                          size: 28,
                                          color: AppColors.greySubtitleText,
                                        ),
                                      ),
                                    )
                              : const SizedBox.shrink(),
                        ),
                      ],
                    ),
              const SizedBox(
                width: 20,
              ),
            ],
          ),
          body: SafeArea(
              child: Padding(
            padding: UIHelper().getScreenType(context) == ScreenType.desktop
                ? EdgeInsets.symmetric(horizontal: 10)
                : UIHelper.columnFieldOnlhorizontalPadding6(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: UIHelper.horizontalPaddingOf12(),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      jobHeader.status == null
                          ? Container()
                          : (jobHeader.description != null &&
                                  jobHeader.description != '')
                              ? (editProvider.state
                                  ? Container()
                                  : Text(
                                      jobHeader.description.toString(),
                                      style: UIHelper.titleStyle14(),
                                    ))
                              : Container(),
                      Visibility(
                        visible: error,
                        child: Text(
                          AppLocalizations.of(context)!.error,
                          style: UIHelper.titleStyle14(),
                        ),
                      ),
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Row(
                            children: [
                              jobHeader.status == null
                                  ? const SizedBox()
                                  : (jobHeader.priority != null &&
                                          jobHeader.priority != '')
                                      ? (editProvider.state
                                          ? Container()
                                          : ElevatedButton(
                                              style: ElevatedButton.styleFrom(
                                                  backgroundColor: UIHelper
                                                      .getPriorityColorByCode(
                                                          jobHeader.priority
                                                              .toString()),
                                                  shape: RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              20))),
                                              onPressed: () {},
                                              child: Text(
                                                ref
                                                    .watch(priorityListProvider
                                                        .notifier)
                                                    .fetchPriorityCode(jobHeader
                                                        .priority
                                                        .toString())
                                                    .toUpperCase(),
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color:
                                                      AppColors.titleTextColor,
                                                  letterSpacing: 0.1,
                                                ),
                                              ),
                                            ))
                                      : Container(),
                              jobHeader.status == null
                                  ? const SizedBox()
                                  : (editProvider.state
                                      ? Container()
                                      : const SizedBox(width: 10)),
                              (jobHeader.job_type != null &&
                                      jobHeader.job_type != '')
                                  ? (editProvider.state
                                      ? Container()
                                      : ElevatedButton(
                                          style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  AppColors.yellowColor,
                                              shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          20))),
                                          onPressed: () {},
                                          child: Text(
                                            ref
                                                .watch(jobTypeListProvider
                                                    .notifier)
                                                .fetchjobTypeFromCode(jobHeader
                                                    .job_type
                                                    .toString()),
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: AppColors.titleTextColor,
                                              letterSpacing: 0.1,
                                            ),
                                          ),
                                        ))
                                  : Container(),
                              (editProvider.state
                                  ? Container()
                                  : const SizedBox(width: 10)),
                              (jobHeader.fault_id != null &&
                                      jobHeader.fault_id != '')
                                  ? ElevatedButton(
                                      style: ElevatedButton.styleFrom(
                                          backgroundColor:
                                              AppColors.faultIconOrange,
                                          shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(20))),
                                      onPressed: () async {
                                        bool data =
                                            await checkIfModifiedJobDataThere();
                                        if (!data) {
                                          if (mounted) {
                                            UIHelper
                                                .showConfirmationDialogWithYesOrNo(
                                                    context,
                                                    description: AppLocalizations
                                                            .of(context)!
                                                        .moving_to_fault_screen,
                                                    no: () {
                                              Navigator.of(context,
                                                      rootNavigator: true)
                                                  .pop();
                                            }, yes: () async {
                                              if (jobHeader.p_mode ==
                                                  AppConstants.add) {
                                                {
                                                  String deleteQuery =
                                                      'DELETE FROM ${JOB_HEADER.TABLE_NAME} WHERE ${JOB_HEADER.FIELD_JOB_ID} = ${jobHeader.job_id}';
                                                  await AppDatabaseManager()
                                                      .execute(deleteQuery);
                                                  Navigator.of(context,
                                                          rootNavigator: true)
                                                      .pop();
                                                  onFault(jobHeader);
                                                }
                                              } else {
                                                onUnSaveJob(jobHeader);
                                              }
                                            });
                                          }
                                        } else {
                                          onFault(jobHeader);
                                        }
                                      },
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Icon(
                                            Icons.error_outline,
                                            color: AppColors.titleTextColor,
                                            size: 17,
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: 8.0),
                                            child: Text(
                                              jobHeader.fault_id.toString(),
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: AppColors.titleTextColor,
                                                letterSpacing: 0.1,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  : Container(),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                jobHeader.infoMsgCat == AppConstants.InfoMessageFailure
                    ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: FutureBuilder<List<InfoMessageData?>>(
                            future: fetchInfoMessage,
                            builder: (context, snapshot) {
                              if (snapshot.hasData) {
                                return MobileErrorAndLongTextview(
                                    type: TextDisplayType.error,
                                    text: snapshot.data!
                                        .map((e) => e!.message)
                                        .join("\n"));
                              } else {
                                return const SizedBox();
                              }
                            }),
                      )
                    : const SizedBox(),
                jobHeader.infoMsgCat == AppConstants.InfoMessageFailure
                    ? 10.0.spaceY
                    : const SizedBox(),
                jobHeader.infoMsgCat == AppConstants.InfoMessageWarning
                    ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: FutureBuilder<List<InfoMessageData?>>(
                            future: fetchInfoMessage,
                            builder: (context, snapshot) {
                              if (snapshot.hasData) {
                                return MobileErrorAndLongTextview(
                                    type: TextDisplayType.warning,
                                    // titleStyle: AppStyles.headLine15_600
                                    //     .copyWith(color: Colors.orangeAccent),
                                    text: snapshot.data!
                                        .map((e) => e!.message)
                                        .join("\n"));
                              } else {
                                return const SizedBox();
                              }
                            }),
                      )
                    : const SizedBox(),
                jobHeader.infoMsgCat == AppConstants.InfoMessageWarning
                    ? 10.0.spaceY
                    : const SizedBox(),
                (jobHeader.details != null && jobHeader.details != '')
                    ? editProvider.state!
                        ? Container()
                        : Padding(
                            padding: UIHelper.allPaddingOf8(),
                            child: MobileErrorAndLongTextview(
                              backgroundColor: AppColors.white,
                              bodyStyle: TextStyle(
                                  fontSize: 14, color: AppColors.black),
                              text: longTextController.text,
                            ),
                          )
                    : Container(),
                TabBar(
                  tabs: <Widget>[
                    Tab(
                      icon: FittedBox(
                        child: Text(AppLocalizations.of(context)!.general,
                            style: const TextStyle(
                                color: AppColors.blackTitleText)),
                      ),
                    ),
                    /*      Tab(
                      icon: FittedBox(
                        child: Text(
                          AppLocalizations.of(context)!.activity,
                          style:
                              const TextStyle(color: AppColors.blackTitleText),
                        ),
                      ),
                    ),*/
                    Tab(
                      icon: FittedBox(
                        child: Text(
                          AppLocalizations.of(context)!.attachments,
                          style:
                              const TextStyle(color: AppColors.blackTitleText),
                        ),
                      ),
                    ),
                  ],
                ),
                Expanded(
                  child: TabBarView(
                    children: <Widget>[
                      JobCreationGeneralTabFault(),
                      // JobCreationActivityTabFault(),
                      JobCreationAttachmentTabFault(),
                    ],
                  ),
                ),
              ],
            ),
          )),
        ),
      ),
    );
  }

  clearJobStates() {
    final description = ref.watch(jobDescriptionProvider.notifier);
    final longText = ref.watch(jobLongTextProvider.notifier);
    final priority = ref.watch(jobPriorityProvider.notifier);
    final jobType = ref.watch(jobTypeProvider.notifier);
    final assignedTo = ref.watch(jobAssignedToProvider.notifier);
    final startDate = ref.watch(jobStartOnProvider.notifier);
    final endDate = ref.watch(jobEndOnProvider.notifier);
    description.clearJobDescription();
    longText.clearLongText();
    priority.clearPriority();
    jobType.clearJobType();
    assignedTo.clearAssignedTo();
    startDate.clearStartDate();
    endDate.clearEndDate();
  }

  bool validate() {
    final attachment = ref.watch(getJobDocumentProvider.notifier).state;
    final selectedDescription =
        ref.watch(jobDescriptionProvider.notifier).state;
    final selectedLongText = ref.watch(jobLongTextProvider.notifier).state;
    final selectedJobType = ref.watch(jobTypeProvider.notifier).state;
    final selectedPriority = ref.watch(priorityProvider.notifier).state;
    final endDate = ref.watch(jobEndOnProvider.notifier).state;

    bool data = false;
    if (selectedDescription.isEmpty) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_enter_description);
      return data;
    }
    if (selectedLongText.isEmpty) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_enter_long_text);
      return data;
    } else if (selectedJobType.description == null) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_jobType);
      return data;
    } else if (selectedPriority.priority_code == null) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_priority);
      return data;
    } else if (endDate == 0) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_endDate);
      return data;
    }
    // else if (attachment.isEmpty) {
    //   UIHelper.showErrorDialog(context,
    //       description: AppLocalizations.of(context)!
    //           .please_upload_atleast_one_attachment);
    //   return data;
    // }
    return true;
  }

  void onSave() async {
    final jobHeader = ref.watch(jobHeaderProvider.notifier).state;
    if (kIsWeb) {
      if (!(await URLService.isInternetConnected())) {
        if (mounted) {
          UIHelper.showErrorDialog(
            context,
            description:
                AppLocalizations.of(context)!.noInternetConnectivityString,
          );
        }
      }
    } else {
      if (jobHeader.p_mode == AppConstants.add) {
        UIHelper().progressDialog(
            context: context,
            message: AppLocalizations.of(context)!.saving_job);
      } else {
        UIHelper().progressDialog(
            context: context,
            message: AppLocalizations.of(context)!.updating_job);
      }
      final headerData = ref.watch(getJobDocumentHeaderProvider.notifier).state;

      List<DOCUMENT_HEADER> header = headerData
          .where((element) => element.objectStatus == ObjectStatus.add)
          .toList();

      if (kIsWeb) {
        for (DOCUMENT_HEADER document in header) {
          var doc = await DbHelper()
              .getAttachmentFromIndexDbByUid(document.doc_id ?? "");
          await SyncEngine().uploadAttachmentSync(
              doc ?? "", document.file_name ?? "", document.doc_id ?? "");
        }
        await PAHelper.addDocumentInSyncMode(context, header);
      } else {
        await PAHelper.addDocumentInAsyncMode(context, header);
      }

      await newCreationOfJob();
    }
  }

  Future<void> newCreationOfJob() async {
    final faultHeaderData = ref.watch(faultHeaderProvider.notifier);
    FAULT_HEADER faultHeader = faultHeaderData.state;
    final jobHeader = ref.watch(jobHeaderProvider.notifier).state;
    final jobDescription = ref.watch(jobDescriptionProvider.notifier).state;
    final jobLongText = ref.watch(jobLongTextProvider.notifier).state;
    final priority = ref.watch(jobPriorityProvider.notifier).state;
    final jobType = ref.watch(jobTypeProvider.notifier).state;
    final jobStartOn = ref.watch(jobStartOnProvider.notifier).state;
    final jobEndOn = ref.watch(jobEndOnProvider.notifier).state;
    final jobAssignedTo = ref.watch(jobAssignedToProvider.notifier).state;
    jobHeader.description = jobDescription;
    jobHeader.details = jobLongText;
    jobHeader.priority = priority.priority_code;
    jobHeader.job_type = jobType.job_type;
    jobHeader.start_date = jobStartOn;
    if (jobEndOn != 0) {
      jobHeader.end_date = jobEndOn;
    }
    if (jobAssignedTo.isNotEmpty) {
      jobHeader.assigned_to = jobAssignedTo;
    }
    // await AppDatabaseManager()
    //     .update(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));
    
    JOB_HEADER? existingJob =
        await DbHelper.getJobHeaderById(jobHeader.job_id.toString());
    print('existing fault $existingJob');
    if (existingJob == null) {
      print('fault not existing');
      await AppDatabaseManager()
          .insert(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));
    } else {
      print('fault exists');
      await AppDatabaseManager()
          .update(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));
    }

    JOB_ACTION? jobAction =
        await DbHelper.getJobActionByJobId(jobHeader.job_id.toString());
    if (jobAction != null) {
      await AppDatabaseManager()
          .update(DBInputEntity(JOB_ACTION.TABLE_NAME, jobAction.toJson()));
    } else {
      JOB_ACTION newAction = JOB_ACTION(
          job_id: jobHeader.job_id, user_action: Constants.JOB_STATE_OSNO);
      newAction.fid = jobHeader.lid;
      await AppDatabaseManager()
          .insert(DBInputEntity(JOB_ACTION.TABLE_NAME, newAction.toJson()));
      if (jobHeader.assigned_to != null && jobHeader.assigned_to != '') {
        JOB_ACTION? jobAction =
            await DbHelper.getJobActionByJobId(jobHeader.job_id.toString());
        if (jobAction != null) {
          jobAction.user_action = Constants.JOB_STATE_ORAS;
          await AppDatabaseManager()
              .update(DBInputEntity(JOB_ACTION.TABLE_NAME, jobAction.toJson()));
        }
      }
    }
    Result? result;
    if (!kIsWeb) {
      await PAHelper.addOrModifyJobInAsyncMode(context, jobHeader);
    } else {
      result = await PAHelper.addOrModifyJobInSyncMode(context, jobHeader);
      Navigator.pop(context);
      if (result.body != null) {
        if (jobHeader.p_mode != AppConstants.add &&
            (result.body['JOB'] as List)[0]['JOB_HEADER']['STATUS'] ==
                Constants.JOB_STATE_OSNO) {
          UIHelper.showResultInfoDialog(context,
              description:
                  'Job ${(result.body['JOB'] as List)[0]['JOB_HEADER']['JOB_ID']} Updated Successfully.',
              onPressed: () {
            Navigator.pop(context);
            if (widget.fromFault) {
              Navigator.pop(context, result!.body["JOB"][0]["JOB_HEADER"]);
              jobDetailViewNotifier.value = null;
            }
          });
        } else {
          UIHelper.showResultInfoDialog(context,
              description:
                  'Job ${(result.body['JOB'] as List)[0]['JOB_HEADER']['JOB_ID']} ${UIHelper().toCamelCase((result.body['JOB'] as List)[0]['JOB_HEADER']['STATUS'])} Successfully.',
              onPressed: () {
            Navigator.pop(context);
            if (widget.fromFault) {
              Navigator.pop(context, result!.body["JOB"][0]["JOB_HEADER"]);
              jobDetailViewNotifier.value = null;
            }
          });
        }
      }
      final editProvider = ref.watch(editJobCreationProvider.notifier);
      editProvider.getEditJobCreationEnable(!editProvider.state);
      setState(() {});
    }
    if (!kIsWeb) {
      faultHeader.job_id = jobHeader.job_id;
      await AppDatabaseManager()
          .update(DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));
      if (UIHelper().getScreenType(context) != ScreenType.desktop) {
        Navigator.pop(context);
        Navigator.pop(context, jobHeader.job_id);
      } else {
        final plant = ref.read(plantProvider.notifier).state;
        Navigator.pop(context);
        JOB_HEADER? jobsData = await DbHelper.getJobHeaderByFautlId(
            faultHeader.fault_id.toString());
        if (jobsData != null) {
          faultHeader.status = Constants.FAULT_STATE_ORAS;
          await AppDatabaseManager().update(
              DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));
        }
        List jobist = await AppDatabaseManager()
            .select(DBInputEntity(JOB_HEADER.TABLE_NAME, {}));
        if (jobist.isNotEmpty) {}
        await ref
            .read(jobHeaderListProvider.notifier)
            .fetchJobHeaderList(plant);
        Navigator.pop(context, jobHeader.job_id);
      }
    }
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    JOB_HEADER? jobsData =
        await DbHelper.getJobHeaderByFautlId(faultHeader.fault_id.toString());
    if (jobsData != null) {
      faultHeader.status = Constants.FAULT_STATE_ORAS;
      await AppDatabaseManager()
          .update(DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));
    }
    List jobist = await AppDatabaseManager()
        .select(DBInputEntity(JOB_HEADER.TABLE_NAME, {}));
    if (jobist.isNotEmpty) {}
    await ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
    final jobHeaderDatata = ref.read(jobHeaderListProvider);
    final filteredJobTypeNotifier =
        ref.read(filteredJobCreationHeaderListProvider.notifier);
    final search = ref.read(searchTextJobProvider.notifier).state;
    final filterOfJobType = ref.read(filterOfJobTypeProvider.notifier).state;
    final filterOfJobTypeCode =
        ref.read(filterOfJobTypeCodeProvider.notifier).state;
    final filterOfPriority = ref.read(filterOfPriorityProvider.notifier).state;
    final filterOfPriorityCode =
        ref.read(filterOfPriorityCodeProvider.notifier).state;
    final statusFilter = ref.read(statusFilterProvider.notifier).state;
    final statusTypeFilter = ref.read(statusTypeFilterProvider.notifier).state;
    final selectedDate = ref.read(selectedCalendarDateJobProvider);
    if ((search != '') ||
        filterOfJobType.isNotEmpty ||
        filterOfJobTypeCode.isNotEmpty ||
        filterOfPriorityCode.isNotEmpty ||
        filterOfPriority.isNotEmpty ||
        statusTypeFilter.isNotEmpty ||
        statusFilter.isNotEmpty ||
        selectedDate != null) {
      await filteredJobTypeNotifier.filteredJobHeaderList(
        typeList: filterOfJobTypeCode,
        priorityList: filterOfPriorityCode,
        statusList: statusTypeFilter,
        type: (search != '') ? AppConstants.search : AppConstants.jobType,
        plantId: plant,
        plantSec: plantSection,
        search: search,
      );
    } else {
      await filteredJobTypeNotifier.filteredJobHeaderList(
          type: 'Initial',
          jobList: jobHeaderDatata,
          plantId: plant,
          plantSec: plantSection);
    }

    final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
    await faultHeaderList.fetchFaultHeaderList(plant);
    final filteredFaultType =
        ref.read(filteredFaultHeaderListProvider.notifier);
    final filterOfFaultType =
        ref.read(filterOfFaultTypeProvider.notifier).state;
    final filterOfFaultCode =
        ref.read(filterOfFaultCodeProvider.notifier).state;
    final filterOfFaultPriorityCode =
        ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
    final filterOfFaultPriority =
        ref.read(filterOfFaultPriorityProvider.notifier).state;
    final statusTypeFaultFilter =
        ref.read(statusTypeFaultFilterProvider.notifier).state;
    final statusFaultFilter =
        ref.read(statusFaultFilterProvider.notifier).state;
    final selectedDateFault = ref.read(selectedCalendarDateProvider);
    final searchFault = ref.read(searchTextProvider.notifier).state;
    if ((searchFault != '') ||
        filterOfFaultType.isNotEmpty ||
        filterOfFaultCode.isNotEmpty ||
        filterOfFaultPriorityCode.isNotEmpty ||
        filterOfFaultPriority.isNotEmpty ||
        statusTypeFaultFilter.isNotEmpty ||
        statusFaultFilter.isNotEmpty ||
        selectedDateFault != null) {
      await filteredFaultType.filteredFaultHeaderList(
          faulttypeList: filterOfFaultCode,
          priorityList: filterOfPriorityCode,
          statusList: statusTypeFilter,
          type: (searchFault != '')
              ? AppConstants.search
              : AppConstants.faultType,
          plantId: plant,
          plantSec: plantSection,
          search: searchFault);
    } else {
      await filteredFaultType.filteredFaultHeaderList(
          type: 'Initial',
          faultList: faultHeaderList.state,
          plantId: plant,
          plantSec: plantSection);
    }

    if (ScreenType.desktop != UIHelper().getScreenType(context)) {
      if (widget.fromFault) {
        Navigator.pop(context);
      }
      ref.read(bottomNavIndexProvider.notifier).state = 3;
    }
  }

  Future<bool> checkIfModifiedJobDataThere() async {
    final jobHeaderr = ref.read(jobHeaderProvider.notifier).state;
    final originalJobHeader = ref.read(jobHeaderProvider);
    final jobDescription = ref.watch(jobDescriptionProvider.notifier).state;
    final priority = ref.watch(jobPriorityProvider.notifier).state;
    final jobType = ref.watch(jobTypeProvider.notifier).state;
    final startDate = ref.watch(jobStartOnProvider.notifier).state;
    final endDate = ref.watch(jobEndOnProvider.notifier).state;
    final longText = ref.read(jobLongTextProvider.notifier).state;
    JOB_HEADER jobHeader = JOB_HEADER.fromJson(jobHeaderr.toJson());
    if (startDate != 0) {
      jobHeader.start_date = startDate;
    }
    if (endDate != 0) {
      jobHeader.end_date = endDate;
    }
    if (jobDescription.isNotEmpty && jobDescription != '') {
      jobHeader.description = jobDescription;
    }
    if (longText.isNotEmpty && longText != '') {
      jobHeader.details = longText;
    }
    if (priority.priority_code != null && priority.priority_code != '') {
      jobHeader.priority = priority.priority_code;
    }
    if (jobType.job_type != null && jobType.job_type != '') {
      jobHeader.job_type = jobType.job_type;
    }
    bool edited = true;
    edited = areJsonEqual(originalJobHeader.toJson(), jobHeader.toJson());
    return edited;
  }

  bool areJsonEqual(Map<String, dynamic> json1, Map<String, dynamic> json2) {
    return const DeepCollectionEquality().equals(json1, json2);
  }

  void onCheckback(String? status, {bool onPressback = false}) async {
    final jobHeader = ref.watch(jobHeaderProvider.notifier).state;
    if (!validate()) {
      return;
    }
    if (onPressback) {
      if (kIsWeb) {
        if (!(await URLService.isInternetConnected())) {
          if (mounted) {
            UIHelper.showErrorDialog(context,
                description:
                    AppLocalizations.of(context)!.noInternetConnectivityString);
          }
        } else {
          onSave();
        }
      } else {
        onSave();
      }
    } else {
      if (jobHeader.p_mode == AppConstants.add) {
        UIHelper.showConfirmationDialog(
          context,
          description: AppLocalizations.of(context)!.save_job_confirmation,
          positiveButtonString: AppLocalizations.of(context)!.ok,
          positiveButtonOnTap: () async {
            Navigator.pop(context);
            if (kIsWeb) {
              if (!(await URLService.isInternetConnected())) {
                if (mounted) {
                  UIHelper.showErrorDialog(context,
                      description: AppLocalizations.of(context)!
                          .noInternetConnectivityString);
                }
              } else {
                onSave();
              }
            } else {
              onSave();
            }
          },
        );
      } else {
        UIHelper.showConfirmationDialog(
          context,
          description: AppLocalizations.of(context)!.update_job_confirmation,
          positiveButtonString: AppLocalizations.of(context)!.ok,
          positiveButtonOnTap: () async {
            Navigator.pop(context);
            if (kIsWeb) {
              if (!(await URLService.isInternetConnected())) {
                if (mounted) {
                  UIHelper.showErrorDialog(context,
                      description: AppLocalizations.of(context)!
                          .noInternetConnectivityString);
                }
              } else {
                onSave();
              }
            } else {
              onSave();
            }
          },
        );
      }
    }
  }

  void onCheck(String? status) {
    final jobHeader = ref.watch(jobHeaderProvider.notifier).state;
    if (!validate()) {
      return;
    }
    if (jobHeader.p_mode == AppConstants.add) {
      UIHelper.showConfirmationDialog(
        context,
        description: AppLocalizations.of(context)!.save_job_confirmation,
        positiveButtonString: AppLocalizations.of(context)!.ok,
        positiveButtonOnTap: () {
          Navigator.pop(context);
          onSave();
        },
      );
    } else {
      UIHelper.showConfirmationDialog(
        context,
        description: AppLocalizations.of(context)!.update_job_confirmation,
        positiveButtonString: AppLocalizations.of(context)!.ok,
        positiveButtonOnTap: () {
          Navigator.pop(context);
          onSave();
        },
      );
    }
  }

  getStatusActionButtons(JOB_HEADER jobHeader) {
    final user = ref.read(userProvider.notifier).state;
    if (jobHeader.status == null || jobHeader.p_mode == 'A') {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent)
          ? Container()
          : Row(children: [
              InkWell(
                onTap: () {
                  onDeleteJob(jobHeader);
                },
                child: const Icon(
                  Icons.delete,
                  size: 30,
                  color: AppColors.redAccentColor,
                ),
              )
            ]);
    } else if (jobHeader.status == Constants.JOB_STATE_OSNO) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? Container()
          : isAssignVisible()
              ? Row(children: [
                  InkWell(
                    onTap: (jobHeader.syncStatus == SyncStatus.error ||
                            jobHeader.syncStatus == SyncStatus.queued)
                        ? () {}
                        : () async {
                            if (!(await URLService.isInternetConnected())) {
                              if (mounted) {
                                UIHelper.showErrorDialog(
                                  context,
                                  description: AppLocalizations.of(context)!
                                      .noInternetConnectivityString,
                                );
                              }
                            } else {
                              if (mounted) {
                                handleStatusActionButtonsFunctions(
                                    'assign', jobHeader, context, ref);
                              }
                            }
                          },
                    child: const Icon(
                      Icons.assignment_turned_in,
                      size: 28,
                      color: AppColors.blue,
                    ),
                  ),
                ])
              : const SizedBox();
    } else if (jobHeader.status == Constants.JOB_STATE_ORAS) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? Container()
          : jobHeader.assigned_to == user!.user_id.toString()
              ? Row(children: [
                  InkWell(
                    onTap: (jobHeader.syncStatus == SyncStatus.error ||
                            jobHeader.syncStatus == SyncStatus.queued)
                        ? () {}
                        : () {
                            handleStatusActionButtonsFunctions(
                                'accept', jobHeader, context, ref);
                          },
                    child: const Icon(
                      Icons.check_circle_outline,
                      size: 30,
                      color: AppColors.greenSentColor,
                    ),
                  ),
                  const SizedBox(
                    width: 20,
                  ),
                  InkWell(
                    onTap: (jobHeader.syncStatus == SyncStatus.error ||
                            jobHeader.syncStatus == SyncStatus.queued)
                        ? () {}
                        : () {
                            handleStatusActionButtonsFunctions(
                                'reject', jobHeader, context, ref);
                          },
                    child: const Icon(
                      Icons.cancel,
                      size: 30,
                      color: AppColors.redColor,
                    ),
                  ),
                ])
              : const SizedBox();
    } else if (jobHeader.status == Constants.JOB_STATE_ACPT) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? Container()
          : jobHeader.assigned_to == user!.user_id.toString()
              ? Row(children: [
                  InkWell(
                    onTap: (jobHeader.syncStatus == SyncStatus.error ||
                            jobHeader.syncStatus == SyncStatus.queued)
                        ? () {}
                        : () {
                            handleStatusActionButtonsFunctions(
                                'reject', jobHeader, context, ref);
                          },
                    child: const Icon(
                      Icons.cancel,
                      size: 30,
                      color: AppColors.redAccentColor,
                    ),
                  ),
                  const SizedBox(
                    width: 20,
                  ),
                  InkWell(
                    onTap: (jobHeader.syncStatus == SyncStatus.error ||
                            jobHeader.syncStatus == SyncStatus.queued)
                        ? () {}
                        : () {
                            handleStatusActionButtonsFunctions(
                                'complete', jobHeader, context, ref);
                          },
                    child: const Icon(
                      Icons.flag,
                      size: 30,
                      color: AppColors.greenColor,
                    ),
                  ),
                ])
              : const SizedBox();
    } else if (jobHeader.status == Constants.JOB_STATE_RJCT) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? Container()
          : isAssignVisible()
              ? Row(children: [
                  InkWell(
                    onTap: (jobHeader.syncStatus == SyncStatus.error ||
                            jobHeader.syncStatus == SyncStatus.queued)
                        ? () {}
                        : () async {
                            if (!(await URLService.isInternetConnected())) {
                              if (mounted) {
                                UIHelper.showErrorDialog(
                                  context,
                                  description: AppLocalizations.of(context)!
                                      .noInternetConnectivityString,
                                );
                              }
                            } else {
                              if (mounted) {
                                handleStatusActionButtonsFunctions(
                                    'assign', jobHeader, context, ref);
                              }
                            }
                          },
                    child: const Icon(
                      Icons.assignment_turned_in,
                      size: 28,
                      color: AppColors.blue,
                    ),
                  )
                ])
              : const SizedBox();
    } else if (jobHeader.status == Constants.JOB_STATE_NOCO) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent)
          ? Container()
          : Container();
    } else {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent)
          ? Container()
          : Container();
    }
  }

  getStatusActionButtonsWeb(JOB_HEADER jobHeader) {
    final user = ref.read(userProvider.notifier).state;
    if (jobHeader.status == null || jobHeader.p_mode == 'A') {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent)
          ? Container()
          : Row(children: [
              (kIsWeb ||
                      UIHelper().getScreenType(context) == ScreenType.desktop)
                  ? ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.redAccentColor),
                      onPressed: () {
                        onDeleteJob(jobHeader);
                      },
                      child: Text('Delete',
                          style: TextStyle(color: AppColors.white)),
                    )
                  : InkWell(
                      onTap: () {
                        onDeleteJob(jobHeader);
                      },
                      child: const Icon(
                        Icons.delete,
                        size: 30,
                        color: AppColors.redAccentColor,
                      ),
                    )
            ]);
    } else if (jobHeader.status == Constants.JOB_STATE_OSNO) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? Container()
          : isAssignVisible()
              ? Row(children: [
                  (kIsWeb ||
                          UIHelper().getScreenType(context) ==
                              ScreenType.desktop)
                      ? ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.blue),
                          onPressed: () {
                            handleStatusActionButtonsFunctions(
                                'assign', jobHeader, context, ref);
                          },
                          child: Text('Assign',
                              style: TextStyle(color: AppColors.white)),
                        )
                      : InkWell(
                          onTap: (jobHeader.syncStatus == SyncStatus.error ||
                                  jobHeader.syncStatus == SyncStatus.queued)
                              ? () {}
                              : () async {
                                  if (!(await URLService
                                      .isInternetConnected())) {
                                    if (mounted) {
                                      UIHelper.showErrorDialog(
                                        context,
                                        description:
                                            AppLocalizations.of(context)!
                                                .noInternetConnectivityString,
                                      );
                                    }
                                  } else {
                                    if (mounted) {
                                      handleStatusActionButtonsFunctions(
                                          'assign', jobHeader, context, ref);
                                    }
                                  }
                                },
                          child: const Icon(
                            Icons.assignment_turned_in,
                            size: 28,
                            color: AppColors.blue,
                          ),
                        ),
                ])
              : SizedBox();
    } else if (jobHeader.status == Constants.JOB_STATE_ORAS) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? Container()
          : jobHeader.assigned_to == user!.user_id.toString()
              ? Row(children: [
                  (kIsWeb ||
                          UIHelper().getScreenType(context) ==
                              ScreenType.desktop)
                      ? ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.greenSentColor),
                          onPressed:
                              (jobHeader.syncStatus == SyncStatus.error ||
                                      jobHeader.syncStatus == SyncStatus.queued)
                                  ? () {}
                                  : () {
                                      handleStatusActionButtonsFunctions(
                                          'accept', jobHeader, context, ref);
                                    },
                          child: Text('Accept',
                              style: TextStyle(color: AppColors.white)),
                        )
                      : InkWell(
                          onTap: (jobHeader.syncStatus == SyncStatus.error ||
                                  jobHeader.syncStatus == SyncStatus.queued)
                              ? () {}
                              : () {
                                  handleStatusActionButtonsFunctions(
                                      'accept', jobHeader, context, ref);
                                },
                          child: Icon(
                            Icons.check_circle_outline,
                            size: 30,
                            color: AppColors.greenSentColor,
                          ),
                        ),
                  const SizedBox(
                    width: 20,
                  ),
                  (kIsWeb ||
                          UIHelper().getScreenType(context) ==
                              ScreenType.desktop)
                      ? ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.redAccentColor),
                          onPressed:
                              (jobHeader.syncStatus == SyncStatus.error ||
                                      jobHeader.syncStatus == SyncStatus.queued)
                                  ? () {}
                                  : () {
                                      handleStatusActionButtonsFunctions(
                                          'reject', jobHeader, context, ref);
                                    },
                          child: Text('Reject',
                              style: TextStyle(color: AppColors.white)),
                        )
                      : InkWell(
                          onTap: (jobHeader.syncStatus == SyncStatus.error ||
                                  jobHeader.syncStatus == SyncStatus.queued)
                              ? () {}
                              : () {
                                  handleStatusActionButtonsFunctions(
                                      'reject', jobHeader, context, ref);
                                },
                          child: Icon(
                            Icons.cancel,
                            size: 30,
                            color: AppColors.redAccentColor,
                          ),
                        ),
                ])
              : SizedBox();
    } else if (jobHeader.status == Constants.JOB_STATE_ACPT) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? Container()
          : jobHeader.assigned_to == user!.user_id.toString()
              ? Row(children: [
                  (kIsWeb ||
                          UIHelper().getScreenType(context) ==
                              ScreenType.desktop)
                      ? ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.redAccentColor),
                          onPressed:
                              (jobHeader.syncStatus == SyncStatus.error ||
                                      jobHeader.syncStatus == SyncStatus.queued)
                                  ? () {}
                                  : () {
                                      handleStatusActionButtonsFunctions(
                                          'reject', jobHeader, context, ref);
                                    },
                          child: Text('Reject',
                              style: TextStyle(color: AppColors.white)),
                        )
                      : InkWell(
                          onTap: (jobHeader.syncStatus == SyncStatus.error ||
                                  jobHeader.syncStatus == SyncStatus.queued)
                              ? () {}
                              : () {
                                  handleStatusActionButtonsFunctions(
                                      'reject', jobHeader, context, ref);
                                },
                          child: Icon(
                            Icons.cancel,
                            size: 30,
                            color: AppColors.redAccentColor,
                          ),
                        ),
                  const SizedBox(
                    width: 20,
                  ),
                  (kIsWeb ||
                          UIHelper().getScreenType(context) ==
                              ScreenType.desktop)
                      ? ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.greenColor),
                          onPressed:
                              (jobHeader.syncStatus == SyncStatus.error ||
                                      jobHeader.syncStatus == SyncStatus.queued)
                                  ? () {}
                                  : () {
                                      handleStatusActionButtonsFunctions(
                                          'complete', jobHeader, context, ref);
                                    },
                          child: Text('Complete',
                              style: TextStyle(color: AppColors.white)),
                        )
                      : InkWell(
                          onTap: (jobHeader.syncStatus == SyncStatus.error ||
                                  jobHeader.syncStatus == SyncStatus.queued)
                              ? () {}
                              : () {
                                  handleStatusActionButtonsFunctions(
                                      'complete', jobHeader, context, ref);
                                },
                          child: Icon(
                            Icons.flag,
                            size: 30,
                            color: AppColors.greenColor,
                          ),
                        ),
                ])
              : SizedBox();
    } else if (jobHeader.status == Constants.JOB_STATE_RJCT) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? Container()
          : isAssignVisible()
              ? Row(children: [
                  (kIsWeb ||
                          UIHelper().getScreenType(context) ==
                              ScreenType.desktop)
                      ? ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.blue),
                          onPressed: (jobHeader.syncStatus ==
                                      SyncStatus.error ||
                                  jobHeader.syncStatus == SyncStatus.queued)
                              ? () {}
                              : () async {
                                  if (!(await URLService
                                      .isInternetConnected())) {
                                    if (mounted) {
                                      UIHelper.showErrorDialog(
                                        context,
                                        description:
                                            AppLocalizations.of(context)!
                                                .noInternetConnectivityString,
                                      );
                                    }
                                  } else {
                                    if (mounted) {
                                      handleStatusActionButtonsFunctions(
                                          'assign', jobHeader, context, ref);
                                    }
                                  }
                                },
                          child: Text('Assign',
                              style: TextStyle(color: AppColors.white)),
                        )
                      : InkWell(
                          onTap: (jobHeader.syncStatus == SyncStatus.error ||
                                  jobHeader.syncStatus == SyncStatus.queued)
                              ? () {}
                              : () async {
                                  if (!(await URLService
                                      .isInternetConnected())) {
                                    if (mounted) {
                                      UIHelper.showErrorDialog(
                                        context,
                                        description:
                                            AppLocalizations.of(context)!
                                                .noInternetConnectivityString,
                                      );
                                    }
                                  } else {
                                    if (mounted) {
                                      handleStatusActionButtonsFunctions(
                                          'assign', jobHeader, context, ref);
                                    }
                                  }
                                },
                          child: const Icon(
                            Icons.assignment_turned_in,
                            size: 28,
                            color: AppColors.blue,
                          ),
                        )
                ])
              : SizedBox();
    } else if (jobHeader.status == Constants.JOB_STATE_NOCO) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent)
          ? Container()
          : Container();
    } else {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent)
          ? Container()
          : Container();
    }
  }

  void onDeleteJob(JOB_HEADER jobHeader) async {
    UIHelper.showConfirmationDialogWithYesOrNo(context,
        description: AppLocalizations.of(context)!.confirmation_to_delete_job,
        yes: () async {
      Navigator.pop(context);
      String deleteQuery =
          'DELETE FROM ${JOB_HEADER.TABLE_NAME} WHERE ${JOB_HEADER.FIELD_JOB_ID} = ${jobHeader.job_id}';
      await AppDatabaseManager().execute(deleteQuery);
      if (!kIsWeb) {
        Navigator.pop(context);
      } else {
        jobDetailViewNotifier.value = InteractiveItemModel(
          type: "",
          data: {"type": "", "index": null},
        );
      }
      final plant = ref.read(plantProvider.notifier).state;
      final plantSection = ref.read(plantSectionProvider.notifier).state;
      await ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
      final jobHeaderDatata = ref.read(jobHeaderListProvider);
      final filteredJobTypeNotifier =
          ref.read(filteredJobCreationHeaderListProvider.notifier);

      final search = ref.read(searchTextJobProvider.notifier).state;
      final filterOfJobType = ref.read(filterOfJobTypeProvider.notifier).state;
      final filterOfJobTypeCode =
          ref.read(filterOfJobTypeCodeProvider.notifier).state;
      final filterOfPriority =
          ref.read(filterOfPriorityProvider.notifier).state;
      final filterOfPriorityCode =
          ref.read(filterOfPriorityCodeProvider.notifier).state;
      final statusFilter = ref.read(statusFilterProvider.notifier).state;
      final statusTypeFilter =
          ref.read(statusTypeFilterProvider.notifier).state;
      final selectedDate = ref.read(selectedCalendarDateJobProvider);
      if ((search != '') ||
          filterOfJobType.isNotEmpty ||
          filterOfJobTypeCode.isNotEmpty ||
          filterOfPriorityCode.isNotEmpty ||
          filterOfPriority.isNotEmpty ||
          statusTypeFilter.isNotEmpty ||
          statusFilter.isNotEmpty ||
          selectedDate != null) {
        await filteredJobTypeNotifier.filteredJobHeaderList(
          typeList: filterOfJobTypeCode,
          priorityList: filterOfPriorityCode,
          statusList: statusTypeFilter,
          type: (search != '') ? AppConstants.search : AppConstants.jobType,
          plantId: plant,
          plantSec: plantSection,
          search: search,
        );
      } else {
        await filteredJobTypeNotifier.filteredJobHeaderList(
            type: 'Initial',
            jobList: jobHeaderDatata,
            plantId: plant,
            plantSec: plantSection);
      }

      /*     await filteredjobTypeNotifier.filteredJobHeaderList(
          type: 'Initial',
          jobList: jobHeaderDatata,
          plantId: plant,
          plantSec: plantSection);*/
    }, no: () {
      Navigator.pop(context);
    });
  }

  handleStatusActionButtonsFunctions(String type, JOB_HEADER jobHeader,
      BuildContext context, WidgetRef ref) async {
    if (type == 'assign') {
      if (!(await URLService.isInternetConnected())) {
        UIHelper.showErrorDialog(
          context,
          description:
              AppLocalizations.of(context)!.noInternetConnectivityString,
        );
      } else {
        UIHelper.showConfirmationDialogWithYesOrNo(context,
            description: AppLocalizations.of(context)!
                .do_you_want_to_assign_job, yes: () {
          Navigator.pop(context);
          onAssign(type, jobHeader, Constants.JOB_STATE_ORAS, context);
        }, no: () {
          Navigator.pop(context);
        });
      }
    }
    if (type == 'accept') {
      if (!(await URLService.isInternetConnected())) {
        UIHelper.showErrorDialog(
          context,
          description:
              AppLocalizations.of(context)!.noInternetConnectivityString,
        );
      } else {
        UIHelper.showConfirmationDialogWithYesOrNo(context,
            description: AppLocalizations.of(context)!
                .do_you_want_to_accept_job, yes: () {
          Navigator.pop(context);
          onYes(type, jobHeader, Constants.JOB_STATE_ACPT, context);
        }, no: () {
          Navigator.pop(context);
        });
      }
    }
    if (type == 'reject') {
      if (!(await URLService.isInternetConnected())) {
        UIHelper.showErrorDialog(
          context,
          description:
              AppLocalizations.of(context)!.noInternetConnectivityString,
        );
      } else {
        UIHelper.showConfirmationDialogWithYesOrNo(context,
            description: AppLocalizations.of(context)!
                .do_you_want_to_reject_job, yes: () {
          Navigator.pop(context);
          onYes(type, jobHeader, Constants.JOB_STATE_RJCT, context);
        }, no: () {
          Navigator.pop(context);
        });
      }
    } else if (type == 'complete') {
      if (kIsWeb) {
        if (!(await URLService.isInternetConnected())) {
          UIHelper.showErrorDialog(
            context,
            description:
                AppLocalizations.of(context)!.noInternetConnectivityString,
          );
        } else {
          UIHelper.showConfirmationDialogWithYesOrNo(context,
              description: AppLocalizations.of(context)!
                  .do_you_want_to_complete_job, yes: () {
            Navigator.pop(context);
            onYes(type, jobHeader, Constants.JOB_STATE_NOCO, context);
          }, no: () {
            Navigator.pop(context);
          });
        }
      } else {
        UIHelper.showConfirmationDialogWithYesOrNo(context,
            description: AppLocalizations.of(context)!
                .do_you_want_to_complete_job, yes: () {
          Navigator.pop(context);
          onYes(type, jobHeader, Constants.JOB_STATE_NOCO, context);
        }, no: () {
          Navigator.pop(context);
        });
      }
    }
  }

  onYes(String type, JOB_HEADER jobHeader, String statusType,
      BuildContext context) async {
    final jobHeaderData = ref.watch(jobHeaderProvider.notifier);
    final documents = ref.watch(getJobDocumentHeaderProvider.notifier).state;
    JOB_HEADER header = jobHeader;
    await AppDatabaseManager()
        .update(DBInputEntity(JOB_HEADER.TABLE_NAME, header.toJson()));
    await jobHeaderData.getJobHeader(
        jobId: jobHeaderData.state.job_id.toString());
    JOB_ACTION? action =
        await DbHelper.getJobActionByJobId(header.job_id.toString());

    if (action != null) {
      action.user_action = statusType;
      var status = await AppDatabaseManager()
          .update(DBInputEntity(JOB_ACTION.TABLE_NAME, action.toJson()));
    } else {
      JOB_ACTION? action;

      action = JOB_ACTION(job_id: header.job_id, user_action: statusType);
      action.fid = header.lid;
      bool status = await AppDatabaseManager()
          .insert(DBInputEntity(JOB_ACTION.TABLE_NAME, action.toJson()));
    }

    List<DOCUMENT_HEADER> documentHeaders = documents
        .where((element) => element.objectStatus == ObjectStatus.add)
        .toList();

    if (documentHeaders.isNotEmpty) {
      UIHelper()
          .progressDialog(context: context, message: "Uploading Attachments");
    }

    for (var data in documentHeaders) {
      JOB_DOCUMENT newDocument = JOB_DOCUMENT(
          job_id: jobHeader.job_id!.toInt(),
          doc_id: data.doc_id,
          p_mode: AppConstants.add);
      newDocument.fid = jobHeader.lid;

      JOB_DOCUMENT? doc = await DbHelper.getJobSingleDocumentsByJobId(
          jobHeader.job_id.toString(), data.doc_id.toString());

      if (doc != null) {
        // DOCUMENT_HEADER? header =
        //     await DbHelper.getDocumentHeadersByDocsId(doc.doc_id.toString());

        // if (header != null) {
        //   await AppDatabaseManager().insert(
        //       DBInputEntity(JOB_DOCUMENT.TABLE_NAME, newDocument.toJson()));
        // }

        if (kIsWeb) {
          var documentBase64 =
              await DbHelper().getAttachmentFromIndexDbByUid(data.doc_id ?? "");
          await SyncEngine().uploadAttachmentSync(
              documentBase64 ?? "", data.file_name ?? "", data.doc_id ?? "");
        }
      }
    }

    if (documentHeaders.isNotEmpty) {
      if (kIsWeb) {
        await PAHelper.addDocumentInSyncMode(context, documentHeaders)
            .then((value) {
          Navigator.of(context, rootNavigator: true).pop();
        });
      } else {
        PAHelper.addDocumentInAsyncMode(context, documentHeaders).then((value) {
          Navigator.of(context, rootNavigator: true).pop();
        });
      }
    }

    if (mounted) {
/*      Navigator.pop(context);*/
      await sendToServer(type, jobHeader, context);
    }
  }

  sendToServer(String type, JOB_HEADER jobHeader, BuildContext context) async {
    if (type == AppConstants.assign) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.assigning_job);
    } else if (type == AppConstants.accept) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.accepting_job);
    } else if (type == AppConstants.reject) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.rejecting_job);
    } else if (type == AppConstants.complete) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.completing_job);
    }
    if (type == AppConstants.complete) {
      Result? result;
      if (!kIsWeb) {
        result = await PAHelper.addOrModifyJobInAsyncMode(context, jobHeader);
      } else {
        result = await PAHelper.addOrModifyJobInSyncMode(context, jobHeader);
      }

      if (result != null) {
        if (result.body['InfoMessage'] != null) {
          Navigator.pop(context);
          if (result.body['InfoMessage'][0]['category'] == 'FAILURE') {
            if (result.body['InfoMessage'][0]['message'] != null) {
              if (context.mounted) {
                UIHelper.showErrorDialog(
                  context,
                  description: result.body['InfoMessage'][0]['message'],
                );
              }
            }
          } else {
            if (result.body['InfoMessage'][0]['message'] != null) {
              if (context.mounted) {
                UIHelper.showErrorDialog(context,
                    description: result.body['InfoMessage'][0]['message']);
              }
            }
          }
        } else {
          await Future.delayed(const Duration(seconds: 3));
          Navigator.pop(context);
          if(result.body.isNotEmpty){
            UIHelper.showResultInfoDialog(context,
              description:
                  'Job ${(result.body['JOB'] as List)[0]['JOB_HEADER']['JOB_ID']} ${UIHelper().toCamelCase((result.body['JOB'] as List)[0]['JOB_HEADER']['STATUS'])} Successfully.',
              onPressed: () {
            Navigator.pop(context);
          });
          } else{
            Navigator.pop(context);
          }
          
          final jobHeaderList = ref.watch(jobHeaderListProvider.notifier);
          final jobHeaderProviderr = ref.watch(jobHeaderProvider.notifier);

          final plant = ref.watch(plantProvider.notifier).state;

          final jobAction = ref.watch(getJobActionProvider.notifier);
          final jobDocument = ref.watch(getJobDocumentProvider.notifier);
          final documentAttachmentProviderData =
              ref.watch(documentAttachmentProvider.notifier);
          final jobStartOn = ref.watch(jobStartOnProvider.notifier);
          final jobEndOn = ref.watch(jobEndOnProvider.notifier);

          final jobDescription = ref.watch(jobDescriptionProvider.notifier);
          final jobLongText = ref.watch(jobLongTextProvider.notifier);
          final jobPriority = ref.watch(jobPriorityProvider.notifier);
          final jobType = ref.watch(jobTypeProvider.notifier);
          JOB_HEADER? jobHeaderData2 =
              await DbHelper.getJobHeaderById(jobHeader.job_id.toString());
          if (jobHeaderData2 != null) {
            await jobHeaderProviderr.getJobHeader(
                jobId: jobHeader.job_id.toString());
          } else {
            await jobHeaderProviderr.getJobHeader(data: jobHeader);
          }
          if (jobHeader.status != null) {
            if (jobHeader.start_date != null) {
              jobStartOn.getJobStartOn(jobHeader.start_date!);
            }
            if (jobHeader.end_date != null) {
              jobEndOn.getJobEndOn(jobHeader.end_date!);
            }
          }
          if (jobHeader.description != null) {
            jobDescription.getJobDescription(jobHeader.description.toString());
          }

          if (jobHeader.priority != null) {
            jobPriority.getJobPriority(ref
                .watch(priorityListProvider.notifier)
                .fetchPriorityCode(jobHeader.priority.toString()));
          }
          if (jobHeader.job_type != null) {
            jobType.getJobType(ref
                .watch(jobTypeListProvider.notifier)
                .fetchjobTypeFromCode(jobHeader.job_type.toString()));
          }
          if (jobHeader.details != null) {
            jobLongText.getJobLongText(jobHeader.details.toString());
          }

          await jobAction.getJobAction(jobHeader.job_id.toString());
          await jobDocument.getJobDocuments(jobHeader.job_id.toString());
          await documentAttachmentProviderData.fetchDocumentAttachments();
          await jobHeaderList.fetchJobHeaderList(plant);

          setState(() {});
          jobDetailViewNotifier.value = InteractiveItemModel(
            type: "JOB_HEADER",
            data: {
              "type": "",
              "index": null,
              "fromFault": false,
            },
          );
          /*     Navigator.pop(context);
          // //TODO
          Navigator.pop(context);*/
        }
      }

      if (!kIsWeb) {
        jobHeader.status = Constants.JOB_STATE_NOCO;
        await AppDatabaseManager()
            .update(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));
      }

      final plant = ref.read(plantProvider.notifier).state;
      final plantSection = ref.read(plantSectionProvider.notifier).state;
      await ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
      final jobHeaderDatata = ref.read(jobHeaderListProvider);
      final filteredJobTypeNotifier =
          ref.read(filteredJobCreationHeaderListProvider.notifier);

      final searchJob = ref.read(searchTextJobProvider.notifier).state;
      final filterOfJobType = ref.read(filterOfJobTypeProvider.notifier).state;
      final filterOfJobTypeCode =
          ref.read(filterOfJobTypeCodeProvider.notifier).state;
      final filterOfJobPriority =
          ref.read(filterOfPriorityProvider.notifier).state;
      final filterOfJobPriorityCode =
          ref.read(filterOfPriorityCodeProvider.notifier).state;
      final statusJobFilter = ref.read(statusFilterProvider.notifier).state;
      final statusTypeJobFilter =
          ref.read(statusTypeFilterProvider.notifier).state;
      final selectedDateJob = ref.read(selectedCalendarDateJobProvider);
      if ((searchJob != '') ||
          filterOfJobType.isNotEmpty ||
          filterOfJobTypeCode.isNotEmpty ||
          filterOfJobPriorityCode.isNotEmpty ||
          filterOfJobPriority.isNotEmpty ||
          statusTypeJobFilter.isNotEmpty ||
          statusJobFilter.isNotEmpty ||
          selectedDateJob != null) {
        await filteredJobTypeNotifier.filteredJobHeaderList(
          typeList: filterOfJobTypeCode,
          priorityList: filterOfJobPriorityCode,
          statusList: statusTypeJobFilter,
          type: (searchJob != '') ? AppConstants.search : AppConstants.jobType,
          plantId: plant,
          plantSec: plantSection,
          search: searchJob,
        );
      } else {
        await filteredJobTypeNotifier.filteredJobHeaderList(
            type: 'Initial',
            jobList: jobHeaderDatata,
            plantId: plant,
            plantSec: plantSection);
      }

      final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
      await faultHeaderList.fetchFaultHeaderList(plant);
      final filteredFaultType =
          ref.read(filteredFaultHeaderListProvider.notifier);

      final filterOfFaultType =
          ref.read(filterOfFaultTypeProvider.notifier).state;
      final filterOfFaultCode =
          ref.read(filterOfFaultCodeProvider.notifier).state;
      final filterOfPriorityCode =
          ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
      final filterOfPriority =
          ref.read(filterOfFaultPriorityProvider.notifier).state;
      final statusTypeFilter =
          ref.read(statusTypeFaultFilterProvider.notifier).state;
      final statusFilter = ref.read(statusFaultFilterProvider.notifier).state;
      final selectedDate = ref.read(selectedCalendarDateProvider);
      final search = ref.read(searchTextProvider.notifier).state;

      if ((search != '') ||
          filterOfFaultType.isNotEmpty ||
          filterOfFaultCode.isNotEmpty ||
          filterOfPriorityCode.isNotEmpty ||
          filterOfPriority.isNotEmpty ||
          statusTypeFilter.isNotEmpty ||
          statusFilter.isNotEmpty ||
          selectedDate != null) {
        await filteredFaultType.filteredFaultHeaderList(
            faulttypeList: filterOfFaultCode,
            priorityList: filterOfPriorityCode,
            statusList: statusTypeFilter,
            type: (search != '') ? AppConstants.search : AppConstants.faultType,
            plantId: plant,
            plantSec: plantSection,
            search: search);
      } else {
        await filteredFaultType.filteredFaultHeaderList(
            type: 'Initial',
            faultList: faultHeaderList.state,
            plantId: plant,
            plantSec: plantSection);
      }
    } else {
      if (!(await URLService.isInternetConnected())) {
        return UIHelper.showErrorDialog(
          context,
          description:
              AppLocalizations.of(context)!.noInternetConnectivityString,
        );
      } else {
        Result? result =
            await PAHelper.addOrModifyJobInSyncMode(context, jobHeader);
        if (result != null) {
          if (result.body['InfoMessage'] != null) {
            Navigator.pop(context);
            if (result.body['InfoMessage'][0]['category'] == 'FAILURE') {
              if (result.body['InfoMessage'][0]['message'] != null) {
                if (context.mounted) {
                  UIHelper.showErrorDialog(
                    context,
                    description: result.body['InfoMessage'][0]['message'],
                  );
                }
              }
            } else {
              if (result.body['InfoMessage'][0]['message'] != null) {
                if (context.mounted) {
                  UIHelper.showErrorDialog(context,
                      description: result.body['InfoMessage'][0]['message']);
                }
              }
            }
          } else {
            if (mounted) {
              Navigator.pop(context);
              UIHelper.showResultInfoDialog(context,
                  description:
                      'Job ${(result.body['JOB'] as List)[0]['JOB_HEADER']['JOB_ID']} ${UIHelper().toCamelCase((result.body['JOB'] as List)[0]['JOB_HEADER']['STATUS'])} Successfully.',
                  onPressed: () {
                Navigator.pop(context);
              });
            }
            // //TODO
            if (ScreenType.desktop != UIHelper().getScreenType(context)) {
              Navigator.pop(context);
            } else {
              final jobHeaderList = ref.watch(jobHeaderListProvider.notifier);
              final jobHeaderProviderr = ref.watch(jobHeaderProvider.notifier);

              final plant = ref.watch(plantProvider.notifier).state;

              final jobAction = ref.watch(getJobActionProvider.notifier);
              final jobDocument = ref.watch(getJobDocumentProvider.notifier);
              final documentAttachmentProviderData =
                  ref.watch(documentAttachmentProvider.notifier);
              final jobStartOn = ref.watch(jobStartOnProvider.notifier);
              final jobEndOn = ref.watch(jobEndOnProvider.notifier);

              final jobDescription = ref.watch(jobDescriptionProvider.notifier);
              final jobLongText = ref.watch(jobLongTextProvider.notifier);
              final jobPriority = ref.watch(jobPriorityProvider.notifier);
              final jobType = ref.watch(jobTypeProvider.notifier);
              JOB_HEADER? jobHeaderData2 =
                  await DbHelper.getJobHeaderById(jobHeader.job_id.toString());
              if (jobHeaderData2 != null) {
                await jobHeaderProviderr.getJobHeader(
                    jobId: jobHeader.job_id.toString());
              } else {
                await jobHeaderProviderr.getJobHeader(data: jobHeader);
              }
              if (jobHeader.status != null) {
                if (jobHeader.start_date != null) {
                  jobStartOn.getJobStartOn(jobHeader.start_date!);
                }
                if (jobHeader.end_date != null) {
                  jobEndOn.getJobEndOn(jobHeader.end_date!);
                }
              }
              if (jobHeader.description != null) {
                jobDescription
                    .getJobDescription(jobHeader.description.toString());
              }

              if (jobHeader.priority != null) {
                jobPriority.getJobPriority(ref
                    .watch(priorityListProvider.notifier)
                    .fetchPriorityCode(jobHeader.priority.toString()));
              }
              if (jobHeader.job_type != null) {
                jobType.getJobType(ref
                    .watch(jobTypeListProvider.notifier)
                    .fetchjobTypeFromCode(jobHeader.job_type.toString()));
              }
              if (jobHeader.details != null) {
                jobLongText.getJobLongText(jobHeader.details.toString());
              }

              await jobAction.getJobAction(jobHeader.job_id.toString());
              await jobDocument.getJobDocuments(jobHeader.job_id.toString());
              await documentAttachmentProviderData.fetchDocumentAttachments();
              await jobHeaderList.fetchJobHeaderList(plant);
            }
            final plant = ref.read(plantProvider.notifier).state;
            final plantSection = ref.read(plantSectionProvider.notifier).state;
            await ref
                .read(jobHeaderListProvider.notifier)
                .fetchJobHeaderList(plant);
            final jobHeaderDatata = ref.read(jobHeaderListProvider);
            final filteredJobTypeNotifier =
                ref.read(filteredJobCreationHeaderListProvider.notifier);

            final search = ref.read(searchTextJobProvider.notifier).state;
            final filterOfJobType =
                ref.read(filterOfJobTypeProvider.notifier).state;
            final filterOfJobTypeCode =
                ref.read(filterOfJobTypeCodeProvider.notifier).state;
            final filterOfPriority =
                ref.read(filterOfPriorityProvider.notifier).state;
            final filterOfPriorityCode =
                ref.read(filterOfPriorityCodeProvider.notifier).state;
            final statusFilter = ref.read(statusFilterProvider.notifier).state;
            final statusTypeFilter =
                ref.read(statusTypeFilterProvider.notifier).state;
            final selectedDate = ref.read(selectedCalendarDateJobProvider);
            if ((search != '') ||
                filterOfJobType.isNotEmpty ||
                filterOfJobTypeCode.isNotEmpty ||
                filterOfPriorityCode.isNotEmpty ||
                filterOfPriority.isNotEmpty ||
                statusTypeFilter.isNotEmpty ||
                statusFilter.isNotEmpty ||
                selectedDate != null) {
              await filteredJobTypeNotifier.filteredJobHeaderList(
                typeList: filterOfJobTypeCode,
                priorityList: filterOfPriorityCode,
                statusList: statusTypeFilter,
                type:
                    (search != '') ? AppConstants.search : AppConstants.jobType,
                plantId: plant,
                plantSec: plantSection,
                search: search,
              );
            } else {
              await filteredJobTypeNotifier.filteredJobHeaderList(
                  type: 'Initial',
                  jobList: jobHeaderDatata,
                  plantId: plant,
                  plantSec: plantSection);
            }

            setState(() {});
            jobDetailViewNotifier.value = InteractiveItemModel(
              type: "JOB_HEADER",
              data: {
                "type": "",
                "index": null,
                "fromFault": false,
              },
            );
          }
        }
      }
    }
  }

  void onAssign(
    String type,
    JOB_HEADER jobHeader,
    String jobStatus,
    BuildContext context,
  ) async {
    var result;
    if (!kIsWeb) {
      if (UIHelper().getScreenType(context) == ScreenType.desktop) {
        result = await showDialog(
            context: context,
            barrierDismissible: false,
            builder: (rootDialogContext) => Dialog(
                backgroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0),
                ),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxWidth: 420,
                    maxHeight: 800,
                  ),
                  child: const Padding(
                    padding: EdgeInsets.all(20.0),
                    child: AssignScreen(
                      isSaveButtonForWeb: true,
                    ),
                  ),
                )));
      } else {
        result =
            await Navigator.push(context, MaterialPageRoute(builder: (context) {
          return const AssignScreen();
        }));
      }
    } else {
      result = await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (rootDialogContext) => Dialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              child: ConstrainedBox(
                constraints: const BoxConstraints(
                  maxWidth: 420,
                  maxHeight: 800,
                ),
                child: const Padding(
                  padding: EdgeInsets.all(20.0),
                  child: AssignScreen(
                    isSaveButtonForWeb: true,
                  ),
                ),
              )));
    }
    if (result != null) {
      USER_HEADER user = result as USER_HEADER;
      jobHeader.assigned_to = user.user_id;
      if (jobHeader.p_mode != AppConstants.add) {
        jobHeader.p_mode = AppConstants.modified;
      }
      await AppDatabaseManager()
          .update(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));
      if (mounted) {
        onYes(type, jobHeader, jobStatus, context);
      }
    }
  }

  void onFault(JOB_HEADER jobHeader) async {
    final faultHeader = ref.read(faultHeaderProvider.notifier);
    final editProvider = ref.read(editFaultFieldProvider.notifier);
    await faultHeader.getFaultHeader(faultId: jobHeader.fault_id.toString());

    editProvider.getEditFaultFieldEnable(false);
    if (mounted) {
      if (!kIsWeb) {
        if (UIHelper().getScreenType(context) != ScreenType.desktop) {
          Navigator.pop(context);
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) =>
                    const FaultDetailScreen(type: AppConstants.fault)),
          );
        } else {
          if (ref.read(bottomNavIndexProvider.notifier).state == 1) {
            Navigator.pop(context);
          }
          if (ref.read(bottomNavIndexProvider.notifier).state == 1 ||
              ref.read(bottomNavIndexProvider.notifier).state == 3) {
            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (rootDialogContext) => Dialog(
                    backgroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(
                        maxWidth: 600,
                        maxHeight: 800,
                      ),
                      child: const Padding(
                          padding: EdgeInsets.all(20.0),
                          child: FaultDetailScreen(
                            type: AppConstants.fault,
                            isBackrequired: true,
                            display: true,
                          )),
                    )));
          } else {
            Navigator.pop(context);
          }
        }
      } else {
        if (ref.read(bottomNavIndexProvider.notifier).state == 1) {
          Navigator.pop(context);
        }
        if (ref.read(bottomNavIndexProvider.notifier).state == 1 ||
            ref.read(bottomNavIndexProvider.notifier).state == 3) {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (rootDialogContext) => Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxWidth: 600,
                      maxHeight: 800,
                    ),
                    child: const Padding(
                        padding: EdgeInsets.all(20.0),
                        child: FaultDetailScreen(
                          type: AppConstants.fault,
                          isBackrequired: true,
                          display: true,
                        )),
                  )));
        } else {
          Navigator.pop(context);
        }
      }
    }
  }

  void onUnSaveJob(JOB_HEADER jobHeader) async {
    Navigator.pop(context);
    if (jobHeader.p_mode !=null && jobHeader.p_mode == AppConstants.add) {
      String deleteQuery =
          'DELETE FROM ${JOB_HEADER.TABLE_NAME} WHERE ${JOB_HEADER.FIELD_JOB_ID} = ${jobHeader.job_id}';
      await AppDatabaseManager().execute(deleteQuery);
    }
    if (mounted) {
      if (ScreenType.desktop != UIHelper().getScreenType(context)) {
        Navigator.pop(context, null);
      } else {
        Navigator.pop(context, jobHeader);
      }
    }
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    await ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
    final jobHeaderDatata = ref.read(jobHeaderListProvider);
    final filteredJobTypeNotifier =
        ref.read(filteredJobCreationHeaderListProvider.notifier);

    final search = ref.read(searchTextJobProvider.notifier).state;
    final filterOfJobType = ref.read(filterOfJobTypeProvider.notifier).state;
    final filterOfJobTypeCode =
        ref.read(filterOfJobTypeCodeProvider.notifier).state;
    final filterOfPriority = ref.read(filterOfPriorityProvider.notifier).state;
    final filterOfPriorityCode =
        ref.read(filterOfPriorityCodeProvider.notifier).state;
    final statusFilter = ref.read(statusFilterProvider.notifier).state;
    final statusTypeFilter = ref.read(statusTypeFilterProvider.notifier).state;
    final selectedDate = ref.read(selectedCalendarDateJobProvider);
    if ((search != '') ||
        filterOfJobType.isNotEmpty ||
        filterOfJobTypeCode.isNotEmpty ||
        filterOfPriorityCode.isNotEmpty ||
        filterOfPriority.isNotEmpty ||
        statusTypeFilter.isNotEmpty ||
        statusFilter.isNotEmpty ||
        selectedDate != null) {
      await filteredJobTypeNotifier.filteredJobHeaderList(
        typeList: filterOfJobTypeCode,
        priorityList: filterOfPriorityCode,
        statusList: statusTypeFilter,
        type: (search != '') ? AppConstants.search : AppConstants.jobType,
        plantId: plant,
        plantSec: plantSection,
        search: search,
      );
    } else {
      await filteredJobTypeNotifier.filteredJobHeaderList(
          type: 'Initial',
          jobList: jobHeaderDatata,
          plantId: plant,
          plantSec: plantSection);
    }

    if(!kIsWeb){
      clearJobStates();
    }
/*    await filteredjobTypeNotifier.filteredJobHeaderList(
        type: 'Initial',
        jobList: jobHeaderDatata,
        plantId: plant,
        plantSec: plantSection);*/
  }

  bool isAssignVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isAssign(role.task!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isJobCreateVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isCreate(role.task!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isExecutionVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isExecute(role.task!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  retainOriginalData() {
    final jobHeader = ref.read(jobHeaderProvider.notifier).state;
    final description = ref.read(jobDescriptionProvider.notifier);
    final longText = ref.read(jobLongTextProvider.notifier);
    final jobStartOn = ref.read(jobStartOnProvider.notifier);
    final jobEndOn = ref.read(jobEndOnProvider.notifier);
    final jobPriority = ref.read(jobPriorityProvider.notifier);
    final jobType = ref.read(jobTypeProvider.notifier);
    if (jobHeader.priority != null) {
      jobPriority.getJobPriority(ref
          .read(priorityListProvider.notifier)
          .fetchPriorityCode(jobHeader.priority.toString()));
    }
    if (jobHeader.job_type != null) {
      jobType.getJobType(ref
          .read(jobTypeListProvider.notifier)
          .fetchjobTypeFromCode(jobHeader.job_type.toString()));
    }

    if (jobHeader.start_date != null) {
      jobStartOn.getJobStartOn(jobHeader.start_date!);
    }
    if (jobHeader.end_date != null) {
      jobEndOn.getJobEndOn(jobHeader.end_date!);
    }
    if (jobHeader.description != null && jobHeader.description != '') {
      descriptionController.text = jobHeader.description.toString();
      description.getJobDescription(descriptionController.text);
    }
    if (jobHeader.details != null && jobHeader.details != '') {
      longTextController.text = jobHeader.details.toString();
      longText.getJobLongText(longTextController.text);
    }
    fetchInfoMessage = InfoMessageHelper().getInfoMessageByBeLid(jobHeader.lid);
  }
}
