import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:rounds/be/JOB_HEADER.dart';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:rounds/models/intractive_Item_Model.dart';
import 'package:rounds/pages/job_creation/job_creation_detail_screen.dart';
import 'package:rounds/pages/job_creation/tabs/edit_job_creation_field_provider.dart';
import 'package:rounds/utils/utils.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import '../../../../helpers/ui_helper.dart';

import '../../../../utils/app_colors.dart';
import '../../../../utils/constants.dart';
import '../../../be/DOCUMENT_HEADER.dart';
import '../../../be/FAULT_HEADER.dart';
import '../../../be/JOB_ACTION.dart';
import '../../../be/JOB_DOCUMENT.dart';
import '../../../helpers/db_helper.dart';
import '../../../helpers/pa_helper.dart';
import '../../../be/USER_HEADER.dart';
import '../../../providers/attachments/attachment_provider.dart';
import '../../../providers/fault/fault_header_provider.dart';
import '../../../providers/fault/fault_type_provider.dart';
import '../../../providers/job_creation/job_creation_filter_provider.dart';
import '../../../providers/job_creation/job_header_provider.dart';
import '../../../providers/user_provider.dart';
import '../../../utils/app_constants.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';

import '../../assign_plan_screen.dart';
import '../../dashboard/dashboard.dart';
import '../../fault/fault_filter_provider.dart';
import '../../fault/fault_screen.dart';

import '../job_creation_screen.dart';

class JobCreationCard extends ConsumerStatefulWidget {
  final int index;
  final Color color;
  final JOB_HEADER item;
  final Function(InteractiveItemModel) onTap;

  JobCreationCard(
      {super.key,
      this.color = Colors.white,
      required this.index,
      required this.item,
      required this.onTap});

  @override
  _JobCreationCardState createState() => _JobCreationCardState();
}

class _JobCreationCardState extends ConsumerState<JobCreationCard> {
  @override
  void initState() {
    super.initState();
    // if (kIsWeb) {
    //   WidgetsBinding.instance.addPostFrameCallback((_) {
    //     autoSelectIndexOne();
    //   });
    // }
  }

  void autoSelectIndexOne() async {
    final jobHeaderList = ref.watch(jobHeaderListProvider.notifier);
    final jobHeaderProviderr = ref.watch(jobHeaderProvider.notifier);
    final plant = ref.watch(plantProvider.notifier).state;
    final jobAction = ref.watch(getJobActionProvider.notifier);
    final jobDocument = ref.watch(getJobDocumentProvider.notifier);
    final documentAttachmentProviderData =
        ref.watch(documentAttachmentProvider.notifier);
    final jobStartOn = ref.watch(jobStartOnProvider.notifier);
    final jobEndOn = ref.watch(jobEndOnProvider.notifier);
    final jobDescription = ref.watch(jobDescriptionProvider.notifier);
    final jobLongText = ref.watch(jobLongTextProvider.notifier);
    final jobPriority = ref.watch(jobPriorityProvider.notifier);
    final jobType = ref.watch(jobTypeProvider.notifier);

    if (ScreenType.desktop == UIHelper().getScreenType(context)) {
      if (widget.index == 0) {
        JOB_HEADER? jobHeader =
            await DbHelper.getJobHeaderById(widget.item.job_id.toString());
        if (jobHeader != null) {
          await jobHeaderProviderr.getJobHeader(
              jobId: widget.item.job_id.toString());
        } else {
          await jobHeaderProviderr.getJobHeader(data: widget.item);
        }
        if (widget.item.status != null) {
          if (widget.item.start_date != null) {
            jobStartOn.getJobStartOn(widget.item.start_date!);
          }
          if (widget.item.end_date != null) {
            jobEndOn.getJobEndOn(widget.item.end_date!);
          }
        }
        if (widget.item.description != null) {
          jobDescription.getJobDescription(widget.item.description.toString());
        }

        if (widget.item.priority != null) {
          jobPriority.getJobPriority(ref
              .watch(priorityListProvider.notifier)
              .fetchPriorityCode(widget.item.priority.toString()));
        }
        if (widget.item.job_type != null) {
          jobType.getJobType(ref
              .watch(jobTypeListProvider.notifier)
              .fetchjobTypeFromCode(widget.item.job_type.toString()));
        }
        if (widget.item.details != null) {
          jobLongText.getJobLongText(widget.item.details.toString());
        }

        await jobAction.getJobAction(widget.item.job_id.toString());
        await jobDocument.getJobDocuments(widget.item.job_id.toString());
        await documentAttachmentProviderData.fetchDocumentAttachments();

        if (context.mounted) {
          if (UIHelper().getScreenType(context) != ScreenType.desktop) {
            await Navigator.push(context, MaterialPageRoute(builder: (context) {
              return JobCreationDetailScreen(
                fromFault: false,
              );
            }));
          } else {
            widget.onTap(InteractiveItemModel(
                type: "JOB_HEADER",
                data: {"fromFault": false, "index": widget.index}));
          }
        }
        await jobHeaderList.fetchJobHeaderList(plant);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return getCard();
  }

  Widget getCard() {
    if (ScreenType.desktop == UIHelper().getScreenType(context)) {
      return getJobCreationCard(context, ref);
    } else {
      return _getJobSlidableCard(context);
    }
  }

  _getJobSlidableCard(BuildContext context) {
    return Slidable(
      groupTag: AppLocalizations.of(context)!.jobs,
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: isExecutionVisible()
            ? getJobStatusRightActionButtons(widget.item)
            : [
                SlidableAction(
                  onPressed: (context) {},
                  label: '',
                )
              ],
      ),
      startActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: isExecutionVisible()
            ? getJobStatusLeftActionButtons(widget.item)
            : [
                SlidableAction(
                  onPressed: (context) {},
                  label: '',
                )
              ],
      ),
      child: getJobCreationCard(context, ref),
    );
  }

  clearJobStates() {
    final description = ref.watch(jobDescriptionProvider.notifier);
    final longText = ref.watch(jobLongTextProvider.notifier);
    final priority = ref.watch(jobPriorityProvider.notifier);
    final jobType = ref.watch(jobTypeProvider.notifier);
    final assignedTo = ref.watch(jobAssignedToProvider.notifier);
    final startDate = ref.watch(jobStartOnProvider.notifier);
    final endDate = ref.watch(jobEndOnProvider.notifier);
    description.clearJobDescription();
    longText.clearLongText();
    priority.clearPriority();
    jobType.clearJobType();
    assignedTo.clearAssignedTo();
    startDate.clearStartDate();
    endDate.clearEndDate();
  }

  Widget getJobCreationCard(BuildContext context, WidgetRef ref) {
    final jobHeaderList = ref.watch(jobHeaderListProvider.notifier);
    final jobHeaderProviderr = ref.watch(jobHeaderProvider.notifier);
    final editProviderr = ref.watch(editJobCreationProvider.notifier);
    final plant = ref.watch(plantProvider.notifier).state;
    final plantSection = ref.watch(plantSectionProvider.notifier).state;
    final jobAction = ref.watch(getJobActionProvider.notifier);
    final jobDocument = ref.watch(getJobDocumentProvider.notifier);
    final documentAttachmentProviderData =
        ref.watch(documentAttachmentProvider.notifier);
    final jobStartOn = ref.watch(jobStartOnProvider.notifier);
    final jobEndOn = ref.watch(jobEndOnProvider.notifier);
    final userList = ref.watch(usersListProvider);
    final jobDescription = ref.watch(jobDescriptionProvider.notifier);
    final jobLongText = ref.watch(jobLongTextProvider.notifier);
    final jobPriority = ref.watch(jobPriorityProvider.notifier);
    final jobType = ref.watch(jobTypeProvider.notifier);
    USER_HEADER? assignedTo;
    if (widget.item.assigned_to != null && widget.item.assigned_to != '') {
      assignedTo = DbHelper.getAssignUser(userList, widget.item.assigned_to!);
    } else {
      assignedTo = USER_HEADER(user_id: null);
    }

    return InkWell(
        onTap: !isDisplayVisible()
            ? () {}
            : () async {
                bool data = await checkIfModifiedJobDataThere();
                if (!data) {
                  if (mounted) {
                    UIHelper.showConfirmationDialogWithYesOrNo(context,
                        description: AppLocalizations.of(context)!
                            .unsave_data_do_you_want_save_it, no: () {
                      onUnSaveJob(jobHeaderProviderr.state);
                    }, yes: () {
                      Navigator.pop(context);
                      onCheckback(null, onPressback: true);
                    });
                  }
                } else {
                  clearJobStates();
                  editProviderr.getEditJobCreationEnable(false);

                  JOB_HEADER? jobHeader = await DbHelper.getJobHeaderById(
                      widget.item.job_id.toString());
                  if (jobHeader != null) {
                    await jobHeaderProviderr.getJobHeader(
                        jobId: widget.item.job_id.toString());
                  } else {
                    await jobHeaderProviderr.getJobHeader(data: widget.item);
                  }
                  if (widget.item.status != null) {
                    if (widget.item.start_date != null) {
                      jobStartOn.getJobStartOn(widget.item.start_date!);
                    }
                    if (widget.item.end_date != null) {
                      jobEndOn.getJobEndOn(widget.item.end_date!);
                    }
                  }
                  if (widget.item.description != null) {
                    jobDescription
                        .getJobDescription(widget.item.description.toString());
                  }

                  if (widget.item.priority != null) {
                    jobPriority.getJobPriority(ref
                        .watch(priorityListProvider.notifier)
                        .fetchPriorityCode(widget.item.priority.toString()));
                  }
                  if (widget.item.job_type != null) {
                    jobType.getJobType(ref
                        .watch(jobTypeListProvider.notifier)
                        .fetchjobTypeFromCode(widget.item.job_type.toString()));
                  }
                  if (widget.item.details != null) {
                    jobLongText.getJobLongText(widget.item.details.toString());
                  }

                  await jobAction.getJobAction(widget.item.job_id.toString());
                  await jobDocument
                      .getJobDocuments(widget.item.job_id.toString());
                  await documentAttachmentProviderData
                      .fetchDocumentAttachments();

                  if (context.mounted) {
                    if (UIHelper().getScreenType(context) !=
                        ScreenType.desktop) {
                      await Navigator.push(context,
                          MaterialPageRoute(builder: (context) {
                        return JobCreationDetailScreen(
                          fromFault: false,
                        );
                      }));
                    } else {
                      widget.onTap(InteractiveItemModel(
                          type: "JOB_HEADER",
                          data: {"fromFault": false, "index": widget.index}));
                    }
                  }
                  await jobHeaderList.fetchJobHeaderList(plant);
                }
              },
        child: Container(
            decoration: UIHelper.cardDecoration(cardColor: widget.color),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: Image.asset('assets/icon/setting.png',
                              width: 18,
                              height: 18,
                              color: AppColors.primaryColor),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: Row(
                            children: [
                              Text(
                                widget.item.status == null
                                    ? AppLocalizations.of(context)!.new_string
                                    : widget.item.job_id.toString(),
                                style: TextStyle(
                                  color: AppColors.secondaryTextColor,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Spacer(),
                        Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: Container(
                              alignment: Alignment.topRight,
                              height: 20,
                              child: Text(
                                ref
                                    .watch(jobTypeListProvider.notifier)
                                    .fetchjobTypeFromCode(
                                        widget.item.job_type.toString()),
                                style: const TextStyle(
                                    fontSize: 11, fontWeight: FontWeight.bold),
                              ),
                            )),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(
                                    top: 8.0, bottom: 4.0),
                                child: Text(
                                  widget.item.description.toString(),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: AppColors.titleTextColor,
                                    fontWeight: FontWeight.w700,
                                    letterSpacing: 0.1,
                                  ),
                                ),
                              ),
                              Spacer(),
                              Align(
                                alignment: Alignment.centerRight,
                                child: Padding(
                                  padding: const EdgeInsets.only(right: 0.0),
                                  child: Container(
                                    alignment: Alignment.centerRight,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 6, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: UIHelper.getPriorityColorByCode(widget
                                          .item.priority
                                          .toString()), // Handle null priority
                                      borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(12),
                                        bottomLeft: Radius.circular(12),
                                      ),
                                    ),
                                    child: Text(
                                      ref
                                          .watch(priorityListProvider.notifier)
                                          .fetchPriorityCode(
                                              widget.item.priority.toString()),
                                      style: TextStyle(
                                        color: AppColors.white,
                                        fontSize: 11,
                                        letterSpacing: 0.5,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 5),
                          _getDate(widget.item.start_date, null),
                        ],
                      ),
                    ),
                    Row(
                      children: [
                        assignedTo != null
                            ? assignedTo.first_name != null
                                ? Padding(
                                    padding: const EdgeInsets.only(left: 8.0),
                                    child: Row(
                                      children: [
                                        const Icon(
                                          Icons.account_circle,
                                          color: Colors.blue,
                                          size: 12,
                                        ),
                                        const SizedBox(width: 4),
                                        Align(
                                          alignment: Alignment.bottomLeft,
                                          child: Text(
                                            UIHelper().toCamelCase(
                                                '${assignedTo.first_name.toString()} ${assignedTo.last_name.toString()}'),
                                            style: TextStyle(
                                              color:
                                                  AppColors.secondaryTextColor,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : Container()
                            : Container(),
                        const Spacer(),
                        Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: Align(
                            alignment: Alignment.bottomRight,
                            child: Text(
                              UIHelper.getJobStatusString(
                                  widget.item.status.toString()),
                              style: TextStyle(
                                color: UIHelper.getJobCreationStatusColor(
                                    widget.item.status.toString()),
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (widget.item.syncStatus.index == 1 ||
                        widget.item.syncStatus.index == 2 ||
                        widget.item.syncStatus.index == 3)
                      Center(
                          child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            backgroundColor: getSyncStatusColor(widget.item)),
                        onPressed: () async {
                          if (widget.item.objectStatus != ObjectStatus.global) {
                            if (widget.item.syncStatus == SyncStatus.error ||
                                widget.item.syncStatus == SyncStatus.none) {
                              JOB_HEADER? jobHeader =
                                  await DbHelper.getJobHeaderById(
                                      widget.item.job_id.toString());
                              if (jobHeader != null) {
                                String prevStatus = "";
                                if (jobHeader.status ==
                                        Constants.JOB_STATE_OSNO &&
                                    jobHeader.syncStatus == SyncStatus.error) {
                                  prevStatus = jobHeader.status ?? "";
                                  jobHeader.status = "";
                                }
                                await AppDatabaseManager().update(DBInputEntity(
                                    JOB_HEADER.TABLE_NAME, jobHeader.toJson()));
                                if (!kIsWeb) {
                                  await PAHelper.addOrModifyJobInAsyncMode(
                                      context, jobHeader);
                                } else {
                                  await PAHelper.addOrModifyJobInSyncMode(
                                      context, jobHeader);
                                }
                                if (prevStatus != "") {
                                  jobHeader.status = prevStatus;
                                  await AppDatabaseManager().update(
                                      DBInputEntity(JOB_HEADER.TABLE_NAME,
                                          jobHeader.toJson()));
                                }
                                await ref
                                    .read(jobHeaderListProvider.notifier)
                                    .fetchJobHeaderList(plant);
                                final jobHeaderDatata =
                                    ref.read(jobHeaderListProvider);
                                final filteredJobTypeNotifier = ref.read(
                                    filteredJobCreationHeaderListProvider
                                        .notifier);

                                final searchJob = ref
                                    .read(searchTextJobProvider.notifier)
                                    .state;
                                final filterOfJobType = ref
                                    .read(filterOfJobTypeProvider.notifier)
                                    .state;
                                final filterOfJobTypeCode = ref
                                    .read(filterOfJobTypeCodeProvider.notifier)
                                    .state;
                                final filterOfJobPriority = ref
                                    .read(filterOfPriorityProvider.notifier)
                                    .state;
                                final filterOfJobPriorityCode = ref
                                    .read(filterOfPriorityCodeProvider.notifier)
                                    .state;
                                final statusJobFilter = ref
                                    .read(statusFilterProvider.notifier)
                                    .state;
                                final statusTypeJobFilter = ref
                                    .read(statusTypeFilterProvider.notifier)
                                    .state;
                                final selectedDateJob =
                                    ref.read(selectedCalendarDateJobProvider);
                                if ((searchJob != '') ||
                                    filterOfJobType.isNotEmpty ||
                                    filterOfJobTypeCode.isNotEmpty ||
                                    filterOfJobPriorityCode.isNotEmpty ||
                                    filterOfJobPriority.isNotEmpty ||
                                    statusTypeJobFilter.isNotEmpty ||
                                    statusJobFilter.isNotEmpty ||
                                    selectedDateJob != null) {
                                  await filteredJobTypeNotifier
                                      .filteredJobHeaderList(
                                    typeList: filterOfJobTypeCode,
                                    priorityList: filterOfJobPriorityCode,
                                    statusList: statusTypeJobFilter,
                                    type: (searchJob != '')
                                        ? AppConstants.search
                                        : AppConstants.jobType,
                                    plantId: plant,
                                    plantSec: plantSection,
                                    search: searchJob,
                                  );
                                } else {
                                  await filteredJobTypeNotifier
                                      .filteredJobHeaderList(
                                          type: 'Initial',
                                          jobList: jobHeaderDatata,
                                          plantId: plant,
                                          plantSec: plantSection);
                                }

                                /*               await filteredjobTypeNotifier
                                    .filteredJobHeaderList(
                                        type: 'Initial',
                                        jobList: jobHeaderDatata,
                                        plantId: plant,
                                        plantSec: plantSection);*/

                                final faultHeaderList =
                                    ref.read(faultHeaderListProvider.notifier);
                                await faultHeaderList
                                    .fetchFaultHeaderList(plant);
                                final filteredFaultType = ref.read(
                                    filteredFaultHeaderListProvider.notifier);
                                final filterOfFaultType = ref
                                    .read(filterOfFaultTypeProvider.notifier)
                                    .state;
                                final filterOfFaultCode = ref
                                    .read(filterOfFaultCodeProvider.notifier)
                                    .state;
                                final filterOfFaultPriorityCode = ref
                                    .read(filterOfFaultPriorityCodeProvider
                                        .notifier)
                                    .state;
                                final filterOfFaultPriority = ref
                                    .read(
                                        filterOfFaultPriorityProvider.notifier)
                                    .state;
                                final statusTypeFaultFilter = ref
                                    .read(
                                        statusTypeFaultFilterProvider.notifier)
                                    .state;
                                final statusFaultFilter = ref
                                    .read(statusFaultFilterProvider.notifier)
                                    .state;
                                final selectedDateFault =
                                    ref.read(selectedCalendarDateProvider);
                                final searchFault =
                                    ref.read(searchTextProvider.notifier).state;

                                if ((searchFault != '') ||
                                    filterOfFaultType.isNotEmpty ||
                                    filterOfFaultCode.isNotEmpty ||
                                    filterOfFaultPriorityCode.isNotEmpty ||
                                    filterOfFaultPriority.isNotEmpty ||
                                    statusTypeFaultFilter.isNotEmpty ||
                                    statusFaultFilter.isNotEmpty ||
                                    selectedDateFault != null) {
                                  await filteredFaultType
                                      .filteredFaultHeaderList(
                                          faulttypeList: filterOfFaultCode,
                                          priorityList:
                                              filterOfFaultPriorityCode,
                                          statusList: statusTypeFaultFilter,
                                          type: (searchFault != '')
                                              ? AppConstants.search
                                              : AppConstants.faultType,
                                          plantId: plant,
                                          plantSec: plantSection,
                                          search: searchFault);
                                } else {
                                  await filteredFaultType
                                      .filteredFaultHeaderList(
                                          type: 'Initial',
                                          faultList: faultHeaderList.state,
                                          plantId: plant,
                                          plantSec: plantSection);
                                }

                                /*                      await filteredFaultType.filteredFaultHeaderList(
                                    type: 'Initial',
                                    faultList: faultHeaderList.state,
                                    plantId: plant,
                                    plantSec: plantSection);*/
                              }
                            }
                          }
                        },
                        child: Text(getSyncStatus(widget.item)),
                      ))
                  ]),
            )));
  }

  Widget _getDate(int? reqDate, int? endDate) {
    String? date;
    Widget widget = Container();
    double clockSize = 12;
    TextStyle style = TextStyle(
      color: AppColors.secondaryTextColor,
      fontSize: 12,
      fontWeight: FontWeight.w600,
    );
    if (reqDate != null && endDate == null) {
      date = UIHelper.formatDate(reqDate.toString());
      widget = Row(
        children: [
          Icon(
            Icons.access_time,
            color: Colors.blue,
            size: clockSize,
          ),
          const SizedBox(width: 4),
          Text(
            date,
            style: style,
          ),
        ],
      );
      return widget;
    } else if (reqDate == null && endDate != null) {
      date = UIHelper.formatDate(endDate.toString());
      widget = Row(
        children: [
          Icon(
            Icons.access_time,
            color: Colors.blue,
            size: clockSize,
          ),
          const SizedBox(width: 4),
          Text(
            date,
            style: style,
          ),
        ],
      );
      return widget;
    } else if (reqDate != null && endDate != null) {
      String formattedReqDate = UIHelper.formatDate(reqDate.toString());
      String formattedEndDate = UIHelper.formatDate(endDate.toString());
      date = '$formattedReqDate - $formattedEndDate';
      widget = Row(
        children: [
          Icon(
            Icons.access_time,
            color: Colors.blue,
            size: clockSize,
          ),
          const SizedBox(width: 4),
          Text(
            date,
            style: style,
          ),
        ],
      );
      return widget;
    } else {
      return widget;
    }
  }

  getJobStatusRightActionButtons(JOB_HEADER jobHeader) {
    if (jobHeader.status == null || jobHeader.p_mode == AppConstants.add) {
      return [
        SlidableAction(
          onPressed: (context) => () {},
          label: '',
        ),
      ];
    } else if (jobHeader.status == Constants.JOB_STATE_ORAS) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? [Container()]
          : [
              SlidableAction(
                onPressed: (context) {
                  handleStatusActionButtonsFunctions('reject', jobHeader);
                },
                backgroundColor: Colors.red,
                foregroundColor: AppColors.white,
                borderRadius: BorderRadius.circular(10),
                icon: Icons.cancel,
                label: AppLocalizations.of(context)!.reject,
              )
            ];
    } else if (jobHeader.status == Constants.JOB_STATE_ACPT) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? [Container()]
          : [
              SlidableAction(
                onPressed: (context) {
                  handleStatusActionButtonsFunctions('reject', jobHeader);
                },
                backgroundColor: Colors.red,
                foregroundColor: AppColors.white,
                borderRadius: BorderRadius.circular(10),
                icon: Icons.cancel,
                label: AppLocalizations.of(context)!.reject,
              ),
              SlidableAction(
                onPressed: (context) {
                  handleStatusActionButtonsFunctions('complete', jobHeader);
                },
                backgroundColor: AppColors.greenColor,
                foregroundColor: AppColors.white,
                borderRadius: BorderRadius.circular(10),
                icon: Icons.flag,
                label: AppLocalizations.of(context)!.complete,
              )
            ];
    } else if (jobHeader.status == Constants.JOB_STATE_RJCT) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? [Container()]
          : isAssignVisible()
              ? [
                  SlidableAction(
                    onPressed: (context) {
                      handleStatusActionButtonsFunctions('assign', jobHeader);
                    },
                    backgroundColor: AppColors.blue,
                    foregroundColor: AppColors.white,
                    borderRadius: BorderRadius.circular(10),
                    icon: Icons.assignment_turned_in,
                    label: AppLocalizations.of(context)!.assign,
                  ),
                ]
              : [
                  SlidableAction(
                    onPressed: (context) {},
                    label: '',
                  )
                ];
    } else if (jobHeader.status == Constants.JOB_STATE_NOCO) {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        ),
      ];
    } else {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        ),
      ];
    }
  }

  getJobStatusLeftActionButtons(JOB_HEADER jobHeader) {
    if (jobHeader.status == null || jobHeader.p_mode == 'A') {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        ),
      ];
    } else if (jobHeader.status == Constants.JOB_STATE_OSNO) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? [Container()]
          : isAssignVisible()
              ? [
                  SlidableAction(
                    onPressed: (context) {
                      handleStatusActionButtonsFunctions('assign', jobHeader);
                    },
                    backgroundColor: AppColors.blue,
                    foregroundColor: AppColors.white,
                    borderRadius: BorderRadius.circular(10),
                    icon: Icons.assignment_turned_in,
                    label: AppLocalizations.of(context)!.assign,
                  ),
                ]
              : [
                  SlidableAction(
                    onPressed: (context) {},
                    label: '',
                  )
                ];
    } else if (jobHeader.status == Constants.JOB_STATE_ORAS) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? [Container()]
          : [
              SlidableAction(
                onPressed: (context) {
                  handleStatusActionButtonsFunctions('accept', jobHeader);
                },
                backgroundColor: AppColors.greenColor,
                foregroundColor: AppColors.white,
                borderRadius: BorderRadius.circular(10),
                icon: Icons.assignment_turned_in,
                label: AppLocalizations.of(context)!.accept,
              ),
            ];
    } else if (jobHeader.status == Constants.JOB_STATE_RJCT) {
      return (jobHeader.syncStatus == SyncStatus.queued ||
              jobHeader.syncStatus == SyncStatus.sent ||
              jobHeader.syncStatus == SyncStatus.error)
          ? [Container()]
          : isAssignVisible()
              ? [
                  SlidableAction(
                    onPressed: (context) {
                      handleStatusActionButtonsFunctions('assign', jobHeader);
                    },
                    backgroundColor: AppColors.blue,
                    foregroundColor: AppColors.white,
                    borderRadius: BorderRadius.circular(10),
                    icon: Icons.assignment_turned_in,
                    label: AppLocalizations.of(context)!.assign,
                  ),
                ]
              : [
                  SlidableAction(
                    onPressed: (context) {},
                    label: '',
                  )
                ];
    } else if (jobHeader.status == Constants.JOB_STATE_NOCO) {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        ),
      ];
    } else {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        ),
      ];
    }
  }

  void onAssign(
    String type,
    JOB_HEADER jobHeader,
    String jobStatus,
    BuildContext context,
  ) async {
    var result =
        await Navigator.push(context, MaterialPageRoute(builder: (context) {
      return const AssignScreen();
    }));
    if (result != null) {
      USER_HEADER user = result as USER_HEADER;
      jobHeader.assigned_to = user.user_id;
      await AppDatabaseManager()
          .update(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));
      if (mounted) {
        onYes(type, jobHeader, jobStatus, context);
      }
    }
  }

  handleStatusActionButtonsFunctions(String type, JOB_HEADER jobHeader) async {
    bool connectionState = await Utils.hasInternetConnection();
    if (type == 'assign') {
      if (connectionState) {
        UIHelper.showConfirmationDialogWithYesOrNo(context,
            description: AppLocalizations.of(context)!
                .do_you_want_to_assign_job, yes: () {
          Navigator.pop(context);
          onAssign(type, jobHeader, Constants.JOB_STATE_ORAS, context);
        }, no: () {
          Navigator.pop(context);
        });
      } else {
        UIHelper.showErrorDialog(context,
            description: AppLocalizations.of(context)!.offline_message);
      }
    }
    if (type == 'accept') {
      if (connectionState) {
        UIHelper.showConfirmationDialogWithYesOrNo(context,
            description: AppLocalizations.of(context)!
                .do_you_want_to_accept_job, yes: () {
          onYes(type, jobHeader, Constants.JOB_STATE_ACPT, context);
        }, no: () {
          Navigator.pop(context);
        });
      } else {
        UIHelper.showErrorDialog(context,
            description: AppLocalizations.of(context)!.offline_message);
      }
    }
    if (type == 'reject') {
      if (connectionState) {
        UIHelper.showConfirmationDialogWithYesOrNo(context,
            description: AppLocalizations.of(context)!
                .do_you_want_to_reject_job, yes: () {
          onYes(type, jobHeader, Constants.JOB_STATE_RJCT, context);
        }, no: () {
          Navigator.pop(context);
        });
      } else {
        UIHelper.showErrorDialog(context,
            description: AppLocalizations.of(context)!.offline_message);
      }
    } else if (type == 'complete') {
      UIHelper.showConfirmationDialogWithYesOrNo(context,
          description: AppLocalizations.of(context)!
              .do_you_want_to_complete_job, yes: () {
        onYes(type, jobHeader, Constants.JOB_STATE_NOCO, context);
      }, no: () {
        Navigator.pop(context);
      });
    }
  }

  onYes(String type, JOB_HEADER jobHeader, String statusType,
      BuildContext context) async {
    final jobHeaderData = ref.watch(jobHeaderProvider.notifier);
    final documents = ref.watch(getFaultDocumentHeaderProvider.notifier).state;
    JOB_HEADER header = jobHeader;
    JOB_HEADER? headerData =
        await DbHelper.getJobHeaderById(jobHeader.job_id.toString());
    if (headerData != null) {
      await AppDatabaseManager()
          .update(DBInputEntity(JOB_HEADER.TABLE_NAME, header.toJson()));
    } else {
      await AppDatabaseManager()
          .update(DBInputEntity(JOB_HEADER.TABLE_NAME, header.toJson()));
    }
    await jobHeaderData.getJobHeader(
        jobId: jobHeaderData.state.job_id.toString());
    JOB_ACTION? action =
        await DbHelper.getJobActionByJobId(header.job_id.toString());
    if (action != null) {
      action.user_action = statusType;
      await AppDatabaseManager()
          .update(DBInputEntity(JOB_ACTION.TABLE_NAME, action.toJson()));
    } else {
      JOB_ACTION? action;

      action = JOB_ACTION(job_id: header.job_id, user_action: statusType);
      action.fid = header.lid;
      await AppDatabaseManager()
          .insert(DBInputEntity(JOB_ACTION.TABLE_NAME, action.toJson()));
    }
    for (var data in documents) {
      JOB_DOCUMENT newDocument = JOB_DOCUMENT(
          job_id: jobHeader.job_id!.toInt(),
          doc_id: data.doc_id,
          p_mode: AppConstants.add);
      newDocument.fid = jobHeader.lid;
      JOB_DOCUMENT? doc = await DbHelper.getJobSingleDocumentsByJobId(
          jobHeader.job_id.toString(), data.doc_id.toString());
      if (doc != null) {
      } else {
        if (doc != null) {
          DOCUMENT_HEADER? header =
              await DbHelper.getDocumentHeadersByDocsId(doc.doc_id.toString());
          if (header != null) {
            await AppDatabaseManager().insert(
                DBInputEntity(JOB_DOCUMENT.TABLE_NAME, newDocument.toJson()));
          }
        }
      }
    }
    if (mounted) {
      Navigator.pop(context);
      await sendToServer(type, jobHeader, context);
    }
  }

  sendToServer(String type, JOB_HEADER jobHeader, BuildContext context) async {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    if (type == AppConstants.assign) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.assigning_job);
    } else if (type == AppConstants.accept) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.accepting_job);
    } else if (type == AppConstants.reject) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.rejecting_job);
    } else if (type == AppConstants.complete) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.completing_job);
    }
    if (type == AppConstants.complete) {
      Result? result;
      if (!kIsWeb) {
        result = await PAHelper.addOrModifyJobInAsyncMode(context, jobHeader);
      } else {
        result = await PAHelper.addOrModifyJobInSyncMode(context, jobHeader);
      }
      jobHeader.status = Constants.JOB_STATE_NOCO;
      await AppDatabaseManager()
          .update(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));
      final plant = ref.read(plantProvider.notifier).state;

      await ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
      final jobHeaderDatata = ref.read(jobHeaderListProvider);
      final filteredJobTypeNotifier =
          ref.read(filteredJobCreationHeaderListProvider.notifier);

      final searchJob = ref.read(searchTextJobProvider.notifier).state;
      final filterOfJobType = ref.read(filterOfJobTypeProvider.notifier).state;
      final filterOfJobTypeCode =
          ref.read(filterOfJobTypeCodeProvider.notifier).state;
      final filterOfJobPriority =
          ref.read(filterOfPriorityProvider.notifier).state;
      final filterOfJobPriorityCode =
          ref.read(filterOfPriorityCodeProvider.notifier).state;
      final statusJobFilter = ref.read(statusFilterProvider.notifier).state;
      final statusTypeJobFilter =
          ref.read(statusTypeFilterProvider.notifier).state;
      final selectedDateJob = ref.read(selectedCalendarDateJobProvider);
      if ((searchJob != '') ||
          filterOfJobType.isNotEmpty ||
          filterOfJobTypeCode.isNotEmpty ||
          filterOfJobPriorityCode.isNotEmpty ||
          filterOfJobPriority.isNotEmpty ||
          statusTypeJobFilter.isNotEmpty ||
          statusJobFilter.isNotEmpty ||
          selectedDateJob != null) {
        await filteredJobTypeNotifier.filteredJobHeaderList(
          typeList: filterOfJobTypeCode,
          priorityList: filterOfJobPriorityCode,
          statusList: statusTypeJobFilter,
          type: (searchJob != '') ? AppConstants.search : AppConstants.jobType,
          plantId: plant,
          plantSec: plantSection,
          search: searchJob,
        );
      } else {
        await filteredJobTypeNotifier.filteredJobHeaderList(
            type: 'Initial',
            jobList: jobHeaderDatata,
            plantId: plant,
            plantSec: plantSection);
      }

      final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
      await faultHeaderList.fetchFaultHeaderList(plant);
      final filteredFaultType =
          ref.read(filteredFaultHeaderListProvider.notifier);

      final filterOfFaultType =
          ref.read(filterOfFaultTypeProvider.notifier).state;
      final filterOfFaultCode =
          ref.read(filterOfFaultCodeProvider.notifier).state;
      final filterOfFaultPriorityCode =
          ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
      final filterOfFaultPriority =
          ref.read(filterOfFaultPriorityProvider.notifier).state;
      final statusTypeFaultFilter =
          ref.read(statusTypeFaultFilterProvider.notifier).state;
      final statusFaultFilter =
          ref.read(statusFaultFilterProvider.notifier).state;
      final selectedDateFault = ref.read(selectedCalendarDateProvider);
      final searchFault = ref.read(searchTextProvider.notifier).state;

      if ((searchFault != '') ||
          filterOfFaultType.isNotEmpty ||
          filterOfFaultCode.isNotEmpty ||
          filterOfFaultPriorityCode.isNotEmpty ||
          filterOfFaultPriority.isNotEmpty ||
          statusTypeFaultFilter.isNotEmpty ||
          statusFaultFilter.isNotEmpty ||
          selectedDateFault != null) {
        await filteredFaultType.filteredFaultHeaderList(
            faulttypeList: filterOfFaultCode,
            priorityList: filterOfFaultPriorityCode,
            statusList: statusTypeFaultFilter,
            type: (searchFault != '')
                ? AppConstants.search
                : AppConstants.faultType,
            plantId: plant,
            plantSec: plantSection,
            search: searchFault);
      } else {
        await filteredFaultType.filteredFaultHeaderList(
            type: 'Initial',
            faultList: faultHeaderList.state,
            plantId: plant,
            plantSec: plantSection);
      }

/*      await filteredFaultType.filteredFaultHeaderList(
          type: 'Initial',
          faultList: faultHeaderList.state,
          plantId: plant,
          plantSec: plantSection);*/
      if (result != null) {
        if (result.body['InfoMessage'] != null) {
          if (mounted) {
            Navigator.pop(context);
          }
          if (result.body['InfoMessage'][0]['category'] == 'FAILURE') {
            if (result.body['InfoMessage'][0]['message'] != null) {
              if (context.mounted) {
                UIHelper.showErrorDialog(
                  context,
                  description: result.body['InfoMessage'][0]['message'],
                );
              }
            }
          } else {
            if (result.body['InfoMessage'][0]['message'] != null) {
              if (context.mounted) {
                UIHelper.showErrorDialog(context,
                    description: result.body['InfoMessage'][0]['message']);
              }
            }
          }
        } else {
          Navigator.pop(context);
          // //TODO
          Navigator.pop(context);
        }
      }
    } else {
      if (!(await URLService.isInternetConnected())) {
        if (mounted) {
          return UIHelper.showErrorDialog(
            context,
            description:
                AppLocalizations.of(context)!.noInternetConnectivityString,
          );
        }
      } else {
        if (mounted) {
          Result? result =
              await PAHelper.addOrModifyJobInSyncMode(context, jobHeader);
          if (result != null) {
            if (result.body['InfoMessage'] != null) {
              if (mounted) {
                Navigator.pop(context);
              }
              if (result.body['InfoMessage'][0]['category'] == 'FAILURE') {
                if (result.body['InfoMessage'][0]['message'] != null) {
                  if (context.mounted) {
                    UIHelper.showErrorDialog(
                      context,
                      description: result.body['InfoMessage'][0]['message'],
                    );
                  }
                }
              } else {
                if (result.body['InfoMessage'][0]['message'] != null) {
                  if (context.mounted) {
                    UIHelper.showErrorDialog(context,
                        description: result.body['InfoMessage'][0]['message']);
                  }
                }
              }
            } else {
              if (mounted) {
                Navigator.pop(context);
                // //TODO
                Navigator.pop(context);
              }
              final plant = ref.read(plantProvider.notifier).state;

              await ref
                  .read(jobHeaderListProvider.notifier)
                  .fetchJobHeaderList(plant);
              final jobHeaderDatata = ref.read(jobHeaderListProvider);
              final filteredJobTypeNotifier =
                  ref.read(filteredJobCreationHeaderListProvider.notifier);

              final searchJob = ref.read(searchTextJobProvider.notifier).state;
              final filterOfJobType =
                  ref.read(filterOfJobTypeProvider.notifier).state;
              final filterOfJobTypeCode =
                  ref.read(filterOfJobTypeCodeProvider.notifier).state;
              final filterOfJobPriority =
                  ref.read(filterOfPriorityProvider.notifier).state;
              final filterOfJobPriorityCode =
                  ref.read(filterOfPriorityCodeProvider.notifier).state;
              final statusJobFilter =
                  ref.read(statusFilterProvider.notifier).state;
              final statusTypeJobFilter =
                  ref.read(statusTypeFilterProvider.notifier).state;
              final selectedDateJob = ref.read(selectedCalendarDateJobProvider);
              if ((searchJob != '') ||
                  filterOfJobType.isNotEmpty ||
                  filterOfJobTypeCode.isNotEmpty ||
                  filterOfJobPriorityCode.isNotEmpty ||
                  filterOfJobPriority.isNotEmpty ||
                  statusTypeJobFilter.isNotEmpty ||
                  statusJobFilter.isNotEmpty ||
                  selectedDateJob != null) {
                await filteredJobTypeNotifier.filteredJobHeaderList(
                  typeList: filterOfJobTypeCode,
                  priorityList: filterOfJobPriorityCode,
                  statusList: statusTypeJobFilter,
                  type: (searchJob != '')
                      ? AppConstants.search
                      : AppConstants.jobType,
                  plantId: plant,
                  plantSec: plantSection,
                  search: searchJob,
                );
              } else {
                await filteredJobTypeNotifier.filteredJobHeaderList(
                    type: 'Initial',
                    jobList: jobHeaderDatata,
                    plantId: plant,
                    plantSec: plantSection);
              }
/*              await filteredjobTypeNotifier.filteredJobHeaderList(
                  type: 'Initial',
                  jobList: jobHeaderDatata,
                  plantId: plant,
                  plantSec: plantSection);*/
              setState(() {});
            }
          }
        }
      }
    }
  }

  String getSyncStatus(JOB_HEADER item) {
    switch (item.syncStatus.index) {
      case 0:
        if (item.objectStatus.index != 0) {
          return '';
        }
        if (item.objectStatus.index == 2) {
          return AppLocalizations.of(context)!.sync_now;
        }
        return '';

      case 1:
        return AppLocalizations.of(context)!.queued;

      case 2:
        return AppLocalizations.of(context)!.sent;

      case 3:
        return AppLocalizations.of(context)!.error;

      default:
        return '';
    }
  }

  Color getSyncStatusColor(JOB_HEADER item) {
    switch (item.syncStatus.index ?? 0) {
      case 0:
        return Colors.transparent;
      case 1:
        return Colors.orange;
      case 2:
        return AppColors.primaryColor;
      case 3:
        return Colors.red;
      default:
        return Colors.transparent;
    }
  }

  bool isAssignVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isAssign(role.task!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isDisplayVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isDisplay(role.task!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isExecutionVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isExecute(role.task!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  Future<bool> checkIfModifiedJobDataThere() async {
    final jobHeaderr = ref.read(jobHeaderProvider.notifier).state;
    final originalJobHeader = ref.read(jobHeaderProvider);
    final jobDescription = ref.watch(jobDescriptionProvider.notifier).state;
    final priority = ref.watch(jobPriorityProvider.notifier).state;
    final jobType = ref.watch(jobTypeProvider.notifier).state;
    final startDate = ref.watch(jobStartOnProvider.notifier).state;
    final endDate = ref.watch(jobEndOnProvider.notifier).state;
    final longText = ref.read(jobLongTextProvider.notifier).state;
/*    JOB_HEADER jobHeader = jobHeaderr;*/
    JOB_HEADER jobHeader = JOB_HEADER.fromJson(jobHeaderr.toJson());
    if (startDate != 0) {
      jobHeader.start_date = startDate;
    }
    if (endDate != 0) {
      jobHeader.end_date = endDate;
    }
    if (jobDescription.isNotEmpty && jobDescription != '') {
      jobHeader.description = jobDescription;
    }
    if (longText.isNotEmpty && longText != '') {
      jobHeader.details = longText;
    }
    if (priority.priority_code != null && priority.priority_code != '') {
      jobHeader.priority = priority.priority_code;
    }
    if (jobType.job_type != null && jobType.job_type != '') {
      jobHeader.job_type = jobType.job_type;
    }
    bool edited = true;
    edited = areJsonEqual(originalJobHeader.toJson(), jobHeader.toJson());

    return edited;
  }

  void onUnSaveJob(JOB_HEADER jobHeader) async {
    final editProviderr = ref.watch(editJobCreationProvider.notifier);
    Navigator.pop(context);
    if (!kIsWeb) {
      await AppDatabaseManager()
          .delete(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));
      if (mounted) {
        Navigator.pop(context, null);
      }
    } else {
      editProviderr.getEditJobCreationEnable(false);
      widget.onTap(InteractiveItemModel(
          type: "JOB_HEADER",
          data: {"fromFault": false, "index": widget.index}));
    }
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    await ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
    final jobHeaderDatata = ref.read(jobHeaderListProvider);
    final filteredJobTypeNotifier =
        ref.read(filteredJobCreationHeaderListProvider.notifier);

    final search = ref.read(searchTextJobProvider.notifier).state;
    final filterOfJobType = ref.read(filterOfJobTypeProvider.notifier).state;
    final filterOfJobTypeCode =
        ref.read(filterOfJobTypeCodeProvider.notifier).state;
    final filterOfPriority = ref.read(filterOfPriorityProvider.notifier).state;
    final filterOfPriorityCode =
        ref.read(filterOfPriorityCodeProvider.notifier).state;
    final statusFilter = ref.read(statusFilterProvider.notifier).state;
    final statusTypeFilter = ref.read(statusTypeFilterProvider.notifier).state;
    final selectedDate = ref.read(selectedCalendarDateJobProvider);
    if ((search != '') ||
        filterOfJobType.isNotEmpty ||
        filterOfJobTypeCode.isNotEmpty ||
        filterOfPriorityCode.isNotEmpty ||
        filterOfPriority.isNotEmpty ||
        statusTypeFilter.isNotEmpty ||
        statusFilter.isNotEmpty ||
        selectedDate != null) {
      await filteredJobTypeNotifier.filteredJobHeaderList(
        typeList: filterOfJobTypeCode,
        priorityList: filterOfPriorityCode,
        statusList: statusTypeFilter,
        type: (search != '') ? AppConstants.search : AppConstants.jobType,
        plantId: plant,
        plantSec: plantSection,
        search: search,
      );
    } else {
      await filteredJobTypeNotifier.filteredJobHeaderList(
          type: 'Initial',
          jobList: jobHeaderDatata,
          plantId: plant,
          plantSec: plantSection);
    }

    clearJobStates();
  }

  bool validate() {
    final attachment = ref.watch(getJobDocumentProvider.notifier).state;
    final selectedDescription =
        ref.watch(jobDescriptionProvider.notifier).state;
    final selectedLongText = ref.watch(jobLongTextProvider.notifier).state;
    final selectedJobType = ref.watch(jobTypeProvider.notifier).state;
    final selectedPriority = ref.watch(priorityProvider.notifier).state;
    final endDate = ref.watch(jobEndOnProvider.notifier).state;

    bool data = false;
    if (selectedDescription.isEmpty) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_enter_description);
      return data;
    }
    if (selectedLongText.isEmpty) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_enter_long_text);
      return data;
    } else if (selectedJobType.description == null) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_jobType);
      return data;
    } else if (selectedPriority.priority_code == null) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_priority);
      return data;
    } else if (endDate == 0) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_endDate);
      return data;
    }
    //  else if (attachment.isEmpty) {
    //   UIHelper.showErrorDialog(context,
    //       description: AppLocalizations.of(context)!
    //           .please_upload_atleast_one_attachment);
    //   return data;
    // }
    return true;
  }

  void onCheckback(String? status, {bool onPressback = false}) async {
    if (!validate()) {
      return;
    }
    if (onPressback) {
      if (kIsWeb) {
        if (!(await URLService.isInternetConnected())) {
          if (mounted) {
            UIHelper.showErrorDialog(context,
                description:
                    AppLocalizations.of(context)!.noInternetConnectivityString);
          }
        } else {
          onSave();
        }
      } else {
        onSave();
      }
    } else {
      UIHelper.showConfirmationDialog(
        context,
        description: AppLocalizations.of(context)!.save_job_confirmation,
        positiveButtonString: AppLocalizations.of(context)!.ok,
        positiveButtonOnTap: () async {
          Navigator.pop(context);
          if (!(await URLService.isInternetConnected())) {
            if (mounted) {
              UIHelper.showErrorDialog(context,
                  description: AppLocalizations.of(context)!
                      .noInternetConnectivityString);
            }
          } else {
            onSave();
          }
        },
      );
    }
  }

  void onSave() async {
    UIHelper().progressDialog(
        context: context, message: AppLocalizations.of(context)!.saving_job);
    final headerData = ref.watch(getJobDocumentHeaderProvider.notifier).state;

    List<DOCUMENT_HEADER> header = headerData
        .where((element) => element.objectStatus == ObjectStatus.add)
        .toList();
   if(kIsWeb) {
      await PAHelper.addDocumentInSyncMode(context, header);
    }
else{
     await PAHelper.addDocumentInAsyncMode(context, header);
   }
    await newCreationOfJob();
  }

  Future<void> newCreationOfJob() async {
    final faultHeaderData = ref.watch(faultHeaderProvider.notifier);
    FAULT_HEADER faultHeader = faultHeaderData.state;
    final jobHeader = ref.watch(jobHeaderProvider.notifier).state;
    final jobDescription = ref.watch(jobDescriptionProvider.notifier).state;
    final jobLongText = ref.watch(jobLongTextProvider.notifier).state;
    final priority = ref.watch(jobPriorityProvider.notifier).state;
    final jobType = ref.watch(jobTypeProvider.notifier).state;
    final jobStartOn = ref.watch(jobStartOnProvider.notifier).state;
    final jobEndOn = ref.watch(jobEndOnProvider.notifier).state;

    final jobAssignedTo = ref.watch(jobAssignedToProvider.notifier).state;

    jobHeader.description = jobDescription;
    jobHeader.details = jobLongText;
    jobHeader.priority = priority.priority_code;
    jobHeader.job_type = jobType.job_type;
    jobHeader.start_date = jobStartOn;
    if (jobEndOn != 0) {
      jobHeader.end_date = jobEndOn;
    }
    if (jobAssignedTo.isNotEmpty) {
      jobHeader.assigned_to = jobAssignedTo;
    }
    await AppDatabaseManager()
        .update(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));
    JOB_ACTION? jobAction =
        await DbHelper.getJobActionByJobId(jobHeader.job_id.toString());
    if (jobAction != null) {
      await AppDatabaseManager()
          .update(DBInputEntity(JOB_ACTION.TABLE_NAME, jobAction.toJson()));
    } else {
      JOB_ACTION newAction = JOB_ACTION(
          job_id: jobHeader.job_id, user_action: Constants.JOB_STATE_OSNO);
      newAction.fid = jobHeader.lid;
      await AppDatabaseManager()
          .insert(DBInputEntity(JOB_ACTION.TABLE_NAME, newAction.toJson()));
      if (jobHeader.assigned_to != null && jobHeader.assigned_to != '') {
        JOB_ACTION? jobAction =
            await DbHelper.getJobActionByJobId(jobHeader.job_id.toString());
        if (jobAction != null) {
          jobAction.user_action = Constants.JOB_STATE_ORAS;
          await AppDatabaseManager()
              .update(DBInputEntity(JOB_ACTION.TABLE_NAME, jobAction.toJson()));
        }
      }
    }
    Result? result;
    if (!kIsWeb) {
      result = await PAHelper.addOrModifyJobInAsyncMode(context, jobHeader);
    } else {
      result = await PAHelper.addOrModifyJobInSyncMode(context, jobHeader);
      Navigator.pop(context);
      final jobHeaderList = ref.watch(jobHeaderListProvider);
      final filteredJobHeader =
          ref.watch(filteredJobCreationHeaderListProvider);
      final isFiltering = filteredJobHeader != jobHeaderList;
      final displayedList = isFiltering ? filteredJobHeader : jobHeaderList;

      displayedList.sort((a, b) {
        DateTime dateA = convertToDateTime(a.start_date.toString());
        DateTime dateB = convertToDateTime(b.start_date.toString());
        return dateA.compareTo(dateB);
      });

      // find index for selction job in a list

      int index = displayedList
          .indexWhere((element) => element.job_id == jobHeader.job_id);

      if (result.body != null) {
        widget.onTap(InteractiveItemModel(
            type: "JOB_HEADER", data: {"fromFault": false, "index": index}));
      }
    }
    if (!kIsWeb) {
      faultHeader.job_id = jobHeader.job_id;
      await AppDatabaseManager()
          .update(DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));

      Navigator.pop(context);
      Navigator.pop(context, jobHeader.job_id);
    }
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
/*    jobHeader.status = Constants.JOB_STATE_OSNO;
    await AppDatabaseManager()
        .update(DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()));*/
    JOB_HEADER? jobsData =
        await DbHelper.getJobHeaderByFautlId(faultHeader.fault_id.toString());
    if (jobsData != null) {
      faultHeader.status = Constants.FAULT_STATE_ORAS;
      await AppDatabaseManager()
          .update(DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));
    }
    List jobist = await AppDatabaseManager()
        .select(DBInputEntity(JOB_HEADER.TABLE_NAME, {}));
    if (jobist.isNotEmpty) {}
    await ref.read(jobHeaderListProvider.notifier).fetchJobHeaderList(plant);
    final jobHeaderDatata = ref.read(jobHeaderListProvider);
    final filteredJobTypeNotifier =
        ref.read(filteredJobCreationHeaderListProvider.notifier);
    final search = ref.read(searchTextJobProvider.notifier).state;
    final filterOfJobType = ref.read(filterOfJobTypeProvider.notifier).state;
    final filterOfJobTypeCode =
        ref.read(filterOfJobTypeCodeProvider.notifier).state;
    final filterOfPriority = ref.read(filterOfPriorityProvider.notifier).state;
    final filterOfPriorityCode =
        ref.read(filterOfPriorityCodeProvider.notifier).state;
    final statusFilter = ref.read(statusFilterProvider.notifier).state;
    final statusTypeFilter = ref.read(statusTypeFilterProvider.notifier).state;
    final selectedDate = ref.read(selectedCalendarDateJobProvider);
    if ((search != '') ||
        filterOfJobType.isNotEmpty ||
        filterOfJobTypeCode.isNotEmpty ||
        filterOfPriorityCode.isNotEmpty ||
        filterOfPriority.isNotEmpty ||
        statusTypeFilter.isNotEmpty ||
        statusFilter.isNotEmpty ||
        selectedDate != null) {
      await filteredJobTypeNotifier.filteredJobHeaderList(
        typeList: filterOfJobTypeCode,
        priorityList: filterOfPriorityCode,
        statusList: statusTypeFilter,
        type: (search != '') ? AppConstants.search : AppConstants.jobType,
        plantId: plant,
        plantSec: plantSection,
        search: search,
      );
    } else {
      await filteredJobTypeNotifier.filteredJobHeaderList(
          type: 'Initial',
          jobList: jobHeaderDatata,
          plantId: plant,
          plantSec: plantSection);
    }

    final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
    await faultHeaderList.fetchFaultHeaderList(plant);
    final filteredFaultType =
        ref.read(filteredFaultHeaderListProvider.notifier);

    //
    // final faultHeaderDatas = ref.read(faultHeaderListProvider);
    final filterOfFaultType =
        ref.read(filterOfFaultTypeProvider.notifier).state;
    final filterOfFaultCode =
        ref.read(filterOfFaultCodeProvider.notifier).state;
    final filterOfFaultPriorityCode =
        ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
    final filterOfFaultPriority =
        ref.read(filterOfFaultPriorityProvider.notifier).state;
    final statusTypeFaultFilter =
        ref.read(statusTypeFaultFilterProvider.notifier).state;
    final statusFaultFilter =
        ref.read(statusFaultFilterProvider.notifier).state;
    final selectedDateFault = ref.read(selectedCalendarDateProvider);
    final searchFault = ref.read(searchTextProvider.notifier).state;

    if ((searchFault != '') ||
        filterOfFaultType.isNotEmpty ||
        filterOfFaultCode.isNotEmpty ||
        filterOfFaultPriorityCode.isNotEmpty ||
        filterOfFaultPriority.isNotEmpty ||
        statusTypeFaultFilter.isNotEmpty ||
        statusFaultFilter.isNotEmpty ||
        selectedDateFault != null) {
      await filteredFaultType.filteredFaultHeaderList(
          faulttypeList: filterOfFaultCode,
          priorityList: filterOfPriorityCode,
          statusList: statusTypeFilter,
          type: (searchFault != '')
              ? AppConstants.search
              : AppConstants.faultType,
          plantId: plant,
          plantSec: plantSection,
          search: searchFault);
    } else {
      await filteredFaultType.filteredFaultHeaderList(
          type: 'Initial',
          faultList: faultHeaderList.state,
          // faultList: faultHeaderDatas,
          plantId: plant,
          plantSec: plantSection);
    }

/*    if (widget.fromFault) {
      Navigator.pop(context);
    }*/
    ref.read(bottomNavIndexProvider.notifier).state = 3;
  }

  bool areJsonEqual(Map<String, dynamic> json1, Map<String, dynamic> json2) {
    return const DeepCollectionEquality().equals(json1, json2);
  }
}
