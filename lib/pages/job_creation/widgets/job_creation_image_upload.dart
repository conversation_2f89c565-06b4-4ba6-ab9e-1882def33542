import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/DOCUMENT_ATTACHMENT.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:rounds/be/JOBTYPE_HEADER.dart';
import 'package:rounds/be/JOB_HEADER.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/pages/job_creation/tabs/edit_job_creation_field_provider.dart';
import 'package:rounds/providers/job_creation/job_header_provider.dart';
import 'package:rounds/utils/constants.dart';
import 'package:rounds/utils/utils.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../../be/JOB_DOCUMENT.dart';
import '../../../helpers/db_helper.dart';
import '../../../providers/attachments/attachment_provider.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

import '../../../providers/upload_progress_provider.dart';
import '../../../widgets/round_attachment_picker.dart';

class JobCreationFileOrImageUploadWidget extends ConsumerStatefulWidget {
  @override
  _JobCreationFileOrImageUploadWidgetState createState() =>
      _JobCreationFileOrImageUploadWidgetState();
}

class _JobCreationFileOrImageUploadWidgetState
    extends ConsumerState<JobCreationFileOrImageUploadWidget> {
  static const sourceClass = 'JobCreationFileOrImageUploadWidget';
  bool isHovered = false;
  double containerHeightOfImage = 80;
  int selectedImageIndex = -1;
  int hoveredIndices = -1;

  bool isEditAttachments() {
    JOB_HEADER job_header = ref.watch(jobHeaderProvider);
    if (job_header.status == Constants.JOB_STATE_NOCO) {
      return false;
    } else {
      return true;
    }
  }

  Widget attachmentWidget(BuildContext context) {
    final jobDocument = ref.watch(getJobDocumentProvider.notifier).state;
    final editProvider = ref.watch(editJobCreationProvider.notifier);
    final documentAttachments =
        ref.watch(documentAttachmentProvider.notifier).state;
    List<DOCUMENT_HEADER> headers = jobDocument.isNotEmpty ? jobDocument : [];

    Set<String> headerIds = headers.map((header) => header.lid).toSet();

    List<DOCUMENT_ATTACHMENT> attachmentList = documentAttachments
        .where((attachment) => headerIds.contains(attachment.fid))
        .toList();
/*    List<DOCUMENT_ATTACHMENT> attachmentList =
        documentAttachments.where((attachment) {
      return headers.any((header) => header.lid == attachment.fid);
    }).toList();*/

    return RoundsAttachmentPicker(
      isAddButtonRequired: isEditAttachments(),
      onAttachmentPicked: (result) async {
        final jobHeader = ref.watch(jobHeaderProvider.notifier).state;
        if (result != null &&
            (kIsWeb
                ? result.files.single.bytes != null
                : result.files.single.path != null)) {
          for (var data in result.files) {
            String? docId = generate32BitDocId();
            String fileName = data.name;
            String fileDocType = fileName.split('.').last.toUpperCase();
            String extension = fileName.split('.').last.toLowerCase();
            String base64String = '';
            Uint8List? fileBytes;
            String filePath = (kIsWeb ? '' : data.path) ?? ''; // Mobile only
            if (kIsWeb) {
              fileBytes = data.bytes;
            } else {
              if (data.path != null) {
                final File file = File(data.path!);
                fileBytes = await file.readAsBytes();
              }
            }

            if (fileBytes != null) {
              // if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
              //     .contains(extension)) {
              //   base64String = base64Encode(fileBytes);
              // } else if (['mp4', 'mov', 'avi', 'mkv', 'flv', 'wmv', 'webm']
              //     .contains(extension)) {
              //   final byteData =
              //       await rootBundle.load('assets/icon/task_icons/video.png');
              //   fileBytes = byteData.buffer.asUint8List();
              //   base64String = base64Encode(fileBytes);
              // } else if (extension == 'pdf') {
              //   final byteData =
              //       await rootBundle.load('assets/icon/task_icons/pdff.png');
              //   fileBytes = byteData.buffer.asUint8List();
              //   base64String = base64Encode(fileBytes);
              // }
              base64String = base64Encode(fileBytes);
            }

            DOCUMENT_HEADER documentHeader = DOCUMENT_HEADER(
              doc_id: docId,
              doc_type: fileDocType,
              file_name: fileName,
              title: fileName,
              mime_type: 'application/${fileDocType.toLowerCase()}',
            );

            if (kIsWeb) {
              DbHelper()
                  .saveAttachmentinIndexDbByUid(docId ?? "", base64String);
            }

            List list1 = await AppDatabaseManager().select(DBInputEntity(
                DOCUMENT_HEADER.TABLE_NAME, {})
              ..setWhereClause("${DOCUMENT_HEADER.FIELD_DOC_ID} = '$docId'"));

            if (list1.isEmpty) {
              await DbHelper.insertDocumentsHeader(documentHeader);
            } else {
              await DbHelper.updateDocumentsHeader(documentHeader);
            }

            DOCUMENT_ATTACHMENT documentAttachment =
                DOCUMENT_ATTACHMENT(uid: '');
            documentAttachment.fid = documentHeader.lid;
            documentAttachment.uid = docId;
            documentAttachment.local_path = filePath;
            documentAttachment.file_name = fileName;
            documentAttachment.mime_type =
                'application/${fileDocType.toLowerCase()}';
            documentAttachment.external_url = "";
            documentAttachment.url_requires_auth = "";
            documentAttachment.attachment_status =
                AttachmentStatusSavedForUpload;

            List list2 =
                await DbHelper.getDocumentAttachmentsByFid(documentHeader.lid);
            if (list2.isEmpty) {
              await DbHelper.insertDocumentAttachment(documentAttachment);
            } else {
              await DbHelper.updateDocumentAttachment(documentAttachment);
            }

            final faultHeaderExists = await AppDatabaseManager().select(
                DBInputEntity(JOB_HEADER.TABLE_NAME, {})
                  ..setWhereClause("FAULT_ID = '${jobHeader.fault_id}'"));
                  
           if (faultHeaderExists.isEmpty && ScreenType.desktop !=  UIHelper().getScreenType(context)) {
              // Insert FAULT_HEADER first
              await AppDatabaseManager().insert(
                DBInputEntity(JOB_HEADER.TABLE_NAME, jobHeader.toJson()),
              );
            }

            JOB_DOCUMENT job_document = JOB_DOCUMENT(
                job_id: jobHeader.job_id, doc_id: documentAttachment.uid);
            job_document.fid = jobHeader.lid;

            await AppDatabaseManager().insert(
                DBInputEntity(JOB_DOCUMENT.TABLE_NAME, job_document.toJson()));
            final jobDocumentHeaders =
                ref.watch(getJobDocumentHeaderProvider.notifier);
            await jobDocumentHeaders
                .getJobDocumentsHeaders(jobHeader.job_id.toString());

            final jobDocument = ref.watch(getJobDocumentProvider.notifier);
            await jobDocument.getJobDocuments(jobHeader.job_id.toString());
            selectedImageIndex = jobDocument.state.length;
              ref.read(documentHeaderProvider.notifier).fetchDocumentHeaders();
              ref
                  .read(documentAttachmentProvider.notifier)
                  .fetchDocumentAttachments();
            setState(() {
              
            });

            /* uploadFile(ref, documentAttachment.lid);*/
          }
        }
      },
      viewType: RoundsAttachmentPickerViewType.grid,
      attachments: attachmentList.asMap().entries.map((e) {
        final uploadProgress =
            ref.watch(jobUploadProgressProvider)[e.key] ?? 1.0;
        return DocumentAttachmentContainer(
          attachment: e.value,
          isFromPlanner: e.value.objectStatus == ObjectStatus.global,
          isUploading: uploadProgress < 1.0,
          uploadProgress: uploadProgress,
          onDelete: (value) {
            removeFile(document_attachment: e.value, index: e.key);
          },
          onTap: (value) {},
        );
      }).toList(),
    );
  }

  String? generate32BitDocId() {
    var uuid = const Uuid();
    return uuid.v4();
  }

/*
  Future<void> uploadFile(WidgetRef ref, String index) async {
    final uploadProgressNotifier = ref.read(jobUploadProgressProvider.notifier);
    uploadProgressNotifier.startUpload(index);

    for (double progress = 0.0; progress <= 1.0; progress += 0.1) {
      await Future.delayed(Duration(milliseconds: 500));
      uploadProgressNotifier.updateProgress(index, progress);
    }

    uploadProgressNotifier.finishUpload(index);
  }
*/

  void removeFile(
      {required DOCUMENT_ATTACHMENT document_attachment,
      required int index}) async {
    try {
      final jobHeader = ref.watch(jobHeaderProvider.notifier).state;
      final documentHeaders = ref.watch(documentHeaderProvider);

      DOCUMENT_HEADER? attachmentHeader;
      for (var element in documentHeaders) {
        if (element.lid == document_attachment.fid) {
          attachmentHeader = element;
          break;
        }
      }

      if (attachmentHeader == null) {
        Logger.logError(sourceClass, 'removeFile',
            'No DOCUMENT_HEADER found for fid: ${document_attachment.fid}');
        return;
      }

      if (kIsWeb) {
        await DbHelper()
            .deleteAttachmentFromIndexDbForUid(attachmentHeader.doc_id ?? "");
      }

      await AppDatabaseManager().delete(
          DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, attachmentHeader.toJson()));
      await AppDatabaseManager().delete(DBInputEntity(
          DOCUMENT_ATTACHMENT.TABLE_NAME, document_attachment.toJson()));

      List<JOB_DOCUMENT> docList = await DbHelper.getJobDocuments();
      JOB_DOCUMENT doc = docList
          .where((element) => element.doc_id == attachmentHeader!.doc_id)
          .toList()
          .first;
      await AppDatabaseManager().execute(
          'DELETE FROM ${JOB_DOCUMENT.TABLE_NAME} WHERE ${JOB_DOCUMENT.FIELD_DOC_ID} = "${doc.doc_id}"');
      final jobDocument = ref.watch(getJobDocumentProvider.notifier);
      await jobDocument.getJobDocuments(jobHeader.job_id.toString());
      setState(() {
        isHovered = false;
        hoveredIndices = -1;
      });
    } catch (e) {
      Logger.logError(sourceClass, 'removeFile', e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Column(
        children: [attachmentWidget(context)],
      ),
    );
  }
}
