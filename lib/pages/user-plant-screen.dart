import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/Logger.dart';
import 'package:multi_select_flutter/util/multi_select_item.dart';
import 'package:rounds/be/APP_SETTING_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';
import 'package:rounds/helpers/pa_helper.dart';
import 'package:rounds/pages/dashboard/dashboard.dart';
import 'package:rounds/pages/widgets/custom_text_widget.dart';
import 'package:rounds/pages/widgets/multi_select_dropdown.dart';
import 'package:rounds/providers/fault/fault_header_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shimmer/shimmer.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../be/SHIFT_HEADER.dart';
import '../helpers/ui_helper.dart';
import '../services/app_notifier.dart';
import '../utils/app_colors.dart';
import '../utils/platform_details.dart';

import '../widgets/custom_icon_button.dart';
import 'downloadDataPage.dart';

class UserPlantScreen extends ConsumerStatefulWidget {
  const UserPlantScreen({super.key});

  @override
  ConsumerState<UserPlantScreen> createState() => _UserPlantScreenState();
}

class _UserPlantScreenState extends ConsumerState<UserPlantScreen> {
  SharedPreferences? preferences;
  String selectedPlant = '';

  String selectedShift = '';
  late Future<bool> selected;
  bool plantMandatory = false;
  bool plantSectionMandatory = false;
  bool shiftMandatory = false;

  late Future<SharedPreferences> fetchPrefereces;

  @override
  void initState() {
    fetchPrefereces = getPreferences();
    Future.delayed(Duration.zero).then((value) async {
      if (mounted) {
        await ref.read(plantListProvider.notifier).fetchPlantsList();
      }
    });
    AppNotifier().notifyInfoMessages((data) async {
      if (data[EventSyncStatusFieldType] ==
          EventSyncStatusTypeInboxProcessingComplete) {
        if (mounted) {
          await ref.read(plantListProvider.notifier).fetchPlantsList();
        }
      }
    });
    AppNotifier().notifySyncStatus((data) async {
      if (data[EventSyncStatusFieldType] ==
          EventSyncStatusTypeInboxProcessingComplete) {
        if (mounted) {
          await ref.read(plantListProvider.notifier).fetchPlantsList();
        }
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    bool isSelected = false;
    final plantsList = ref.watch(plantListProvider);
    return FutureBuilder(
        future: fetchPrefereces,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.connectionState == ConnectionState.done) {
            isSelected = preferences!.getBool("isUserPreferenceSet") ?? false;
          }
          return isSelected
              ? const Dashboard()
              : Scaffold(
                  resizeToAvoidBottomInset: true,
                  backgroundColor: AppColors.scaffoldBackgroundGrey,
                  body: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal:
                                  PlatformDetails.isMobileScreen(context)
                                      ? 20
                                      : MediaQuery.of(context).size.width *
                                          0.20,
                              vertical: 5),
                          child: Padding(
                            padding: const EdgeInsets.only(
                                left: 25, right: 25, top: 25, bottom: 10),
                            child: AnimatedSize(
                              duration: const Duration(milliseconds: 500),
                              curve: Curves.easeInOut,
                              child: AnimatedSwitcher(
                                duration: const Duration(milliseconds: 300),
                                transitionBuilder: (Widget child,
                                    Animation<double> animation) {
                                  return FadeTransition(
                                    opacity: animation,
                                    child: child,
                                  );
                                },
                                child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      CustomHeadingText(
                                        text: AppLocalizations.of(context)!
                                            .select_preference,
                                      ),
                                      const SizedBox(
                                        height: 10,
                                      ),
                                      plantsList.isNotEmpty
                                          ? Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                  UIHelper.buildLabelAndValueAsWidgetOfUserPreferenceDropDown(
                                                      label:
                                                          AppLocalizations.of(
                                                                  context)!
                                                              .plant,
                                                      value: getPlantDropdown(),
                                                      isTextFieldRequiredAsValueWidget:
                                                          true),
                                                  plantMandatory
                                                      ? getError('plant')
                                                      : Container(),
                                                  const SizedBox(height: 16),
                                                  UIHelper.buildLabelAndValueAsWidgetOfUserPreferenceDropDown(
                                                      label:
                                                          AppLocalizations.of(
                                                                  context)!
                                                              .plantSection,
                                                      value:
                                                          getPlantSectionDropdown(),
                                                      isTextFieldRequiredAsValueWidget:
                                                          true),
                                                  plantSectionMandatory
                                                      ? getError(
                                                          'plant sections')
                                                      : Container(),
                                                  const SizedBox(height: 16),
                                                  UIHelper.buildLabelAndValueAsWidgetOfUserPreferenceDropDown(
                                                      label:
                                                          AppLocalizations.of(
                                                                  context)!
                                                              .shift,
                                                      value: getShiftDropdown(),
                                                      isTextFieldRequiredAsValueWidget:
                                                          true),
                                                  shiftMandatory
                                                      ? getError('shift')
                                                      : Container(),
                                                  const SizedBox(height: 16),
                                                  Align(
                                                    alignment:
                                                        Alignment.centerRight,
                                                    child: CustomIconButton(
                                                      // title: context.locale.login,
                                                      isLoading: false,
                                                      ontap: () {
                                                        validateData(context);
                                                      },
                                                    ),
                                                  )
                                                ])
                                          : buildShimmerList()
                                    ]),
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                  ));
        });
  }

  Widget buildShimmerList() {
    return Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Padding(
          padding: UIHelper.columnFieldOnlyVerticalPadding10(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 10,
              ),
              getEmptyContainer(),
              SizedBox(
                height: 20,
              ),
              getEmptyContainer(),
              SizedBox(
                height: 20,
              ),
              getEmptyContainer(),
            ],
          ),
        ));
  }

  getEmptyContainer() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          color: Colors.white,
          child: Text(
            '',
            style: UIHelper.labelStyle(),
          ),
        ),
        SizedBox(
          height: 10,
        ),
        Container(
            width: double.infinity,
            color: Colors.white,
            child: getEmptyDropdown()),
      ],
    );
  }

  Widget getEmptyDropdown() {
    final dropdownItems = [].map((option) {
      return DropdownMenuItem<String>(
        value: option,
        child: Padding(
          padding: const EdgeInsets.only(left: 5.0),
          child: Text(option, style: UIHelper.valueStyle()),
        ),
      );
    }).toList();

    if (dropdownItems.where((item) => item.value == '').isEmpty) {
      dropdownItems.insert(
        0,
        DropdownMenuItem<String>(
          value: '',
          child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text('', style: UIHelper.valueStyle()),
          ),
        ),
      );
    }
    return Container(
      decoration: UIHelper.fieldDecoration(),
      child: DropdownButton<String>(
        elevation: 0,
        isExpanded: true,
        underline: const SizedBox(),
        value: '',
        items: dropdownItems,
        onChanged: (newValue) {},
      ),
    );
  }

  Widget getPlantDropdown() {
    final plants = ref.watch(plantListProvider.notifier).state;

    final dropdownItems = plants.map((option) {
      return DropdownMenuItem<String>(
        value: option.plant_id,
        child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(option.plant_id!, style: UIHelper.valueBoldStyle()),
                Padding(
                  padding: const EdgeInsets.only(top: 1.0),
                  child: Text(option.plant_name!,
                      style: UIHelper.descriptionStyle()),
                ),
              ],
            )),
      );
    }).toList();

    if (dropdownItems.where((item) => item.value == '').isEmpty) {
      dropdownItems.insert(
        0,
        DropdownMenuItem<String>(
          value: '',
          child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(AppLocalizations.of(context)!.select,
                style: UIHelper.valueStyle()),
          ),
        ),
      );
    }
    return Container(
      decoration: UIHelper.fieldDecoration(),
      child: DropdownButton<String>(
        elevation: 0,
        isExpanded: true,
        underline: const SizedBox(),
        padding: EdgeInsets.only(right: 10),
        value: selectedPlant != '' ? selectedPlant : '',
        items: dropdownItems,
        onChanged: (newValue) {
          onChangePlant(newValue!);
        },
      ),
    );
  }

  void onChangePlant(String newValue) async {
    final plant = ref.watch(plantProvider.notifier);
    final plantSectionList = ref.watch(plantSectionListProvider.notifier);
    final shiftList = ref.watch(shiftListProvider.notifier);
    if (mounted) {
      setState(() {
        selectedPlant = newValue;
        if (selectedPlant == '' ||
            selectedPlant == AppLocalizations.of(context)!.select) {
          plantMandatory = true;
          selectedPlant = '';
          plant.clearPlant();
          plantSectionList.clearPlantSectionList();
          shiftList.clearShiftList();
        } else {
          plantMandatory = false;
        }
      });
    }
    if (selectedPlant != '' &&
        selectedPlant != AppLocalizations.of(context)!.select) {
      plant.setPlant(selectedPlant);

      try {
        await ref
            .read(plantSectionListProvider.notifier)
            .fetchPlantsSectionList(selectedPlant);
        await ref
            .read(shiftListProvider.notifier)
            .fetchShiftList(selectedPlant);
      } catch (e) {
        Logger.logError('UserPlantScreen', 'onChangePlant', e.toString());
      }
    }
  }

  List<String> selectedPlantSection = [];
  Widget getPlantSectionDropdown() {
    final plant = ref.watch(plantProvider.notifier).state;
    final plantSections = ref
        .watch(plantSectionListProvider.notifier)
        .state
        .where((element) => element.plant_id == plant)
        .toList();
    final plantSection = ref.watch(plantSectionProvider.notifier);

    final dropdownItems = plantSections.map((option) {
      return MultiSelectItem<String>(
        option.section_id!,
        "${option.section_id!} - ${option.description!}",
      );
    }).toList();

    if (dropdownItems.isEmpty) {
      dropdownItems.insert(
        0,
        MultiSelectItem<String>("", ""),
      );
    }
    return MultiSelectDropDown(
      allOptions: plant == "" ? [] : dropdownItems,
      selected: selectedPlantSection,
      absorbing: dropdownItems.isNotEmpty ? false : true,
      buttonText: plant == "" ? "" : AppLocalizations.of(context)!.select,
      onConfirm: plant == ""
          ? (v) {}
          : (v) {
              selectedPlantSection = v;
              if (selectedPlantSection.isEmpty ||
                  selectedPlantSection
                      .contains(AppLocalizations.of(context)!.select)) {
                plantSectionMandatory = true;
              }
              plantSection.setPlantSection(selectedPlantSection);
            },
    );
  }

  Widget getShiftDropdown() {
    final plant = ref.watch(plantProvider.notifier).state;
    final shifts = ref
        .read(shiftListProvider.notifier)
        .state
        .where((element) => element.plant_id == selectedPlant);
    final shift = ref.read(shiftProvider.notifier);

    final dropdownItems = shifts.map((option) {
      return DropdownMenuItem<String>(
        value: option.shift_code,
        child: Padding(
          padding: const EdgeInsets.only(left: 5.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(option.shift_code!, style: UIHelper.valueBoldStyle()),
              Padding(
                padding: const EdgeInsets.only(top: 1.0),
                child: Text(option.shift_name!,
                    style: UIHelper.descriptionStyle()),
              ),
            ],
          ),
        ),
      );
    }).toList();

    if (dropdownItems.where((item) => item.value == '').isEmpty) {
      dropdownItems.insert(
        0,
        DropdownMenuItem<String>(
          value: '',
          child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(AppLocalizations.of(context)!.select,
                style: UIHelper.valueStyle()),
          ),
        ),
      );
    }
    return Container(
      decoration: UIHelper.fieldDecoration(),
      height: 48,
      child: DropdownButton<String>(
        elevation: 0,
        isExpanded: true,
        icon: plant == ''
            ? Padding(
                padding: const EdgeInsets.only(top: 10),
                child: Icon(
                  Icons.arrow_drop_down,
                  color: AppColors.grey,
                ),
              )
            : null,
        underline: const SizedBox(),
        padding: const EdgeInsets.only(right: 10),
        value: selectedShift.isNotEmpty ? selectedShift : '',
        items: plant == '' ? [] : dropdownItems,
        onChanged: (newValue) {
          if (mounted) {
            setState(() {
              selectedShift = newValue!;
              if (selectedShift == '' ||
                  selectedShift == AppLocalizations.of(context)!.select) {
                shiftMandatory = true;
              } else {
                shiftMandatory = false;
              }
            });
          }
          if (selectedPlant.isNotEmpty &&
              selectedPlant != AppLocalizations.of(context)!.select) {
            shift.setShift(selectedShift);
          }
        },
      ),
    );
  }

  validateData(BuildContext context) async {
    bool data = false;
    final plant = ref.watch(plantProvider.notifier);
    final plantSection = ref.watch(plantSectionProvider.notifier);
    final shift = ref.read(shiftProvider.notifier);
    if (plant.state == '') {
      if (mounted) {
        setState(() {
          plantMandatory = true;
        });
      }
      return data;
    } else if (plantSection.state.isEmpty) {
      if (mounted) {
        setState(() {
          plantSectionMandatory = true;
        });
      }
      return data;
    } else if (shift.state == '') {
      if (mounted) {
        setState(() {
          shiftMandatory = true;
        });
      }
      return data;
    } else {
      if (mounted) {
        setState(() {
          plantMandatory = false;
          plantSectionMandatory = false;
          shiftMandatory = false;
        });
      }
      SHIFT_HEADER? shiftHeader =
          await DbHelper.getShiftHeader(selectedShift, selectedPlant);
      String shiftValue = '';
      if (shiftHeader != null) {
        shiftValue = shiftHeader.shift_code.toString();
      }
      preferences!.setString("Plant", selectedPlant);
      preferences!.setStringList("PlantSection", plantSection.state);
      preferences!.setString("Shift", shiftValue);
      preferences!.setBool("isUserPreferenceSet", true);

      Map<String, dynamic> setData = {
        "plantId": selectedPlant,
        "sectionId": selectedPlantSection,
        "shiftCode": shiftValue
      };
      try {
        await PAHelper.setUserPreferenceLogin(context, setData);
        Result result = await PAHelper.getUserPreferenceLogin(context);

        List<APP_SETTING_HEADER> settingsList =
            result.body.entries.map<APP_SETTING_HEADER>((entry) {
          return APP_SETTING_HEADER(
            prop_name: entry.key,
            prop_value: entry.value is List
                ? jsonEncode(entry.value.map((e) => e.toString()).toList())
                : entry.value.toString(),
          );
        }).toList();

        for (var setting in settingsList) {
          await AppDatabaseManager().insert(
              DBInputEntity(APP_SETTING_HEADER.TABLE_NAME, setting.toJson()));
        }
      } catch (e) {
        Logger.logError('UserPlantScreen', 'validateData', e.toString());
        UIHelper.showErrorDialog(context, description: e.toString());
      }
      List<APP_SETTING_HEADER> list = await DbHelper.getAppSettingHeader();
      if (list.isNotEmpty) {
        for (var item in list) {
          if (item.prop_name == 'plantId') {
            plant.setPlant(item.prop_value.toString());
          } else if (item.prop_name == 'sectionId') {
            List<dynamic> decodedList = jsonDecode(item.prop_value.toString());
            List<String> sectionIds =
                decodedList.map((e) => e.toString()).toList();
            plantSection.setPlantSection(sectionIds);
          } else if (item.prop_name == 'shiftCode') {
            shift.setShift(item.prop_value.toString());
          }
        }
      }
      if (!kIsWeb) {
        await SettingsHelper().setFetchInterval(15);
      }
      Navigator.push(context, MaterialPageRoute(builder: (context) {
        return const DownloadDataPage();
      }));
    }
  }

  Future<SharedPreferences> getPreferences() async {
    await ref.read(plantListProvider.notifier).fetchPlantsList();
    preferences = await SharedPreferences.getInstance();
    return preferences!;
  }

  getError(String errorText) {
    return Text(
      '* Please select $errorText',
      style: TextStyle(color: AppColors.redColor),
    );
  }
}
