import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:unvired_settings/utils/app_styles.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../providers/fault/fault_header_provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/app_colors.dart';

class AssignScreen extends ConsumerStatefulWidget {
  const AssignScreen(
      {super.key,
      this.title,
      this.isConfirmButtonInBottom = false,
      this.isSaveButtonForWeb = false,
      this.onNext})
      : assert(!isConfirmButtonInBottom || onNext != null,
            'onNext cannot be null when isConfirmButtonInBottom is true');
  final bool isConfirmButtonInBottom;
  final bool isSaveButtonForWeb;
  final String? title;
  final Function(USER_HEADER)? onNext;
  @override
  ConsumerState<AssignScreen> createState() => _AssignScreenState();
}

class _AssignScreenState extends ConsumerState<AssignScreen> {
  List<USER_HEADER> names = [];
  List<USER_HEADER> filteredNames = [];
  TextEditingController searchController = TextEditingController();

  late int selectedindex;
  @override
  void initState() {
    // TODO: implement initState
    Future.delayed(Duration.zero).then((value) {
      ref.read(usersListProvider.notifier).filter("",
          plantId: ref.read(plantProvider),
          plantSec: ref.read(plantSectionProvider));
      getUsers();
    });
    selectedindex = -1;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    names = ref.watch(usersListProvider);
    return Scaffold(
      backgroundColor: AppColor.whiteColor,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leadingWidth: 30,
        leading: IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.black,
          ),
        ),
        title: Text(
          widget.title ?? AppLocalizations.of(context)!.selectUser,
          style: UIHelper.titleStyle14(),
        ),
        actions: !kIsWeb && !widget.isSaveButtonForWeb
            ? [
                widget.isConfirmButtonInBottom
                    ? const SizedBox()
                    : IconButton(
                        onPressed: () {
                          if (selectedindex < names.length) {
                            Navigator.pop(context, names[selectedindex]);
                          } else {
                            showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (BuildContext dialogContext) {
                                  return AlertDialog(
                                    title: Text(
                                        AppLocalizations.of(context)!.warning),
                                    content: Text(AppLocalizations.of(context)!
                                        .pleaseSelectUser),
                                    actions: [
                                      TextButton(
                                          onPressed: () {
                                            Navigator.pop(context);
                                          },
                                          child: Text(
                                              AppLocalizations.of(context)!.ok))
                                    ],
                                  );
                                });
                          }
                        },
                        icon: const Icon(
                          Icons.done,
                          color: Colors.black,
                        ))
              ]
            : [],
      ),
      body: SafeArea(
          child: Column(
        children: [
          Container(
            height: 60,
            padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
            child: TextField(
              textAlign: TextAlign.start,
              controller: searchController,
              onChanged: (value) {
                ref
                    .read(usersListProvider.notifier)
                    .filter(value, plantId: plant, plantSec: plantSection);
              },
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context)!.search,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 5.0, vertical: 5.0),
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: AppColors.black),
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.white,
                suffixIcon: IconButton(
                  icon: const Icon(Icons.cancel),
                  onPressed: () {
                    setState(() {
                      ref
                          .read(usersListProvider.notifier)
                          .filter("", plantId: plant, plantSec: plantSection);
                      searchController.clear();
                    });
                    // _animationController.reverse();
                  },
                ),
              ),
            ),
          ),
          names.isNotEmpty
              ? Expanded(
                  child: ListView.builder(
                      itemCount: names.length,
                      itemBuilder: (context, index) {
                        return InkWell(
                            onTap: () {
                              setState(() {
                                selectedindex = index;
                              });
                            },
                            child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 10.0, vertical: 5),
                                child: Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: Colors.transparent, width: 1.0),
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: selectedindex == index
                                        ? AppColors.primaryColor
                                        : AppColor.whiteColor,
                                  ),
                                  child: Row(
                                    children: [
                                      CircleAvatar(
                                        backgroundColor: Colors.primaries[
                                            names[index]
                                                    .first_name!
                                                    .toUpperCase()
                                                    .codeUnitAt(0) %
                                                Colors.primaries.length],
                                        radius: 15,
                                        child: const Icon(
                                          Icons.account_circle,
                                          color: Colors.white,
                                          size: 29,
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Text(
                                          "${toCamelCase(names[index].first_name!)} ${toCamelCase(names[index].last_name!)}",
                                          style: TextStyle(
                                              color: selectedindex == index
                                                  ? AppColor.whiteColor
                                                  : null,
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500),
                                        ),
                                      ),
                                    ],
                                  ),
                                )));
                      }),
                )
              : Center(
                  child: Text(AppLocalizations.of(context)!
                      .no_user_found_with_searched_value),
                ),
        ],
      )),
      bottomNavigationBar: widget.isSaveButtonForWeb
          ? Padding(
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    if (selectedindex < names.length) {
                      Navigator.pop(context, names[selectedindex]);
                    } else {
                      showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (BuildContext dialogContext) {
                            return AlertDialog(
                              title:
                                  Text(AppLocalizations.of(context)!.warning),
                              content: Text(AppLocalizations.of(context)!
                                  .pleaseSelectUser),
                              actions: [
                                TextButton(
                                    onPressed: () {
                                      Navigator.pop(context);
                                    },
                                    child:
                                        Text(AppLocalizations.of(context)!.ok))
                              ],
                            );
                          });
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                  ),
                  child: Text(AppLocalizations.of(context)!.save),
                ),
              ),
            )
          : widget.isConfirmButtonInBottom
              ? Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryColor,
                          ),
                          child: Text(AppLocalizations.of(context)!.back),
                        ),
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: selectedindex < names.length
                              ? () {
                                  if (selectedindex < names.length) {
                                    widget.onNext!(names[selectedindex]);
                                  } else {
                                    showDialog(
                                        context: context,
                                        barrierDismissible: false,
                                        builder: (BuildContext dialogContext) {
                                          return AlertDialog(
                                            title: Text(
                                                AppLocalizations.of(context)!
                                                    .warning),
                                            content: Text(
                                                AppLocalizations.of(context)!
                                                    .pleaseSelectUser),
                                            actions: [
                                              TextButton(
                                                  onPressed: () {
                                                    Navigator.pop(context);
                                                  },
                                                  child: Text(
                                                      AppLocalizations.of(
                                                              context)!
                                                          .ok))
                                            ],
                                          );
                                        });
                                  }
                                }
                              : () {},
                          style: ElevatedButton.styleFrom(
                              backgroundColor: selectedindex < names.length
                                  ? AppColors.primaryColor
                                  : Colors.grey),
                          child: Text(AppLocalizations.of(context)!.next),
                        ),
                      )
                    ],
                  ),
                )
              : const SizedBox(),
    );
  }

  String toCamelCase(String input) {
    if (input.isEmpty) return '';
    return input[0].toUpperCase() + input.substring(1).toLowerCase();
  }

  getUsers() async {
    final plant = ref.read(plantProvider);
    final plantSection = ref.read(plantSectionProvider);
    // names = await DbHelper.getUsersData(plant, plantSection);
    names = await DbHelper.getUsersData(plant, plantSection);
    setState(() {
      selectedindex = names.length;
    });
  }
}
