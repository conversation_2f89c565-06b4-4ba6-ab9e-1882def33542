import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/models/intractive_Item_Model.dart';
import 'package:rounds/pages/fault/widgets/fault_card.dart';
import 'package:rounds/providers/fault/fault_header_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/utils.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../be/FAULT_HEADER.dart';
import '../../utils/app_constants.dart';
import 'fault_filter_provider.dart';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart';

class FaultList extends ConsumerStatefulWidget {
  const FaultList(
      {super.key,
      required this.scrollController,
      required this.faultDetailViewNotifier,
      required this.onItemTap});
  final ScrollController scrollController;
  final Function(InteractiveItemModel) onItemTap;
  final ValueNotifier<InteractiveItemModel?> faultDetailViewNotifier;

  @override
  ConsumerState<FaultList> createState() => _FaultListState();
}

class _FaultListState extends ConsumerState<FaultList> {
  @override
  Widget build(BuildContext context) {
    final faultHeader = ref.watch(faultHeaderListProvider);
    final filteredFaultHeader = ref.watch(filteredFaultHeaderListProvider);

    final isFiltering = filteredFaultHeader != faultHeader;
    //final displayedList = isFiltering ? filteredFaultHeader : faultHeader;

    final displayedFaults = kIsWeb ? (isFiltering ? filteredFaultHeader : faultHeader) :(filteredFaultHeader.isNotEmpty ? filteredFaultHeader : faultHeader);
    final List<FAULT_HEADER> displayedList = List<FAULT_HEADER>.from(displayedFaults);
   /* displayedList.sort((a, b) {
      DateTime dateA = convertToDateTime(a.reported_on.toString());
      DateTime dateB = convertToDateTime(b.reported_on.toString());
      return dateA.compareTo(dateB);
    });*/

    return Container(
      color: AppColors.modernListBackground,
      child: displayedList.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.search_off,
                    size: 64,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No Faults Found',
                    style: UIHelper.modernTitleStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: AppColors.modernPrimaryText,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Try adjusting your search or filters\nto find what you\'re looking for',
                    textAlign: TextAlign.center,
                    style: UIHelper.modernBodyStyle(
                      fontSize: 14,
                      color: AppColors.modernSecondaryText,
                    ),
                  ),
                ],
              ),
            )
          : SlidableAutoCloseBehavior(
              child: ListView.builder(
                controller: widget.scrollController,
                shrinkWrap: true,
                physics: const AlwaysScrollableScrollPhysics(),
                itemCount: displayedList.length,
                itemBuilder: (context, index) {
                  final element = displayedList[index];
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    child: ValueListenableBuilder(
                      valueListenable: widget.faultDetailViewNotifier,
                      builder: (context, value, child) {
                        return FaultCard(
                          index: index,
                          color: (UIHelper().getScreenType(context) ==
                                      ScreenType.desktop &&
                                  value?.getValue("index") == index)
                              ? AppColors.primaryColor.withOpacity(0.05)
                              : AppColors.modernCardBackground,
                          item: element,
                          onTap: (value) {
                            widget.faultDetailViewNotifier.value = value;
                            widget.onItemTap(value);
                          },
                        );
                      },
                    ),
                  );
                },
              ),
            ),
    );
  }

  void sortFaultHeaders(List<FAULT_HEADER> faultHeaders) {
    DateTime now = DateTime.now();
    String today = DateFormat('yyyyMMdd').format(now);

    faultHeaders.sort((a, b) {
      DateTime dateA = DateFormat('yyyyMMdd').parse(a.reported_on.toString());
      DateTime dateB = DateFormat('yyyyMMdd').parse(b.reported_on.toString());
      if (a.reported_on.toString() == today) return -1;
      if (b.reported_on.toString() == today) return 1;

      return dateA.compareTo(dateB);
    });
  }
}
