import 'package:flutter/material.dart';
import '../../../helpers/ui_helper.dart';
import 'modern_filter_panel.dart';

class ModernFilterButton extends StatefulWidget {
  final List<String> selectedFaultTypes;
  final List<String> selectedPriorities;
  final List<String> selectedStatuses;
  final Function(List<String>) onFaultTypesChanged;
  final Function(List<String>) onPrioritiesChanged;
  final Function(List<String>) onStatusesChanged;

  const ModernFilterButton({
    Key? key,
    required this.selectedFaultTypes,
    required this.selectedPriorities,
    required this.selectedStatuses,
    required this.onFaultTypesChanged,
    required this.onPrioritiesChanged,
    required this.onStatusesChanged,
  }) : super(key: key);

  @override
  State<ModernFilterButton> createState() => _ModernFilterButtonState();
}

class _ModernFilterButtonState extends State<ModernFilterButton> {
  OverlayEntry? _overlayEntry;
  final GlobalKey _buttonKey = GlobalKey();

  bool get _areFiltersActive {
    return widget.selectedFaultTypes.isNotEmpty ||
           widget.selectedPriorities.isNotEmpty ||
           widget.selectedStatuses.isNotEmpty;
  }

  int get _activeFilterCount {
    return widget.selectedFaultTypes.length +
           widget.selectedPriorities.length +
           widget.selectedStatuses.length;
  }

  void _showFilterPanel() {
    if (_overlayEntry != null) return;

    final RenderBox renderBox = _buttonKey.currentContext!.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: offset.dy + size.height + 8, // mt-2
        right: MediaQuery.of(context).size.width - offset.dx - size.width, // Align to right edge of button
        child: Material(
          color: Colors.transparent,
          child: ModernFilterPanel(
            selectedFaultTypes: widget.selectedFaultTypes,
            selectedPriorities: widget.selectedPriorities,
            selectedStatuses: widget.selectedStatuses,
            onFaultTypesChanged: widget.onFaultTypesChanged,
            onPrioritiesChanged: widget.onPrioritiesChanged,
            onStatusesChanged: widget.onStatusesChanged,
            onClearAll: _handleClearAll,
            onClose: _hideFilterPanel,
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideFilterPanel() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _handleClearAll() {
    widget.onFaultTypesChanged([]);
    widget.onPrioritiesChanged([]);
    widget.onStatusesChanged([]);
    _hideFilterPanel();
  }

  @override
  void dispose() {
    _hideFilterPanel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      key: _buttonKey,
      onTap: () {
        if (_overlayEntry != null) {
          _hideFilterPanel();
        } else {
          _showFilterPanel();
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8), // More compact
        decoration: BoxDecoration(
          border: Border.all(
            color: _areFiltersActive 
                ? const Color(0xFF2563EB) // brand-blue
                : const Color(0xFFD1D5DB), // gray-300
          ),
          borderRadius: BorderRadius.circular(20), // rounded-full
          color: _areFiltersActive 
              ? const Color(0xFFE0E7FF) // brand-blue-light
              : Colors.white,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.filter_list,
              size: 14, // Smaller icon
              color: _areFiltersActive
                  ? const Color(0xFF2563EB) // brand-blue
                  : const Color(0xFF374151), // gray-700
            ),
            const SizedBox(width: 6), // Reduced spacing
            Text(
              'Filter',
              style: UIHelper.modernBodyStyle(
                fontSize: 13, // Smaller text
                fontWeight: FontWeight.w500,
                color: _areFiltersActive
                    ? const Color(0xFF2563EB) // brand-blue
                    : const Color(0xFF374151), // gray-700
              ),
            ),
            if (_areFiltersActive) ...[
              const SizedBox(width: 6), // Reduced spacing
              Container(
                width: 16, // Smaller badge
                height: 16,
                decoration: const BoxDecoration(
                  color: Color(0xFF2563EB), // brand-blue
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    _activeFilterCount.toString(),
                    style: UIHelper.modernBodyStyle(
                      fontSize: 10, // Smaller text
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
