import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/providers/fault/fault_header_provider.dart';
import 'package:rounds/providers/user_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// Modern User Selection Modal Widget
/// Follows the design patterns from the React Native app with modern UI controls
class ModernUserSelectionModal extends ConsumerStatefulWidget {
  final String title;
  final Function(USER_HEADER) onUserSelected;
  final VoidCallback onClose;

  const ModernUserSelectionModal({
    Key? key,
    required this.title,
    required this.onUserSelected,
    required this.onClose,
  }) : super(key: key);

  @override
  ConsumerState<ModernUserSelectionModal> createState() => _ModernUserSelectionModalState();
}

class _ModernUserSelectionModalState extends ConsumerState<ModernUserSelectionModal> {
  final TextEditingController _searchController = TextEditingController();
  USER_HEADER? _selectedUser;
  List<USER_HEADER> _users = [];
  List<USER_HEADER> _filteredUsers = [];

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadUsers() {
    final plant = ref.read(plantProvider);
    final plantSection = ref.read(plantSectionProvider);
    
    ref.read(usersListProvider.notifier).filter("",
        plantId: plant, plantSec: plantSection);
  }

  void _filterUsers(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredUsers = List.from(_users);
      } else {
        _filteredUsers = _users.where((user) {
          final fullName = '${user.first_name ?? ''} ${user.last_name ?? ''}'.toLowerCase();
          return fullName.contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  void _selectUser(USER_HEADER user) {
    setState(() {
      _selectedUser = user;
    });
  }

  void _confirmSelection() {
    if (_selectedUser != null) {
      widget.onUserSelected(_selectedUser!);
    }
  }

  // Generate avatar colors based on user name
  Color _getAvatarColor(String name) {
    final colors = [
      const Color(0xFF3B82F6), // Blue
      const Color(0xFF10B981), // Green
      const Color(0xFFF59E0B), // Yellow
      const Color(0xFF8B5CF6), // Purple
      const Color(0xFFEF4444), // Red
      const Color(0xFF06B6D4), // Cyan
      const Color(0xFFF97316), // Orange
    ];
    
    final index = name.isNotEmpty ? name.codeUnitAt(0) % colors.length : 0;
    return colors[index];
  }

  String _getUserInitials(USER_HEADER user) {
    final firstName = user.first_name ?? '';
    final lastName = user.last_name ?? '';
    
    String initials = '';
    if (firstName.isNotEmpty) initials += firstName[0].toUpperCase();
    if (lastName.isNotEmpty) initials += lastName[0].toUpperCase();
    
    return initials.isNotEmpty ? initials : '?';
  }

  @override
  Widget build(BuildContext context) {
    _users = ref.watch(usersListProvider);
    
    // Update filtered users when users list changes
    if (_filteredUsers.isEmpty && _users.isNotEmpty) {
      _filteredUsers = List.from(_users);
    }

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            _buildSearchBar(),
            _buildUserList(),
            _buildAssignButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: widget.onClose,
            child: const Icon(
              Icons.arrow_back_ios,
              size: 20,
              color: Color(0xFF374151),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            widget.title,
            style: UIHelper.modernTitleStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        onChanged: _filterUsers,
        decoration: InputDecoration(
          hintText: 'Search users...',
          hintStyle: UIHelper.modernBodyStyle(
            fontSize: 14,
            color: const Color(0xFF9CA3AF),
          ),
          prefixIcon: const Icon(
            Icons.search,
            color: Color(0xFF9CA3AF),
            size: 20,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFFE5E7EB)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFF3B82F6)),
          ),
          filled: true,
          fillColor: const Color(0xFFF9FAFB),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        style: UIHelper.modernBodyStyle(
          fontSize: 14,
          color: const Color(0xFF1F2937),
        ),
      ),
    );
  }

  Widget _buildUserList() {
    return Expanded(
      child: _filteredUsers.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _filteredUsers.length,
              itemBuilder: (context, index) {
                final user = _filteredUsers[index];
                final isSelected = _selectedUser?.user_id == user.user_id;
                
                return _buildUserItem(user, isSelected);
              },
            ),
    );
  }

  Widget _buildUserItem(USER_HEADER user, bool isSelected) {
    final fullName = '${user.first_name ?? ''} ${user.last_name ?? ''}'.trim();
    final avatarColor = _getAvatarColor(fullName);

    return GestureDetector(
      onTap: () => _selectUser(user),
      child: Container(
        margin: const EdgeInsets.only(bottom: 4), // Reduced from 8 to 4 (50% reduction)
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8), // Reduced vertical from 16 to 8 (50% reduction)
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFF3F4F6) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: isSelected
              ? Border.all(color: const Color(0xFF3B82F6), width: 1)
              : null,
        ),
        child: Row(
          children: [
            Container(
              width: 36, // Slightly reduced from 40 to 36 for compactness
              height: 36, // Slightly reduced from 40 to 36
              decoration: BoxDecoration(
                color: avatarColor,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  _getUserInitials(user),
                  style: UIHelper.modernBodyStyle(
                    fontSize: 16, // Keep original font size
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 10), // Reduced from 12 to 10
            Expanded(
              child: Text(
                fullName,
                style: UIHelper.modernBodyStyle(
                  fontSize: 16, // Keep original font size
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF1F2937),
                ),
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: Color(0xFF3B82F6),
                size: 20, // Keep original icon size
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_search,
            size: 48,
            color: const Color(0xFF9CA3AF),
          ),
          const SizedBox(height: 16),
          Text(
            'No users found',
            style: UIHelper.modernBodyStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF6B7280),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search',
            style: UIHelper.modernCaptionStyle(
              fontSize: 14,
              color: const Color(0xFF9CA3AF),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAssignButton() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _selectedUser != null ? _confirmSelection : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: _selectedUser != null 
                ? const Color(0xFF3B82F6) 
                : const Color(0xFFE5E7EB),
            foregroundColor: _selectedUser != null 
                ? Colors.white 
                : const Color(0xFF9CA3AF),
            elevation: 0,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Text(
            'Assign',
            style: UIHelper.modernBodyStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: _selectedUser != null 
                  ? Colors.white 
                  : const Color(0xFF9CA3AF),
            ),
          ),
        ),
      ),
    );
  }
}
