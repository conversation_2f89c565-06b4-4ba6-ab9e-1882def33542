/*
import 'package:flutter/material.dart';

class FilterScreen extends StatefulWidget {
 final Map<String,List<String>> filter;
    FilterScreen({super.key, required this.filter});

  @override
  _FilterScreenState createState() => _FilterScreenState();
}

class _FilterScreenState extends State<FilterScreen> {

  final List<String> categories = ["FAILURE_MODE", "PLANT_ID", "CREATED_BY"];

  final Map<String, List<String>> options = {
    "FAILURE_MODE": ["Mechanical", "Electrical", "Pneumatic", "Automatic","Hydraulic"],
    "PLANT_ID":["1700", "1000", "1234","1710","1002","1003"],
    "CREATED_BY":["Subhas", "Sneha", "Sanjay"],
  };


  String selectedCategory = "FAILURE_MODE";
   Map<String, List<String>> selectedOptions = {};

  @override
  Widget build(BuildContext context) {
    selectedOptions =  widget.filter.isNotEmpty ? widget.filter : selectedOptions;
    return Scaffold(
      appBar: AppBar(
        title: const Text("Filters"),
        actions: [
          TextButton(onPressed: (){
            setState(() {
              selectedOptions.clear();
            });
          }, child:const Text("clear",style: TextStyle(color: Colors.black),))
        ],
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
      ),
      body: Row(
        children: [
          // Left section: Categories
          Expanded(
            flex: 1,
            child: ListView.builder(
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final category = categories[index];
                return ListTile(
                  title: Text(
                    category,
                    style: TextStyle(
                      color: selectedCategory == category
                          ? Colors.blue
                          : Colors.black,
                      fontWeight: selectedCategory == category
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
                  onTap: () {
                    setState(() {
                      selectedCategory = category;
                    });
                  },
                );
              },
            ),
          ),
          const VerticalDivider(), // Divider between sections
          // Right section: Options for selected category
          Expanded(
            flex: 2,
            child: ListView.builder(
              itemCount: options[selectedCategory]?.length ?? 0,
              itemBuilder: (context, index) {
                final option = options[selectedCategory]![index];
                final isSelected =
                    selectedOptions[selectedCategory]?.contains(option) ?? false;

                return CheckboxListTile(
                  title: Text(option),
                  value: isSelected,
                  onChanged: (value) {
                    setState(() {
                      if (value == true) {
                        selectedOptions[selectedCategory] =
                        (selectedOptions[selectedCategory] ?? [])..add(option);
                      } else {
                        selectedOptions[selectedCategory]
                            ?.remove(option);
                      }
                    });
                  },
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ElevatedButton(
          onPressed: () {
            // Apply filter logic here
            Navigator.pop(context,selectedOptions);
            print("Selected Filters: $selectedOptions");
          },
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white, backgroundColor: Colors.orange,
            minimumSize: const Size(double.infinity, 50),
          ),
          child: const Text("Apply"),
        ),
      ),
    );
  }
}
*/
