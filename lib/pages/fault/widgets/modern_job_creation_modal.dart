import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:rounds/be/FAULT_HEADER.dart';
import 'package:rounds/be/JOB_HEADER.dart';
import 'package:rounds/be/JOB_ACTION.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/helpers/pa_helper.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/providers/job_creation/job_header_provider.dart';
import 'package:rounds/providers/fault/fault_type_provider.dart';
import 'package:rounds/providers/fault/fault_header_provider.dart';

import 'package:rounds/utils/app_constants.dart';
import 'package:rounds/utils/constants.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:rounds/pages/fault/widgets/modern_user_selection_modal.dart';

/// Modern Job Creation Modal Widget
/// Follows the design patterns from fault modal with modern UI controls
class ModernJobCreationModal extends ConsumerStatefulWidget {
  final FAULT_HEADER sourceFault;
  final VoidCallback onClose;
  final Function(JOB_HEADER) onJobCreated;

  const ModernJobCreationModal({
    Key? key,
    required this.sourceFault,
    required this.onClose,
    required this.onJobCreated,
  }) : super(key: key);

  @override
  ConsumerState<ModernJobCreationModal> createState() => _ModernJobCreationModalState();
}

class _ModernJobCreationModalState extends ConsumerState<ModernJobCreationModal> {
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _longTextController = TextEditingController();
  final TextEditingController _startDateController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();
  final TextEditingController _assignedToController = TextEditingController();

  // Form state
  String _selectedPriority = '';
  String _selectedJobTypeCode = ''; // Store job type code, not description
  String _selectedAssignedToId = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeFormData();

    // Load job types when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(jobTypeListProvider.notifier).jobTypeList();
    });
  }

  void _initializeFormData() {
    // Pre-populate form with fault data
    _titleController.text = 'Job for: ${widget.sourceFault.description ?? ''}';
    _longTextController.text = widget.sourceFault.details ?? '';

    // Set priority from fault
    if (widget.sourceFault.priority != null) {
      _selectedPriority = ref.read(priorityListProvider.notifier)
          .fetchPriorityCode(widget.sourceFault.priority.toString());
    }

    // Set current date as start date
    DateTime now = DateTime.now();
    String formattedDate = DateFormat('dd MMM yyyy').format(now);
    _startDateController.text = formattedDate;

    // Leave end date empty for user to fill
    _endDateController.text = '';
  }

  @override
  void dispose() {
    _titleController.dispose();
    _longTextController.dispose();
    _startDateController.dispose();
    _endDateController.dispose();
    _assignedToController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 600,
          maxHeight: 700,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Expanded(
              child: _buildContent(),
            ),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB)), // border-gray-200
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.work_outline,
            size: 24,
            color: const Color(0xFF2563EB), // text-blue-600
          ),
          const SizedBox(width: 8),
          Text(
            'Create Job for Fault: FAULT-${widget.sourceFault.fault_id}',
            style: UIHelper.modernTitleStyle(
              fontSize: 20, // text-xl
              fontWeight: FontWeight.w600, // font-semibold
              color: const Color(0xFF1F2937), // text-gray-800
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: widget.onClose,
            child: const Icon(
              Icons.close,
              size: 24,
              color: Color(0xFF9CA3AF), // text-gray-400
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildRelatedFaultDescription(),
            const SizedBox(height: 20),
            _buildJobTitle(),
            const SizedBox(height: 20),
            _buildPriorityRow(),
            const SizedBox(height: 20),
            _buildJobTypeAndAssignedToRow(),
            const SizedBox(height: 20),
            _buildDateRow(),
            const SizedBox(height: 20),
            _buildJobLongText(),
            const SizedBox(height: 20),
            _buildAttachments(),
          ],
        ),
      ),
    );
  }

  // Helper method for consistent form field styling
  Widget _buildFormField(String label, Widget child, {bool isRequired = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isRequired ? '$label*' : label,
          style: UIHelper.modernCaptionStyle(
            fontSize: 12, // text-xs
            fontWeight: FontWeight.w500, // font-medium
            color: const Color(0xFF374151), // text-gray-700
          ),
        ),
        const SizedBox(height: 4),
        child,
      ],
    );
  }

  Widget _buildRelatedFaultDescription() {
    return _buildFormField(
      'Related Fault Description (Read-only)',
      Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8), // py-2 px-2.5
        decoration: BoxDecoration(
          color: const Color(0xFFF9FAFB), // bg-gray-50
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: const Color(0xFFD1D5DB)), // border-gray-300
        ),
        child: Text(
          widget.sourceFault.description ?? 'No description available',
          style: UIHelper.modernBodyStyle(
            fontSize: 14, // text-sm
            fontWeight: FontWeight.w400,
            color: const Color(0xFF6B7280), // text-gray-500
          ),
        ),
      ),
    );
  }

  Widget _buildJobTitle() {
    return _buildFormField(
      'Job Title',
      TextFormField(
        controller: _titleController,
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return 'Job title is required';
          }
          return null;
        },
        decoration: InputDecoration(
          hintText: 'Enter job title',
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFFD1D5DB)), // border-gray-300
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFF2563EB)), // focus:border-brand-blue
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8), // py-2 px-2.5
        ),
        style: UIHelper.modernBodyStyle(
          fontSize: 14, // text-sm
          fontWeight: FontWeight.w400,
          color: const Color(0xFF1F2937), // text-gray-900
        ),
      ),
      isRequired: true,
    );
  }

  Widget _buildPriorityRow() {
    return _buildFormField(
      'Priority',
      Row(
        children: [
          Expanded(
            child: _buildPriorityButton(
              'High',
              const Color(0xFFFEF2F2), // bg-red-50
              const Color(0xFFDC2626), // text-red-600
              _selectedPriority == 'High',
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildPriorityButton(
              'Medium',
              const Color(0xFFFEF3C7), // bg-yellow-50
              const Color(0xFFD97706), // text-yellow-600
              _selectedPriority == 'Medium',
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildPriorityButton(
              'Low',
              const Color(0xFFEFF6FF), // bg-blue-50
              const Color(0xFF2563EB), // text-blue-600
              _selectedPriority == 'Low',
            ),
          ),
        ],
      ),
      isRequired: true,
    );
  }

  Widget _buildPriorityButton(String label, Color backgroundColor, Color textColor, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPriority = label;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8), // py-2 px-2.5
        decoration: BoxDecoration(
          color: isSelected ? backgroundColor : const Color(0xFFF9FAFB), // bg-gray-50
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? textColor : const Color(0xFFD1D5DB), // border-gray-300
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            label,
            style: UIHelper.modernBodyStyle(
              fontSize: 14, // text-sm
              fontWeight: FontWeight.w500, // font-medium
              color: isSelected ? textColor : const Color(0xFF6B7280), // text-gray-500
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildJobTypeAndAssignedToRow() {
    return Row(
      children: [
        Expanded(
          child: _buildJobTypeDropdown(),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildAssignedToField(),
        ),
      ],
    );
  }

  Widget _buildJobTypeDropdown() {
    final jobTypeList = ref.watch(jobTypeListProvider);

    return _buildFormField(
      'Job Type',
      DropdownButtonFormField<String>(
        value: _selectedJobTypeCode.isEmpty ? null : _selectedJobTypeCode,
        decoration: InputDecoration(
          hintText: 'Select job type',
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFFD1D5DB)), // border-gray-300
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFF2563EB)), // focus:border-brand-blue
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8), // py-2 px-2.5
        ),
        style: UIHelper.modernBodyStyle(
          fontSize: 14, // text-sm
          fontWeight: FontWeight.w400,
          color: const Color(0xFF1F2937), // text-gray-900
        ),
        items: jobTypeList.map((jobType) {
          return DropdownMenuItem<String>(
            value: jobType.job_type, // Use job_type code as value
            child: Text(
              jobType.description ?? '',
              style: UIHelper.modernBodyStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF1F2937),
              ),
            ),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedJobTypeCode = value ?? '';
          });
        },
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Job type is required';
          }
          return null;
        },
      ),
      isRequired: true,
    );
  }

  Widget _buildAssignedToField() {
    return _buildFormField(
      'Assigned To',
      TextFormField(
        controller: _assignedToController,
        readOnly: true,
        onTap: _selectAssignedUser,
        decoration: InputDecoration(
          hintText: 'Click to assign user',
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFFD1D5DB)), // border-gray-300
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFF2563EB)), // focus:border-brand-blue
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8), // py-2 px-2.5
          suffixIcon: const Icon(
            Icons.person_add,
            color: Color(0xFF6B7280), // text-gray-500
            size: 20,
          ),
        ),
        style: UIHelper.modernBodyStyle(
          fontSize: 14, // text-sm
          fontWeight: FontWeight.w400,
          color: const Color(0xFF1F2937), // text-gray-900
        ),
      ),
    );
  }

  Widget _buildDateRow() {
    return Row(
      children: [
        Expanded(
          child: _buildDateField(
            label: 'Start Date',
            controller: _startDateController,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildDateField(
            label: 'Due Date',
            controller: _endDateController,
          ),
        ),
      ],
    );
  }

  Widget _buildDateField({
    required String label,
    required TextEditingController controller,
  }) {
    return _buildFormField(
      label,
      TextFormField(
        controller: controller,
        readOnly: true,
        onTap: () => _selectDate(controller),
        decoration: InputDecoration(
          hintText: 'Select date',
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFFD1D5DB)), // border-gray-300
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFF2563EB)), // focus:border-brand-blue
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8), // py-2 px-2.5
          suffixIcon: const Icon(
            Icons.calendar_today,
            color: Color(0xFF6B7280), // text-gray-500
            size: 20,
          ),
        ),
        style: UIHelper.modernBodyStyle(
          fontSize: 14, // text-sm
          fontWeight: FontWeight.w400,
          color: const Color(0xFF1F2937), // text-gray-900
        ),
      ),
    );
  }

  Widget _buildJobLongText() {
    return _buildFormField(
      'Job Long Text / Instructions',
      TextFormField(
        controller: _longTextController,
        maxLines: 4,
        decoration: InputDecoration(
          hintText: 'Enter job instructions or additional details...',
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFFD1D5DB)), // border-gray-300
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFF2563EB)), // focus:border-brand-blue
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8), // py-2 px-2.5
        ),
        style: UIHelper.modernBodyStyle(
          fontSize: 14, // text-sm
          fontWeight: FontWeight.w400,
          color: const Color(0xFF1F2937), // text-gray-900
        ),
      ),
    );
  }

  Widget _buildAttachments() {
    return _buildFormField(
      'Attachments (inherited from fault, editable)',
      Container(
        width: double.infinity,
        height: 80,
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFD1D5DB)), // border-gray-300
          borderRadius: BorderRadius.circular(6),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.add,
                color: Color(0xFF6B7280), // text-gray-500
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                'Add attachments',
                style: UIHelper.modernCaptionStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xFF6B7280), // text-gray-500
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }





  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB)), // border-gray-200
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: widget.onClose,
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8), // py-2 px-4
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
                side: const BorderSide(color: Color(0xFFD1D5DB)), // border-gray-300
              ),
            ),
            child: Text(
              'Cancel',
              style: UIHelper.modernBodyStyle(
                fontSize: 14, // text-sm
                fontWeight: FontWeight.w500, // font-medium
                color: const Color(0xFF374151), // text-gray-700
              ),
            ),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: _isLoading ? null : _createJob,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2563EB), // bg-blue-600
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8), // py-2 px-4
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'Save Job',
                    style: UIHelper.modernBodyStyle(
                      fontSize: 14, // text-sm
                      fontWeight: FontWeight.w500, // font-medium
                      color: Colors.white,
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(TextEditingController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      String formattedDate = DateFormat('dd MMM yyyy').format(picked);
      controller.text = formattedDate;
    }
  }

  void _selectAssignedUser() {
    showDialog(
      context: context,
      builder: (context) => ModernUserSelectionModal(
        title: 'Select User',
        onUserSelected: (USER_HEADER user) {
          setState(() {
            _selectedAssignedToId = user.user_id ?? '';
            _assignedToController.text = '${user.first_name ?? ''} ${user.last_name ?? ''}'.trim();
          });
          Navigator.pop(context);
        },
        onClose: () => Navigator.pop(context),
      ),
    );
  }

  Future<void> _createJob() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedPriority.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a priority')),
      );
      return;
    }

    if (_selectedJobTypeCode.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a job type')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create job header with pre-populated data from fault
      final plant = ref.read(plantProvider);
      final jobId = UIHelper.generateRandomId();

      // Convert dates to int format
      int? startDateInt;
      int? endDateInt;

      if (_startDateController.text.isNotEmpty) {
        DateTime startDate = DateFormat('dd MMM yyyy').parse(_startDateController.text);
        String formattedStartDate = DateFormat('yyyyMMdd').format(startDate);
        startDateInt = int.parse(formattedStartDate);
      }

      if (_endDateController.text.isNotEmpty) {
        DateTime endDate = DateFormat('dd MMM yyyy').parse(_endDateController.text);
        String formattedEndDate = DateFormat('yyyyMMdd').format(endDate);
        endDateInt = int.parse(formattedEndDate);
      }

      final newJobHeader = JOB_HEADER(
        job_id: jobId,
        plant_id: plant,
        fault_id: widget.sourceFault.fault_id,
        location_id: widget.sourceFault.location_id,
        asset_no: widget.sourceFault.asset_no,
        description: _titleController.text.trim(),
        details: _longTextController.text.trim(),
        priority: widget.sourceFault.priority,
        job_type: _selectedJobTypeCode,
        assigned_to: _selectedAssignedToId.isEmpty ? null : _selectedAssignedToId,
        start_date: startDateInt,
        end_date: endDateInt,
        status: Constants.JOB_STATE_OSNO, // Open status
        p_mode: AppConstants.add,
      );

      // Save job to database
      await ref.read(insertJobHeaderProvider.notifier).insertJobHeader(newJobHeader);

      // Create JOB_ACTION record
      JOB_ACTION newJobAction = JOB_ACTION(
        job_id: jobId,
        user_action: Constants.JOB_STATE_OSNO, // Open status
      );
      newJobAction.fid = newJobHeader.lid;
      await AppDatabaseManager().insert(
        DBInputEntity(JOB_ACTION.TABLE_NAME, newJobAction.toJson()),
      );

      // If job is assigned, update the action status
      if (_selectedAssignedToId.isNotEmpty) {
        newJobAction.user_action = Constants.JOB_STATE_ORAS; // Assigned status
        await AppDatabaseManager().update(
          DBInputEntity(JOB_ACTION.TABLE_NAME, newJobAction.toJson()),
        );
      }

      // Update fault with job ID and status
      widget.sourceFault.job_id = jobId;
      widget.sourceFault.status = Constants.FAULT_STATE_ORAS; // Job assigned status
      widget.sourceFault.p_mode = AppConstants.modified;

      // Update existing fault header instead of inserting
      await AppDatabaseManager().update(
        DBInputEntity(FAULT_HEADER.TABLE_NAME, widget.sourceFault.toJson()),
      );

      // Send job to server
      final currentContext = context;
      if (mounted) {
        if (!kIsWeb) {
          await PAHelper.addOrModifyJobInAsyncMode(currentContext, newJobHeader);
        } else {
          await PAHelper.addOrModifyJobInSyncMode(currentContext, newJobHeader);
        }
      }

      // Call success callback
      widget.onJobCreated(newJobHeader);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Job created successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating job: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}