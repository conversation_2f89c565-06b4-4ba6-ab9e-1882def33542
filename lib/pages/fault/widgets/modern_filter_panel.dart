import 'package:flutter/material.dart';
import '../../../helpers/ui_helper.dart';

class ModernFilterPanel extends StatefulWidget {
  final List<String> selectedFaultTypes;
  final List<String> selectedPriorities;
  final List<String> selectedStatuses;
  final Function(List<String>) onFaultTypesChanged;
  final Function(List<String>) onPrioritiesChanged;
  final Function(List<String>) onStatusesChanged;
  final VoidCallback onClearAll;
  final VoidCallback onClose;

  const ModernFilterPanel({
    Key? key,
    required this.selectedFaultTypes,
    required this.selectedPriorities,
    required this.selectedStatuses,
    required this.onFaultTypesChanged,
    required this.onPrioritiesChanged,
    required this.onStatusesChanged,
    required this.onClearAll,
    required this.onClose,
  }) : super(key: key);

  @override
  State<ModernFilterPanel> createState() => _ModernFilterPanelState();
}

class _ModernFilterPanelState extends State<ModernFilterPanel> {
  final List<String> faultTypeOptions = ['Mechanical', 'Electrical', 'Hydraulic', 'Pneumatic'];
  final List<String> priorityOptions = ['High', 'Medium', 'Low'];
  final List<String> statusOptions = ['Open', 'Job Assigned', 'Completed'];

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 240, // Reduced from 288 to match RN app
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10, // Reduced shadow
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(color: const Color(0xFFE5E7EB)), // gray-200
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(12), // Reduced padding
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Color(0xFFE5E7EB)), // gray-200
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filter Faults',
                  style: UIHelper.modernTitleStyle(
                    fontSize: 14, // Reduced font size
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1F2937), // gray-800
                  ),
                ),
                GestureDetector(
                  onTap: widget.onClose,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: Colors.transparent,
                    ),
                    child: const Icon(
                      Icons.close,
                      size: 16,
                      color: Color(0xFF6B7280), // gray-500
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Filter Options
          Padding(
            padding: const EdgeInsets.all(12), // Reduced padding
            child: Column(
              children: [
                _buildMultiSelectDropdown(
                  'Fault Type',
                  faultTypeOptions,
                  widget.selectedFaultTypes,
                  widget.onFaultTypesChanged,
                ),
                const SizedBox(height: 12), // Reduced spacing
                _buildMultiSelectDropdown(
                  'Priority',
                  priorityOptions,
                  widget.selectedPriorities,
                  widget.onPrioritiesChanged,
                ),
                const SizedBox(height: 12), // Reduced spacing
                _buildMultiSelectDropdown(
                  'Status',
                  statusOptions,
                  widget.selectedStatuses,
                  widget.onStatusesChanged,
                ),
              ],
            ),
          ),

          // Footer
          Container(
            padding: const EdgeInsets.all(12), // Reduced padding
            decoration: const BoxDecoration(
              color: Color(0xFFF9FAFB), // gray-50
              border: Border(
                top: BorderSide(color: Color(0xFFE5E7EB)), // gray-200
              ),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: widget.onClearAll,
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6), // Compact padding
                  ),
                  child: Text(
                    'Clear All',
                    style: UIHelper.modernBodyStyle(
                      fontSize: 13, // Smaller text
                      color: const Color(0xFF2563EB), // brand-blue
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: widget.onClose,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2563EB), // brand-blue
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6), // Compact padding
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    'Done',
                    style: UIHelper.modernBodyStyle(
                      fontSize: 13, // Smaller text
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMultiSelectDropdown(
    String label,
    List<String> options,
    List<String> selectedValues,
    Function(List<String>) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: UIHelper.modernCaptionStyle(
            fontSize: 11, // Smaller text
            fontWeight: FontWeight.w500,
            color: const Color(0xFF374151), // gray-700
          ),
        ),
        const SizedBox(height: 3), // Reduced spacing
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFD1D5DB)), // gray-300
            borderRadius: BorderRadius.circular(6),
          ),
          child: Theme(
            data: Theme.of(context).copyWith(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              dividerColor: Colors.transparent,
              // Reduce the minimum height of list tiles
              listTileTheme: const ListTileThemeData(
                dense: true,
                minVerticalPadding: 0,
                contentPadding: EdgeInsets.zero,
              ),
            ),
            child: ExpansionTile(
              title: Text(
                selectedValues.isEmpty
                    ? 'All ${label}s'
                    : selectedValues.length == 1
                        ? selectedValues.first
                        : '${selectedValues.length} selected', // Show count for multiple selections
                style: UIHelper.modernBodyStyle(
                  fontSize: 13, // Smaller text
                  color: selectedValues.isEmpty
                      ? const Color(0xFF6B7280) // gray-500
                      : const Color(0xFF2563EB), // blue when items selected
                  fontWeight: selectedValues.isEmpty ? FontWeight.w400 : FontWeight.w600,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              tilePadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0), // Minimal vertical padding for compact header
              childrenPadding: EdgeInsets.zero,
              expandedCrossAxisAlignment: CrossAxisAlignment.start,
              maintainState: true,
              iconColor: const Color(0xFF6B7280), // gray-500
              collapsedIconColor: const Color(0xFF6B7280), // gray-500
              children: options.map((option) {
                final isSelected = selectedValues.contains(option);
                return InkWell(
                  onTap: () {
                    List<String> newSelected = List.from(selectedValues);
                    if (isSelected) {
                      newSelected.remove(option);
                    } else {
                      newSelected.add(option);
                    }
                    onChanged(newSelected);
                  },
                  child: Container(
                    height: 28, // Reduced height
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? const Color(0xFF2563EB).withOpacity(0.05) // Very light blue background when selected
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        // Custom checkbox with better visibility
                        Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: isSelected ? const Color(0xFF2563EB) : Colors.transparent,
                            border: Border.all(
                              color: isSelected ? const Color(0xFF2563EB) : const Color(0xFFD1D5DB),
                              width: 1.5,
                            ),
                            borderRadius: BorderRadius.circular(3),
                          ),
                          child: isSelected
                              ? const Icon(
                                  Icons.check,
                                  size: 12,
                                  color: Colors.white,
                                )
                              : null,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            option,
                            style: UIHelper.modernBodyStyle(
                              fontSize: 13,
                              color: isSelected
                                  ? const Color(0xFF2563EB) // blue when selected
                                  : const Color(0xFF1F2937), // gray when not selected
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }
}
