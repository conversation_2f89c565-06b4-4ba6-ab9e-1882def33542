import 'dart:async';
import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/CILT_EXEC_TASK.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:rounds/be/INSP_EXEC_TASK.dart';
import 'package:rounds/be/JOB_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/pages/fault/tabs/attachment_tab_fault.dart';
import 'package:rounds/pages/fault/tabs/edit_fault_field_provider.dart';
import 'package:rounds/pages/fault/tabs/general_tab_fault.dart';
import 'package:rounds/pages/job_creation/job_creation_detail_screen.dart';
import 'package:rounds/pages/job_creation/tabs/edit_job_creation_field_provider.dart';
import 'package:rounds/providers/assets/asset_provider.dart';
import 'package:rounds/providers/cilt/cilt_header_provider.dart';
import 'package:rounds/providers/cilt/cilt_task_provider.dart';
import 'package:rounds/providers/job_creation/job_header_provider.dart';
import 'package:rounds/utils/app_extensions.dart';
import 'package:rounds/utils/utils.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import '../../be/FAULT_HEADER.dart';
import '../../../providers/fault/fault_header_provider.dart';
import '../../../providers/fault/fault_type_provider.dart';
import '../../../utils/app_colors.dart';
import 'package:intl/intl.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../be/FAULT_ACTION.dart';
import '../../be/FAULT_DOCUMENT.dart';
import '../../helpers/pa_helper.dart';
import '../../models/intractive_Item_Model.dart';
import '../../providers/assets/floc_provider.dart';
import '../../providers/attachments/attachment_provider.dart';
import '../../providers/inspection/inspection_header_provider.dart';
import '../../providers/inspection/inspection_task_provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/app_constants.dart';
import '../../utils/app_styles.dart';
import '../../utils/constants.dart';
import '../../utils/fault_status_helper.dart';
import '../dashboard/dashboard.dart';
import '../widgets/error_longtext_view.dart';
import 'fault_filter_provider.dart';
import 'fault_screen.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';

class FaultDetailScreen extends ConsumerStatefulWidget {
  final String type;
  final Function(InteractiveItemModel)? onTap;
  final bool isBackrequired;
  final bool display;
  const FaultDetailScreen(
      {required this.type,
      this.onTap,
      this.isBackrequired = true,
      this.display = false,
      super.key});

  @override
  _FaultDetailScreenState createState() => _FaultDetailScreenState();
}

class _FaultDetailScreenState extends ConsumerState<FaultDetailScreen> {
  static const sourceClass = 'FaultDetailScreen';
  String selectedStatus = "";
  TextEditingController descriptionController = TextEditingController();
  TextEditingController longTextController = TextEditingController();
  bool isExpandedForLocationDetail = false;
  bool isExpandedForAttachment = false;
  bool error = false;
  bool viewMore = false;
  Future<List<InfoMessageData?>>? fetchInfoMessage;

  @override
  void initState() {
    super.initState();
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    final faultNoticedOn = ref.read(faultNoticedOnProvider.notifier);
    final faultDueOn = ref.read(faultDueOnProvider.notifier);
    final faultPriority = ref.read(priorityProvider.notifier);
    final faultType = ref.read(faultTypeProvider.notifier);
    final faultMode = ref.read(faultModeHeaderProvider.notifier);
    final longText = ref.read(faultLongTextProvider.notifier);
    final description = ref.read(faultDescriptionProvider.notifier);
    Future.delayed(Duration.zero).then((value) async {
      final plant = ref.watch(plantProvider);
      ref.read(assetHeaderProvider.notifier).getAssetHeaderList(plant);
      if (faultHeader.reported_on != null) {
        faultNoticedOn.getFaultNoticedOn(faultHeader.reported_on!);
      }
      if (faultHeader.req_end != null) {
        faultDueOn.getDueOn(faultHeader.req_end!);
      }

      if (faultHeader.priority != null) {
        faultPriority.getPriority(ref
            .read(priorityListProvider.notifier)
            .fetchPriorityCode(faultHeader.priority.toString()));
      }
      if (faultHeader.fault_type != null) {
        faultType.getFaultType(ref
            .read(faultTypeListProvider.notifier)
            .fetchFaultTypeCode(faultHeader.fault_type.toString()));
      }
      if (faultHeader.failure_mode != null) {
        faultMode.getFaultMode(ref
            .read(faultModeHeaderListProvider.notifier)
            .fetchFaultModeByCode(faultHeader.failure_mode.toString()));
      }

      if (faultHeader.description != null && faultHeader.description != '') {
        descriptionController.text = faultHeader.description.toString();
        description.getFaultDescription(descriptionController.text);
      }
      if (faultHeader.details != null && faultHeader.details != '') {
        longTextController.text = faultHeader.details.toString();
        longText.getFaultLongText(longTextController.text);
      }
      fetchInfoMessage =
          InfoMessageHelper().getInfoMessageByBeLid(faultHeader.lid);
      if (!kIsWeb) {
        setState(() {});
      }
    });
  }

  Future<bool> checkIfModifiedDataThere() async {
    final faultHeaderr = ref.read(faultHeaderProvider.notifier).state;
    final originalFaultHeader = ref.read(faultHeaderProvider);
    final faultDescription = ref.watch(faultDescriptionProvider.notifier).state;
    final faultNoticedOn = ref.watch(faultNoticedOnProvider.notifier).state;
    final faultDueOn = ref.watch(faultDueOnProvider.notifier).state;
    final faultMode = ref.watch(faultModeHeaderProvider.notifier).state;
    final priority = ref.watch(priorityProvider.notifier).state;
    final faultType = ref.watch(faultTypeProvider.notifier).state;
    final longText = ref.read(faultLongTextProvider.notifier).state;
    FAULT_HEADER faultHeader = FAULT_HEADER.fromJson(faultHeaderr.toJson());

    if (faultNoticedOn != 0) {
      faultHeader.reported_on = faultNoticedOn;
    }
    if (faultDueOn != 0) {
      faultHeader.req_end = faultDueOn;
    }
    if (faultDescription.isNotEmpty && faultDescription != '') {
      faultHeader.description = faultDescription;
    }
    if (longText.isNotEmpty && longText != '') {
      faultHeader.details = longText;
    }
    if (faultMode.failure_code != null && faultMode.failure_code != '') {
      faultHeader.failure_mode = faultMode.failure_code;
    }
    if (priority.priority_code != null && priority.priority_code != '') {
      faultHeader.priority = priority.priority_code;
    }
    if (faultType.fault_code != null && faultType.fault_code != '') {
      faultHeader.fault_type = faultType.fault_code;
    }
    bool edited = true;
    edited = areJsonEqual(originalFaultHeader.toJson(), faultHeader.toJson());
    return edited;
  }

  bool areJsonEqual(Map<String, dynamic> json1, Map<String, dynamic> json2) {
    return const DeepCollectionEquality().equals(json1, json2);
  }

  @override
  Widget build(BuildContext context) {
    final editProvider = ref.watch(editFaultFieldProvider.notifier);
    final plant = ref.watch(plantProvider);
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    faultHeader.plant_id = plant;
    final longText = ref.read(faultLongTextProvider.notifier);

    JOB_HEADER? jobHeader = isJobAvaiable(faultHeader);
    if (faultHeader.details != null &&
        faultHeader.details != '' &&
        ScreenType.desktop == UIHelper().getScreenType(context)) {
      longTextController.text = faultHeader.details.toString();
      longText.getFaultLongText(longTextController.text);
    }
    return WillPopScope(
      onWillPop: () async {
        bool data = await checkIfModifiedDataThere();
        if (!data) {
          if (mounted) {
            UIHelper.showConfirmationDialogWithYesOrNo(context,
                description: AppLocalizations.of(context)!
                    .you_have_unsaved_data, no: () async {
              await onUnsaveFault(faultHeader);
            }, yes: () async {
              Navigator.pop(context);
              await onCheck(faultHeader.status, willScope: true);
            });
          }
          return false;
        } else {
          await AppDatabaseManager().delete(
              DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));
          if (ScreenType.desktop == UIHelper().getScreenType(context)) {
            if (widget.onTap != null) {
              widget.onTap!(InteractiveItemModel(type: "", data: {}));
            }
          }
          return true;
        }
      },
      child: DefaultTabController(
        initialIndex: 0,
        length: 2,
        child: Scaffold(
          backgroundColor: AppColors.white,
          appBar: AppBar(
            elevation: 0,
            backgroundColor: AppColors.white,
            leadingWidth: widget.isBackrequired
                ? 40
                : UIHelper().getScreenType(context) != ScreenType.desktop
                    ? 40
                    : faultHeader.p_mode == AppConstants.add
                        ? 40
                        : 0,
            leading: widget.isBackrequired
                ? IconButton(
                    onPressed: () async {
                      bool data = await checkIfModifiedDataThere();
                      if (!data) {
                        if (mounted) {
                          UIHelper.showConfirmationDialogWithYesOrNo(context,
                              description: AppLocalizations.of(context)!
                                  .you_have_unsaved_data, no: () async {
                            await onUnsaveFault(faultHeader);
                            editProvider.getEditFaultFieldEnable(false);
                          }, yes: () async {
                            Navigator.pop(context);
                            await onCheck(faultHeader.status, willScope: true);
                            editProvider.getEditFaultFieldEnable(false);
                          });
                        }
                      } else {
                        if (widget.display) {
                          Navigator.of(context)
                              .popUntil((route) => route.isFirst);
                        } else {
                          // Recheck - sneha
                        // creating empty faults without data so commented
                         // if (AppConstants.addFault == widget.type) {
                            String deleteQuery =
                                'DELETE FROM ${FAULT_HEADER.TABLE_NAME} WHERE ${FAULT_HEADER.FIELD_FAULT_ID} = ${faultHeader.fault_id}';
                            await AppDatabaseManager().execute(deleteQuery);
                         // }
                          if (ScreenType.desktop ==
                              UIHelper().getScreenType(context)) {
                            if (widget.onTap != null) {
                              widget.onTap!(
                                  InteractiveItemModel(type: "", data: {}));
                            }
                          }
                          editProvider.getEditFaultFieldEnable(false);

                          Navigator.pop(context);
                        }
                      }
                    },
                    icon: Icon(
                      Icons.arrow_back_ios,
                      color: AppColors.titleTextColor,
                      size: 20,
                    ))
                : UIHelper().getScreenType(context) != ScreenType.desktop
                    ? IconButton(
                        onPressed: () async {
                          bool data = await checkIfModifiedDataThere();
                          if (!data) {
                            if (mounted) {
                              UIHelper.showConfirmationDialogWithYesOrNo(
                                  context,
                                  description: AppLocalizations.of(context)!
                                      .you_have_unsaved_data, no: () async {
                                await onUnsaveFault(faultHeader);
                              }, yes: () async {
                                Navigator.pop(context);
                                await onCheck(faultHeader.status,
                                    willScope: true);
                              });
                            }
                          } else {
                            await AppDatabaseManager().delete(DBInputEntity(
                                FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));
                            if (ScreenType.desktop ==
                                UIHelper().getScreenType(context)) {
                              if (widget.onTap != null) {
                                widget.onTap!(
                                    InteractiveItemModel(type: "", data: {}));
                              }
                            }
                            Navigator.pop(context);
                          }
                        },
                        icon: Icon(
                          Icons.arrow_back_ios,
                          color: AppColors.titleTextColor,
                          size: 20,
                        ))
                    : faultHeader.p_mode == AppConstants.add
                        ? IconButton(
                            onPressed: () async {
                              bool data = await checkIfModifiedDataThere();
                              if (!data) {
                                if (mounted) {
                                  UIHelper.showConfirmationDialogWithYesOrNo(
                                      context,
                                      description: AppLocalizations.of(context)!
                                          .you_have_unsaved_data, no: () async {
                                    await onUnsaveFault(faultHeader);
                                    if (ScreenType.desktop ==
                                        UIHelper().getScreenType(context)) {
                                      widget.onTap!(InteractiveItemModel(
                                          type: "", data: {}));
                                    }
                                  }, yes: () async {
                                    Navigator.pop(context);
                                    await onCheck(faultHeader.status,
                                        willScope: true);
                                    if (ScreenType.desktop ==
                                        UIHelper().getScreenType(context)) {
                                      widget.onTap!(InteractiveItemModel(
                                          type: "", data: {}));
                                    }
                                  });
                                }
                              } else {
                                await AppDatabaseManager().delete(DBInputEntity(
                                    FAULT_HEADER.TABLE_NAME,
                                    faultHeader.toJson()));
                                if (ScreenType.desktop ==
                                    UIHelper().getScreenType(context)) {
                                  if (widget.onTap != null) {
                                    if (widget.onTap != null) {
                                      widget.onTap!(InteractiveItemModel(
                                          type: "", data: {}));
                                    }
                                  }
                                }
                                Navigator.pop(context);
                              }
                            },
                            icon: Icon(
                              Icons.arrow_back_ios,
                              color: AppColors.titleTextColor,
                              size: 20,
                            ))
                        : const SizedBox.shrink(),
            title: faultHeader.status == null
                ? Padding(
                    padding:
                        UIHelper().getScreenType(context) != ScreenType.desktop
                            ? const EdgeInsets.only(left: 18)
                            : EdgeInsets.zero,
                    child: Text(
                      AppLocalizations.of(context)!.new_string,
                      style: UIHelper.titleStyle14(),
                    ),
                  )
                : Padding(
                    padding:
                        UIHelper().getScreenType(context) != ScreenType.desktop
                            ? const EdgeInsets.only(left: 8.0)
                            : EdgeInsets.zero,
                    child: Text(
                      faultHeader.fault_id.toString(),
                      style: UIHelper.titleStyle16(),
                    )),
            actions: [
              editProvider.state
                  ? Row(
                      children: [
                        (kIsWeb ||
                                UIHelper().getScreenType(context) ==
                                    ScreenType.desktop)
                            ? ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.greenColor),
                                onPressed: () async {
                                  await onCheck(faultHeader.status);
                                },
                                child: Text('Save',
                                    style: TextStyle(color: AppColors.white)),
                              )
                            : InkWell(
                                onTap: () async {
                                  await onCheck(faultHeader.status);
                                },
                                child: const Icon(
                                  Icons.check,
                                  size: 30,
                                  color: AppColors.greenColor,
                                )),
                        const SizedBox(
                          width: 20,
                        ),
                        faultHeader.status == null
                            ? Container()
                            : (kIsWeb ||
                                    UIHelper().getScreenType(context) ==
                                        ScreenType.desktop)
                                ? ElevatedButton(
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor:
                                            AppColors.redAccentColor),
                                    onPressed: () {
                                      editProvider.getEditFaultFieldEnable(
                                          !editProvider.state);
                                      retainOriginalData();
                                      setState(() {});
                                    },
                                    child: Text('Cancel',
                                        style:
                                            TextStyle(color: AppColors.white)),
                                  )
                                : InkWell(
                                    onTap: () {
                                      editProvider.getEditFaultFieldEnable(
                                          !editProvider.state);
                                      setState(() {});
                                    },
                                    child: const Icon(
                                      Icons.cancel,
                                      color: AppColors.redAccentColor,
                                      size: 30,
                                    ),
                                  ),
                      ],
                    )
                  : Row(
                      children: [
                        Visibility(
                            visible: isExecutionVisible(),
                            child: (kIsWeb ||
                                    UIHelper().getScreenType(context) ==
                                        ScreenType.desktop)
                                ? getStatusActionButtonsWeb(faultHeader)
                                : getStatusActionButtons(faultHeader)),
                        Visibility(
                          visible: kIsWeb
                              ? (isExecutionVisible() || isFaultCreateVisible())
                              : ((isExecutionVisible() ||
                                      isFaultCreateVisible()) &&
                                  faultHeader.syncStatus != SyncStatus.sent),
                          child: isEditAllowed(faultHeader)
                              ? (kIsWeb ||
                                      UIHelper().getScreenType(context) ==
                                          ScreenType.desktop)
                                  ? Padding(
                                      padding: const EdgeInsets.only(left: 20),
                                      child: ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                AppColors.greySubtitleText),
                                        onPressed: () {
                                          editProvider.getEditFaultFieldEnable(
                                              !editProvider.state);
                                          setState(() {});
                                        },
                                        child: Text('Edit',
                                            style: TextStyle(
                                                color: AppColors.white)),
                                      ),
                                    )
                                  : Padding(
                                      padding: const EdgeInsets.only(left: 20),
                                      child: InkWell(
                                        onTap: () {
                                          editProvider.getEditFaultFieldEnable(
                                              !editProvider.state);
                                          setState(() {});
                                        },
                                        child: const Icon(
                                          Icons.edit,
                                          size: 28,
                                          color: AppColors.greySubtitleText,
                                        ),
                                      ),
                                    )
                              : const SizedBox.shrink(),
                        ),
                      ],
                    ),
              const SizedBox(
                width: 20,
              ),
            ],
          ),
          body: Container(
            color: AppColors.modernListBackground,
            child: SafeArea(
              child: Padding(
                padding: UIHelper().getScreenType(context) != ScreenType.desktop
                    ? const EdgeInsets.symmetric(horizontal: 16)
                    : const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Modern header section
                    Container(
                      margin: const EdgeInsets.only(top: 16, bottom: 16),
                      padding: const EdgeInsets.all(20),
                      decoration: UIHelper.modernCardDecoration(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Fault description
                          if (faultHeader.status != null &&
                              faultHeader.description != null &&
                              faultHeader.description != '')
                            if (!editProvider.state)
                              Text(
                                faultHeader.description.toString(),
                                style: UIHelper.modernTitleStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                          const SizedBox(height: 16),

                          // Modern badges section
                          Wrap(
                            spacing: 12,
                            runSpacing: 8,
                            children: [
                              // Priority badge
                              if (faultHeader.priority != null &&
                                  faultHeader.priority != '' &&
                                  !editProvider.state)
                                _buildModernPriorityBadge(faultHeader),

                              // Fault type badge
                              if (faultHeader.fault_type != null &&
                                  faultHeader.fault_type != '' &&
                                  !editProvider.state)
                                _buildModernFaultTypeBadge(faultHeader),

                              // Status badge
                              if (faultHeader.status != null &&
                                  faultHeader.status != '' &&
                                  !editProvider.state)
                                _buildModernStatusBadge(faultHeader),

                              // Job ID badge
                              if (faultHeader.job_id != null && !editProvider.state)
                                _buildModernJobBadge(faultHeader),
                            ],
                          ),
                        ],
                      ),
                    ),
                faultHeader.infoMsgCat == AppConstants.InfoMessageFailure
                    ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: FutureBuilder<List<InfoMessageData?>>(
                            future: fetchInfoMessage,
                            builder: (context, snapshot) {
                              if (snapshot.hasData) {
                                return MobileErrorAndLongTextview(
                                    type: TextDisplayType.error,
                                    text: snapshot.data!
                                        .map((e) => e!.message)
                                        .join("\n"));
                              } else {
                                return const SizedBox();
                              }
                            }),
                      )
                    : const SizedBox(),
                faultHeader.infoMsgCat == AppConstants.InfoMessageFailure
                    ? 10.0.spaceY
                    : const SizedBox(),
                faultHeader.infoMsgCat == AppConstants.InfoMessageWarning
                    ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: FutureBuilder<List<InfoMessageData?>>(
                            future: fetchInfoMessage,
                            builder: (context, snapshot) {
                              if (snapshot.hasData) {
                                return MobileErrorAndLongTextview(
                                    type: TextDisplayType.warning,
                                    // titleStyle: AppStyles.headLine15_600
                                    //     .copyWith(color: Colors.orangeAccent),
                                    text: snapshot.data!
                                        .map((e) => e!.message)
                                        .join("\n"));
                              } else {
                                return const SizedBox();
                              }
                            }),
                      )
                    : const SizedBox(),
                faultHeader.infoMsgCat == AppConstants.InfoMessageWarning
                    ? 10.0.spaceY
                    : const SizedBox(),
                (faultHeader.details != null && faultHeader.details != '')
                    ? editProvider.state!
                        ? Container()
                        : Padding(
                            padding: UIHelper.allPaddingOf8(),
                            child: MobileErrorAndLongTextview(
                              backgroundColor: AppColors.white,
                              bodyStyle: TextStyle(
                                  fontSize: 14, color: AppColors.black),
                              text: faultHeader.details.toString(),
                            ),
                          )
                    : SizedBox(),
                TabBar(
                  tabs: <Widget>[
                    Tab(
                      icon: FittedBox(
                        child: Text(
                          AppLocalizations.of(context)!.details,
                          style:
                              const TextStyle(color: AppColors.blackTitleText),
                        ),
                      ),
                    ),
                    Tab(
                      icon: FittedBox(
                        child: Text(
                          AppLocalizations.of(context)!.attachments,
                          style:
                              const TextStyle(color: AppColors.blackTitleText),
                        ),
                      ),
                    ),
                  ],
                ),
                Expanded(
                  child: TabBarView(
                    children: <Widget>[
                      NotificationListener<ScrollNotification>(
                        onNotification: (_) => true,
                        child: GeneralTabFault(
                          type: widget.type,
                          fault_header: faultHeader,
                        ),
                      ),
                      NotificationListener<ScrollNotification>(
                        onNotification: (_) => true,
                        child: AttachmentTabFault(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ),
    ),
    );
  }

  handleStatusActionButtonsFunctions(String type, FAULT_HEADER faultHeader,
      BuildContext context, WidgetRef ref) {
    if (type == 'assign') {
      UIHelper.showConfirmationDialogWithYesOrNo(context,
          description: AppLocalizations.of(context)!.do_you_want_to_assign_job,
          yes: () {
        onYes(type, faultHeader, '', context);
      }, no: () {
        Navigator.pop(context);
      });
    } else if (type == 'complete') {
      UIHelper.showConfirmationDialogWithYesOrNo(context,
          description: AppLocalizations.of(context)!
              .do_you_want_to_complete_fault, yes: () {
        onYes(type, faultHeader, Constants.FAULT_STATE_NOCO, context);
      }, no: () {
        Navigator.pop(context);
      });
    }
  }

  onYes(String type, FAULT_HEADER faultHeader, String statusType,
      BuildContext context) async {
    final faultHeaderData = ref.watch(faultHeaderProvider.notifier);
    final documents = ref.watch(getFaultDocumentHeaderProvider.notifier).state;
    final jobHeaderData = ref.watch(jobHeaderProvider.notifier).state;
    FAULT_HEADER header = faultHeader;
    await AppDatabaseManager()
        .update(DBInputEntity(FAULT_HEADER.TABLE_NAME, header.toJson()));
    await faultHeaderData.getFaultHeader(
        faultId: faultHeaderData.state.fault_id.toString());
    FAULT_ACTION? action =
        await DbHelper.getFaultActionByFaultId(header.fault_id.toString());
    if (action != null) {
      action.user_action = statusType;
      await AppDatabaseManager()
          .update(DBInputEntity(FAULT_ACTION.TABLE_NAME, action.toJson()));
    } else {
      FAULT_ACTION action = FAULT_ACTION(
          fault_id: header.fault_id.toString(), user_action: statusType);
      action.fid = header.lid;

      await AppDatabaseManager()
          .insert(DBInputEntity(FAULT_ACTION.TABLE_NAME, action.toJson()));
    }
    for (var data in documents) {
      FAULT_DOCUMENT newDocument = FAULT_DOCUMENT(
          fault_id: faultHeader.fault_id!.toInt(),
          doc_id: data.doc_id,
          p_mode: AppConstants.add);
      newDocument.fid = faultHeader.lid;
      FAULT_DOCUMENT? doc = await DbHelper.getFaultDocumentsByFaultId(
          faultHeader.fault_id.toString(), data.doc_id.toString());
      if (doc != null) {
      } else {
        if (doc != null) {
          DOCUMENT_HEADER? header =
              await DbHelper.getDocumentHeadersByDocsId(doc!.doc_id.toString());
          if (header != null) {
            await AppDatabaseManager().insert(
                DBInputEntity(FAULT_DOCUMENT.TABLE_NAME, newDocument.toJson()));
          }
        }
      }
    }

    //Navigator.pop(context);
    Navigator.of(context, rootNavigator: true).pop();
    if (type == 'assign') {
      var result = await navigateToJobCreationScreen(faultHeader);
      if (result != null) {
        header.job_id = jobHeaderData.job_id;
        await AppDatabaseManager()
            .insert(DBInputEntity(FAULT_HEADER.TABLE_NAME, header.toJson()));
        await faultHeaderData.getFaultHeader(
            faultId: faultHeader.fault_id.toString());
        FAULT_ACTION? action =
            await DbHelper.getFaultActionByFaultId(header.fault_id.toString());
        if (action != null) {
          action.user_action = statusType;
          await AppDatabaseManager()
              .update(DBInputEntity(FAULT_ACTION.TABLE_NAME, action.toJson()));
        } else {
          FAULT_ACTION action = FAULT_ACTION(
              fault_id: header.fault_id.toString(), user_action: statusType);
          action.fid = header.lid;

          await AppDatabaseManager()
              .insert(DBInputEntity(FAULT_ACTION.TABLE_NAME, action.toJson()));
        }
        if (mounted) {
          sendToServer('assign', faultHeader, context);
        }
      }
    } else {
      if (mounted) {
        await sendToServer(type, faultHeader, context);
      }
    }
  }

  sendToServer(
      String type, FAULT_HEADER faultHeader, BuildContext context) async {
    if (type == AppConstants.assign) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.assigning_job);
    } else if (type == AppConstants.complete) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.completing_fault);
    }
    Result? result;
    if (!kIsWeb) {
      result = await PAHelper.addOrModifyFaultInAsyncMode(context, faultHeader);
    } else {
      result = await PAHelper.addOrModifyFaultInSyncMode(context, faultHeader);
      if (result.body != null) {
        if (mounted) {
          Navigator.pop(context);
          final faultHeaderProviderData =
              ref.watch(faultHeaderProvider.notifier);
          final faultAction = ref.watch(getFaultActionProvider.notifier);
          final faultDocument = ref.watch(getFaultDocumentProvider.notifier);
          final documentAttachmentProviderData =
              ref.watch(documentAttachmentProvider.notifier);
          final faultNoticedOn = ref.watch(faultNoticedOnProvider.notifier);
          final dueOn = ref.watch(faultDueOnProvider.notifier);

          final faultDescription = ref.watch(faultDescriptionProvider.notifier);
          final faultLongText = ref.watch(faultLongTextProvider.notifier);
          final faultMode = ref.watch(faultModeHeaderProvider.notifier);
          final faultPriority = ref.watch(priorityProvider.notifier);
          final faultType = ref.watch(faultTypeProvider.notifier);
          final assetLocList = ref.read(assetLocListProvider.notifier);
          final plant = ref.read(plantProvider.notifier).state;
          await faultHeaderProviderData.getFaultHeader(
              faultId: faultHeader.fault_id.toString());
          if (faultHeaderProviderData.state.status != null) {
            if (faultHeaderProviderData.state.reported_on != null) {
              faultNoticedOn.getFaultNoticedOn(
                  faultHeaderProviderData.state.reported_on!);
            }
            if (faultHeaderProviderData.state.req_end != null) {
              dueOn.getDueOn(faultHeaderProviderData.state.req_end!);
            }
          }
          if (faultHeaderProviderData.state.description != null) {
            faultDescription.getFaultDescription(
                faultHeaderProviderData.state.description.toString());
          }
          if (faultHeaderProviderData.state.failure_mode != null) {
            faultMode.getFaultMode(ref
                .read(faultModeHeaderListProvider.notifier)
                .fetchFaultModeByCode(
                    faultHeaderProviderData.state.failure_mode.toString()));
          }
          if (faultHeaderProviderData.state.priority != null) {
            faultPriority.getPriority(ref
                .read(priorityListProvider.notifier)
                .fetchPriorityCode(
                    faultHeaderProviderData.state.priority.toString()));
          }
          if (faultHeaderProviderData.state.fault_type != null) {
            faultType.getFaultType(ref
                .watch(faultTypeListProvider.notifier)
                .fetchFaultTypeCode(
                    faultHeaderProviderData.state.fault_type.toString()));
          }
          if (faultHeaderProviderData.state.details != null) {
            faultLongText.getFaultLongText(
                faultHeaderProviderData.state.details.toString());
          }
          if (faultHeaderProviderData.state.asset_no != null) {
            await assetLocList.fetchAssetLocList(
                faultHeaderProviderData.state.location_id.toString());
          }
          await faultAction.getFaultAction(
              faultHeaderProviderData.state.fault_id.toString());
          await faultDocument.getFaultDocuments(
              faultHeaderProviderData.state.fault_id.toString());

          await documentAttachmentProviderData.fetchDocumentAttachments();
          final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
          await faultHeaderList.fetchFaultHeaderList(plant);
          setState(() {});
          if (mounted) {
            UIHelper.showResultInfoDialog(context,
                description:
                    'Fault ${(result.body['FAULT'] as List)[0]['FAULT_HEADER']['FAULT_ID']} ${UIHelper().toCamelCase((result.body['FAULT'] as List)[0]['FAULT_HEADER']['STATUS'])} Successfully.',
                onPressed: () async {
              Navigator.pop(context);
              faultDetailViewNotifier.value = InteractiveItemModel(
                  type: "FAULT_HEADER",
                  data: {"type": AppConstants.fault, "index": null});
            });
          }
        }
      }
    }

    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
    await faultHeaderList.fetchFaultHeaderList(plant);

    final filteredFaultType =
        ref.read(filteredFaultHeaderListProvider.notifier);
    final filterOfFaultType =
        ref.read(filterOfFaultTypeProvider.notifier).state;
    final filterOfFaultCode =
        ref.read(filterOfFaultCodeProvider.notifier).state;
    final filterOfPriorityCode =
        ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
    final filterOfPriority =
        ref.read(filterOfFaultPriorityProvider.notifier).state;
    final statusTypeFilter =
        ref.read(statusTypeFaultFilterProvider.notifier).state;
    final statusFilter = ref.read(statusFaultFilterProvider.notifier).state;
    final selectedDate = ref.read(selectedCalendarDateProvider);
    final search = ref.read(searchTextProvider.notifier).state;

    if ((search != '') ||
        filterOfFaultType.isNotEmpty ||
        filterOfFaultCode.isNotEmpty ||
        filterOfPriorityCode.isNotEmpty ||
        filterOfPriority.isNotEmpty ||
        statusTypeFilter.isNotEmpty ||
        statusFilter.isNotEmpty ||
        selectedDate != null) {
      await filteredFaultType.filteredFaultHeaderList(
          faulttypeList: filterOfFaultCode,
          priorityList: filterOfPriorityCode,
          statusList: statusTypeFilter,
          type: (search != '') ? AppConstants.search : AppConstants.faultType,
          plantId: plant,
          plantSec: plantSection,
          search: search);
    } else {
      await filteredFaultType.filteredFaultHeaderList(
          type: 'Initial',
          faultList: faultHeaderList.state,
          // faultList: faultHeaderDatas,
          plantId: plant,
          plantSec: plantSection);
    }

    if (!kIsWeb && ScreenType.desktop == UIHelper().getScreenType(context)) {
      Navigator.pop(context);
    } else if (!kIsWeb && ScreenType.desktop != UIHelper().getScreenType(context)){
      await Future.delayed(const Duration(seconds: 2));
      Navigator.pop(context);
       Navigator.pop(context);
    }
/*    faultDetailViewNotifier.value = InteractiveItemModel(
      type: "",
      data: {"type": "", "index": null},
    );*/
  }

  bool validate() {
    final attachment = ref.watch(getFaultDocumentProvider.notifier).state;
    final faultNoticedOnController =
        ref.watch(faultNoticedOnProvider.notifier).state;
    final dueOnController = ref.watch(faultDueOnProvider.notifier).state;
    final selectedLocation = ref.watch(locationProvider.notifier).state;
    final locationList = ref.watch(flocHeaderProvider.notifier).state;
    final assetList = ref.watch(assetHeaderProvider.notifier).state;
    final assetLocList = ref.read(assetLocListProvider.notifier).state;
    final selectedAsset = ref.watch(assetProvider.notifier).state;
    final selectedPlant = ref.watch(plantProvider.notifier).state;
    final selectedDescription =
        ref.watch(faultDescriptionProvider.notifier).state;
    final selectedLongText = ref.watch(faultLongTextProvider.notifier).state;
    final selectedFailureMode =
        ref.watch(faultModeHeaderProvider.notifier).state;
    final selectedFaultType = ref.watch(faultTypeProvider.notifier).state;
    final selectedPriority = ref.watch(priorityProvider.notifier).state;

    bool data = false;
    if (widget.type != AppConstants.ciltFault &&
        widget.type != AppConstants.inspFault) {
      if (selectedLocation.isEmpty || selectedAsset.isEmpty) {
        if (locationList.isEmpty &&
            selectedLocation.isEmpty &&
            assetList.isNotEmpty &&
            selectedAsset.isEmpty) {
          UIHelper.showErrorDialog(context,
              description: AppLocalizations.of(context)!.please_enter_asset);
          return data;
        } else if (locationList.isNotEmpty &&
            selectedLocation.isEmpty &&
            assetList.isEmpty &&
            selectedAsset.isEmpty) {
          UIHelper.showErrorDialog(context,
              description: AppLocalizations.of(context)!.please_enter_location);
          return data;
        } else if (locationList.isNotEmpty &&
            selectedLocation.isEmpty &&
            assetList.isNotEmpty &&
            selectedAsset.isEmpty) {
          UIHelper.showErrorDialog(context,
              description:
                  AppLocalizations.of(context)!.please_enter_location_or_asset);
          return data;
        }
      }
    }
    if (assetLocList.isNotEmpty) {
      if (selectedAsset.isEmpty) {
        UIHelper.showErrorDialog(context,
            description: AppLocalizations.of(context)!.please_enter_asset);
        return data;
      }
    }
    if (selectedPlant.isEmpty) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_enter_plant);
      return data;
    }

    if (selectedDescription.isEmpty) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_enter_description);
      return data;
    }
    if (selectedLongText.isEmpty) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_enter_long_text);
      return data;
    } else if (selectedFaultType.description == null) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_faultType);
      return data;
    } else if (selectedPriority.description == null) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_priority);
      return data;
    } else if (selectedFailureMode.description == null) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_failureMode);
      return data;
    } else if (faultNoticedOnController == 0) {
      UIHelper.showErrorDialog(context,
          description:
              AppLocalizations.of(context)!.please_select_faultNoticedDate);
      return data;
    } else if (dueOnController == 0) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_dueDate);
      return data;
    }
    return true;
  }

  Future<void> onNewFaultSave() async {
    try {
      if (kIsWeb) {
        if (!(await URLService.isInternetConnected())) {
          if (mounted) {
            UIHelper.showErrorDialog(
              context,
              description:
                  AppLocalizations.of(context)!.noInternetConnectivityString,
            );
          }
        }
      } else {
        if (mounted) {
          UIHelper().progressDialog(
              context: context,
              message: AppLocalizations.of(context)!.saving_fault);
          final header =
              ref.watch(getFaultDocumentHeaderProvider.notifier).state;
          if (kIsWeb) {
            List<DOCUMENT_HEADER> documents = header
                .where((element) => element.objectStatus == ObjectStatus.add)
                .toList();
            for (DOCUMENT_HEADER document in documents) {
              var doc = await DbHelper()
                  .getAttachmentFromIndexDbByUid(document.doc_id ?? "");
              await SyncEngine().uploadAttachmentSync(
                  doc ?? "", document.file_name ?? "", document.doc_id ?? "");
            }
            await PAHelper.addDocumentInSyncMode(context, documents);
          } else {
            Result? result =
                await PAHelper.addDocumentInAsyncMode(context, header);
            await ref
                .read(documentHeaderProvider.notifier)
                .fetchDocumentHeaders();
            await ref
                .watch(documentAttachmentProvider.notifier)
                .fetchDocumentAttachments();
          }
          await SyncEngine().receive();
          await creationOfFault();
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, 'onNewFaultSave', e.toString());
    }
  }

  Future<void> creationOfFault() async {
    try {
      final faultMode = ref.watch(faultModeHeaderProvider.notifier).state;
      final priority = ref.watch(priorityProvider.notifier).state;
      final faultType = ref.watch(faultTypeProvider.notifier).state;
      final header = ref.watch(getFaultDocumentHeaderProvider.notifier).state;
      var ciltTask;
      var ciltHeader;
      CILT_EXEC_TASK? ciltExecTask;
      var inspTask;
      var inspHeader;
      INSP_EXEC_TASK? inspExecTask;

      if (widget.type == AppConstants.ciltFault) {
        ciltTask = ref.watch(ciltTaskProvider.notifier).state;
        ciltHeader = ref.watch(ciltHeaderProvider);
        ciltExecTask = ref
            .watch(ciltExecuteTaskListProvider.notifier)
            .getciltExecTaskHeaderByTask(ciltTask, ciltHeader);
      } else if (widget.type == AppConstants.inspFault) {
        inspTask = ref.watch(inspectionTaskProvider.notifier).state;
        inspHeader = ref.watch(inspectionHeaderProvider);
        inspExecTask = ref
            .watch(inspExecuteTaskListProvider.notifier)
            .getInspectionExecTaskHeaderByTask(inspTask, inspHeader);
      }

      final faultHeader = ref.watch(faultHeaderProvider.notifier).state;
      final faultDescription =
          ref.watch(faultDescriptionProvider.notifier).state;
      final longText = ref.watch(faultLongTextProvider.notifier).state;
      final location = ref.read(locationProvider.notifier).state;
      final asset = ref.read(assetProvider.notifier).state;
      final plant = ref.read(plantProvider.notifier).state;
      final user = ref.watch(userProvider);
      var faultReportedByController =
          ref.watch(faultReportedByProvider.notifier).state;
      faultReportedByController = user!.user_id.toString();
      String reportedBy = faultReportedByController;
      final faultNoticedOnController =
          ref.watch(faultNoticedOnProvider.notifier).state;
      final dueOnController = ref.watch(faultDueOnProvider.notifier).state;
      faultHeader.location_id = location;
      if (asset.isNotEmpty) {
        faultHeader.asset_no = int.parse(asset);
      }
      faultHeader.plant_id = plant;
      faultHeader.description = faultDescription;
      faultHeader.details = longText;
      faultHeader.priority = priority.priority_code;
      faultHeader.failure_mode = faultMode.failure_code;
      faultHeader.fault_type = faultType.fault_code;
      faultHeader.cilt_task_id = widget.type == AppConstants.ciltFault
          ? ciltExecTask?.cilt_task_id
          : null;
      faultHeader.insp_task_id = widget.type == AppConstants.inspFault
          ? inspExecTask?.insp_task_id
          : null;

      faultHeader.reported_by = reportedBy;
      faultHeader.reported_on = faultNoticedOnController;
      if (dueOnController != 0) {
        faultHeader.req_end = dueOnController;
      }

      // TODO : Deepika
      FAULT_HEADER? existingFault = await DbHelper.getFaultHeaderByFaultId(
          faultHeader.fault_id.toString());
      if (existingFault == null) {
        await AppDatabaseManager().insert(
            DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));
      } else {
        await AppDatabaseManager().update(
            DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));
      }

      FAULT_ACTION newAction = FAULT_ACTION(
          fault_id: faultHeader.fault_id!.toString(),
          user_action: Constants.FAULT_STATE_OSNO);
      newAction.fid = faultHeader.lid;
      FAULT_ACTION? action = await DbHelper.getFaultActionByFaultId(
          faultHeader.fault_id.toString());
      if (action != null) {
        await AppDatabaseManager()
            .update(DBInputEntity(FAULT_ACTION.TABLE_NAME, action.toJson()));
      } else {
        await AppDatabaseManager()
            .insert(DBInputEntity(FAULT_ACTION.TABLE_NAME, newAction.toJson()));
      }
      for (var data in header) {
        FAULT_DOCUMENT newDocument = FAULT_DOCUMENT(
            fault_id: faultHeader.fault_id!.toInt(),
            doc_id: data.doc_id,
            p_mode: AppConstants.add);
        newDocument.fid = faultHeader.lid;
        FAULT_DOCUMENT? doc = await DbHelper.getFaultDocumentsByFaultId(
            faultHeader.fault_id.toString(), data.doc_id.toString());
        if (doc != null) {
          await AppDatabaseManager()
              .update(DBInputEntity(FAULT_DOCUMENT.TABLE_NAME, doc.toJson()));
        } else {
          await AppDatabaseManager().insert(
              DBInputEntity(FAULT_DOCUMENT.TABLE_NAME, newDocument.toJson()));
        }
      }

      Result? result;
      if (!kIsWeb) {
        await PAHelper.addOrModifyFaultInAsyncMode(context, faultHeader);
      } else {
        result =
            await PAHelper.addOrModifyFaultInSyncMode(context, faultHeader);
        Navigator.pop(context);
        if (result.body != null) {
          UIHelper.showResultInfoDialog(context,
              description:
                  'Fault  ${(result.body['FAULT'] as List)[0]['FAULT_HEADER']['FAULT_ID']} ${UIHelper().toCamelCase((result.body['FAULT'] as List)[0]['FAULT_HEADER']['STATUS'])} Successfully.',
              onPressed: () {
            Navigator.pop(context);
            Navigator.pop(context, result!.body["FAULT"][0]["FAULT_HEADER"]);
          });
        }
      }
      final plantSection = ref.read(plantSectionProvider.notifier).state;
      final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
      await faultHeaderList.fetchFaultHeaderList(plant);
      final filteredFaultType =
          ref.read(filteredFaultHeaderListProvider.notifier);

      final filterOfFaultType =
          ref.read(filterOfFaultTypeProvider.notifier).state;
      final filterOfFaultCode =
          ref.read(filterOfFaultCodeProvider.notifier).state;
      final filterOfPriorityCode =
          ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
      final filterOfPriority =
          ref.read(filterOfFaultPriorityProvider.notifier).state;
      final statusTypeFilter =
          ref.read(statusTypeFaultFilterProvider.notifier).state;
      final statusFilter = ref.read(statusFaultFilterProvider.notifier).state;
      final selectedDate = ref.read(selectedCalendarDateProvider);
      final search = ref.read(searchTextProvider.notifier).state;

      if ((search != '') ||
          filterOfFaultType.isNotEmpty ||
          filterOfFaultCode.isNotEmpty ||
          filterOfPriorityCode.isNotEmpty ||
          filterOfPriority.isNotEmpty ||
          statusTypeFilter.isNotEmpty ||
          statusFilter.isNotEmpty ||
          selectedDate != null) {
        await filteredFaultType.filteredFaultHeaderList(
            faulttypeList: filterOfFaultCode,
            priorityList: filterOfPriorityCode,
            statusList: statusTypeFilter,
            type: (search != '') ? AppConstants.search : AppConstants.faultType,
            plantId: plant,
            plantSec: plantSection,
            search: search);
      } else {
        await filteredFaultType.filteredFaultHeaderList(
            type: 'Initial',
            faultList: faultHeaderList.state,
            plantId: plant,
            plantSec: plantSection);
      }
      if (!kIsWeb) {
        Navigator.pop(context);
        Navigator.pop(context, faultHeader);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'creationOfFault', e.toString());
    }
  }

  Future<void> saveGeneralModifiedFault() async {
    try {
      final faultHeaderr = ref.read(faultHeaderProvider.notifier).state;
      final user = ref.watch(userProvider);

      if (kIsWeb) {
        if (!(await URLService.isInternetConnected())) {
          if (mounted) {
            UIHelper.showErrorDialog(context,
                description:
                    AppLocalizations.of(context)!.noInternetConnectivityString);
          }
        }
      }

      if (faultHeaderr.p_mode == AppConstants.add) {
        UIHelper().progressDialog(
            context: context,
            message: AppLocalizations.of(context)!.saving_fault);
      } else {
        UIHelper().progressDialog(
            context: context,
            message: AppLocalizations.of(context)!.updating_fault);
      }
      final editProvider = ref.watch(editFaultFieldProvider.notifier);
      final faultHeaderProviderr = ref.watch(faultHeaderProvider.notifier);
      final faultDescription =
          ref.watch(faultDescriptionProvider.notifier).state;
      /*     var faultReportedBy = ref.watch(faultReportedByProvider.notifier).state;

      faultReportedBy = user!.user_id.toString();
      String reportedBy = faultReportedBy;*/

      final faultNoticedOn = ref.watch(faultNoticedOnProvider.notifier).state;
      final faultDueOn = ref.watch(faultDueOnProvider.notifier).state;
      final faultMode = ref.watch(faultModeHeaderProvider.notifier).state;
      final priority = ref.watch(priorityProvider.notifier).state;
      final faultType = ref.watch(faultTypeProvider.notifier).state;
      final longText = ref.read(faultLongTextProvider.notifier).state;

      FAULT_HEADER faultHeader = faultHeaderr;
      if (faultNoticedOn != 0) {
        faultHeader.reported_on = faultNoticedOn;
      }
      if (faultDueOn != 0) {
        faultHeader.req_end = faultDueOn;
      }

      if (faultDescription.isNotEmpty || faultDescription != '') {
        faultHeader.description = faultDescription;
      }

      if (longText.isNotEmpty || longText != '') {
        faultHeader.details = longText;
      }

      if (faultMode.failure_code != null || faultMode.failure_code != '') {
        faultHeader.failure_mode = faultMode.failure_code;
      }
      if (priority.priority_code != null || priority.priority_code != '') {
        faultHeader.priority = priority.priority_code;
      }
      if (faultType.fault_code != null || faultType.fault_code != '') {
        faultHeader.fault_type = faultType.fault_code;
      }
      if (faultHeader.p_mode != AppConstants.add) {
        faultHeader.p_mode = AppConstants.modified;
      }
      await AppDatabaseManager()
          .update(DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));

      editProvider.getEditFaultFieldEnable(!editProvider.state);
      await faultHeaderProviderr.getFaultHeader(
          faultId: faultHeader.fault_id.toString());
      setState(() {});
    } catch (e) {
      Logger.logError(sourceClass, 'saveGeneralModifiedFault', e.toString());
    }
  }

  Future<void> saveModifiedFault() async {
    try {
      final faultHeader = ref.read(faultHeaderProvider.notifier).state;
      final headerData =
          ref.watch(getFaultDocumentHeaderProvider.notifier).state;
      FAULT_ACTION? actionData = await DbHelper.getFaultActionByFaultId(
          faultHeader.fault_id.toString());
      if (actionData == null) {
        FAULT_ACTION action = FAULT_ACTION(
            fault_id: faultHeader.fault_id!.toString(),
            user_action: Constants.FAULT_STATE_OSNO);
        action.fid = faultHeader.lid;
        await AppDatabaseManager()
            .insert(DBInputEntity(FAULT_ACTION.TABLE_NAME, action.toJson()));
      } else {
        await AppDatabaseManager().update(
            DBInputEntity(FAULT_ACTION.TABLE_NAME, actionData.toJson()));
      }

      List<DOCUMENT_HEADER> documents = headerData
          .where((element) => element.objectStatus == ObjectStatus.add)
          .toList();
      if (kIsWeb) {
        for (DOCUMENT_HEADER document in documents) {
          var doc = await DbHelper()
              .getAttachmentFromIndexDbByUid(document.doc_id ?? "");
          await SyncEngine().uploadAttachmentSync(
              doc ?? "", document.file_name ?? "", document.doc_id ?? "");
        }
        await PAHelper.addDocumentInSyncMode(context, documents);
      } else {
        await PAHelper.addDocumentInAsyncMode(context, documents);
        await ref.read(documentHeaderProvider.notifier).fetchDocumentHeaders();
        await ref
            .watch(documentAttachmentProvider.notifier)
            .fetchDocumentAttachments();
      }
      Result? result;
      if (!kIsWeb) {
        await PAHelper.addOrModifyFaultInAsyncMode(context, faultHeader);
      } else {
        result =
            await PAHelper.addOrModifyFaultInSyncMode(context, faultHeader);
        if (result.body != null) {
          Navigator.pop(context);
          if (faultHeader.p_mode != AppConstants.add &&
              (result.body['FAULT'] as List)[0]['FAULT_HEADER']['STATUS'] ==
                  Constants.FAULT_STATE_OSNO) {
            UIHelper.showResultInfoDialog(context,
                description:
                    'Fault  ${(result.body['FAULT'] as List)[0]['FAULT_HEADER']['FAULT_ID']} Updated Successfully.',
                onPressed: () {
              Navigator.pop(context);
            });
          } else {
            UIHelper.showResultInfoDialog(context,
                description:
                    'Fault  ${(result.body['FAULT'] as List)[0]['FAULT_HEADER']['FAULT_ID']} ${UIHelper().toCamelCase((result.body['FAULT'] as List)[0]['FAULT_HEADER']['STATUS'])} uccessfully.',
                onPressed: () {
              Navigator.pop(context);
            });
          }
        }
      }

      if (!kIsWeb && UIHelper().getScreenType(context) == ScreenType.desktop) {
        Navigator.pop(context);
      } else {
        Navigator.pop(context);
        Navigator.pop(context);
      }

      final plant = ref.read(plantProvider.notifier).state;
      final plantSection = ref.read(plantSectionProvider.notifier).state;
      final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
      await faultHeaderList.fetchFaultHeaderList(plant);
      final filteredFaultType =
          ref.read(filteredFaultHeaderListProvider.notifier);

      final filterOfFaultType =
          ref.read(filterOfFaultTypeProvider.notifier).state;
      final filterOfFaultCode =
          ref.read(filterOfFaultCodeProvider.notifier).state;
      final filterOfPriorityCode =
          ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
      final filterOfPriority =
          ref.read(filterOfFaultPriorityProvider.notifier).state;
      final statusTypeFilter =
          ref.read(statusTypeFaultFilterProvider.notifier).state;
      final statusFilter = ref.read(statusFaultFilterProvider.notifier).state;
      final selectedDate = ref.read(selectedCalendarDateProvider);
      final search = ref.read(searchTextProvider.notifier).state;

      if ((search != '') ||
          filterOfFaultType.isNotEmpty ||
          filterOfFaultCode.isNotEmpty ||
          filterOfPriorityCode.isNotEmpty ||
          filterOfPriority.isNotEmpty ||
          statusTypeFilter.isNotEmpty ||
          statusFilter.isNotEmpty ||
          selectedDate != null) {
        await filteredFaultType.filteredFaultHeaderList(
            faulttypeList: filterOfFaultCode,
            priorityList: filterOfPriorityCode,
            statusList: statusTypeFilter,
            type: (search != '') ? AppConstants.search : AppConstants.faultType,
            plantId: plant,
            plantSec: plantSection,
            search: search);
      } else {
        await filteredFaultType.filteredFaultHeaderList(
            type: 'Initial',
            search: search,
            faultList: faultHeaderList.state,
            // faultList: faultHeaderDatas,
            plantId: plant,
            plantSec: plantSection);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'saveModifiedFault', e.toString());
    }
  }

  Future<void> onCheck(String? status, {bool willScope = false}) async {
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    if (status != null) {
      if (willScope) {
        await saveGeneralModifiedFault();
        await saveModifiedFault();
      } else {
        UIHelper.showConfirmationDialogWithYesOrNo(context,
            description: AppLocalizations.of(context)!
                .confirmation_saving_modified_fault, yes: () async {
          Navigator.pop(context);
          await saveGeneralModifiedFault();
          await saveModifiedFault();
        }, no: () {
          Navigator.pop(context);
        });
      }
    } else {
      if (!validate()) {
        return;
      }
      if (willScope) {
        await onNewFaultSave();
      } else {
        if (faultHeader.p_mode == AppConstants.add) {
          UIHelper.showConfirmationDialog(
            context,
            description: AppLocalizations.of(context)!.save_fault_confirmation,
            positiveButtonString: AppLocalizations.of(context)!.ok,
            positiveButtonOnTap: () async {
              Navigator.pop(context);
              await onNewFaultSave();
            },
          );
        } else {
          UIHelper.showConfirmationDialog(
            context,
            description:
                AppLocalizations.of(context)!.update_fault_confirmation,
            positiveButtonString: AppLocalizations.of(context)!.ok,
            positiveButtonOnTap: () async {
              Navigator.pop(context);
              await onNewFaultSave();
            },
          );
        }
      }
    }
  }

  getStatusActionButtons(FAULT_HEADER faultHeader) {
    // For new faults (null status or p_mode == 'A'), show delete button
    if (faultHeader.status == null || faultHeader.p_mode == 'A') {
      return (faultHeader.syncStatus == SyncStatus.queued ||
              faultHeader.syncStatus == SyncStatus.sent)
          ? Container()
          : Row(children: [
              InkWell(
                onTap: () {
                  onDeleteFault(faultHeader);
                },
                child: const Icon(
                  Icons.delete,
                  size: 30,
                  color: AppColors.redAccentColor,
                ),
              )
            ]);
    }

    // For Open faults, show Create Job and Complete buttons
    if (FaultStatusHelper.isFaultOpen(faultHeader)) {
      return (faultHeader.syncStatus == SyncStatus.queued ||
              faultHeader.syncStatus == SyncStatus.sent ||
              faultHeader.syncStatus == SyncStatus.error)
          ? Container()
          : Row(children: [
              // Create Job button - only if job creation is allowed and no job exists
              (isJobCreateVisible() && FaultStatusHelper.shouldShowCreateJobButton(faultHeader))
                  ? InkWell(
                      onTap: () {
                        handleStatusActionButtonsFunctions(
                            'assign', faultHeader, context, ref);
                      },
                      child: const Icon(
                        Icons.assignment_turned_in,
                        size: 28,
                        color: AppColors.blue,
                      ),
                    )
                  : SizedBox(),
              (isJobCreateVisible() && FaultStatusHelper.shouldShowCreateJobButton(faultHeader))
                  ? const SizedBox(width: 20)
                  : SizedBox(),
              // Complete button - always show for Open faults
              FaultStatusHelper.shouldShowCompleteButton(faultHeader)
                  ? InkWell(
                      onTap: () {
                        handleStatusActionButtonsFunctions(
                            'complete', faultHeader, context, ref);
                      },
                      child: const Icon(
                        Icons.flag,
                        size: 30,
                        color: AppColors.greenColor,
                      ),
                    )
                  : SizedBox(),
            ]);
    }

    // For all other statuses (Assigned, Completed, etc.), show no action buttons
    return Container();
  }

  getStatusActionButtonsWeb(FAULT_HEADER faultHeader) {
    if (faultHeader.status == null || faultHeader.p_mode == 'A') {
      return (faultHeader.syncStatus == SyncStatus.queued ||
              faultHeader.syncStatus == SyncStatus.sent)
          ? Container()
          : Row(children: [
              ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.redAccentColor),
                  onPressed: () {
                    onDeleteFault(faultHeader);
                  },
                  child:
                      Text('Delete', style: TextStyle(color: AppColors.white)))
            ]);
    }

    // For Open faults, show Create Job and Complete buttons
    if (FaultStatusHelper.isFaultOpen(faultHeader)) {
      return (faultHeader.syncStatus == SyncStatus.queued ||
              faultHeader.syncStatus == SyncStatus.sent ||
              faultHeader.syncStatus == SyncStatus.error)
          ? Container()
          : Row(children: [
              // Create Job button - only if job creation is allowed and no job exists
              (isJobCreateVisible() && FaultStatusHelper.shouldShowCreateJobButton(faultHeader))
                  ? ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.blue),
                      onPressed: () {
                        handleStatusActionButtonsFunctions(
                            'assign', faultHeader, context, ref);
                      },
                      child: Text(
                        'Create Job',
                        style: TextStyle(color: AppColors.white),
                      ))
                  : SizedBox(),
              (isJobCreateVisible() && FaultStatusHelper.shouldShowCreateJobButton(faultHeader))
                  ? const SizedBox(width: 20)
                  : SizedBox(),
              // Complete button - always show for Open faults
              FaultStatusHelper.shouldShowCompleteButton(faultHeader)
                  ? ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.greenColor),
                      onPressed: () {
                        handleStatusActionButtonsFunctions(
                            'complete', faultHeader, context, ref);
                      },
                      child: Text(
                        'Complete',
                        style: TextStyle(color: AppColors.white),
                      ))
                  : SizedBox(),
            ]);
    }

    // For all other statuses (Assigned, Completed, etc.), show no action buttons
    return Container();
  }

  void onDeleteFault(FAULT_HEADER faultHeader) async {
    UIHelper.showConfirmationDialogWithYesOrNo(context,
        description: AppLocalizations.of(context)!.confirmation_to_delete_fault,
        yes: () async {
      Navigator.pop(context);
      String deleteQuery =
          'DELETE FROM ${FAULT_HEADER.TABLE_NAME} WHERE ${FAULT_HEADER.FIELD_FAULT_ID} = ${faultHeader.fault_id}';
      await AppDatabaseManager().execute(deleteQuery);
      if (!kIsWeb) {
        Navigator.pop(context);
      } else {
        faultDetailViewNotifier.value = InteractiveItemModel(
          type: "",
          data: {"type": AppConstants.fault, "index": null},
        );
        clearFaultStates();
      }
      final plant = ref.read(plantProvider.notifier).state;
      final plantSection = ref.read(plantSectionProvider.notifier).state;
      final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
      await faultHeaderList.fetchFaultHeaderList(plant);
      final filteredFaultType =
          ref.read(filteredFaultHeaderListProvider.notifier);
      final filterOfFaultType =
          ref.read(filterOfFaultTypeProvider.notifier).state;
      final filterOfFaultCode =
          ref.read(filterOfFaultCodeProvider.notifier).state;
      final filterOfPriorityCode =
          ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
      final filterOfPriority =
          ref.read(filterOfFaultPriorityProvider.notifier).state;
      final statusTypeFilter =
          ref.read(statusTypeFaultFilterProvider.notifier).state;
      final statusFilter = ref.read(statusFaultFilterProvider.notifier).state;
      final selectedDate = ref.read(selectedCalendarDateProvider);
      final search = ref.read(searchTextProvider.notifier).state;

      if ((search != '') ||
          filterOfFaultType.isNotEmpty ||
          filterOfFaultCode.isNotEmpty ||
          filterOfPriorityCode.isNotEmpty ||
          filterOfPriority.isNotEmpty ||
          statusTypeFilter.isNotEmpty ||
          statusFilter.isNotEmpty ||
          selectedDate != null) {
        await filteredFaultType.filteredFaultHeaderList(
            faulttypeList: filterOfFaultCode,
            priorityList: filterOfPriorityCode,
            statusList: statusTypeFilter,
            type: (search != '') ? AppConstants.search : AppConstants.faultType,
            plantId: plant,
            plantSec: plantSection,
            search: search);
      } else {
        await filteredFaultType.filteredFaultHeaderList(
            type: 'Initial',
            faultList: faultHeaderList.state,
            plantId: plant,
            plantSec: plantSection);
      }
    }, no: () {
      Navigator.pop(context);
    });
  }

  Future<void> onUnsaveFault(FAULT_HEADER faultHeader) async {
    Navigator.pop(context); //closing the dialog
    if (faultHeader.p_mode != null && faultHeader.p_mode == AppConstants.add) {
      String deleteQuery =
          'DELETE FROM ${FAULT_HEADER.TABLE_NAME} WHERE ${FAULT_HEADER.FIELD_FAULT_ID} = ${faultHeader.fault_id}';
      await AppDatabaseManager().execute(deleteQuery);
    }
    ref.watch(editFaultFieldProvider.notifier).getEditFaultFieldEnable(false);
    Navigator.pop(context, faultHeader);
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
    await faultHeaderList.fetchFaultHeaderList(plant);
    final filteredFaultType =
        ref.read(filteredFaultHeaderListProvider.notifier);
    final filterOfFaultType =
        ref.read(filterOfFaultTypeProvider.notifier).state;
    final filterOfFaultCode =
        ref.read(filterOfFaultCodeProvider.notifier).state;
    final filterOfPriorityCode =
        ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
    final filterOfPriority =
        ref.read(filterOfFaultPriorityProvider.notifier).state;
    final statusTypeFilter =
        ref.read(statusTypeFaultFilterProvider.notifier).state;
    final statusFilter = ref.read(statusFaultFilterProvider.notifier).state;
    final selectedDate = ref.read(selectedCalendarDateProvider);
    final search = ref.read(searchTextProvider.notifier).state;

    if ((search != '') ||
        filterOfFaultType.isNotEmpty ||
        filterOfFaultCode.isNotEmpty ||
        filterOfPriorityCode.isNotEmpty ||
        filterOfPriority.isNotEmpty ||
        statusTypeFilter.isNotEmpty ||
        statusFilter.isNotEmpty ||
        selectedDate != null) {
      await filteredFaultType.filteredFaultHeaderList(
          faulttypeList: filterOfFaultCode,
          priorityList: filterOfPriorityCode,
          statusList: statusTypeFilter,
          type: (search != '') ? AppConstants.search : AppConstants.faultType,
          plantId: plant,
          plantSec: plantSection,
          search: search);
    } else {
      await filteredFaultType.filteredFaultHeaderList(
          type: 'Initial',
          faultList: faultHeaderList.state,
          plantId: plant,
          plantSec: plantSection);
    }
    if (ScreenType.desktop == UIHelper().getScreenType(context)) {
      clearFaultStates();
    } else if (!kIsWeb) {
      clearFaultStates();
    }
  }

  clearFaultStates() {
    final originalFaultHeader = ref.read(faultHeaderProvider);
    final faultHeader = ref.read(faultHeaderProvider.notifier);
    final location = ref.watch(locationProvider.notifier);
    final assetLocList = ref.watch(assetLocListProvider.notifier);
    final asset = ref.watch(assetProvider.notifier);
    final dueOn = ref.watch(faultDueOnProvider.notifier);
    final faultNoticedOn = ref.watch(faultNoticedOnProvider.notifier);
    final description = ref.watch(faultDescriptionProvider.notifier);
    final faultMode = ref.watch(faultModeHeaderProvider.notifier);
    final priority = ref.watch(priorityProvider.notifier);
    final faultType = ref.watch(faultTypeProvider.notifier);
    final longText = ref.watch(faultLongTextProvider.notifier);
    final reportedBy = ref.watch(faultReportedByProvider.notifier);
    print('original fault header ${originalFaultHeader.toJson()}');
    print('fault header ${faultHeader.state.toJson()}');
    location.clearLocation();
    assetLocList.clearAssetLocList();
    asset.clearAsset();
    faultNoticedOn.clearFaultNoticedOn();
    dueOn.clearDueOn();
    description.clearFaultDescription();
    faultMode.clearFaultMode();
    priority.clearPriority();
    faultType.clearFaultType();
    longText.clearFaultLongText();
    reportedBy.clearFaultReportedBy();
    faultHeader.clearFault();
    final originalFaultHeader1 = ref.watch(faultHeaderProvider.notifier);
    final faultHeader1 = ref.watch(faultHeaderProvider.notifier);
    if (faultHeader.state.p_mode != null &&
        faultHeader.state.p_mode == AppConstants.modified) {
      faultHeader.resetFault(originalFaultHeader);
      description
          .getFaultDescription(originalFaultHeader.description.toString());
      faultNoticedOn.getFaultNoticedOn(originalFaultHeader.reported_on!);

      if (originalFaultHeader.req_end != null) {
        dueOn.getDueOn(originalFaultHeader.req_end!);
      }

      faultMode.getFaultMode(ref
          .read(faultModeHeaderListProvider.notifier)
          .fetchFaultModeByCode(originalFaultHeader.failure_mode.toString()));
      priority.getPriority(ref
          .read(priorityListProvider.notifier)
          .fetchPriorityCode(originalFaultHeader.priority.toString()));
      faultType.getFaultType(ref
          .watch(faultTypeListProvider.notifier)
          .fetchFaultTypeCode(originalFaultHeader.fault_type.toString()));
      longText.getFaultLongText(originalFaultHeader.details.toString());
      assetLocList
          .fetchAssetLocList(originalFaultHeader.location_id.toString());
    }
  }

  clearJobStates() {
    final description = ref.watch(jobDescriptionProvider.notifier);
    final longText = ref.watch(jobLongTextProvider.notifier);
    final priority = ref.watch(jobPriorityProvider.notifier);
    final jobType = ref.watch(jobTypeProvider.notifier);
    final assignedTo = ref.watch(jobAssignedToProvider.notifier);
    final startDate = ref.watch(jobStartOnProvider.notifier);
    final endDate = ref.watch(jobEndOnProvider.notifier);
    description.clearJobDescription();
    longText.clearLongText();
    priority.clearPriority();
    jobType.clearJobType();
    assignedTo.clearAssignedTo();
    startDate.clearStartDate();
    endDate.clearEndDate();
  }

  navigateToJobCreationScreen(FAULT_HEADER faultHeader) async {
    clearJobStates();
    final editJobCreation = ref.watch(editJobCreationProvider.notifier);
    final plant = ref.watch(plantProvider.notifier).state;
    final jobHeaderInsertion = ref.watch(insertJobHeaderProvider.notifier);
    final jobHeader = ref.watch(jobHeaderProvider.notifier);
    final jobAction = ref.watch(getJobActionProvider.notifier);
    final jobDocument = ref.watch(getJobDocumentProvider.notifier);
    editJobCreation.getEditJobCreationEnable(true);
    DateTime now = DateTime.now();
    String faultNoticedOnDate = DateFormat('dd MMM yyyy').format(now);
    DateTime dateTime = DateFormat("dd MMM yyyy").parse(faultNoticedOnDate);
    DateTime adjustedDate = DateTime(
      dateTime.year,
      dateTime.month,
      dateTime.day,
    );
    String formattedDate = DateFormat("yyyyMMdd").format(adjustedDate);
    int dateAsInt = int.parse(formattedDate);
    JOB_HEADER newJobHeader = JOB_HEADER(
        plant_id: plant,
        job_id: UIHelper.generateRandomId(),
        p_mode: AppConstants.add,
        fault_id: faultHeader.fault_id,
        location_id: faultHeader.location_id,
        asset_no: faultHeader.asset_no,
        priority: faultHeader.priority,
        start_date: dateAsInt);
    await jobHeaderInsertion.insertJobHeader(newJobHeader);
    await jobHeader.getJobHeader(jobId: newJobHeader.job_id.toString());
    await jobAction.getJobAction(newJobHeader.job_id.toString());
    await jobDocument.getJobDocuments(newJobHeader.job_id.toString());
    docAttachments.clear();
    dynamic result;
    if (mounted) {
      if (ScreenType.desktop == UIHelper().getScreenType(context)) {
        result = await showDialog(
            context: context,
            barrierDismissible: false,
            builder: (rootDialogContext) => Dialog(
                backgroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0),
                ),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxWidth: 600,
                    maxHeight: 800,
                  ),
                  child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: JobCreationDetailScreen(
                        isBackButtonRequire: true,
                        fromFault: true,
                      )),
                )));
        //     await Navigator.push(context, MaterialPageRoute(builder: (context) {
        //   return JobCreationDetailScreen(
        //     fromFault: true,
        //   );
        // }));
      } else {
        if (ref.read(bottomNavIndexProvider.notifier).state == 1) {
          Navigator.pop(context);
        }
        result =
            await Navigator.push(context, MaterialPageRoute(builder: (context) {
          return JobCreationDetailScreen(
            fromFault: true,
          );
        }));
        // result = await showDialog(
        //     context: context,
        //     barrierDismissible: false,
        //     builder: (rootDialogContext) => Dialog(
        //         backgroundColor: Colors.white,
        //         shape: RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(20.0),
        //         ),
        //         child: ConstrainedBox(
        //           constraints: const BoxConstraints(
        //             maxWidth: 600,
        //             maxHeight: 800,
        //           ),
        //           child: Padding(
        //               padding: const EdgeInsets.all(20.0),
        //               child: JobCreationDetailScreen(
        //                 isBackButtonRequire: true,
        //                 fromFault: true,
        //               )),
        //         )));
      }
      if (result != null) {
        final faultHeaderData = ref.watch(faultHeaderProvider.notifier);
        await faultHeaderData.getFaultHeader(
            faultId: faultHeader.fault_id.toString());

        setState(() {});
      }
    }
  }

  void onJob(JOB_HEADER jobHeaderData) async {
    final jobHeader = ref.read(jobHeaderProvider.notifier);
    final editProvider = ref.read(editJobCreationProvider.notifier);
    await jobHeader.getJobHeader(jobId: jobHeaderData.job_id.toString());

    editProvider.getEditJobCreationEnable(false);
    if (mounted) {
      if (!kIsWeb) {
        if (ScreenType.desktop == UIHelper().getScreenType(context)) {
          Navigator.pop(context);
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (rootDialogContext) => Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxWidth: 600,
                      maxHeight: 800,
                    ),
                    child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: JobCreationDetailScreen(
                          fromFault: false,
                          isBackButtonRequire: true,
                          display: true,
                        )),
                  )));
        } else {
          Navigator.pop(context);
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => JobCreationDetailScreen(
                      fromFault: false,
                    )),
          );
        }
      } else {
        if (ref.read(bottomNavIndexProvider.notifier).state == 1) {
          Navigator.pop(context);
        }
        if (ref.read(bottomNavIndexProvider.notifier).state == 1 ||
            ref.read(bottomNavIndexProvider.notifier).state == 2) {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (rootDialogContext) => Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxWidth: 600,
                      maxHeight: 800,
                    ),
                    child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: JobCreationDetailScreen(
                          fromFault: false,
                          isBackButtonRequire: true,
                          display: true,
                        )),
                  )));
        } else {
          Navigator.pop(context);
        }
      }
    }
  }

  JOB_HEADER? isJobAvaiable(FAULT_HEADER faultHeader) {
    final jobList = ref.watch(jobHeaderListProvider);
    JOB_HEADER? header;
    header = jobList.firstWhereOrNull((element) =>
        element.fault_id.toString() == faultHeader.fault_id!.toString());
    if (header != null) {
      return header;
    }
    return null;
  }

  bool isAssignVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isAssign(role.fault!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isJobCreateVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isCreate(role.task!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isFaultCreateVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isCreate(role.fault!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isExecutionVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isExecute(role.fault!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isEditAllowed(FAULT_HEADER faultHeader) {
    return FaultStatusHelper.isEditAllowed(faultHeader);
  }

  retainOriginalData() {
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    final faultNoticedOn = ref.read(faultNoticedOnProvider.notifier);
    final faultDueOn = ref.read(faultDueOnProvider.notifier);
    final faultPriority = ref.read(priorityProvider.notifier);
    final faultType = ref.read(faultTypeProvider.notifier);
    final faultMode = ref.read(faultModeHeaderProvider.notifier);
    if (faultHeader.reported_on != null) {
      faultNoticedOn.getFaultNoticedOn(faultHeader.reported_on!);
    }
    if (faultHeader.req_end != null) {
      faultDueOn.getDueOn(faultHeader.req_end!);
    }

    if (faultHeader.priority != null) {
      faultPriority.getPriority(ref
          .read(priorityListProvider.notifier)
          .fetchPriorityCode(faultHeader.priority.toString()));
    }
    if (faultHeader.fault_type != null) {
      faultType.getFaultType(ref
          .read(faultTypeListProvider.notifier)
          .fetchFaultTypeCode(faultHeader.fault_type.toString()));
    }
    if (faultHeader.failure_mode != null) {
      faultMode.getFaultMode(ref
          .read(faultModeHeaderListProvider.notifier)
          .fetchFaultModeByCode(faultHeader.failure_mode.toString()));
    }
    final description = ref.watch(faultDescriptionProvider.notifier);
    if (faultHeader.description != null && faultHeader.description != '') {
      descriptionController.text = faultHeader.description.toString();
      description.getFaultDescription(longTextController.text);
    }
    final longText = ref.read(faultLongTextProvider.notifier);

    if (faultHeader.details != null && faultHeader.details != '') {
      longTextController.text = faultHeader.details.toString();
      longText.getFaultLongText(longTextController.text);
    }
    fetchInfoMessage =
        InfoMessageHelper().getInfoMessageByBeLid(faultHeader.lid);

    /*  clearFaultStates();*/
  }

  // Modern badge builders for detail screen
  Widget _buildModernPriorityBadge(FAULT_HEADER faultHeader) {
    String priorityText = ref
        .watch(priorityListProvider.notifier)
        .fetchPriorityCode(faultHeader.priority.toString());

    Color badgeColor;
    switch (priorityText.toLowerCase()) {
      case 'high':
        badgeColor = AppColors.highPriorityBadge;
        break;
      case 'medium':
        badgeColor = AppColors.mediumPriorityBadge;
        break;
      case 'low':
        badgeColor = AppColors.lowPriorityBadge;
        break;
      default:
        badgeColor = AppColors.modernTertiaryText;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        priorityText,
        style: UIHelper.modernCaptionStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildModernFaultTypeBadge(FAULT_HEADER faultHeader) {
    String faultTypeText = ref
        .watch(faultTypeListProvider.notifier)
        .fetchFaultTypeCode(faultHeader.fault_type.toString());

    Color badgeColor;
    switch (faultTypeText.toLowerCase()) {
      case 'mechanical':
      case 'mechanical breakdown':
        badgeColor = AppColors.mechanicalBadge;
        break;
      case 'electrical':
        badgeColor = AppColors.electricalBadge;
        break;
      case 'hydraulic':
        badgeColor = AppColors.hydraulicBadge;
        break;
      case 'instrument':
        badgeColor = AppColors.instrumentBadge;
        break;
      default:
        badgeColor = AppColors.primaryColor;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: badgeColor.withOpacity(0.3)),
      ),
      child: Text(
        faultTypeText,
        style: UIHelper.modernCaptionStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: badgeColor,
        ),
      ),
    );
  }

  Widget _buildModernStatusBadge(FAULT_HEADER faultHeader) {
    String statusText = UIHelper.getStatusString(faultHeader.status.toString());
    Color badgeColor;

    switch (statusText.toLowerCase()) {
      case 'open':
        badgeColor = AppColors.openStatusColor;
        break;
      case 'assigned':
      case 'job assigned':
        badgeColor = AppColors.assignedStatusColor;
        break;
      case 'completed':
        badgeColor = AppColors.completedStatusColor;
        break;
      default:
        badgeColor = AppColors.modernTertiaryText;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        statusText,
        style: UIHelper.modernCaptionStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildModernJobBadge(FAULT_HEADER faultHeader) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.assignedStatusColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.work_outline,
            size: 14,
            color: Colors.white,
          ),
          const SizedBox(width: 4),
          Text(
            faultHeader.job_id.toString(),
            style: UIHelper.modernCaptionStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
