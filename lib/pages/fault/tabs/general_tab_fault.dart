import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/be/ASSET_HEADER.dart';
import 'package:rounds/be/LOCATION_HEADER.dart';
import 'package:rounds/pages/fault/tabs/edit_fault_field_provider.dart';
import 'package:rounds/utils/app_constants.dart';
import 'package:rounds/utils/utils.dart';

import '../../../be/FAULT_HEADER.dart';
import '../../../be/USER_HEADER.dart';
import '../../../helpers/ui_helper.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../providers/assets/asset_provider.dart';
import '../../../providers/assets/floc_provider.dart';
import '../../../providers/fault/fault_header_provider.dart';
import '../../../providers/fault/fault_type_provider.dart';
import '../../../providers/user_provider.dart';
import '../../../utils/app_colors.dart';
import 'package:intl/intl.dart';

import '../../../widgets/custom_drop_down.dart';

class GeneralTabFault extends ConsumerStatefulWidget {
  final String type;
  final FAULT_HEADER fault_header;
  GeneralTabFault({required this.type, required this.fault_header, super.key});

  @override
  _GeneralTabFaultState createState() => _GeneralTabFaultState();
}

class _GeneralTabFaultState extends ConsumerState<GeneralTabFault> {
  String selectedLocation = '';
  String selectedAsset = '';
  String selectedPlant = '';
  String selectedStatus = '';
  String selectedFaultType = '';
  String selectedFailureMode = '';
  String selectedPriority = '';
  TextEditingController reportedByController = TextEditingController();
  TextEditingController faultNoticedOnDateController = TextEditingController();
  TextEditingController dueOnDateController = TextEditingController();
  @override
  void initState() {
    // TODO: implement initState
    selectedLocation = widget.fault_header.location_id != null
        ? getLocationDescription('${widget.fault_header.location_id}')
        : "";
    selectedAsset = widget.fault_header.asset_no != null
        ? getAssetDescription('${widget.fault_header.asset_no}')
        : "";
    selectedPlant = widget.fault_header.plant_id ?? "";
    selectedStatus = widget.fault_header.status ?? "";
    selectedFaultType = (widget.fault_header.fault_type != null)
        ? ref
            .read(faultTypeListProvider.notifier)
            .fetchFaultTypeCode(widget.fault_header.fault_type.toString())
        : '';
    selectedFailureMode = (widget.fault_header.failure_mode != null)
        ? ref.read(faultModeHeaderListProvider.notifier).fetchFaultModeByCode(
            widget.fault_header.failure_mode.toString().toUpperCase())
        : '';
    selectedPriority = (widget.fault_header.priority != null)
        ? ref
            .read(priorityListProvider.notifier)
            .fetchPriorityCode(widget.fault_header.priority.toString())
        : '';
    if (widget.fault_header.req_end != null &&
        widget.fault_header.req_end != 0) {
      int? dateAsInt = widget.fault_header.req_end;
      String dateString = dateAsInt.toString();
      DateTime dateTime = DateTime.parse(dateString);
      String formattedDate = DateFormat("dd MMM yyyy").format(dateTime);
      dueOnDateController.text = formattedDate;
    }
    final faultNoticedOn = ref.read(faultNoticedOnProvider.notifier);
    if (faultNoticedOn.state != 0) {
      int? dateAsInt = faultNoticedOn.state;
      String dateString = dateAsInt.toString();
      DateTime dateTime = DateTime.parse(dateString);
      String formattedDate = DateFormat("dd MMM yyyy").format(dateTime);
      faultNoticedOnDateController.text = formattedDate;
    }

    reportedByController.text = widget.fault_header.reported_by ?? "";
    super.initState();
  }

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();

    final faultHeader = ref.read(faultHeaderProvider.notifier).state;

  if (UIHelper().getScreenType(context) != ScreenType.desktop) {
    if (faultHeader.description?.isNotEmpty ?? false) {
      descriptionController.text = faultHeader.description!;
      ref.read(faultDescriptionProvider.notifier)
         .getFaultDescription(faultHeader.description!);
    }

    if (faultHeader.details?.isNotEmpty ?? false) {
      longTextController.text = faultHeader.details!;
      ref.read(faultLongTextProvider.notifier)
         .getFaultLongText(faultHeader.details!);
    }
  }
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        child: Column(
          children: [
            UIHelper.sizedBox8(),
            getDetails(),
          ],
        ),
      ),
    );
  }

  Widget getDetails() {
    return Padding(
      padding: UIHelper.columnFieldPadding(),
      child: Column(
        children: [
          Container(
            decoration: UIHelper.cardDecoration(),
            child: Padding(
              padding: UIHelper.allPaddingOf10(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ScreenType.desktop != UIHelper().getScreenType(context)
                      ? getGeneralField()
                      : getGeneralFieldWeb(),
                  Divider(thickness: 1, color: Colors.grey[300]),
                  UIHelper.sizedBox8(),
                  ScreenType.desktop != UIHelper().getScreenType(context)
                      ? getTechnicalObjects()
                      : getTechnicalObjectsWeb(),
                  Divider(thickness: 1, color: Colors.grey[300]),
                  UIHelper.sizedBox8(),
                  ScreenType.desktop != UIHelper().getScreenType(context)
                      ? getDates()
                      : getDatesWeb(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget getPriorityChoiceChip() {
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    final priorityList = ref.watch(priorityListProvider.notifier).state;
    final priority = ref.watch(priorityProvider.notifier);
    if (selectedPriority.isEmpty || selectedPriority == '') {
      if (faultHeader.priority != null || faultHeader.priority != '') {
        selectedPriority = ref
            .read(priorityListProvider.notifier)
            .fetchPriorityCode(priority.state.priority_code.toString());

        priority.getPriority(selectedPriority);
      }
    }
    if (priority.state.priority_code != null &&
        priority.state.priority_code!.isNotEmpty) {
      selectedPriority = ref
          .read(priorityListProvider.notifier)
          .fetchPriorityCode(priority.state.priority_code.toString());
    }
    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: UIHelper.fieldDecoration(),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
              child: LayoutBuilder(builder: (context, constraints) {
                return Wrap(
                  spacing: 12,
                  children: priorityList.map((option) {
                    final isSelected = selectedPriority == option.description;
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: ChoiceChip(
                        labelPadding: const EdgeInsets.symmetric(
                            horizontal: 15, vertical: 8),
                        label: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            option.description.toString(),
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColors.titleTextColor,
                              letterSpacing: 0.1,
                            ),
                          ),
                        ),
                        selected: isSelected,
                        selectedColor:
                            UIHelper.getPriorityColor(selectedPriority),
                        backgroundColor: Colors.grey[200],
                        onSelected: (selected) {
                          setState(() {
                            selectedPriority = option.description!;
                            priority.getPriority(selectedPriority);
                          });
                        },
                      ),
                    );
                  }).toList(),
                );
              }),
            ),
          ),
        ),
      ],
    );
  }

  TextEditingController descriptionController = TextEditingController();
  TextEditingController longTextController = TextEditingController();
  bool isExpandedForLocationDetail = false;

  Widget getTechnicalObjects() {
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    final location = ref.read(locationProvider.notifier);
    final assetLocList = ref.read(assetLocListProvider.notifier).state;
    final asset = ref.read(assetProvider.notifier);
    if (faultHeader.location_id != null) {
      location.getLocation(faultHeader.location_id.toString());
    }
    if (faultHeader.asset_no != null) {
      asset.getAsset(faultHeader.asset_no.toString());
    }
    if (location.state.isNotEmpty) {
      selectedLocation = getLocationDescription(location.state);
    }
    if (asset.state.isNotEmpty) {
      selectedAsset = getAssetDescription(asset.state);
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.technical_objects,
          style: UIHelper.faultHeaderStyle(),
        ),
        widget.type == AppConstants.addFault && faultHeader.location_id == null
            ? UIHelper.buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                label: AppLocalizations.of(context)!.location_id,
                value: getLocationIdDropdown(),
                isTextFieldRequiredAsValueWidget: true)
            : (faultHeader.location_id != null)
                ? (faultHeader.location_id != "")
                    ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                        label: AppLocalizations.of(context)!.functionalLocation,
                        value: faultHeader.location_id.toString(),
                        controller: TextEditingController(),
                        isTextFieldRequiredAsValueWidget: false)
                    : Container()
                : Container(),
        widget.type == AppConstants.addFault && faultHeader.asset_no == null
            ? assetLocList.isNotEmpty
                ? UIHelper.buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                    label: AppLocalizations.of(context)!.asset,
                    value: getAssetDropdown(),
                    isTextFieldRequiredAsValueWidget: true)
                : Container()
            : (faultHeader.asset_no != null)
                ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                    label: AppLocalizations.of(context)!.asset,
                    value: faultHeader.asset_no.toString(),
                    controller: TextEditingController(),
                    isTextFieldRequiredAsValueWidget: false)
                : Container(),
      ],
    );
  }

  Widget getTechnicalObjectsWeb() {
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    final locationList = ref.watch(flocHeaderProvider.notifier).state;
    final location = ref.read(locationProvider.notifier);
    final assetLocList = ref.read(assetLocListProvider.notifier).state;
    final assetList = ref.read(assetHeaderProvider.notifier).state;
    final asset = ref.read(assetProvider.notifier);
    if (faultHeader.location_id != null) {
      location.getLocation(faultHeader.location_id.toString());
    }
    if (faultHeader.asset_no != null) {
      asset.getAsset(faultHeader.asset_no.toString());
    }
    if (location.state.isNotEmpty) {
      selectedLocation = getLocationDescription(location.state);
    }
    if (asset.state.isNotEmpty) {
      selectedAsset = getAssetDescription(asset.state);
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.technical_objects,
          style: UIHelper.faultHeaderStyle(),
        ),
        Row(children: [
          locationList.isNotEmpty
              ? widget.type == AppConstants.addFault &&
                      faultHeader.location_id == null
                  ? Expanded(
                      child: widget.type == AppConstants.addFault &&
                              faultHeader.location_id == null
                          ? UIHelper
                              .buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                                  label:
                                      AppLocalizations.of(context)!.location_id,
                                  value: getLocationIdDropdown(),
                                  isTextFieldRequiredAsValueWidget: true)
                          : (faultHeader.location_id != null)
                              ? (faultHeader.location_id != "")
                                  ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                                      label: AppLocalizations.of(context)!
                                          .functionalLocation,
                                      value: faultHeader.location_id.toString(),
                                      controller: TextEditingController(),
                                      isTextFieldRequiredAsValueWidget: false)
                                  : Container()
                              : Container())
                  : (faultHeader.location_id != null)
                      ? (faultHeader.location_id != "")
                          ? Expanded(
                              child: UIHelper.buildLabelAndValueAsWidgetOfFault(
                                  label: AppLocalizations.of(context)!
                                      .functionalLocation,
                                  value: faultHeader.location_id.toString(),
                                  controller: TextEditingController(),
                                  isTextFieldRequiredAsValueWidget: false))
                          : Container()
                      : Container()
              : Container(),
          locationList.isNotEmpty
              ? widget.type == AppConstants.addFault &&
                      faultHeader.asset_no == null
                  ? SizedBox(width: 10)
                  : (faultHeader.asset_no != null
                      ? SizedBox(
                          width: 10,
                        )
                      : SizedBox())
              : SizedBox(),
          widget.type == AppConstants.addFault && faultHeader.asset_no == null
              ? assetList.isNotEmpty
                  ? (selectedLocation != '' && selectedLocation != '--Select--')
                      ? assetLocList.isNotEmpty
                          ? Expanded(
                              child: UIHelper
                                  .buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                                      label:
                                          AppLocalizations.of(context)!.asset,
                                      value: getAssetDropdownWeb('assetLoc'),
                                      isTextFieldRequiredAsValueWidget: true),
                            )
                          : Container()
                      : Expanded(
                          child: UIHelper
                              .buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                                  label: AppLocalizations.of(context)!.asset,
                                  value: getAssetDropdownWeb('asset'),
                                  isTextFieldRequiredAsValueWidget: true),
                        )
                  : Container()
              : (faultHeader.asset_no != null)
                  ? Expanded(
                      child: UIHelper.buildLabelAndValueAsWidgetOfFault(
                          label: AppLocalizations.of(context)!.asset,
                          value: faultHeader.asset_no.toString(),
                          controller: TextEditingController(),
                          isTextFieldRequiredAsValueWidget: false),
                    )
                  : Container(),
        ])
      ],
    );
  }

  Widget getDatesWeb() {
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    final editProvider = ref.watch(editFaultFieldProvider.notifier).state;
    final reportedBy = ref.watch(faultReportedByProvider.notifier);
    final faultNoticedOn = ref.watch(faultNoticedOnProvider.notifier);
    final user = ref.watch(userProvider);
    final dueOn = ref.watch(faultDueOnProvider.notifier).state;
    final userList = ref.watch(usersListProvider);
    USER_HEADER? reportedByData;
    if (faultNoticedOn.state != 0) {
      int? dateAsInt = faultNoticedOn.state;
      String dateString = dateAsInt.toString();
      // Parse YYYYMMDD format correctly
      DateTime dateTime = convertToDateTime(dateString);
      String formattedDate = DateFormat("dd MMM yyyy").format(dateTime);
      faultNoticedOnDateController.text = formattedDate;
    }
    if (dueOn != 0) {
      int? dateAsInt = dueOn;
      String dateString = dateAsInt.toString();
      // Parse YYYYMMDD format correctly
      DateTime dateTime = convertToDateTime(dateString);
      String formattedDate = DateFormat("dd MMM yyyy").format(dateTime);
      dueOnDateController.text = formattedDate;
    }
    if (faultHeader.reported_by != null && faultHeader.reported_by != '') {
      reportedByData = userList.firstWhereOrNull(
          (element) => element.user_id == faultHeader.reported_by);
      if (reportedByData != null) {
        reportedByController.text = UIHelper().toCamelCase(
            '${reportedByData.first_name} ${reportedByData.last_name}');
      }
      reportedBy.getFaultReportedBy((reportedByController.text));
    } else {
      faultHeader.reported_by = user!.user_id;
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.dates,
          style: UIHelper.faultHeaderStyle(),
        ),
        (faultHeader.status == null)
            ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                readOnly: true,
                label: AppLocalizations.of(context)!.reportedby,
                value: UIHelper().toCamelCase(reportedBy.state),
                isTextFieldRequiredAsValueWidget: editProvider,
                suffixIcon: false,
                controller: reportedByController,
              )
            : (faultHeader.reported_by != null)
                ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                    readOnly: true,
                    label: AppLocalizations.of(context)!.reportedby,
                    value: UIHelper().toCamelCase(reportedBy.state),
                    isTextFieldRequiredAsValueWidget: editProvider,
                    suffixIcon: false,
                    onChanged: (v) {
                      reportedByController.text = v;
                      reportedBy.getFaultReportedBy(v);
                    },
                    controller: reportedByController,
                  )
                : Container(),
        Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: (faultHeader.status == null)
                    ? Padding(
                        padding: UIHelper.columnFieldOnlyVerticalPadding10(),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context)!.faultNoticedOn,
                              style: UIHelper.labelStyle(),
                            ),
                            editProvider
                                ? TextFormField(
                                    style: UIHelper.valueStyle14(),
                                    controller: faultNoticedOnDateController,
                                    onChanged: (v) {},
                                    enabled: true,
                                    onTap: () {},
                                    maxLines: null,
                                    readOnly: true,
                                    decoration: InputDecoration(
                                        contentPadding:
                                            const EdgeInsets.only(left: 5),
                                        border: OutlineInputBorder(
                                            gapPadding: 2,
                                            borderRadius:
                                                const BorderRadius.all(
                                                    Radius.circular(5)),
                                            borderSide: BorderSide(
                                                color: AppColors.cardBorderGrey,
                                                width: 1)),
                                        enabledBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                                color: AppColors.cardBorderGrey,
                                                width: 1)),
                                        focusedBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                                color: AppColors.cardBorderGrey,
                                                width: 1)),
                                        suffixIcon: InkWell(
                                            onTap: () {
                                              selectFaultNoticedOnDate(
                                                  context: context,
                                                  type: AppConstants
                                                      .faultNoticedOn,
                                                  faultHeader: faultHeader,
                                                  initialDate: convertedToDateTime(
                                                      faultNoticedOnDateController
                                                          .text),
                                                  lastDate: DateTime.now());
                                            },
                                            child:
                                                const Icon(Icons.date_range))),
                                  )
                                : Text(
                                    !editProvider
                                        ? UIHelper.formatDate(
                                            faultHeader.reported_on.toString())
                                        : '',
                                    style: UIHelper.valueStyle14(),
                                  ),
                          ],
                        ),
                      )
                    : (faultHeader.reported_on != null)
                        ? Padding(
                            padding:
                                UIHelper.columnFieldOnlyVerticalPadding10(),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppLocalizations.of(context)!.faultNoticedOn,
                                  style: UIHelper.labelStyle(),
                                ),
                                editProvider
                                    ? TextFormField(
                                        style: UIHelper.valueStyle14(),
                                        controller:
                                            faultNoticedOnDateController,
                                        onChanged: (v) {},
                                        enabled: true,
                                        onTap: () {},
                                        maxLines: null,
                                        readOnly: true,
                                        decoration: InputDecoration(
                                            contentPadding:
                                                const EdgeInsets.only(left: 5),
                                            border: OutlineInputBorder(
                                                gapPadding: 2,
                                                borderRadius:
                                                    const BorderRadius.all(
                                                        Radius.circular(5)),
                                                borderSide: BorderSide(
                                                    color: AppColors
                                                        .cardBorderGrey,
                                                    width: 1)),
                                            enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    color: AppColors
                                                        .cardBorderGrey,
                                                    width: 1)),
                                            focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    color: AppColors
                                                        .cardBorderGrey,
                                                    width: 1)),
                                            suffixIcon: InkWell(
                                                onTap: () {
                                                  selectFaultNoticedOnDate(
                                                      context: context,
                                                      type: AppConstants
                                                          .faultNoticedOn,
                                                      faultHeader: faultHeader,
                                                      initialDate:
                                                          convertedToDateTime(
                                                              faultNoticedOnDateController
                                                                  .text),
                                                      firstDate: DateTime(2000),
                                                      lastDate: DateTime.now());
                                                },
                                                child: const Icon(
                                                    Icons.date_range))),
                                      )
                                    : Text(
                                        UIHelper.formatDate(
                                            faultHeader.reported_on.toString()),
                                        style: UIHelper.valueStyle14(),
                                      ),
                              ],
                            ),
                          )
                        : Container(),
              ),
              SizedBox(width: 10),
              Expanded(
                child: (faultHeader.status == null)
                    ? Padding(
                        padding: UIHelper.columnFieldOnlyVerticalPadding10(),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context)!.dueOn,
                              style: UIHelper.labelStyle(),
                            ),
                            editProvider
                                ? TextFormField(
                                    style: UIHelper.valueStyle14(),
                                    controller: dueOnDateController,
                                    onChanged: (v) {},
                                    enabled: true,
                                    onTap: () {},
                                    maxLines: null,
                                    readOnly: true,
                                    decoration: InputDecoration(
                                        contentPadding:
                                            const EdgeInsets.only(left: 5),
                                        border: OutlineInputBorder(
                                            gapPadding: 2,
                                            borderRadius:
                                                const BorderRadius.all(
                                                    Radius.circular(5)),
                                            borderSide: BorderSide(
                                                color: AppColors.cardBorderGrey,
                                                width: 1)),
                                        enabledBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                                color: AppColors.cardBorderGrey,
                                                width: 1)),
                                        focusedBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                                color: AppColors.cardBorderGrey,
                                                width: 1)),
                                        suffixIcon: InkWell(
                                            onTap: () {
                                              selectDueOnDate(
                                                  context: context,
                                                  type: AppConstants.dueOn,
                                                  faultHeader: faultHeader,
                                                  initialDate: DateTime.now(),
                                                  firstDate: DateTime.now(),
                                                  lastDate: DateTime(2100));
                                            },
                                            child: Icon(Icons.date_range))),
                                  )
                                : Text(
                                    faultHeader.req_end == null
                                        ? ''
                                        : UIHelper.formatDate(
                                            faultHeader.req_end.toString()),
                                    style: UIHelper.valueStyle14(),
                                  ),
                          ],
                        ),
                      )
                    : (faultHeader.req_end != null)
                        ? Padding(
                            padding:
                                UIHelper.columnFieldOnlyVerticalPadding10(),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppLocalizations.of(context)!.dueOn,
                                  style: UIHelper.labelStyle(),
                                ),
                                editProvider
                                    ? TextFormField(
                                        style: UIHelper.valueStyle14(),
                                        controller: dueOnDateController,
                                        onChanged: (v) {},
                                        enabled: true,
                                        onTap: () {},
                                        maxLines: null,
                                        readOnly: true,
                                        decoration: InputDecoration(
                                            contentPadding:
                                                const EdgeInsets.only(left: 5),
                                            border: OutlineInputBorder(
                                                gapPadding: 2,
                                                borderRadius:
                                                    const BorderRadius.all(
                                                        Radius.circular(5)),
                                                borderSide: BorderSide(
                                                    color: AppColors
                                                        .cardBorderGrey,
                                                    width: 1)),
                                            enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    color: AppColors
                                                        .cardBorderGrey,
                                                    width: 1)),
                                            focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                    color: AppColors
                                                        .cardBorderGrey,
                                                    width: 1)),
                                            suffixIcon: InkWell(
                                                onTap: () {
                                                  selectDueOnDate(
                                                      context: context,
                                                      type: AppConstants.dueOn,
                                                      faultHeader: faultHeader,
                                                      initialDate:
                                                          DateTime.now(),
                                                      firstDate: DateTime.now(),
                                                      lastDate: DateTime(2100));
                                                },
                                                child: Icon(Icons.date_range))),
                                      )
                                    : Text(
                                        !editProvider
                                            ? UIHelper.formatDate(
                                                faultHeader.req_end.toString())
                                            : '',
                                        style: UIHelper.valueStyle14(),
                                      ),
                              ],
                            ),
                          )
                        : Container(),
              )
            ])
      ],
    );
  }

  Widget getDates() {
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    final editProvider = ref.watch(editFaultFieldProvider.notifier).state;
    final reportedBy = ref.watch(faultReportedByProvider.notifier);
    final faultNoticedOn = ref.watch(faultNoticedOnProvider.notifier);
    final user = ref.watch(userProvider);
    final dueOn = ref.watch(faultDueOnProvider.notifier).state;
    final userList = ref.watch(usersListProvider);
    USER_HEADER? reportedByData;
    if (faultNoticedOn.state != 0) {
      int? dateAsInt = faultNoticedOn.state;
      String dateString = dateAsInt.toString();
      // Parse YYYYMMDD format correctly
      DateTime dateTime = convertToDateTime(dateString);
      String formattedDate = DateFormat("dd MMM yyyy").format(dateTime);
      faultNoticedOnDateController.text = formattedDate;
    }
    if (dueOn != 0) {
      int? dateAsInt = dueOn;
      String dateString = dateAsInt.toString();
      // Parse YYYYMMDD format correctly
      DateTime dateTime = convertToDateTime(dateString);
      String formattedDate = DateFormat("dd MMM yyyy").format(dateTime);
      dueOnDateController.text = formattedDate;
    }
    if (faultHeader.reported_by != null && faultHeader.reported_by != '') {
      reportedByData = userList.firstWhereOrNull(
          (element) => element.user_id == faultHeader.reported_by);
      if (reportedByData != null) {
        reportedByController.text = UIHelper().toCamelCase(
            '${reportedByData.first_name} ${reportedByData.last_name}');
      }
      reportedBy.getFaultReportedBy((reportedByController.text));
    } else {
      faultHeader.reported_by = user!.user_id;
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.dates,
          style: UIHelper.faultHeaderStyle(),
        ),
        (faultHeader.status == null)
            ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                readOnly: true,
                label: AppLocalizations.of(context)!.reportedby,
                value: UIHelper().toCamelCase(reportedBy.state),
                isTextFieldRequiredAsValueWidget: editProvider,
                suffixIcon: false,
                controller: reportedByController,
              )
            : (faultHeader.reported_by != null)
                ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                    readOnly: true,
                    label: AppLocalizations.of(context)!.reportedby,
                    value: UIHelper().toCamelCase(reportedBy.state),
                    isTextFieldRequiredAsValueWidget: editProvider,
                    suffixIcon: false,
                    onChanged: (v) {
                      reportedByController.text = v;
                      reportedBy.getFaultReportedBy(v);
                    },
                    controller: reportedByController,
                  )
                : Container(),
        (faultHeader.status == null)
            ? Padding(
                padding: UIHelper.columnFieldOnlyVerticalPadding10(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.faultNoticedOn,
                      style: UIHelper.labelStyle(),
                    ),
                    editProvider
                        ? TextFormField(
                            style: UIHelper.valueStyle14(),
                            controller: faultNoticedOnDateController,
                            onChanged: (v) {},
                            enabled: true,
                            onTap: () {},
                            maxLines: null,
                            readOnly: true,
                            decoration: InputDecoration(
                                contentPadding: const EdgeInsets.only(left: 5),
                                border: OutlineInputBorder(
                                    gapPadding: 2,
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(5)),
                                    borderSide: BorderSide(
                                        color: AppColors.cardBorderGrey,
                                        width: 1)),
                                enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: AppColors.cardBorderGrey,
                                        width: 1)),
                                focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: AppColors.cardBorderGrey,
                                        width: 1)),
                                suffixIcon: InkWell(
                                    onTap: () {
                                      selectFaultNoticedOnDate(
                                          context: context,
                                          type: AppConstants.faultNoticedOn,
                                          faultHeader: faultHeader,
                                          initialDate: convertedToDateTime(
                                              faultNoticedOnDateController
                                                  .text),
                                          lastDate: DateTime.now());
                                    },
                                    child: const Icon(Icons.date_range))),
                          )
                        : Text(
                            !editProvider
                                ? UIHelper.formatDate(
                                    faultHeader.reported_on.toString())
                                : '',
                            style: UIHelper.valueStyle14(),
                          ),
                  ],
                ),
              )
            : (faultHeader.reported_on != null)
                ? Padding(
                    padding: UIHelper.columnFieldOnlyVerticalPadding10(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context)!.faultNoticedOn,
                          style: UIHelper.labelStyle(),
                        ),
                        editProvider
                            ? TextFormField(
                                style: UIHelper.valueStyle14(),
                                controller: faultNoticedOnDateController,
                                onChanged: (v) {},
                                enabled: true,
                                onTap: () {},
                                maxLines: null,
                                readOnly: true,
                                decoration: InputDecoration(
                                    contentPadding:
                                        const EdgeInsets.only(left: 5),
                                    border: OutlineInputBorder(
                                        gapPadding: 2,
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(5)),
                                        borderSide: BorderSide(
                                            color: AppColors.cardBorderGrey,
                                            width: 1)),
                                    enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: AppColors.cardBorderGrey,
                                            width: 1)),
                                    focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: AppColors.cardBorderGrey,
                                            width: 1)),
                                    suffixIcon: InkWell(
                                        onTap: () {
                                          selectFaultNoticedOnDate(
                                              context: context,
                                              type: AppConstants.faultNoticedOn,
                                              faultHeader: faultHeader,
                                              initialDate: convertedToDateTime(
                                                  faultNoticedOnDateController
                                                      .text),
                                              firstDate: DateTime(2000),
                                              lastDate: DateTime.now());
                                        },
                                        child: const Icon(Icons.date_range))),
                              )
                            : Text(
                                UIHelper.formatDate(
                                    faultHeader.reported_on.toString()),
                                style: UIHelper.valueStyle14(),
                              ),
                      ],
                    ),
                  )
                : Container(),
        (faultHeader.status == null)
            ? Padding(
                padding: UIHelper.columnFieldOnlyVerticalPadding10(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.dueOn,
                      style: UIHelper.labelStyle(),
                    ),
                    editProvider
                        ? TextFormField(
                            style: UIHelper.valueStyle14(),
                            controller: dueOnDateController,
                            onChanged: (v) {},
                            enabled: true,
                            onTap: () {},
                            maxLines: null,
                            readOnly: true,
                            decoration: InputDecoration(
                                contentPadding: const EdgeInsets.only(left: 5),
                                border: OutlineInputBorder(
                                    gapPadding: 2,
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(5)),
                                    borderSide: BorderSide(
                                        color: AppColors.cardBorderGrey,
                                        width: 1)),
                                enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: AppColors.cardBorderGrey,
                                        width: 1)),
                                focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: AppColors.cardBorderGrey,
                                        width: 1)),
                                suffixIcon: InkWell(
                                    onTap: () {
                                      selectDueOnDate(
                                          context: context,
                                          type: AppConstants.dueOn,
                                          faultHeader: faultHeader,
                                          initialDate: DateTime.now(),
                                          firstDate: DateTime.now(),
                                          lastDate: DateTime(2100));
                                    },
                                    child: Icon(Icons.date_range))),
                          )
                        : Text(
                            faultHeader.req_end == null
                                ? ''
                                : UIHelper.formatDate(
                                    faultHeader.req_end.toString()),
                            style: UIHelper.valueStyle14(),
                          ),
                  ],
                ),
              )
            : (faultHeader.req_end != null)
                ? Padding(
                    padding: UIHelper.columnFieldOnlyVerticalPadding10(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context)!.dueOn,
                          style: UIHelper.labelStyle(),
                        ),
                        editProvider
                            ? TextFormField(
                                style: UIHelper.valueStyle14(),
                                controller: dueOnDateController,
                                onChanged: (v) {},
                                enabled: true,
                                onTap: () {},
                                maxLines: null,
                                readOnly: true,
                                decoration: InputDecoration(
                                    contentPadding:
                                        const EdgeInsets.only(left: 5),
                                    border: OutlineInputBorder(
                                        gapPadding: 2,
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(5)),
                                        borderSide: BorderSide(
                                            color: AppColors.cardBorderGrey,
                                            width: 1)),
                                    enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: AppColors.cardBorderGrey,
                                            width: 1)),
                                    focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: AppColors.cardBorderGrey,
                                            width: 1)),
                                    suffixIcon: InkWell(
                                        onTap: () {
                                          selectDueOnDate(
                                              context: context,
                                              type: AppConstants.dueOn,
                                              faultHeader: faultHeader,
                                              initialDate: DateTime.now(),
                                              firstDate: DateTime.now(),
                                              lastDate: DateTime(2100));
                                        },
                                        child: Icon(Icons.date_range))),
                              )
                            : Text(
                                !editProvider
                                    ? UIHelper.formatDate(
                                        faultHeader.req_end.toString())
                                    : '',
                                style: UIHelper.valueStyle14(),
                              ),
                      ],
                    ),
                  )
                : Container(),
      ],
    );
  }

  DateTime? _selectedFaultNoticedOnDate;
  Future<void> selectFaultNoticedOnDate(
      {required BuildContext context,
      required String type,
      required FAULT_HEADER faultHeader,
      DateTime? firstDate,
      DateTime? lastDate,
      required DateTime initialDate}) async {
    final faultNoticedOn = ref.read(faultNoticedOnProvider.notifier);
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedFaultNoticedOnDate ?? initialDate,
      firstDate: firstDate ?? DateTime(2000),
      lastDate: lastDate ?? DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Colors.blue,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      setState(() {
        _selectedFaultNoticedOnDate = pickedDate;
        if (type == AppConstants.faultNoticedOn) {
          faultNoticedOnDateController.text =
              DateFormat('dd MMM yyyy').format(_selectedFaultNoticedOnDate!);
          DateTime dateTime =
              DateFormat("dd MMM yyyy").parse(faultNoticedOnDateController.text);
          String formattedDate = DateFormat("yyyyMMdd").format(dateTime);

          int dateAsInt = int.parse(formattedDate);
          faultNoticedOn.getFaultNoticedOn(dateAsInt);
        }
      });
    }
  }

  DateTime? _selectedFaultDueOnDate;
  Future<void> selectDueOnDate(
      {required BuildContext context,
      required String type,
      required FAULT_HEADER faultHeader,
      DateTime? firstDate,
      DateTime? lastDate,
      required DateTime initialDate}) async {
    final dueOn = ref.read(faultDueOnProvider.notifier);
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedFaultDueOnDate ?? initialDate,
      firstDate: firstDate ?? DateTime(2000),
      lastDate: lastDate ?? DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Colors.blue,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      setState(() {
        _selectedFaultDueOnDate = pickedDate;

        if (type == AppConstants.dueOn) {
          dueOnDateController.text =
              DateFormat('dd MMM yyyy').format(_selectedFaultDueOnDate!);
          DateTime dateTime =
              DateFormat("dd MMM yyyy").parse(dueOnDateController.text);
          String formattedDate = DateFormat("yyyyMMdd").format(dateTime);
          int dateAsInt = int.parse(formattedDate);
          // faultHeader.req_end = dateAsInt;
          dueOn.getDueOn(dateAsInt);
        }
      });
    }
  }

  Widget getGeneralField() {
    final editProvider = ref.watch(editFaultFieldProvider.notifier).state;
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    final description = ref.watch(faultDescriptionProvider.notifier);
    final longText = ref.watch(faultLongTextProvider.notifier);
    if (faultHeader.description != null && faultHeader.description != '' && ScreenType.desktop == UIHelper().getScreenType(context)) {
      descriptionController.text = faultHeader.description.toString();
      description.getFaultDescription(descriptionController.text);
    }
    if (description.state.isNotEmpty) {
      descriptionController.text = description.state;
    }
    if (faultHeader.details != null && faultHeader.details != '' && ScreenType.desktop == UIHelper().getScreenType(context)) {
      longTextController.text = faultHeader.details.toString();
      longText.getFaultLongText(longTextController.text);
    }
    if (longText.state.isNotEmpty) {
      longTextController.text = longText.state;
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.general,
          style: UIHelper.faultHeaderStyle(),
        ),
        (faultHeader.status == null)
            ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                label: AppLocalizations.of(context)!.description,
                value: faultHeader.description.toString(),
                controller: descriptionController,
                inputFormatters: [
                  LengthLimitingTextInputFormatter(40),
                ],
                onChanged: (v) {
                  if(ScreenType.desktop == UIHelper().getScreenType(context)){
                    TextSelection previousSelection =
                      descriptionController.selection;
                  descriptionController.text = v;
                  descriptionController.selection = previousSelection;
                  // faultHeader.description = descriptionController.text;
                  description.getFaultDescription(v);
                  } else{
                    description.getFaultDescription(v);
                  }
                },
                isTextFieldRequiredAsValueWidget: editProvider)
            : (faultHeader.description != null || faultHeader.description != '')
                ? editProvider
                    ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                        label: AppLocalizations.of(context)!.description,
                        value: faultHeader.description.toString(),
                        controller: descriptionController,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(40),
                        ],
                        onChanged: (v) {
                          if (ScreenType.desktop ==
                              UIHelper().getScreenType(context)) {
                            TextSelection previousSelection =
                                descriptionController.selection;
                            descriptionController.text = v;
                            descriptionController.selection = previousSelection;
                            // faultHeader.description = descriptionController.text;
                            description.getFaultDescription(v);
                          } else {
                            description.getFaultDescription(v);
                          }
                        },
                        isTextFieldRequiredAsValueWidget: editProvider)
                    : Container()
                : Container(),
        (faultHeader.status == null)
            ? (faultHeader.fault_type == null || faultHeader.fault_type == '')
                ? UIHelper.buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                    label: AppLocalizations.of(context)!.faultType,
                    value: getFaultTypeDropdown(),
                    isTextFieldRequiredAsValueWidget: editProvider)
                : UIHelper.buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                    label: AppLocalizations.of(context)!.faultType,
                    value: getFaultTypeDropdown(),
                    isTextFieldRequiredAsValueWidget: editProvider)
            : editProvider
                ? faultHeader.p_mode == AppConstants.add
                    ? UIHelper
                        .buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                            label: AppLocalizations.of(context)!.faultType,
                            value: getFaultTypeDropdown(),
                            isTextFieldRequiredAsValueWidget: editProvider)
                    : const SizedBox()
                : const SizedBox(),
        (faultHeader.status == null)
            ? (faultHeader.priority == null || faultHeader.priority == '')
                ? UIHelper.buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                    label: AppLocalizations.of(context)!.priority,
                    value: getPriorityChoiceChip(),
                    isTextFieldRequiredAsValueWidget: editProvider)
                : Container()
            : editProvider
                ? UIHelper.buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                    label: AppLocalizations.of(context)!.priority,
                    value: getPriorityChoiceChip(),
                    isTextFieldRequiredAsValueWidget: editProvider)
                : Container(),
        (faultHeader.status == null)
            ? UIHelper.buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                label: AppLocalizations.of(context)!.faultMode,
                value: editProvider
                    ? getFailureModeDropdown()
                    : Text(
                        ref
                            .watch(faultModeHeaderListProvider.notifier)
                            .fetchFaultModeByCode(
                                faultHeader.failure_mode.toString()),
                        style: UIHelper.valueStyle14(),
                      ),
                isTextFieldRequiredAsValueWidget: true)
            : (faultHeader.failure_mode != null &&
                    faultHeader.failure_mode != '')
                ? UIHelper.buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                    label: AppLocalizations.of(context)!.faultMode,
                    value: editProvider
                        ? getFailureModeDropdown()
                        : Text(
                            ref
                                .watch(faultModeHeaderListProvider.notifier)
                                .fetchFaultModeByCode(
                                    faultHeader.failure_mode.toString()),
                            style: UIHelper.valueStyle14(),
                          ),
                    isTextFieldRequiredAsValueWidget: true)
                : Container(),
        (faultHeader.status == null)
            ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                label: AppLocalizations.of(context)!.long_text,
                value: faultHeader.details.toString(),
                controller: longTextController,
                onChanged: (v) {
                  if (ScreenType.desktop == UIHelper().getScreenType(context)) {
                    TextSelection previousSelection =
                        longTextController.selection;
                    longTextController.text = v;
                    longTextController.selection = previousSelection;
                    // faultHeader.details = longTextController.text;
                    longText.getFaultLongText(v);
                  } else {
                    longText.getFaultLongText(v);
                  }
                },
                isTextFieldRequiredAsValueWidget: editProvider)
            : (faultHeader.details != null || faultHeader.details != '')
                ? editProvider
                    ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                        label: AppLocalizations.of(context)!.long_text,
                        value: faultHeader.details.toString(),
                        controller: longTextController,
                        onChanged: (v) {
                          if (ScreenType.desktop ==
                              UIHelper().getScreenType(context)) {
                            TextSelection previousSelection =
                                longTextController.selection;
                            longTextController.text = v;
                            longTextController.selection = previousSelection;
                            // faultHeader.details = longTextController.text;
                            longText.getFaultLongText(v);
                          } else {
                            longText.getFaultLongText(v);
                          }
                        },
                        isTextFieldRequiredAsValueWidget: editProvider)
                    : Container()
                : Container(),
        (faultHeader.status == null)
            ? Container()
            : UIHelper.buildLabelAndValueAsWidgetOfFault(
                label: AppLocalizations.of(context)!.status,
                value: UIHelper.getStatusString(faultHeader.status.toString()),
                controller: TextEditingController(),
                isTextFieldRequiredAsValueWidget: false),
        (faultHeader.status == null)
            ? Container()
            : (faultHeader.job_id != null)
                ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                    label: AppLocalizations.of(context)!.jobId,
                    value: faultHeader.job_id.toString(),
                    controller: TextEditingController(),
                    isTextFieldRequiredAsValueWidget: editProvider)
                : Container(),
      ],
    );
  }

  Widget getGeneralFieldWeb() {
    final editProvider = ref.watch(editFaultFieldProvider.notifier).state;
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    final description = ref.watch(faultDescriptionProvider.notifier);
    final longText = ref.watch(faultLongTextProvider.notifier);

    if (description.state.isNotEmpty) {
      descriptionController.text = description.state;
    }

    if (longText.state.isNotEmpty) {
      longTextController.text = longText.state;
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.general,
          style: UIHelper.faultHeaderStyle(),
        ),
        Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: (faultHeader.status == null)
                    ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                        label: AppLocalizations.of(context)!.description,
                        value: descriptionController.text,
                        controller: descriptionController,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(40),
                        ],
                        onChanged: (v) {
                          TextSelection previousSelection =
                              descriptionController.selection;
                          descriptionController.text = v;
                          descriptionController.selection = previousSelection;
                          // faultHeader.description = descriptionController.text;
                          description.getFaultDescription(v);
                        },
                        isTextFieldRequiredAsValueWidget: editProvider)
                    : (faultHeader.description != null ||
                            faultHeader.description != '')
                        ? editProvider
                            ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                                label:
                                    AppLocalizations.of(context)!.description,
                                value: descriptionController.text,
                                controller: descriptionController,
                                inputFormatters: [
                                  LengthLimitingTextInputFormatter(40),
                                ],
                                onChanged: (v) {
                                  TextSelection previousSelection =
                                      descriptionController.selection;
                                  descriptionController.text = v;
                                  descriptionController.selection =
                                      previousSelection;
                                  // faultHeader.description = descriptionController.text;
                                  description.getFaultDescription(v);
                                },
                                isTextFieldRequiredAsValueWidget: editProvider)
                            : Container()
                        : Container(),
              ),
            ]),
        Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              editProvider
                  ? Expanded(
                      child: (faultHeader.status == null)
                          ? (faultHeader.fault_type == null ||
                                  faultHeader.fault_type == '')
                              ? UIHelper
                                  .buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                                      label: AppLocalizations.of(context)!
                                          .faultType,
                                      value: getFaultTypeDropdown(),
                                      isTextFieldRequiredAsValueWidget:
                                          editProvider)
                              : UIHelper
                                  .buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                                      label: AppLocalizations.of(context)!
                                          .faultType,
                                      value: getFaultTypeDropdown(),
                                      isTextFieldRequiredAsValueWidget:
                                          editProvider)
                          : editProvider
                              ? faultHeader.p_mode == AppConstants.add
                                  ? UIHelper
                                      .buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                                          label: AppLocalizations.of(context)!
                                              .faultType,
                                          value: getFaultTypeDropdown(),
                                          isTextFieldRequiredAsValueWidget:
                                              editProvider)
                                  : UIHelper
                                      .buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                                          label: AppLocalizations.of(context)!
                                              .faultType,
                                          value: getFaultTypeDropdown(),
                                          isTextFieldRequiredAsValueWidget:
                                              editProvider)
                              : Container(),
                    )
                  : Container(),
              editProvider ? SizedBox(width: 10) : SizedBox(),
              Expanded(
                child: (faultHeader.status == null)
                    ? UIHelper
                        .buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                            label: AppLocalizations.of(context)!.faultMode,
                            value: editProvider
                                ? getFailureModeDropdown()
                                : Text(
                                    ref
                                        .watch(faultModeHeaderListProvider
                                            .notifier)
                                        .fetchFaultModeByCode(faultHeader
                                            .failure_mode
                                            .toString()),
                                    style: UIHelper.valueStyle14(),
                                  ),
                            isTextFieldRequiredAsValueWidget: true)
                    : (faultHeader.failure_mode != null &&
                            faultHeader.failure_mode != '')
                        ? UIHelper
                            .buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                                label: AppLocalizations.of(context)!.faultMode,
                                value: editProvider
                                    ? getFailureModeDropdown()
                                    : Text(
                                        ref
                                            .watch(faultModeHeaderListProvider
                                                .notifier)
                                            .fetchFaultModeByCode(faultHeader
                                                .failure_mode
                                                .toString()),
                                        style: UIHelper.valueStyle14(),
                                      ),
                                isTextFieldRequiredAsValueWidget: true)
                        : Container(),
              ),
            ]),
        Row(
          children: [
            Expanded(
              child: (faultHeader.status == null)
                  ? (faultHeader.priority == null || faultHeader.priority == '')
                      ? UIHelper
                          .buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                              label: AppLocalizations.of(context)!.priority,
                              value: getPriorityChoiceChip(),
                              isTextFieldRequiredAsValueWidget: editProvider)
                      : Container()
                  : editProvider
                      ? UIHelper
                          .buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
                              label: AppLocalizations.of(context)!.priority,
                              value: getPriorityChoiceChip(),
                              isTextFieldRequiredAsValueWidget: editProvider)
                      : Container(),
            ),
          ],
        ),
        Row(
          children: [
            Expanded(
              child: (faultHeader.status == null)
                  ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                      label: AppLocalizations.of(context)!.long_text,
                      value: longTextController.text,
                      controller: longTextController,
                      onChanged: (v) {
                        TextSelection previousSelection =
                            longTextController.selection;
                        longTextController.text = v;
                        longTextController.selection = previousSelection;
                        // faultHeader.details = longTextController.text;
                        longText.getFaultLongText(v);
                      },
                      isTextFieldRequiredAsValueWidget: editProvider)
                  : (faultHeader.details != null || faultHeader.details != '')
                      ? editProvider
                          ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                              label: AppLocalizations.of(context)!.long_text,
                              value: longTextController.text,
                              controller: longTextController,
                              onChanged: (v) {
                                TextSelection previousSelection =
                                    longTextController.selection;
                                longTextController.text = v;
                                longTextController.selection =
                                    previousSelection;
                                // faultHeader.details = longTextController.text;
                                longText.getFaultLongText(v);
                              },
                              isTextFieldRequiredAsValueWidget: editProvider)
                          : Container()
                      : Container(),
            ),
          ],
        ),
        (faultHeader.status == null)
            ? Container()
            : UIHelper.buildLabelAndValueAsWidgetOfFault(
                label: AppLocalizations.of(context)!.status,
                value: UIHelper.getStatusString(faultHeader.status.toString()),
                controller: TextEditingController(),
                isTextFieldRequiredAsValueWidget: false),
        (faultHeader.status == null)
            ? Container()
            : (faultHeader.job_id != null)
                ? UIHelper.buildLabelAndValueAsWidgetOfFault(
                    label: AppLocalizations.of(context)!.jobId,
                    value: faultHeader.job_id.toString(),
                    controller: TextEditingController(),
                    isTextFieldRequiredAsValueWidget: editProvider)
                : Container(),
      ],
    );
  }

  Widget getLocationIdDropdown() {
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    final locationList = ref.watch(flocHeaderProvider.notifier).state;
    final location = ref.watch(locationProvider.notifier);
    final assetLocList = ref.read(assetLocListProvider.notifier);
    final asset = ref.watch(assetProvider.notifier);
    if (selectedLocation.isEmpty || selectedLocation == '') {
      if (faultHeader.location_id == null || faultHeader.location_id == '') {
        selectedLocation = '';
        location.getLocation(selectedLocation);
      } else {
        selectedLocation =
            getLocationDescription(faultHeader.location_id.toString());
        location.getLocation(faultHeader.location_id.toString());
      }
    }
    final dropdownItems = [
      '--Select--',
      ...locationList
          .map((option) => '${option.location_id!}\n${option.description}')
    ];
    return CustomDropdown<String>(
      items: dropdownItems,
      itemLabel: (item) => item,
      selectedItems: (selectedLocation.isNotEmpty && selectedLocation != '')
          ? [selectedLocation]
          : [],
      hint: AppLocalizations.of(context)!.select,
      isMultiSelect: false,
      onChanged: (newValue) {
        if (newValue != null) {
          if (newValue == '--Select--') {
            setState(() {
              selectedLocation = newValue;
              location.getLocation('');
              assetLocList.clearAssetLocList();
              if (assetLocList.state.isEmpty) {
                selectedAsset = '';
                asset.getAsset(selectedAsset);
              }
            });
          } else {
            onChangeLocation(newValue);
          }
        }
      },
    );
  }

  onChangeLocation(String newValue) async {
    final locationList = ref.watch(flocHeaderProvider.notifier).state;
    final assetLocList = ref.read(assetLocListProvider.notifier);
    final location = ref.watch(locationProvider.notifier);
    final asset = ref.watch(assetProvider.notifier);
    {
      final selectedLocId = locationList.firstWhere(
          (item) => '${item.location_id!}\n${item.description}' == newValue);

      selectedLocation = newValue;
      location.getLocation(selectedLocId.location_id.toString());

      await assetLocList
          .fetchAssetLocList(selectedLocId.location_id.toString());
      if (assetLocList.state.isEmpty) {
        selectedAsset = '';
        asset.getAsset(selectedAsset);
      }
      setState(() {});
    }
  }

  Widget getAssetDropdown() {
    final asset = ref.watch(assetProvider.notifier);
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    final assetLocList = ref.read(assetLocListProvider.notifier).state;
    if (selectedAsset.isEmpty || selectedAsset == '') {
      if (faultHeader.asset_no == null) {
        selectedAsset = '';
        asset.getAsset(selectedAsset);
      } else {
        selectedAsset = getAssetDescription(faultHeader.asset_no.toString());
        asset.getAsset(faultHeader.asset_no.toString());
      }
    }

    return CustomDropdown<String>(
      items: assetLocList
          .map((option) =>
              '${option.asset_no.toString()}\n${option.description.toString()}')
          .toList(),
      itemLabel: (item) => item,
      selectedItems: (selectedAsset.isNotEmpty && selectedAsset != '')
          ? [selectedAsset]
          : [],
      hint: AppLocalizations.of(context)!.select,
      isMultiSelect: false,
      onChanged: (newValue) {
        if (newValue != null) {
          onChangeAsset(newValue);
        }
      },
    );
  }

  Widget getAssetDropdownWeb(String assetData) {
    final asset = ref.watch(assetProvider.notifier);
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    final assetLocList = ref.read(assetLocListProvider.notifier).state;
    final assetList = ref.read(assetHeaderProvider.notifier).state;
    if (selectedAsset.isEmpty || selectedAsset == '') {
      if (faultHeader.asset_no == null) {
        selectedAsset = '';
        asset.getAsset(selectedAsset);
      } else {
        selectedAsset = getAssetDescription(faultHeader.asset_no.toString());
        asset.getAsset(faultHeader.asset_no.toString());
      }
    }
    final dropdownItems = assetData == 'assetLoc'
        ? [
            '--Select--',
            ...assetLocList.map((option) =>
                '${option.asset_no.toString()}\n${option.description.toString()}')
          ]
        : [
            '--Select--',
            ...assetList.map((option) =>
                '${option.asset_no.toString()}\n${option.description.toString()}')
          ];

    return CustomDropdown<String>(
      items: dropdownItems,
      itemLabel: (item) => item,
      selectedItems: selectedAsset.isNotEmpty ? [selectedAsset] : [],
      hint: AppLocalizations.of(context)!.select,
      isMultiSelect: false,
      onChanged: (newValue) {
        if (newValue != null) {
          if (newValue == '--Select--') {
            setState(() {
              selectedAsset = newValue;
              asset.getAsset('');
            });
          } else {
            onChangeAssetWeb(newValue, assetData);
          }
        }
      },
    );
  }

  onChangeAsset(String newValue) async {
    final assetLocList = ref.read(assetLocListProvider.notifier).state;
    final asset = ref.watch(assetProvider.notifier);
    final selectedAssetNo = assetLocList.firstWhere(
        (item) => '${item.asset_no!}\n${item.description}' == newValue);
    setState(() {
      selectedAsset = newValue;
      asset.getAsset(selectedAssetNo.asset_no.toString());
    });
  }

  onChangeAssetWeb(String newValue, String assetData) async {
    final assetLocList = ref.read(assetLocListProvider.notifier).state;
    final assetList = ref.read(assetHeaderProvider.notifier).state;
    final asset = ref.watch(assetProvider.notifier);
    final selectedAssetNo = assetData == 'assetLoc'
        ? assetLocList.firstWhere(
            (item) => '${item.asset_no!}\n${item.description}' == newValue)
        : assetList.firstWhere(
            (item) => '${item.asset_no!}\n${item.description}' == newValue);

    selectedAsset = newValue;
    asset.getAsset(selectedAssetNo.asset_no.toString());
    setState(() {});
  }

  Widget getFaultTypeDropdown() {
    final faultTypeHeader = ref.watch(faultTypeListProvider.notifier).state;
    final faultType = ref.watch(faultTypeProvider.notifier);
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    if (selectedFaultType.isEmpty) {
      selectedFaultType =
          (faultHeader.fault_type != null && faultHeader.fault_type!.isNotEmpty)
              ? ref
                  .watch(faultTypeListProvider.notifier)
                  .fetchFaultTypeCode(faultHeader.fault_type.toString())
              : '';
      faultType.getFaultType(selectedFaultType);
    }

    if (faultType.state.fault_code != null &&
        faultType.state.fault_code!.isNotEmpty) {
      selectedFaultType = ref
          .watch(faultTypeListProvider.notifier)
          .fetchFaultTypeCode(faultType.state.fault_code.toString());
    }

    final dropdownItems = faultTypeHeader.map((option) {
      return DropdownMenuItem<String>(
        value: option.description,
        child: Padding(
          padding: const EdgeInsets.only(left: 5.0),
          child: Text(option.description!, style: UIHelper.valueStyle()),
        ),
      );
    }).toList();

    if (!dropdownItems.any((item) => item.value == '')) {
      dropdownItems.insert(
        0,
        DropdownMenuItem<String>(
          value: '',
          child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(AppLocalizations.of(context)!.select,
                style: UIHelper.valueStyle()),
          ),
        ),
      );
    }

    return Container(
      decoration: UIHelper.fieldDecoration(),
      child: DropdownButton<String>(
        elevation: 0,
        isExpanded: true,
        underline: const SizedBox(),
        value: dropdownItems.any((item) => item.value == selectedFaultType)
            ? selectedFaultType
            : '',
        items: (faultHeader.fault_type != null && faultHeader.fault_type != '')
            ? []
            : dropdownItems,
        disabledHint: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(
              selectedFaultType.isNotEmpty
                  ? selectedFaultType
                  : AppLocalizations.of(context)!.select,
              style: UIHelper.valueStyle(),
            )),
        onChanged: (newValue) {
          if (newValue != null) {
            setState(() {
              selectedFaultType = newValue;
              faultType.getFaultType(selectedFaultType);
            });
          }
        },
      ),
    );
  }

  Widget getFailureModeDropdown() {
    final faultModeHeader =
        ref.watch(faultModeHeaderListProvider.notifier).state;
    final faultMode = ref.watch(faultModeHeaderProvider.notifier);
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;

    if (selectedFailureMode.isEmpty) {
      selectedFailureMode = (faultHeader.failure_mode != null &&
              faultHeader.failure_mode!.isNotEmpty)
          ? ref
              .read(faultModeHeaderListProvider.notifier)
              .fetchFaultModeByCode(faultHeader.failure_mode!.toUpperCase())
          : '';
      faultMode.getFaultMode(selectedFailureMode);
    }

    if (faultMode.state.failure_code != null &&
        faultMode.state.failure_code!.isNotEmpty) {
      selectedFailureMode = ref
          .read(faultModeHeaderListProvider.notifier)
          .fetchFaultModeByCode(faultMode.state.failure_code.toString());
    }

    final dropdownItems = faultModeHeader.map((option) {
      return DropdownMenuItem<String>(
        value: option.description,
        child: Padding(
          padding: const EdgeInsets.only(left: 5.0),
          child: Text(option.description!, style: UIHelper.valueStyle()),
        ),
      );
    }).toList();

    // Add a default "--Select--" option
    if (!dropdownItems.any((item) => item.value == '')) {
      dropdownItems.insert(
        0,
        DropdownMenuItem<String>(
          value: '',
          child: Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(AppLocalizations.of(context)!.select,
                style: UIHelper.valueStyle()),
          ),
        ),
      );
    }

    return Container(
      decoration: UIHelper.fieldDecoration(),
      child: DropdownButton<String>(
        elevation: 0,
        isExpanded: true,
        underline: const SizedBox(),
        value: dropdownItems.any((item) => item.value == selectedFailureMode)
            ? selectedFailureMode
            : '',
        items: dropdownItems,
        onChanged: (newValue) {
          if (newValue != null) {
            setState(() {
              selectedFailureMode = newValue;
              faultMode.getFaultMode(selectedFailureMode);
            });
          }
        },
      ),
    );
  }

  DateTime convertedToDateTime(String dateAsInt) {
    String dateString = dateAsInt.toString();
    DateTime parsedDate = DateFormat("dd MMM yyyy").parse(dateString);
    return parsedDate;
  }

  String getLocationDescription(String loc) {
    final locationList = ref.read(flocHeaderProvider);
    if (loc != "") {
      LOCATION_HEADER location =
          locationList.firstWhere((element) => element.location_id == loc);
      String data =
          '${location.location_id.toString()}\n${location.description.toString()}';

      return data;
    } else {
      return "";
    }
  }

  String getAssetDescription(String assetNo) {
    final assetList = ref.read(assetHeaderProvider);
    if (assetNo != "") {
      if (assetList.isNotEmpty) {
        ASSET_HEADER asset = assetList.firstWhere(
            (element) => element.asset_no.toString() == assetNo,
            orElse: () => ASSET_HEADER(asset_no: int.parse(assetNo)));

        String data =
            '${asset.asset_no.toString()}\n${asset.description.toString()}';
        return data;
      } else {
        String data = assetNo.toString();
        return data;
      }
    } else {
      return "";
    }
  }
}
