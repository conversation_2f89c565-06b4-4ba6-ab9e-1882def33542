import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';

final editFaultFieldProvider =
StateNotifierProvider<EditFaultFieldNotifier, bool>((ref) {
  return EditFaultFieldNotifier();
});

class EditFaultFieldNotifier extends StateNotifier<bool> {
  EditFaultFieldNotifier() : super(false);

  Future<void> getEditFaultFieldEnable(bool data) async {
    try {
      bool enableFaultFields = data;
        state = enableFaultFields;

    } catch (e) {
      Logger.logError('EditFaultFieldNotifier', 'getEditFaultFieldEnable', e.toString());
    }
  }
}