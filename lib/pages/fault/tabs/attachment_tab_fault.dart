import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../helpers/ui_helper.dart';

import '../fault_image_upload.dart';

class AttachmentTabFault extends ConsumerStatefulWidget {
  AttachmentTabFault({super.key});

  @override
  _AttachmentTabFaultState createState() => _AttachmentTabFaultState();
}

class _AttachmentTabFaultState extends ConsumerState<AttachmentTabFault> {
  bool isExpandedForAttachment = false;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        child: Padding(
          padding: UIHelper.columnFieldOnlhorizontalPadding(),
          child: <PERSON>um<PERSON>(
            children: [
              UIHelper.sizedBox8(),
              getAttachments(),
            ],
          ),
        ),
      ),
    );
  }

  Widget getAttachments() {
    return Padding(
      padding: UIHelper.columnFieldPadding(),
      child: Column(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [FaultFileOrImageUploadWidget()],
          ),
        ],
      ),
    );
  }
}
