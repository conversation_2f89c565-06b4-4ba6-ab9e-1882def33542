import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../helpers/ui_helper.dart';
import 'package:intl/intl.dart';

import '../../../providers/fault/fault_header_provider.dart';
import 'edit_fault_field_provider.dart';

class ActivityTabFault extends ConsumerStatefulWidget {
  ActivityTabFault({super.key});

  @override
  _ActivityTabFaultState createState() => _ActivityTabFaultState();
}

class _ActivityTabFaultState extends ConsumerState<ActivityTabFault> {
  TextEditingController inputController = TextEditingController();
  String readOnlyText = "";

  @override
  void initState() {
    super.initState();
    inputController = TextEditingController();
    final faultHeader = ref.read(faultHeaderProvider.notifier).state;
    if (faultHeader.details != null) {
      final currentDateTime =
          DateFormat('dd-MM-yyyy hh:mm a').format(DateTime.now());
      readOnlyText = '$currentDateTime \n ${faultHeader.details} \n';
    } else {
      final currentDateTime =
          DateFormat('dd-MM-yyyy hh:mm a').format(DateTime.now());
      readOnlyText = '$currentDateTime \n';
    }
  }

  @override
  Widget build(BuildContext context) {
    final editProvider = ref.watch(editFaultFieldProvider.notifier).state;
    return SafeArea(
      child: SingleChildScrollView(
        child: Padding(
          padding: UIHelper.columnFieldPadding(),
          child: Column(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  UIHelper.buildLabelAndValueAsWidgetOfFaultActivity(
                    readOnlyText: readOnlyText,
                    controller: inputController,
                    isTextFieldRequiredAsValueWidget: !editProvider,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
