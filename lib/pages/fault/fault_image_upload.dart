import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/DOCUMENT_ATTACHMENT.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:rounds/be/FAULT_DOCUMENT.dart';
import 'package:rounds/be/FAULT_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/pages/fault/tabs/edit_fault_field_provider.dart';
import 'package:rounds/utils/utils.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../providers/attachments/attachment_provider.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

import '../../providers/fault/fault_header_provider.dart';
import '../../providers/upload_progress_provider.dart';
import '../../widgets/round_attachment_picker.dart';

class FaultFileOrImageUploadWidget extends ConsumerStatefulWidget {
  @override
  _FaultFileOrImageUploadWidgetState createState() =>
      _FaultFileOrImageUploadWidgetState();
}

class _FaultFileOrImageUploadWidgetState
    extends ConsumerState<FaultFileOrImageUploadWidget> {
  static const sourceClass = 'FaultFileOrImageUploadWidget';
  bool isHovered = false;
  double containerHeightOfImage = 80;
  int selectedImageIndex = -1;
  int hoveredIndices = -1;

  Widget attachmentWidget(BuildContext context) {
    final faultDocument = ref.watch(getFaultDocumentProvider.notifier).state;
    final editProvider = ref.watch(editFaultFieldProvider.notifier);
    final documentAttachments =
        ref.watch(documentAttachmentProvider.notifier).state;
    List<DOCUMENT_HEADER> headers =
        faultDocument.isNotEmpty ? faultDocument : [];
    Set<String> headerIds = headers.map((header) => header.lid).toSet();

    List<DOCUMENT_ATTACHMENT> attachmentList = documentAttachments
        .where((attachment) => headerIds.contains(attachment.fid))
        .toList();

    return RoundsAttachmentPicker(
      isAddButtonRequired: editProvider.state ? true : false,
      onAttachmentPicked: (result) async {
        final faultHeader = ref.watch(faultHeaderProvider.notifier).state;
        if (result != null &&
            (kIsWeb
                ? result.files.single.bytes != null
                : result.files.single.path != null)) {
          for (var data in result.files) {
            String? docId = generate32BitDocId();
            String fileName = data.name;
            String fileDocType = fileName.split('.').last.toUpperCase();
            String extension = fileName.split('.').last.toLowerCase();
            String base64String = '';
            Uint8List? fileBytes;
            String filePath = (kIsWeb ? '' : data.path) ?? ''; // Mobile only
            if (kIsWeb) {
              fileBytes = data.bytes;
            } else {
              if (data.path != null) {
                final File file = File(data.path!);
                fileBytes = await file.readAsBytes();
              }
            }

            if (fileBytes != null) {
              // if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
              //     .contains(extension)) {
              //   base64String = base64Encode(fileBytes);
              // } else if (['mp4', 'mov', 'avi', 'mkv', 'flv', 'wmv', 'webm']
              //     .contains(extension)) {
              //   final byteData =
              //       await rootBundle.load('assets/icon/task_icons/video.png');
              //   fileBytes = byteData.buffer.asUint8List();
              //   base64String = base64Encode(fileBytes);
              // } else if (extension == 'pdf') {
              //   final byteData =
              //       await rootBundle.load('assets/icon/task_icons/pdff.png');
              //   fileBytes = byteData.buffer.asUint8List();
              //   base64String = base64Encode(fileBytes);
              // }
              base64String = base64Encode(fileBytes);
            }

            DOCUMENT_HEADER documentHeader = DOCUMENT_HEADER(
              doc_id: docId,
              doc_type: fileDocType,
              file_name: fileName,
              title: fileName,
              mime_type: 'application/${fileDocType.toLowerCase()}',
            );
            if (kIsWeb) {
              DbHelper()
                  .saveAttachmentinIndexDbByUid(docId ?? "", base64String);
            }

            List list1 = await AppDatabaseManager().select(DBInputEntity(
                DOCUMENT_HEADER.TABLE_NAME, {})
              ..setWhereClause("${DOCUMENT_HEADER.FIELD_DOC_ID} = '$docId'"));

            if (list1.isEmpty) {
              await DbHelper.insertDocumentsHeader(documentHeader);
            } else {
              await DbHelper.updateDocumentsHeader(documentHeader);
            }

            DOCUMENT_ATTACHMENT documentAttachment =
                DOCUMENT_ATTACHMENT(uid: '');
            documentAttachment.fid = documentHeader.lid;
            documentAttachment.uid = docId;
            documentAttachment.local_path = kIsWeb ? "" : filePath;
            documentAttachment.file_name = fileName;
            documentAttachment.mime_type =
                'application/${fileDocType.toLowerCase()}';
            documentAttachment.external_url = "";
            documentAttachment.url_requires_auth = "";
            documentAttachment.attachment_status =
                AttachmentStatusSavedForUpload;

            List list2 =
                await DbHelper.getDocumentAttachmentsByFid(documentHeader.lid);
            if (list2.isEmpty) {
              await DbHelper.insertDocumentAttachment(documentAttachment);
            } else {
              await DbHelper.updateDocumentAttachment(documentAttachment);
            }
          //   final faultHeaderExists = await AppDatabaseManager().select(
          //       DBInputEntity(FAULT_HEADER.TABLE_NAME, {})
          //         ..setWhereClause("FAULT_ID = '${faultHeader.fault_id}'"));
          // if(ScreenType.desktop != UIHelper().getScreenType(context)){
          
          //   if (faultHeaderExists.isEmpty) {
          //     await AppDatabaseManager().insert(
          //       DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()),
          //     );
          //   }
          // }

            FAULT_DOCUMENT faultDocument = FAULT_DOCUMENT(
              fault_id: faultHeader.fault_id,
              doc_id: documentAttachment.uid,
            );
            faultDocument.fid = faultHeader.lid;

            await AppDatabaseManager().insert(DBInputEntity(
                FAULT_DOCUMENT.TABLE_NAME, faultDocument.toJson()));

            final faultDocumentHeaders =
                ref.watch(getFaultDocumentHeaderProvider.notifier);
            await faultDocumentHeaders
                .getFaultDocumentsHeaders(faultHeader.fault_id.toString());

            final faultDocumentProvider =
                ref.watch(getFaultDocumentProvider.notifier);
            await faultDocumentProvider
                .getFaultDocuments(faultHeader.fault_id.toString());

            ref.read(documentHeaderProvider.notifier).fetchDocumentHeaders();
            ref
                .read(documentAttachmentProvider.notifier)
                .fetchDocumentAttachments();

            setState(() {});
          }
        }
      },
      viewType: RoundsAttachmentPickerViewType.grid,
      attachments: attachmentList.asMap().entries.map((e) {
        final uploadProgress = ref.watch(uploadProgressProvider)[e.key] ?? 1.0;
        return DocumentAttachmentContainer(
          attachment: e.value,
          isFromPlanner: e.value.objectStatus == ObjectStatus.global,
          isUploading: uploadProgress < 1.0,
          uploadProgress: uploadProgress,
          onDelete: (value) {
            removeFile(document_attachment: e.value, index: e.key);
          },
          onTap: (value) {},
        );
      }).toList(),
    );
  }

  String? generate32BitDocId() {
    var uuid = const Uuid();
    return uuid.v4();
  }

/*
  Future<void> uploadFile(WidgetRef ref, String index) async {
    final uploadProgressNotifier = ref.read(uploadProgressProvider.notifier);
    uploadProgressNotifier.startUpload(index);

    for (double progress = 0.0; progress <= 1.0; progress += 0.1) {
      await Future.delayed(const Duration(milliseconds: 500));
      uploadProgressNotifier.updateProgress(index, progress);
    }

    uploadProgressNotifier.finishUpload(index);
  }
*/

  void removeFile(
      {required DOCUMENT_ATTACHMENT document_attachment,
      required int index}) async {
    try {
      final faultHeader = ref.watch(faultHeaderProvider.notifier).state;
      final documentHeaders = ref.watch(documentHeaderProvider);

      DOCUMENT_HEADER? attachmentHeader;
      for (var element in documentHeaders) {
        if (element.lid == document_attachment.fid) {
          attachmentHeader = element;
          break;
        }
      }

      if (attachmentHeader == null) {
        Logger.logError(sourceClass, 'removeFile',
            'No DOCUMENT_HEADER found for fid: ${document_attachment.fid}');
        return;
      }

      if(kIsWeb){
        await DbHelper()
            .deleteAttachmentFromIndexDbForUid(attachmentHeader.doc_id ?? "");
      }

      await AppDatabaseManager().delete(
          DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, attachmentHeader.toJson()));

      await AppDatabaseManager().delete(DBInputEntity(
          DOCUMENT_ATTACHMENT.TABLE_NAME, document_attachment.toJson()));

      List<FAULT_DOCUMENT> docList = await DbHelper.getFaultDocuments();
      FAULT_DOCUMENT doc = docList
          .where((element) => element.doc_id == attachmentHeader!.doc_id)
          .toList()
          .first;

      await AppDatabaseManager().execute(
          'DELETE FROM ${FAULT_DOCUMENT.TABLE_NAME} WHERE ${FAULT_DOCUMENT.FIELD_DOC_ID} = "${doc.doc_id}"');
      final faultDocument = ref.watch(getFaultDocumentProvider.notifier);
      await faultDocument.getFaultDocuments(faultHeader.fault_id.toString());
      setState(() {
        isHovered = false;
        hoveredIndices = -1;
      });
    } catch (e) {
      Logger.logError(sourceClass, 'removeFile', e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Column(
        children: [attachmentWidget(context)],
      ),
    );
  }
}
