import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:rounds/be/ROLE_HEADER.dart';
import 'package:rounds/helpers/pushNotification_helper.dart';
import 'package:rounds/models/intractive_Item_Model.dart';
import 'package:rounds/pages/dashboard/widgets/top_header.dart';
import 'package:rounds/pages/fault/fault_list.dart';
import 'package:rounds/pages/fault/tabs/edit_fault_field_provider.dart';
import 'package:rounds/pages/widgets/search_bar.dart';
import 'package:rounds/providers/attachments/attachment_provider.dart';
import 'package:rounds/providers/assets/asset_provider.dart';
import 'package:rounds/providers/assets/floc_provider.dart';
import 'package:rounds/providers/fault/fault_type_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:rounds/utils/notification_toast.dart';
import 'package:rounds/utils/utils.dart';
import 'package:rounds/widgets/overlay_toast.dart';
import 'package:rounds/widgets/task_calender.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../be/FAULT_HEADER.dart';
import '../../helpers/ui_helper.dart';
import '../../providers/fault/fault_header_provider.dart';
import '../../providers/user_provider.dart';
import '../../services/app_notifier.dart';
import '../../utils/app_constants.dart';
import 'fault_detail_screen.dart';
import 'widgets/modern_fault_detail_view.dart';
import 'widgets/modern_filter_button.dart';
import 'widgets/create_fault_modal.dart';
import 'fault_filter_provider.dart';
import 'package:intl/intl.dart';

final filterOfFaultTypeProvider = StateProvider<List<String>>((ref) => []);
final faultSearchProvider = StateProvider<String>((ref) => "");
final filterOfFaultCodeProvider = StateProvider<List<String>>((ref) => []);
final filterOfFaultPriorityCodeProvider =
    StateProvider<List<String>>((ref) => []);

// Modern filter providers for multi-select
final selectedFaultTypesProvider = StateProvider<List<String>>((ref) => []);
final selectedPrioritiesProvider = StateProvider<List<String>>((ref) => []);
final selectedStatusesProvider = StateProvider<List<String>>((ref) => []);
final filterOfFaultPriorityProvider = StateProvider<List<String>>((ref) => []);
final statusTypeFaultFilterProvider = StateProvider<List<String>>((ref) => []);
final statusFaultFilterProvider = StateProvider<List<String>>((ref) => []);
final selectedCalendarDateProvider = StateProvider<DateTime?>((ref) => null);

final searchTextProvider = StateProvider<String>((ref) => '');

ValueNotifier<InteractiveItemModel?> faultDetailViewNotifier =
    ValueNotifier<InteractiveItemModel?>(null);

class FaultScreen extends ConsumerStatefulWidget {
  const FaultScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _FaultScreenState();
}

class _FaultScreenState extends ConsumerState<FaultScreen> {
  static const sourceClass = 'FaultScreen';
  TextEditingController searchController = TextEditingController();
  Map<String, List<String>>? filter;
  AppNotifier appNotifier = AppNotifier();
  ScrollController scrollController = ScrollController();
  ValueNotifier<DateTime> currentGroupNotifier =
      ValueNotifier<DateTime>(DateTime.now());
  late final StreamSubscription<void> _subscription;
  late ValueNotifier<InteractiveItemModel?> faultDetailViewNotifier;
  bool _isCreateFaultModalOpen = false;

  @override
  void initState() {
    super.initState();
    faultDetailViewNotifier = ValueNotifier<InteractiveItemModel?>(null);
    _initNotifier();

    final filteredFaultTypeNotifier =
        ref.read(filteredFaultHeaderListProvider.notifier);

    final faultHeader = ref.read(faultHeaderListProvider);
    scrollController.addListener(_onScroll);
    final plant = ref.read(plantProvider);
    final plantSection = ref.read(plantSectionProvider);
    Future.delayed(Duration.zero).then((value) async {
      ref.read(searchTextProvider.notifier).state = "";
      ref.read(faultModeHeaderListProvider.notifier).fetchFaultModeList();
      ref.read(faultTypeListProvider.notifier).fetchFaultTypeList();
      // Initialize asset provider for fault cards to display asset names
      await ref.read(assetHeaderProvider.notifier).getAssetHeaderList(plant);
      // Initialize functional location provider for fault cards to display location names
      await ref.read(flocHeaderProvider.notifier).getLocHeaderList(plant);

      final filterOfFaultType = ref.read(filterOfFaultTypeProvider);
      final filterOfFaultCode = ref.read(filterOfFaultCodeProvider);
      final filterOfPriorityCode = ref.read(filterOfFaultPriorityCodeProvider);
      final filterOfPriority = ref.read(filterOfFaultPriorityProvider);
      final statusTypeFilter = ref.read(statusTypeFaultFilterProvider);
      final statusFilter = ref.read(statusFaultFilterProvider);
      final selectedDate = ref.read(selectedCalendarDateProvider);
      final search = ref.read(searchTextProvider);

      if ((search != '') ||
          filterOfFaultType.isNotEmpty ||
          filterOfFaultCode.isNotEmpty ||
          filterOfPriorityCode.isNotEmpty ||
          filterOfPriority.isNotEmpty ||
          statusTypeFilter.isNotEmpty ||
          statusFilter.isNotEmpty ||
          selectedDate != null) {
        // Reset fault detail view when any filter is applied
        faultDetailViewNotifier.value = InteractiveItemModel(
          type: "",
          data: {"type": "", "index": null},
        );
        await filteredFaultTypeNotifier.filteredFaultHeaderList(
            faulttypeList: filterOfFaultCode,
            priorityList: filterOfPriorityCode,
            statusList: statusTypeFilter,
            type: (search != '') ? AppConstants.search : AppConstants.faultType,
            plantId: plant,
            plantSec: plantSection,
            search: search);
      } else {
        await filteredFaultTypeNotifier.filteredFaultHeaderList(
            type: 'Initial',
            faultList: faultHeader,
            plantId: plant,
            plantSec: plantSection);
      }
    });
  }

  void _onScroll() {
    double offset = scrollController.offset;
    final faultHeader = ref.watch(faultHeaderListProvider);
    final filteredFaultHeader = ref.watch(filteredFaultHeaderListProvider);

    final isFiltering = filteredFaultHeader != faultHeader;
    final displayedList = isFiltering ? filteredFaultHeader : faultHeader;

    for (int i = 0; i < displayedList.length; i++) {
      DateTime reportedDate =
          convertToDateTime(displayedList[i].reported_on.toString());

      double groupPosition = i * 120.0;

      if (offset >= groupPosition && offset < (groupPosition + 35)) {
        currentGroupNotifier.value = reportedDate;
        break;
      }
    }
  }

  _initNotifier() {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    appNotifier.notifyAttachmentStatus((data) async {
      if (data[EventAttachmentStatusFieldStatus] ==
          EventAttachmentStatusSuccess) {
        await ref.read(documentHeaderProvider.notifier).fetchDocumentHeaders();
        await ref
            .read(documentAttachmentProvider.notifier)
            .fetchDocumentAttachments();
      } else if (data[EventAttachmentStatusFieldStatus] ==
          EventAttachmentStatusError) {}
    });
    appNotifier.notifySyncStatus(
      (data) async {
        final filterOfFaultType =
            ref.read(filterOfFaultTypeProvider.notifier).state;
        final filterOfFaultCode =
            ref.read(filterOfFaultCodeProvider.notifier).state;
        final filterOfPriorityCode =
            ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
        final filterOfPriority =
            ref.read(filterOfFaultPriorityProvider.notifier).state;
        final statusTypeFilter =
            ref.read(statusTypeFaultFilterProvider.notifier).state;
        final statusFilter = ref.read(statusFaultFilterProvider.notifier).state;
        final selectedDate = ref.read(selectedCalendarDateProvider);
        if (data[EventSyncStatusFieldType] ==
            EventSyncStatusTypeInboxProcessingComplete) {
          if (mounted) {
            final faultHeader = ref.read(faultHeaderListProvider);
            final filteredFaultTypeNotifier =
                ref.read(filteredFaultHeaderListProvider.notifier);
            final search = ref.read(searchTextProvider.notifier).state;
            if ((searchController.text.isNotEmpty && search != '') ||
                filterOfFaultType.isNotEmpty ||
                filterOfFaultCode.isNotEmpty ||
                filterOfPriorityCode.isNotEmpty ||
                filterOfPriority.isNotEmpty ||
                statusTypeFilter.isNotEmpty ||
                statusFilter.isNotEmpty ||
                selectedDate != null) {
              // Reset fault detail view when any filter is applied
  /*            faultDetailViewNotifier.value = InteractiveItemModel(
                type: "",
                data: {"type": "", "index": null},
              );*/
              await filteredFaultTypeNotifier.filteredFaultHeaderList(
                  faulttypeList: filterOfFaultCode,
                  priorityList: filterOfPriorityCode,
                  statusList: statusTypeFilter,
                  type: (searchController.text.isNotEmpty && search != '')
                      ? AppConstants.search
                      : AppConstants.faultType,
                  plantId: plant,
                  plantSec: plantSection,
                  search: searchController.text);
            } else {
              await filteredFaultTypeNotifier.filteredFaultHeaderList(
                  type: 'Initial',
                  faultList: faultHeader,
                  plantId: plant,
                  plantSec: plantSection);
            }
          }
        }
      },
    );

    if (kIsWeb) {
      _subscription = PushNotifications.onNotificationProcessed.listen((_) {
        // 🚀 React to notification queue processed
        debugPrint("📦 Notification queue processed, updating UI");
        final plant = ref.watch(plantProvider);
        final plantSection = ref.watch(plantSectionProvider);
        final shift = ref.watch(shiftProvider);
        final filterOfFaultCode =
            ref.read(filterOfFaultCodeProvider.notifier).state;
        final filterOfPriorityCode =
            ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
        final filterOfPriority =
            ref.read(filterOfFaultPriorityProvider.notifier).state;
        final statusTypeFilter =
            ref.read(statusTypeFaultFilterProvider.notifier).state;

        final filteredFaultTypeNotifier =
            ref.read(filteredFaultHeaderListProvider.notifier);

        filteredFaultTypeNotifier.filteredFaultHeaderList(
            faulttypeList: filterOfFaultCode,
            priorityList: filterOfPriorityCode,
            statusList: statusTypeFilter,
            type: AppConstants.search,
            plantId: plant,
            plantSec: plantSection,
            search: searchController.text);
        NotificationToastManager.showRefreshToast(context, onRefresh: () {
          final plant = ref.watch(plantProvider);
          ref
              .read(faultHeaderListProvider.notifier)
              .fetchFaultHeaderList(plant);
        });
        // TODO: e.g., refresh data, show snackbar, etc.
      });
    }
  }

  @override
  void dispose() {
    if (mounted) {
      if (kIsWeb) {
        _subscription.cancel();
      }

      scrollController.removeListener(() {});
      scrollController.dispose();
      appNotifier.unSubscribeNotifySyncStatus();
      appNotifier.unSubscribeNotifyAttachmentStatus();
    }
    searchController.dispose();
    super.dispose();
  }

  Future<void> refreshData() async {
    await downloadTransactionData(); // Calls all necessary data fetch functions
  }

  downloadTransactionData() async {
    try {
      await SyncEngine().receive();
      UIHelper.showSnackBar(context,
          message: AppLocalizations.of(context)!.refreshing);
    } catch (error) {
      UIHelper.showSnackBar(context, message: error.toString());
    }
  }

  void scrollToTargetDate(DateTime targetDate) {
    int index = findTargetGroupIndex(targetDate);
    if (index != -1) {
      scrollController.animateTo(
        index * 120.0,
        duration: const Duration(seconds: 1),
        curve: Curves.easeInOut,
      );
    }
  }

  int findTargetGroupIndex(DateTime targetDate) {
    final faultHeader = ref.watch(faultHeaderListProvider);
    final filteredFaultHeader = ref.watch(filteredFaultHeaderListProvider);

    final isFiltering = filteredFaultHeader != faultHeader;
    final displayedList = isFiltering ? filteredFaultHeader : faultHeader;

    displayedList.sort((a, b) {
      DateTime dateA = convertToDateTime(a.reported_on.toString());
      DateTime dateB = convertToDateTime(b.reported_on.toString());
      return dateA.compareTo(dateB);
    });
    for (int i = 0; i < displayedList.length; i++) {
      DateTime reportedDate =
          convertToDateTime(displayedList[i].reported_on.toString());
      if (formatDate(reportedDate) == formatDate(targetDate)) {
        return i;
      }
    }
    return -1;
  }

  @override
  Widget build(BuildContext context) {
    final role = ref.watch(roleProvider);
    final filteredFaultType =
        ref.watch(filteredFaultHeaderListProvider.notifier);
    final plant = ref.watch(plantProvider.notifier).state;

    final filterOfFaultType =
        ref.watch(filterOfFaultTypeProvider.notifier).state;
    final filterOfFaultCode =
        ref.watch(filterOfFaultCodeProvider.notifier).state;
    final filterOfPriorityCode =
        ref.watch(filterOfFaultPriorityCodeProvider.notifier).state;
    final filterOfPriority =
        ref.watch(filterOfFaultPriorityProvider.notifier).state;
    final statusTypeFilter =
        ref.watch(statusTypeFaultFilterProvider.notifier).state;
    final statusFilter = ref.watch(statusFaultFilterProvider.notifier).state;
    final searchJob = ref.watch(searchTextProvider.notifier).state;

    return SafeArea(
      child: Stack(
        children: [
          Scaffold(
            backgroundColor: AppColors.modernListBackground,
            body: LayoutBuilder(
              builder: (context, constraints) {
                ScreenType screenType = UIHelper().getScreenType(context);
                switch (screenType) {
                  case ScreenType.mobile:
                    return Column(
                      children: [
                        getAppBar(role, plant),
                        getSearchBar(filteredFaultType, context),
                        getFaultList(),
                      ],
                    );
                  case ScreenType.tablet:
                    return Column(
                      children: [
                        getAppBar(role, plant),
                        getSearchBar(filteredFaultType, context),
                        getFaultList(),
                      ],
                    );
                  case ScreenType.desktop:
                    return Row(
                      children: [
                        Container(
                          color: AppColors.white,
                          child: ConstrainedBox(
                            constraints: const BoxConstraints(
                              minWidth: 533, // 410 * 1.3 = 533
                              maxWidth: 533,
                            ),
                            child: Column(
                              children: [
                                getAppBar(role, plant),
                                getSearchBar(filteredFaultType, context),
                                getFaultList(),
                              ],
                            ),
                          ),
                        ),
                        Expanded(
                            flex: 6,
                            child: ValueListenableBuilder(
                              valueListenable: faultDetailViewNotifier,
                              builder: (context, value, child) {
                                if (value == null) {
                                  return Container(
                                    color: AppColors.modernListBackground,
                                  );
                                } else {
                                  if (value.type == "FAULT_HEADER") {
                                    return ModernFaultDetailView(
                                      type: value.getValue("type"),
                                      isBackRequired: false,
                                    );
                                  } else {
                                    return Container(
                                      color: AppColors.modernListBackground,
                                    );
                                  }
                                }
                              },
                            ))
                      ],
                    );
                }
              },
            ),
          ),
          // Create Fault Modal
          CreateFaultModal(
            isOpen: _isCreateFaultModalOpen,
            onClose: () {
              setState(() {
                _isCreateFaultModalOpen = false;
              });
            },
          ),
        ],
      ),
    );
  }

  Expanded getFaultList() {
    return Expanded(
      child: RefreshIndicator(
          onRefresh: refreshData,
          child: FaultList(
            faultDetailViewNotifier: faultDetailViewNotifier,
            // faultDetailViewNotifier.value?.data["index"],
            onItemTap: (value) {
              //TODO sneha
              faultDetailViewNotifier.value = value;
            },
            scrollController: scrollController,
          )),
    );
  }

  Container getFilters(
      List<String> filterOfFaultCode,
      List<String> filterOfPriorityCode,
      List<String> statusTypeFilter,
      List<String> filterOfFaultType,
      List<String> filterOfPriority,
      FilteredFaultHeaderListNotifier filteredFaultType) {
    return Container(
      padding: UIHelper().getScreenType(context) == ScreenType.desktop
          ? const EdgeInsets.symmetric(horizontal: 8)
          : const EdgeInsets.symmetric(horizontal: 18, vertical: 6),
      child: Row(
        children: [
          Expanded(child: _faultType()),
          const SizedBox(width: 7),
          Expanded(
            child: _priorityType(),
          ),
          const SizedBox(width: 7),
          Expanded(
            child: _status(),
          ),
          const SizedBox(width: 7),
          if (filterOfFaultCode.isNotEmpty ||
              filterOfPriorityCode.isNotEmpty ||
              statusTypeFilter.isNotEmpty ||
              filterOfFaultType.isNotEmpty ||
              filterOfPriority.isNotEmpty ||
              statusTypeFilter.isNotEmpty)
            _clearFilter(filteredFaultType)
          // Add more filter options here if needed
        ],
      ),
    );
  }

  Container getSearchBar(
      FilteredFaultHeaderListNotifier filteredFaultType, BuildContext context) {
    return Container(
      color: AppColors.modernListBackground,
      padding: UIHelper().getScreenType(context) == ScreenType.desktop
          ? const EdgeInsets.symmetric(horizontal: 16, vertical: 8)
          : const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: SizedBox(
              height: 36, // Reduced height
              child: CustomSearchBar(
                onChanged: (v) {
                  // Reset fault detail view IMMEDIATELY when user starts typing
                  faultDetailViewNotifier.value = InteractiveItemModel(
                    type: "",
                    data: {"type": "", "index": null},
                  );

                  ref.read(searchTextProvider.notifier).state = v;
                  onSearchFault(filteredFaultType, v);
                },
                controller: searchController,
                onCancel: () {
                  // Reset fault detail view IMMEDIATELY when clearing search
                  faultDetailViewNotifier.value = InteractiveItemModel(
                    type: "",
                    data: {"type": "", "index": null},
                  );

                  setState(() {
                    ref.read(searchTextProvider.notifier).state = "";
                    onClearSearchFault(filteredFaultType);
                    if (scrollController.hasClients) {
                      scrollController.animateTo(
                        0.0,
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeOut,
                      );
                    }
                  });
                },
              ),
            ),
          ),
          const SizedBox(width: 12),
          // Modern filter button
          ModernFilterButton(
            selectedFaultTypes: ref.watch(selectedFaultTypesProvider),
            selectedPriorities: ref.watch(selectedPrioritiesProvider),
            selectedStatuses: ref.watch(selectedStatusesProvider),
            onFaultTypesChanged: (values) {
              ref.read(selectedFaultTypesProvider.notifier).state = values;
            },
            onPrioritiesChanged: (values) {
              ref.read(selectedPrioritiesProvider.notifier).state = values;
            },
            onStatusesChanged: (values) {
              ref.read(selectedStatusesProvider.notifier).state = values;
            },
          ),
        ],
      ),
    );
  }



  Container getAppBar(ROLE_HEADER? role, String plant) {
    return Container(
      padding: UIHelper().getScreenType(context) == ScreenType.desktop
          ? const EdgeInsets.symmetric(horizontal: 16, vertical: 12)
          : const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      color: AppColors.modernListBackground,
      child: Row(
        children: [
          // Left side - "Reported Faults" title
          Text(
            'Reported Faults',
            style: UIHelper.modernTitleStyle(
              fontSize: 22,
              fontWeight: FontWeight.w700,
              color: AppColors.modernPrimaryText,
            ),
          ),
          const Spacer(),
          // Right side - Create Fault button
          if (role != null && UIHelper.isCreate(role.fault!))
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _isCreateFaultModalOpen = true;
                });
              },
              icon: const Icon(Icons.add, size: 16),
              label: Text(
                'Create Fault',
                style: UIHelper.modernBodyStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
                elevation: 0,
              ),
            ),
        ],
      ),
    );
  }

  _faultType() {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final faultType = ref.watch(faultTypeListProvider.notifier);
    final filteredFaultType =
        ref.watch(filteredFaultHeaderListProvider.notifier);

    final filterOfFaultType =
        ref.watch(filterOfFaultTypeProvider.notifier).state;

    final filterOfFaultCode =
        ref.watch(filterOfFaultCodeProvider.notifier).state;

    final filterOfPriorityCode =
        ref.watch(filterOfFaultPriorityCodeProvider.notifier).state;

    final statusTypeFilter =
        ref.watch(statusTypeFaultFilterProvider.notifier).state;

    return _filterRow(
      itemBuilder: (context) {
        return List.generate(faultType.state.length, (index) {
          String type = faultType.state[index].description!;
          String typeCode = faultType.state[index].fault_code!;
          return PopupMenuItem(
            value: type,
            child: SizedBox(
              width: double.infinity,
              child: CheckboxListTile(
                controlAffinity: ListTileControlAffinity.leading,
                activeColor: filterOfFaultType.contains(type)
                    ? AppColors.primaryColor
                    : AppColors.transparent,
                title: Text(
                  type,
                ),
                value: filterOfFaultType.contains(type),
                onChanged: (value) async {
                  if (value != null) {
                    if (value) {
                      if (!filterOfFaultType.contains(type)) {
                        filterOfFaultType.add(type);
                        filterOfFaultCode.add(typeCode);
                        Navigator.pop(context, filterOfFaultType);
                        await filteredFaultType.filteredFaultHeaderList(
                            faulttypeList: filterOfFaultCode,
                            priorityList: filterOfPriorityCode,
                            statusList: statusTypeFilter,
                            type: AppConstants.faultType,
                            plantId: plant,
                            plantSec: plantSection);
                        setState(() {});
                      }
                    } else {
                      faultDetailViewNotifier.value = InteractiveItemModel(
                        type: "",
                        data: {"type": "", "index": null},
                      );
                      filterOfFaultType.remove(type);
                      filterOfFaultCode.remove(typeCode);
                      Navigator.pop(context, filterOfFaultType);
                      if (filterOfFaultCode.isNotEmpty ||
                          filterOfPriorityCode.isNotEmpty ||
                          statusTypeFilter.isNotEmpty) {
                        await filteredFaultType.filteredFaultHeaderList(
                            faulttypeList: filterOfFaultCode,
                            priorityList: filterOfPriorityCode,
                            statusList: statusTypeFilter,
                            type: AppConstants.faultType,
                            plantId: plant,
                            plantSec: plantSection);
                      } else {
                        await filteredFaultType.filteredFaultHeaderList(
                            type: 'Initial',
                            faultList: ref.read(faultHeaderListProvider),
                            plantId: plant,
                            plantSec: plantSection);
                      }
                    }
                    faultDetailViewNotifier.value = InteractiveItemModel(
                      type: "",
                      data: {"type": "", "index": null},
                    );
                    setState(() {});
                    if (scrollController.hasClients) {
                      scrollController.animateTo(
                        0.0,
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeOut,
                      );
                    }
                  }
                },
              ),
            ),
          );
        });
      },
      boxColor: filterOfFaultCode.isEmpty
          ? AppColors.white
          : AppColors.filterButtonColor,
      titleColor: Colors.black,
      title: AppLocalizations.of(context)!.faultType,
    );
  }

  _priorityType() {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final priority = ref.watch(priorityListProvider.notifier).state;
    final filteredFaultType =
        ref.watch(filteredFaultHeaderListProvider.notifier);

    final filterOfFaultCode =
        ref.watch(filterOfFaultCodeProvider.notifier).state;
    final filterOfPriorityCode =
        ref.watch(filterOfFaultPriorityCodeProvider.notifier).state;
    final filterOfPriority =
        ref.watch(filterOfFaultPriorityProvider.notifier).state;
    final statusTypeFilter =
        ref.watch(statusTypeFaultFilterProvider.notifier).state;

    return _filterRow(
      itemBuilder: (context) {
        return List.generate(priority.length, (index) {
          String type = priority[index].description!;
          String typeCode = priority[index].priority_code!;
          return PopupMenuItem(
            value: type,
            child: SizedBox(
              width: double.infinity,
              child: CheckboxListTile(
                controlAffinity: ListTileControlAffinity.leading,
                activeColor: filterOfPriority.contains(type)
                    ? AppColors.primaryColor
                    : AppColors.transparent,
                title: Text(
                  type,
                ),
                value: filterOfPriority.contains(type),
                onChanged: (value) async {
                  if (value != null) {
                    if (value) {
                      if (!filterOfPriority.contains(type)) {
                        filterOfPriority.add(type);
                        filterOfPriorityCode.add(typeCode);
                        Navigator.pop(context, filterOfPriority);
                        await filteredFaultType.filteredFaultHeaderList(
                            faulttypeList: filterOfFaultCode,
                            priorityList: filterOfPriorityCode,
                            statusList: statusTypeFilter,
                            type: AppConstants.priority,
                            plantId: plant,
                            plantSec: plantSection);
                        setState(() {});
                      }
                    } else {
                      filterOfPriority.remove(type);
                      filterOfPriorityCode.remove(typeCode);
                      Navigator.pop(context, filterOfPriority);

                      if (filterOfFaultCode.isNotEmpty ||
                          filterOfPriorityCode.isNotEmpty ||
                          statusTypeFilter.isNotEmpty) {
                        await filteredFaultType.filteredFaultHeaderList(
                            faulttypeList: filterOfFaultCode,
                            priorityList: filterOfPriorityCode,
                            statusList: statusTypeFilter,
                            type: AppConstants.priority,
                            plantId: plant,
                            plantSec: plantSection);
                      } else {
                        await filteredFaultType.filteredFaultHeaderList(
                            type: 'Initial',
                            faultList: ref.read(faultHeaderListProvider),
                            plantId: plant,
                            plantSec: plantSection);
                      }
                    }
                    faultDetailViewNotifier.value = InteractiveItemModel(
                      type: "",
                      data: {"type": "", "index": null},
                    );
                    setState(() {});
                    if (scrollController.hasClients) {
                      scrollController.animateTo(
                        0.0,
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeOut,
                      );
                    }
                  }
                },
              ),
            ),
          );
        });
      },
      boxColor: filterOfPriorityCode.isEmpty
          ? AppColors.white
          : AppColors.filterButtonColor,
      titleColor: Colors.black,
      title: AppLocalizations.of(context)!.priority,
    );
  }

  ///Redo
  _status() {
    List<String> status = [
      "Open",
      "Job Assigned",
      "Completed",
    ];
    final filteredFaultType =
        ref.watch(filteredFaultHeaderListProvider.notifier);
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;

    final filterOfFaultCode =
        ref.watch(filterOfFaultCodeProvider.notifier).state;
    final filterOfPriorityCode =
        ref.watch(filterOfFaultPriorityCodeProvider.notifier).state;
    final statusTypeFilter =
        ref.watch(statusTypeFaultFilterProvider.notifier).state;
    final statusFilter = ref.watch(statusFaultFilterProvider.notifier).state;

    return _filterRow(
      itemBuilder: (context) {
        return List.generate(status.length, (index) {
          String data = status[index];
          return PopupMenuItem(
            value: data,
            child: SizedBox(
              width: double.infinity,
              child: CheckboxListTile(
                controlAffinity: ListTileControlAffinity.leading,
                activeColor: statusFilter.contains(data)
                    ? AppColors.primaryColor
                    : AppColors.transparent,
                title: Text(
                  data,
                ),
                value: statusFilter.contains(data),
                onChanged: (value) async {
                  if (value != null) {
                    if (value) {
                      if (!statusFilter.contains(data)) {
                        statusFilter.add(data);

                        String dataa = getStatusType(data);
                        statusTypeFilter.add(dataa);
                        Navigator.pop(context, statusFilter);
                        await filteredFaultType.filteredFaultHeaderList(
                            faulttypeList: filterOfFaultCode,
                            priorityList: filterOfPriorityCode,
                            statusList: statusTypeFilter,
                            type: AppConstants.status,
                            plantId: plant,
                            plantSec: plantSection);

                        setState(() {});
                      }
                    } else {
                      statusFilter.remove(data);
                      String dataa = getStatusType(data);
                      statusTypeFilter.remove(dataa);
                      Navigator.pop(context, statusFilter);
                      if (filterOfFaultCode.isNotEmpty ||
                          filterOfPriorityCode.isNotEmpty ||
                          statusTypeFilter.isNotEmpty) {
                        await filteredFaultType.filteredFaultHeaderList(
                            faulttypeList: filterOfFaultCode,
                            priorityList: filterOfPriorityCode,
                            statusList: statusTypeFilter,
                            type: AppConstants.status,
                            plantId: plant,
                            plantSec: plantSection);
                      } else {
                        await filteredFaultType.filteredFaultHeaderList(
                            type: 'Initial',
                            faultList: ref.read(faultHeaderListProvider),
                            plantId: plant,
                            plantSec: plantSection);
                      }
                    }
                    faultDetailViewNotifier.value = InteractiveItemModel(
                      type: "",
                      data: {"type": "", "index": null},
                    );
                    setState(() {});
                    if (scrollController.hasClients) {
                      scrollController.animateTo(
                        0.0,
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeOut,
                      );
                    }
                  }
                },
              ),
            ),
          );
        });
      },
      boxColor:
          statusFilter.isEmpty ? AppColors.white : AppColors.filterButtonColor,
      titleColor: Colors.black,
      title: AppLocalizations.of(context)!.status,
    );
  }

  getStatusType(status) {
    switch (status) {
      case "Open":
        return "CREATED";
      case "Job Assigned":
        return "ASSIGNED";
      case "Completed":
        return "COMPLETED";
      default:
        return "Default";
    }
  }

  _filterRow({
    required String title,
    required List<PopupMenuEntry<Object?>> Function(BuildContext) itemBuilder,
    required Color boxColor,
    required Color titleColor,
  }) {
    return Container(
      height: 46,
      decoration: BoxDecoration(
        color: boxColor,
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: HexColor("#D5DADD")),
      ),
      child: PopupMenuButton(
        padding: EdgeInsets.only(right: 200),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        offset: Offset(0, 50),
        itemBuilder: itemBuilder,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            SizedBox(width: 9),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: titleColor,
              ),
            ),
            Spacer(),
            Icon(
              Icons.keyboard_arrow_down,
              color: titleColor,
            ),
            SizedBox(width: 5),
          ],
        ),
        onSelected: (v) {
          // applyFilter();
        },
      ),
    );
  }

  clearFaultStates() {
    final faultHeader = ref.read(faultHeaderProvider.notifier);
    final location = ref.watch(locationProvider.notifier);
    final assetLocList = ref.watch(assetLocListProvider.notifier);
    final asset = ref.watch(assetProvider.notifier);
    final dueOn = ref.watch(faultDueOnProvider.notifier);
    final description = ref.watch(faultDescriptionProvider.notifier);
    final faultMode = ref.watch(faultModeHeaderProvider.notifier);
    final priority = ref.watch(priorityProvider.notifier);
    final faultType = ref.watch(faultTypeProvider.notifier);
    final longText = ref.watch(faultLongTextProvider.notifier);
    final reportedBy = ref.watch(faultReportedByProvider.notifier);
    location.clearLocation();
    assetLocList.clearAssetLocList();
    asset.clearAsset();
    dueOn.clearDueOn();
    description.clearFaultDescription();
    faultMode.clearFaultMode();
    priority.clearPriority();
    faultType.clearFaultType();
    longText.clearFaultLongText();
    reportedBy.clearFaultReportedBy();
    faultHeader.clearFault();
  }

  onAddCreationOfFault(String plant) async {
    final faultTypeHeader = ref.read(faultTypeListProvider.notifier);
    final faultInsertHeader = ref.read(insertFaultHeaderProvider.notifier);
    final faultHeader = ref.read(faultHeaderProvider.notifier);
    await faultTypeHeader.fetchFaultTypeList();
    final editProvider = ref.read(editFaultFieldProvider.notifier);
    final faultAction = ref.watch(getFaultActionProvider.notifier);
    final faultDocument = ref.watch(getFaultDocumentProvider.notifier);
    final user = ref.watch(userProvider);
    DateTime now = DateTime.now();
    String faultNoticedOnDate = DateFormat('dd MMM yyyy').format(now);
    DateTime dateTime = DateFormat("dd MMM yyyy").parse(faultNoticedOnDate);
    DateTime adjustedDate = DateTime(
      dateTime.year,
      dateTime.month,
      dateTime.day,
    );
    String formattedDate = DateFormat("yyyyMMdd").format(adjustedDate);
    int dateAsInt = int.parse(formattedDate);
    FAULT_HEADER faultHeaderData = FAULT_HEADER(
        fault_id: UIHelper.generateRandomId(),
        reported_by: user!.user_id,
        p_mode: AppConstants.add,
        reported_on: dateAsInt);
    if (plant.isNotEmpty) {
      faultHeaderData.plant_id = plant;
    }
    await faultInsertHeader.insertFaultHeader(faultHeaderData);
    await faultHeader.getFaultHeader(
        faultId: faultHeaderData.fault_id.toString());
    await faultAction.getFaultAction(faultHeaderData.fault_id.toString());
    await faultDocument.getFaultDocuments(faultHeaderData.fault_id.toString());
    dynamic result;
    editProvider.getEditFaultFieldEnable(true);
    if (mounted) {
      if (ScreenType.desktop != UIHelper().getScreenType(context)) {
        result =
            await Navigator.push(context, MaterialPageRoute(builder: (context) {
          return const FaultDetailScreen(
            type: AppConstants.addFault,
          );
        }));
      } else {
        result = await showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => Dialog(
                backgroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0),
                ),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxWidth: 600,
                    maxHeight: 800,
                  ),
                  child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: FaultDetailScreen(
                        type: AppConstants.addFault,
                        onTap: (value) {
                          faultDetailViewNotifier.value = value;
                        },
                      )),
                )));
      }
      if (result != null) {
        final plant = ref.read(plantProvider.notifier).state;
        final plantSection = ref.read(plantSectionProvider.notifier).state;
        final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
        await faultHeaderList.fetchFaultHeaderList(plant);
        final filteredFaultType =
            ref.read(filteredFaultHeaderListProvider.notifier);

        final filterOfFaultType =
            ref.read(filterOfFaultTypeProvider.notifier).state;
        final filterOfFaultCode =
            ref.read(filterOfFaultCodeProvider.notifier).state;
        final filterOfPriorityCode =
            ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
        final filterOfPriority =
            ref.read(filterOfFaultPriorityProvider.notifier).state;
        final statusTypeFilter =
            ref.read(statusTypeFaultFilterProvider.notifier).state;
        final statusFilter = ref.read(statusFaultFilterProvider.notifier).state;
        final selectedDate = ref.read(selectedCalendarDateProvider);
        final search = ref.read(searchTextProvider.notifier).state;
        if ((search != '') ||
            filterOfFaultType.isNotEmpty ||
            filterOfFaultCode.isNotEmpty ||
            filterOfPriorityCode.isNotEmpty ||
            filterOfPriority.isNotEmpty ||
            statusTypeFilter.isNotEmpty ||
            statusFilter.isNotEmpty ||
            selectedDate != null) {
          // Reset fault detail view when any filter is applied
          faultDetailViewNotifier.value = InteractiveItemModel(
            type: "",
            data: {"type": "", "index": null},
          );
          await filteredFaultType.filteredFaultHeaderList(
              faulttypeList: filterOfFaultCode,
              priorityList: filterOfPriorityCode,
              statusList: statusTypeFilter,
              type:
                  (search != '') ? AppConstants.search : AppConstants.faultType,
              plantId: plant,
              plantSec: plantSection,
              search: search);
        } else {
          await filteredFaultType.filteredFaultHeaderList(
              type: 'Initial',
              faultList: faultHeaderList.state,
              // faultList: faultHeaderDatas,
              plantId: plant,
              plantSec: plantSection);
        }
        if (ScreenType.desktop == UIHelper().getScreenType(context)) {
          await handleNewFaultCreated(result as FAULT_HEADER);
          setState(() {});
        }
      }
    }
  }

  Future<void> handleNewFaultCreated(FAULT_HEADER newFault) async {
    final faultList = ref.read(faultHeaderListProvider);

    int newIndex =
        faultList.indexWhere((item) => item.fault_id == newFault.fault_id);
    if (newIndex != -1) {
      scrollController.animateTo(
        newIndex * (116 + 20),
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
    faultDetailViewNotifier.value = InteractiveItemModel(
      type: "",
      data: {"type": AppConstants.fault, "index": null},
    );
  }

  void onSearchFault(
      FilteredFaultHeaderListNotifier filteredFaultType, String v) async {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;

    final filterOfFaultCode =
        ref.read(filterOfFaultCodeProvider.notifier).state;
    final filterOfPriorityCode =
        ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
    final statusTypeFilter =
        ref.read(statusTypeFaultFilterProvider.notifier).state;

    await filteredFaultType.filteredFaultHeaderList(
        faulttypeList: filterOfFaultCode,
        priorityList: filterOfPriorityCode,
        statusList: statusTypeFilter,
        search: v,
        type: AppConstants.search,
        plantId: plant,
        plantSec: plantSection);
    setState(() {});
    if (scrollController.hasClients) {
      scrollController.animateTo(
        0.0,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void onClearSearchFault(
      FilteredFaultHeaderListNotifier filteredFaultType) async {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final faultHeader = ref.read(faultHeaderListProvider.notifier).state;
    await filteredFaultType.filteredFaultHeaderList(
        type: 'Initial',
        faultList: faultHeader,
        plantId: plant,
        plantSec: plantSection);
    setState(() {
      searchController.clear();
      ref.read(searchTextProvider.notifier).state = '';
    });
  }

  _clearFilter(FilteredFaultHeaderListNotifier filteredFaultType) {
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;

    return InkWell(
      onTap: () async {
        final faultHeader = ref.read(faultHeaderListProvider.notifier).state;
        await filteredFaultType.filteredFaultHeaderList(
            type: 'Initial',
            faultList: faultHeader,
            plantId: plant,
            plantSec: plantSection);
        setState(() {
          ref.read(filterOfFaultTypeProvider.notifier).state = [];
          ref.read(filterOfFaultCodeProvider.notifier).state = [];
          ref.read(filterOfFaultPriorityCodeProvider.notifier).state = [];
          ref.read(filterOfFaultPriorityProvider.notifier).state = [];
          ref.read(statusTypeFaultFilterProvider.notifier).state = [];
          ref.read(statusFaultFilterProvider.notifier).state = [];
          faultDetailViewNotifier.value = InteractiveItemModel(
            type: "",
            data: {"type": "", "index": null},
          );
        });
        if (scrollController.hasClients) {
          scrollController.animateTo(
            0.0,
            duration: Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      },
      child: const Icon(
        Icons.cancel,
        size: 30,
      ),
    );
  }


}
