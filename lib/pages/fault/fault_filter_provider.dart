import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/utils/utils.dart';
import '../../be/FAULT_HEADER.dart';
import '../../helpers/db_helper.dart';
import '../../utils/app_constants.dart';

final filteredFaultHeaderListProvider =
    StateNotifierProvider<FilteredFaultHeaderListNotifier, List<FAULT_HEADER>>(
        (ref) {
  return FilteredFaultHeaderListNotifier();
});

class FilteredFaultHeaderListNotifier
    extends StateNotifier<List<FAULT_HEADER>> {
  FilteredFaultHeaderListNotifier() : super([]);

  Future<void> filteredFaultHeaderList(
      {List<FAULT_HEADER>? faultList,
      List<String>? faulttypeList,
      List<String>? priorityList,
      List<String>? statusList,
      required String type,
      String? search,
      required String plantId,
      required List<String> plantSec}) async {
    try {
      if (faultList != null && type == 'Initial') {
        state = List.from(faultList);
      } else if ((statusList != null && statusList.isNotEmpty) ||
          (faulttypeList != null && faulttypeList.isNotEmpty) ||
          (priorityList != null && priorityList.isNotEmpty)) {
        List<FAULT_HEADER> faultTypeHeaders =
            await DbHelper.getFilteredAndSearchedFaultHeaderList(
                plantId: plantId,
                plantSec: plantSec,
                searchQuery: search ?? "",
                typeList: faulttypeList ?? [],
                priorityList: priorityList ?? [],
                statusList: statusList ?? []);

        faultTypeHeaders.sort((a, b) {
          DateTime dateA = convertToDateTime(a.reported_on.toString());
          DateTime dateB = convertToDateTime(b.reported_on.toString());
          return dateA.compareTo(dateB);
        });

        state = List.from(faultTypeHeaders);
      } else if (search != null && type == AppConstants.search) {
        List<FAULT_HEADER> faultTypeHeaders =
            await DbHelper.getFilteredAndSearchedFaultHeaderList(
                plantId: plantId,
                plantSec: plantSec,
                searchQuery: search,
                typeList: faulttypeList ?? [],
                priorityList: priorityList ?? [],
                statusList: statusList ?? []);
        ;
        faultTypeHeaders.sort((a, b) {
          DateTime dateA = convertToDateTime(a.reported_on.toString());
          DateTime dateB = convertToDateTime(b.reported_on.toString());
          return dateA.compareTo(dateB);
        });
        state = faultTypeHeaders;
      }
    } catch (e) {
      Logger.logError('FilteredFaultHeaderListNotifier',
          'filteredFaultHeaderList', e.toString());
    }
  }
}
