import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class EamIcon {
  static final _assets = 'assets';
  static final _icons = 'icons';
  static final _fileSeparator = '/';
  static final _svgExt = '.svg';

  static final add = 'add';
  static final alert = 'alert';
  static final arrow_back = 'arrow_back';
  static final arrow_down = 'arrow_down';
  static final arrow_forward = 'arrow_forward';
  static final arrow_up = 'arrow_up';
  static final aspect_ratio = 'aspect_ratio';
  static final brush = 'brush';
  static final calender = 'calender';
  static final close = 'close';
  static final color_pallet = 'color_pallet';
  static final camera = 'camera';
  static final clock = 'clock';
  static final crew = 'crew';
  static final crop = 'crop';
  static final dashboard = 'dashboard';
  static final delete = 'delete';
  static final doc = 'doc';
  static final document = 'document';
  static final done = 'done';
  static final download = 'download';
  static final edit = 'edit';
  static final equip = 'equip';
  static final equipment = 'equipment';
  static final eye = 'eye';
  static final eye_slash = 'eye_slash';
  static final filter = 'filter';
  static final flag = 'flag';
  static final folder = 'folder';
  static final forms = 'forms';
  static final hierarchy = 'hierarchy';
  static final history = 'history';
  static final home = 'home';
  static final hourglass = 'hourglass';
  static final image = 'image';
  static final list = 'list';
  static final location = 'location';
  static final map = 'map';
  static final material = 'material';
  static final menu = 'menu';
  static final menu_drawer = 'menu_drawer';
  static final message = 'message';
  static final minus = 'minus';
  static final notification = 'notification';
  static final operation = 'operation';
  static final order = 'order';
  static final pause = 'pause';
  static final pencil = 'pencil';
  static final play = 'play';
  static final plus = 'plus';
  static final radio_checked = 'radio_checked';
  static final radio_unchecked = 'radio_unchecked';
  static final refresh = 'refresh';
  static final rotate_left = 'rotate_left';
  static final rotate_right = 'rotate_right';
  static final round = 'round';
  static final scan = 'scan';
  static final search = 'search';
  static final shopping_card = 'shopping_card';
  static final stop = 'stop';
  static final sync = 'sync';
  static final techobject = 'techobject';
  static final text = 'text';
  static final time = 'time';
  static final undo = 'undo';
  static final checkeredFlag = 'checkeredFlag';
  static final aws = 'aws';
  static final google = 'google';
  static final device = 'device';
  static final dropbox = 'dropbox';
  static final fileShare = 'fileShare';
  static final ftp = 'ftp';
  static final local = 'local';
  static final other = 'other';
  static final sap = 'sap';
  static final sharePoint = 'sharePoint';
  static final floc = 'Floc';
  static final asset = 'asset';
  static final kpi = 'kpi';

  final String iconName;
  final double height;
  final double width;
  final Color color;

  EamIcon(
      {required this.iconName,
      double this.height = 40,
      double this.width = 40,
      Color this.color = Colors.black});

  EamIcon copyWith({double? height, double? width, Color? color}) => EamIcon(
        iconName: iconName,
        height: height ?? this.height,
        width: width ?? this.width,
        color: color ?? this.color,
      );

  icon() {
    return SvgPicture.asset(
      _assets + _fileSeparator + _icons + _fileSeparator + iconName + _svgExt,
      height: height,
      width: width,
      color: color,
    );
  }
}
