// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
      apiKey: 'AIzaSyDMMWMXRvJxUX6_AjXJuOKcJTnXdQSnQKU',
      appId: '1:704334877465:web:4f9fe8b5b91f32e4586b88',
      messagingSenderId: '704334877465',
      projectId: 'rounds-62bfa',
      authDomain: 'rounds-62bfa.firebaseapp.com',
      storageBucket: 'rounds-62bfa.firebasestorage.app',
      measurementId: "G-NVM75E3PNX");

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBBnO2hXsoG_S-OOjMvRYx3MGWdPobCJUE',
    appId: '1:704334877465:android:a13f14fe22fa6721586b88',
    messagingSenderId: '704334877465',
    projectId: 'rounds-62bfa',
    storageBucket: 'rounds-62bfa.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAZWBZeo3nVKqNiR8XdpQPqcw_cQth1Sfc',
    appId: '1:704334877465:ios:1865628f4cfe2400586b88',
    messagingSenderId: '704334877465',
    projectId: 'rounds-62bfa',
    storageBucket: 'rounds-62bfa.firebasestorage.app',
    iosBundleId: 'com.unvired.rounds',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBBnO2hXsoG_S-OOjMvRYx3MGWdPobCJUE',
    appId: '1:704334877465:android:a13f14fe22fa6721586b88',
    messagingSenderId: '704334877465',
    projectId: 'rounds-62bfa',
    storageBucket: 'rounds-62bfa.firebasestorage.app',
    iosBundleId: 'com.unvired.rounds',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBBnO2hXsoG_S-OOjMvRYx3MGWdPobCJUE',
    appId: '1:704334877465:android:a13f14fe22fa6721586b88',
    messagingSenderId: '704334877465',
    projectId: 'rounds-62bfa',
    authDomain: 'rounds-62bfa.firebaseapp.com',
    storageBucket: 'rounds-62bfa.firebasestorage.app',
  );
}
