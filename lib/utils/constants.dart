//	Generated using Unvired Modeller - Build R-4.000.0139

class Constants {
// App Name
  static const String APP_NAME = "ROUNDS";

  // BE Names
  static const String BE_ABCINDICATOR = "ABCINDICATOR";
  static const String BE_APP_SETTING = "APP_SETTING";
  static const String BE_ASSET = "ASSET";
  static const String BE_ASSET_CATEGORY = "ASSET_CATEGORY";
  static const String BE_CALENDAR = "CALENDAR";
  static const String BE_CILT_EXEC = "CILT_EXEC";
  static const String BE_CILT_PLAN = "CILT_PLAN";
  static const String BE_CILT_SCHED = "CILT_SCHED";
  static const String BE_COUNTRY = "COUNTRY";
  static const String BE_DOCUMENT = "DOCUMENT";
  static const String BE_FAILURE_MODE = "FAILURE_MODE";
  static const String BE_FAULT = "FAULT";
  static const String BE_FAULT_TYPE = "FAULT_TYPE";
  static const String BE_FOLDER = "FOLDER";
  static const String BE_INSPECTION_PLAN = "INSPECTION_PLAN";
  static const String BE_INSP_EXEC = "INSP_EXEC";
  static const String BE_INSP_PLAN_TYPE = "INSP_PLAN_TYPE";
  static const String BE_INSP_SCHED = "INSP_SCHED";
  static const String BE_JOB = "JOB";
  static const String BE_JOBTYPE = "JOBTYPE";
  static const String BE_KPI = "KPI";
  static const String BE_LOCATION = "LOCATION";
  static const String BE_LOCATION_CATEGORY = "LOCATION_CATEGORY";
  static const String BE_PICKLIST = "PICKLIST";
  static const String BE_PLANT = "PLANT";
  static const String BE_PLANT_SECTION = "PLANT_SECTION";
  static const String BE_PRIORITY = "PRIORITY";
  static const String BE_ROLE = "ROLE";
  static const String BE_SHIFT = "SHIFT";
  static const String BE_SKIP_REASON = "SKIP_REASON";
  static const String BE_STATE = "STATE";
  static const String BE_SYSTEM_CONDITION = "SYSTEM_CONDITION";
  static const String BE_UNIT = "UNIT";
  static const String BE_USER = "USER";
  static const String BE_USER_GROUP = "USER_GROUP";

  // PA Function Names
  static const String PA_ACTION_ACCEPT_CILT =
      "ROUNDS_PA_ACTION_ACCEPT_CILT"; // Accept CILT
  static const String PA_ACTION_ACCEPT_INSPECTION =
      "ROUNDS_PA_ACTION_ACCEPT_INSPECTION"; // Accept Inspection
  static const String PA_ACTION_ASSIGN_CILT =
      "ROUNDS_PA_ACTION_ASSIGN_CILT"; // Assign CILT
  static const String PA_ACTION_ASSIGN_INSPECTION =
      "ROUNDS_PA_ACTION_ASSIGN_INSPECTION"; // Assign Inspection
  static const String PA_ACTION_CANCEL_CILT =
      "ROUNDS_PA_ACTION_CANCEL_CILT"; // Cancel CILT
  static const String PA_ACTION_CANCEL_INSPECTION =
      "ROUNDS_PA_ACTION_CANCEL_INSPECTION"; // Cancel Inspection
  static const String PA_ACTION_REJECT_CILT =
      "ROUNDS_PA_ACTION_REJECT_CILT"; // Reject CILT
  static const String PA_ACTION_REJECT_INSPECTION =
      "ROUNDS_PA_ACTION_REJECT_INSPECTION"; // Reject Inspection
  static const String PA_ADD_ABC_INDICATOR =
      "ROUNDS_PA_ADD_ABC_INDICATOR"; // ABC Indicator
  static const String PA_ADD_ASSET = "ROUNDS_PA_ADD_ASSET"; // Add Asset
  static const String PA_ADD_ASSET_CATEGORY =
      "ROUNDS_PA_ADD_ASSET_CATEGORY"; // Add Asset Category
  static const String PA_ADD_CALENDAR =
      "ROUNDS_PA_ADD_CALENDAR"; // Add Calendar
  static const String PA_ADD_CILT = "ROUNDS_PA_ADD_CILT"; // Create Adhoc CILT
  static const String PA_ADD_CILT_SCHEDULE =
      "ROUNDS_PA_ADD_CILT_SCHEDULE"; // Add CILT Schedule
  static const String PA_ADD_DOCUMENT =
      "ROUNDS_PA_ADD_DOCUMENT"; // Create Document
  static const String PA_ADD_FAILURE_MODE =
      "ROUNDS_PA_ADD_FAILURE_MODE"; // Add Failure Mode
  static const String PA_ADD_FAULT_TYPE =
      "ROUNDS_PA_ADD_FAULT_TYPE"; // Add Fault Type
  static const String PA_ADD_FOLDER = "ROUNDS_PA_ADD_FOLDER"; // Add Folder
  static const String PA_ADD_HOLIDAY = "ROUNDS_PA_ADD_HOLIDAY"; // Add Holiday
  static const String PA_ADD_INSPECTION =
      "ROUNDS_PA_ADD_INSPECTION"; // Create Adhoc Inspection
  static const String PA_ADD_INSPECTION_PLAN_TYPE =
      "ROUNDS_PA_ADD_INSPECTION_PLAN_TYPE"; // Add Inspection Plan Type
  static const String PA_ADD_INSPECTION_SCHEDULE =
      "ROUNDS_PA_ADD_INSPECTION_SCHEDULE"; // Add Inspection Schedule
  static const String PA_ADD_JOB_TYPE =
      "ROUNDS_PA_ADD_JOB_TYPE"; // Add Job Type
  static const String PA_ADD_KPI = "ROUNDS_PA_ADD_KPI"; // Add KPI
  static const String PA_ADD_LOCATION =
      "ROUNDS_PA_ADD_LOCATION"; // Add Location
  static const String PA_ADD_LOC_CATEGORY =
      "ROUNDS_PA_ADD_LOC_CATEGORY"; // Add Location Category
  static const String PA_ADD_PICKLIST =
      "ROUNDS_PA_ADD_PICKLIST"; // Add Picklist
  static const String PA_ADD_PICKLIST_CODE =
      "ROUNDS_PA_ADD_PICKLIST_CODE"; // Add Picklist Code
  static const String PA_ADD_PLANT = "ROUNDS_PA_ADD_PLANT"; // Add plant
  static const String PA_ADD_PLANT_SECTION =
      "ROUNDS_PA_ADD_PLANT_SECTION"; // Add Plant Section
  static const String PA_ADD_PRIORITY =
      "ROUNDS_PA_ADD_PRIORITY"; // Add Priority
  static const String PA_ADD_ROLE = "ROUNDS_PA_ADD_ROLE"; // Add Role
  static const String PA_ADD_SHIFT = "ROUNDS_PA_ADD_SHIFT"; // Add Shift
  static const String PA_ADD_SKIP_REASON =
      "ROUNDS_PA_ADD_SKIP_REASON"; // Add Skip Reason
  static const String PA_ADD_SYS_COND =
      "ROUNDS_PA_ADD_SYS_COND"; // Add system condition
  static const String PA_ADD_UNIT = "ROUNDS_PA_ADD_UNIT"; // Add Unit
  static const String PA_ADD_USER = "ROUNDS_PA_ADD_USER"; // Add User
  static const String PA_ADD_USER_GROUP =
      "ROUNDS_PA_ADD_USER_GROUP"; // Add User Group
  static const String PA_BULK_UPLOAD_ASSET =
      "ROUNDS_PA_BULK_UPLOAD_ASSET"; // Bulk upload Asset
  static const String PA_BULK_UPLOAD_KPI =
      "ROUNDS_PA_BULK_UPLOAD_KPI"; // Bulk upload KPI
  static const String PA_BULK_UPLOAD_LOCATION =
      "ROUNDS_PA_BULK_UPLOAD_LOCATION"; // Bulk upload Location
  static const String PA_BULK_UPLOAD_USER =
      "ROUNDS_PA_BULK_UPLOAD_USER"; // Bulk upload User
  static const String PA_CRON_CREATE_CILT =
      "ROUNDS_PA_CRON_CREATE_CILT"; // Create CILT from schedule
  static const String PA_CRON_CREATE_INSPECTION =
      "ROUNDS_PA_CRON_CREATE_INSPECTION"; // Create Inspection from schedule
  static const String PA_DELETE_ABC_INDICATOR =
      "ROUNDS_PA_DELETE_ABC_INDICATOR"; // Delete ABC Indicator
  static const String PA_DELETE_ASSET =
      "ROUNDS_PA_DELETE_ASSET"; // Delete Asset
  static const String PA_DELETE_ASSET_CATEGORY =
      "ROUNDS_PA_DELETE_ASSET_CATEGORY"; // Delete Asset Category
  static const String PA_DELETE_CALENDAR =
      "ROUNDS_PA_DELETE_CALENDAR"; // Delete Calendar
  static const String PA_DELETE_CILT = "ROUNDS_PA_DELETE_CILT"; // Delete CILT
  static const String PA_DELETE_CILT_PLAN =
      "ROUNDS_PA_DELETE_CILT_PLAN"; // Delete CILT Plan
  static const String PA_DELETE_CILT_SCHEDULE =
      "ROUNDS_PA_DELETE_CILT_SCHEDULE"; // Delete CILT Schedule
  static const String PA_DELETE_DOCUMENT =
      "ROUNDS_PA_DELETE_DOCUMENT"; // Delete Document
  static const String PA_DELETE_FAILURE_MODE =
      "ROUNDS_PA_DELETE_FAILURE_MODE"; // Delete Failure Mode
  static const String PA_DELETE_FAULT_TYPE =
      "ROUNDS_PA_DELETE_FAULT_TYPE"; // Delete Fault Type
  static const String PA_DELETE_FOLDER =
      "ROUNDS_PA_DELETE_FOLDER"; // Delete Folder
  static const String PA_DELETE_HOLIDAY =
      "ROUNDS_PA_DELETE_HOLIDAY"; // Delete Holiday
  static const String PA_DELETE_INSPECTION =
      "ROUNDS_PA_DELETE_INSPECTION"; // Delete Inspection
  static const String PA_DELETE_INSPECTION_PLAN =
      "ROUNDS_PA_DELETE_INSPECTION_PLAN"; // Delete Inspection Plan
  static const String PA_DELETE_INSPECTION_PLAN_TYPE =
      "ROUNDS_PA_DELETE_INSPECTION_PLAN_TYPE"; // Delete Inspection Plan Type
  static const String PA_DELETE_INSPECTION_SCHEDULE =
      "ROUNDS_PA_DELETE_INSPECTION_SCHEDULE"; // Delete Inspection Schedule
  static const String PA_DELETE_JOB_TYPE =
      "ROUNDS_PA_DELETE_JOB_TYPE"; // Delete Job Type
  static const String PA_DELETE_KPI = "ROUNDS_PA_DELETE_KPI"; // Delete KPI
  static const String PA_DELETE_LOCATION =
      "ROUNDS_PA_DELETE_LOCATION"; // Delete Location
  static const String PA_DELETE_LOC_CATEGORY =
      "ROUNDS_PA_DELETE_LOC_CATEGORY"; // Delete Location Category
  static const String PA_DELETE_PICKLIST =
      "ROUNDS_PA_DELETE_PICKLIST"; // Delete Picklist
  static const String PA_DELETE_PICKLIST_CODE =
      "ROUNDS_PA_DELETE_PICKLIST_CODE"; // Delete Picklist Code
  static const String PA_DELETE_PLANT =
      "ROUNDS_PA_DELETE_PLANT"; // Delete Plant
  static const String PA_DELETE_PLANT_SECTION =
      "ROUNDS_PA_DELETE_PLANT_SECTION"; // Delete Plant Section
  static const String PA_DELETE_PRIORITY =
      "ROUNDS_PA_DELETE_PRIORITY"; // Delete Priority
  static const String PA_DELETE_ROLE = "ROUNDS_PA_DELETE_ROLE"; // Delete Role
  static const String PA_DELETE_SHIFT =
      "ROUNDS_PA_DELETE_SHIFT"; // Delete Shift
  static const String PA_DELETE_SKIP_REASON =
      "ROUNDS_PA_DELETE_SKIP_REASON"; // Delete Skip Reason
  static const String PA_DELETE_SYS_COND =
      "ROUNDS_PA_DELETE_SYS_COND"; // Delete System Condition
  static const String PA_DELETE_UNIT = "ROUNDS_PA_DELETE_UNIT"; // Delete Unit
  static const String PA_DELETE_USER = "ROUNDS_PA_DELETE_USER"; // Delete User
  static const String PA_DELETE_USER_GROUP =
      "ROUNDS_PA_DELETE_USER_GROUP"; // Delete User Group
  static const String PA_DOWNLOAD_ASSET =
      "ROUNDS_PA_DOWNLOAD_ASSET"; // Download Asset
  static const String PA_DOWNLOAD_CILT =
      "ROUNDS_PA_DOWNLOAD_CILT"; // Download CILT
  static const String PA_DOWNLOAD_CILT_PLAN =
      "ROUNDS_PA_DOWNLOAD_CILT_PLAN"; // Download CILT Plans
  static const String PA_DOWNLOAD_CUSTOMIZING =
      "ROUNDS_PA_DOWNLOAD_CUSTOMIZING"; // Download Customizing
  static const String PA_DOWNLOAD_ENTITY =
      "ROUNDS_PA_DOWNLOAD_ENTITY"; // Download entity
  static const String PA_DOWNLOAD_FAULT =
      "ROUNDS_PA_DOWNLOAD_FAULT"; // Download Fault
  static const String PA_DOWNLOAD_INSPECTION =
      "ROUNDS_PA_DOWNLOAD_INSPECTION"; // Download Inspection
  static const String PA_DOWNLOAD_INSP_PLAN =
      "ROUNDS_PA_DOWNLOAD_INSP_PLAN"; // Download Inspection Plans
  static const String PA_DOWNLOAD_JOB =
      "ROUNDS_PA_DOWNLOAD_JOB"; // Download Job
  static const String PA_DOWNLOAD_KPI =
      "ROUNDS_PA_DOWNLOAD_KPI"; // Download KPI
  static const String PA_DOWNLOAD_LOCATION =
      "ROUNDS_PA_DOWNLOAD_LOCATION"; // Download Location
  static const String PA_GET_ABC_INDICATOR =
      "ROUNDS_PA_GET_ABC_INDICATOR"; // Get ABC Indicator
  static const String PA_GET_ASSET = "ROUNDS_PA_GET_ASSET"; // Get Asset
  static const String PA_GET_ASSET_CATEGORY =
      "ROUNDS_PA_GET_ASSET_CATEGORY"; // Get Asset Category
  static const String PA_GET_BULK_UPLOAD_TEMPLATE =
      "ROUNDS_PA_GET_BULK_UPLOAD_TEMPLATE"; // Get templates to bulk upload data
  static const String PA_GET_CALENDAR =
      "ROUNDS_PA_GET_CALENDAR"; // Get Calendar
  static const String PA_GET_CILT = "ROUNDS_PA_GET_CILT"; // Get CILT
  static const String PA_GET_CILT_PLAN =
      "ROUNDS_PA_GET_CILT_PLAN"; // Get CILT Plan
  static const String PA_GET_CILT_SCHEDULE =
      "ROUNDS_PA_GET_CILT_SCHEDULE"; // Get CILT Schedule
  static const String PA_GET_CILT_SCHED_LINES =
      "ROUNDS_PA_GET_CILT_SCHED_LINES"; // Get CILT Schedule Lines
  static const String PA_GET_COUNTRY = "ROUNDS_PA_GET_COUNTRY"; // Get Country
  static const String PA_GET_CURRENT_USER =
      "ROUNDS_PA_GET_CURRENT_USER"; // Get Current User details
  static const String PA_GET_DASHBOARD =
      "ROUNDS_PA_GET_DASHBOARD"; // Get Dashboard
  static const String PA_GET_DEVICE_CONTEXT =
      "ROUNDS_PA_GET_DEVICE_CONTEXT"; // Get Device context
  static const String PA_GET_DOCUMENT =
      "ROUNDS_PA_GET_DOCUMENT"; // Get Document
  static const String PA_GET_FAILURE_MODE =
      "ROUNDS_PA_GET_FAILURE_MODE"; // Get Failure Mode
  static const String PA_GET_FAULT = "ROUNDS_PA_GET_FAULT"; // Get Fault
  static const String PA_GET_FAULT_TYPE =
      "ROUNDS_PA_GET_FAULT_TYPE"; // Get Fault Type
  static const String PA_GET_FOLDER = "ROUNDS_PA_GET_FOLDER"; // Get Folder
  static const String PA_GET_FOLDER_STRUCTURE =
      "ROUNDS_PA_GET_FOLDER_STRUCTURE"; // Get folder structure
  static const String PA_GET_HOLIDAY = "ROUNDS_PA_GET_HOLIDAY"; // Get Holiday
  static const String PA_GET_INSPECTION =
      "ROUNDS_PA_GET_INSPECTION"; // Get Inspection
  static const String PA_GET_INSPECTION_PLAN =
      "ROUNDS_PA_GET_INSPECTION_PLAN"; // Get Inspection Plan
  static const String PA_GET_INSPECTION_PLAN_TYPE =
      "ROUNDS_PA_GET_INSPECTION_PLAN_TYPE"; // Get Inspection Plan Type
  static const String PA_GET_INSPECTION_SCHEDULE =
      "ROUNDS_PA_GET_INSPECTION_SCHEDULE"; // Get Inspection Schedule
  static const String PA_GET_INSP_SCHED_LINES =
      "ROUNDS_PA_GET_INSP_SCHED_LINES"; // Get Inspection Schedule LInes
  static const String PA_GET_JOB = "ROUNDS_PA_GET_JOB"; // Get Job
  static const String PA_GET_JOB_TYPE =
      "ROUNDS_PA_GET_JOB_TYPE"; // Get Job Type
  static const String PA_GET_KPI = "ROUNDS_PA_GET_KPI"; // Get KPI
  static const String PA_GET_LOCATION =
      "ROUNDS_PA_GET_LOCATION"; // Get Location
  static const String PA_GET_LOC_CATEGORY =
      "ROUNDS_PA_GET_LOC_CATEGORY"; // Get Location Category
  static const String PA_GET_PICKLIST =
      "ROUNDS_PA_GET_PICKLIST"; // Get Picklist
  static const String PA_GET_PICKLIST_CODE =
      "ROUNDS_PA_GET_PICKLIST_CODE"; // Get Picklist Code
  static const String PA_GET_PLANT = "ROUNDS_PA_GET_PLANT"; // Get Plant
  static const String PA_GET_PLANT_SECTION =
      "ROUNDS_PA_GET_PLANT_SECTION"; // Get Plant Section
  static const String PA_GET_PLANT_STRUCTURE =
      "ROUNDS_PA_GET_PLANT_STRUCTURE"; // Get Plant Structure
  static const String PA_GET_PRIORITY =
      "ROUNDS_PA_GET_PRIORITY"; // Get Priority
  static const String PA_GET_ROLE = "ROUNDS_PA_GET_ROLE"; // Get Role
  static const String PA_GET_SHIFT = "ROUNDS_PA_GET_SHIFT"; // Get Shift
  static const String PA_GET_SKIP_REASON =
      "ROUNDS_PA_GET_SKIP_REASON"; // Get Skip Reason
  static const String PA_GET_STATE = "ROUNDS_PA_GET_STATE"; // Get State
  static const String PA_GET_SYS_COND =
      "ROUNDS_PA_GET_SYS_COND"; // Get System Condition
  static const String PA_GET_UNIT = "ROUNDS_PA_GET_UNIT"; // Get Unit
  static const String PA_GET_USER = "ROUNDS_PA_GET_USER"; // Get User
  static const String PA_GET_USER_GROUP =
      "ROUNDS_PA_GET_USER_GROUP"; // Get User Group
  static const String PA_GET_WEB_CONTEXT =
      "ROUNDS_PA_GET_WEB_CONTEXT"; // Get Web context
  static const String PA_MODIFY_CILT_EXEC =
      "ROUNDS_PA_MODIFY_CILT_EXEC"; // Modify CILT Execution
  static const String PA_MODIFY_CILT_PLAN =
      "ROUNDS_PA_MODIFY_CILT_PLAN"; // Modify CILT Plan
  static const String PA_MODIFY_CILT_SCHED_LINES =
      "ROUNDS_PA_MODIFY_CILT_SCHED_LINES"; // Modify CILT Schedule Lines
  static const String PA_MODIFY_FAULT =
      "ROUNDS_PA_MODIFY_FAULT"; // Modify Fault
  static const String PA_MODIFY_INSPECTION_PLAN =
      "ROUNDS_PA_MODIFY_INSPECTION_PLAN"; // Modify Inspection Plan
  static const String PA_MODIFY_INSP_EXEC =
      "ROUNDS_PA_MODIFY_INSP_EXEC"; // Update Inspection
  static const String PA_MODIFY_INSP_SCHED_LINES =
      "ROUNDS_PA_MODIFY_INSP_SCHED_LINES"; // Modify Inspection Schedule Lines
  static const String PA_MODIFY_JOB = "ROUNDS_PA_MODIFY_JOB"; // Modify Job
  static const String PA_MODIFY_USER = "ROUNDS_PA_MODIFY_USER"; // Modify User
  static const String PA_PROVISION_APP =
      "ROUNDS_PA_PROVISION_APP"; // Provision App
  static const String PA_SAVE_CILT_PLAN_DRAFT =
      "ROUNDS_PA_SAVE_CILT_PLAN_DRAFT"; // Save Draft of CILT Plan
  static const String PA_SAVE_INSPECTION_PLAN_DRAFT =
      "ROUNDS_PA_SAVE_INSPECTION_PLAN_DRAFT"; // Save Draft of Inspection Plan
  static const String PA_SEND_OTP = "ROUNDS_PA_SEND_OTP"; // Send OTP
  static const String PA_SET_DEVICE_CONTEXT =
      "ROUNDS_PA_SET_DEVICE_CONTEXT"; // Set Device context
  static const String PA_SET_WEB_CONTEXT =
      "ROUNDS_PA_SET_WEB_CONTEXT"; // Set Web context
  static const String PA_UPDATE_ABC_INDICATOR =
      "ROUNDS_PA_UPDATE_ABC_INDICATOR"; // Update ABC Indicator
  static const String PA_UPDATE_APP_SETTING =
      "ROUNDS_PA_UPDATE_APP_SETTING"; // Update App Setting
  static const String PA_UPDATE_ASSET =
      "ROUNDS_PA_UPDATE_ASSET"; // Update Asset
  static const String PA_UPDATE_ASSET_CATEGORY =
      "ROUNDS_PA_UPDATE_ASSET_CATEGORY"; // Update Asset Category
  static const String PA_UPDATE_CALENDAR =
      "ROUNDS_PA_UPDATE_CALENDAR"; // Update Calendar
  static const String PA_UPDATE_CILT_EXEC =
      "ROUNDS_PA_UPDATE_CILT_EXEC"; // Update CILT Exec
  static const String PA_UPDATE_CILT_SCHEDULE =
      "ROUNDS_PA_UPDATE_CILT_SCHEDULE"; // Update CILT Schedule
  static const String PA_UPDATE_DOCUMENT =
      "ROUNDS_PA_UPDATE_DOCUMENT"; // Update Document
  static const String PA_UPDATE_FAILURE_MODE =
      "ROUNDS_PA_UPDATE_FAILURE_MODE"; // Update Failure Mode
  static const String PA_UPDATE_FAULT_TYPE =
      "ROUNDS_PA_UPDATE_FAULT_TYPE"; // Update Fault Type
  static const String PA_UPDATE_FOLDER =
      "ROUNDS_PA_UPDATE_FOLDER"; // Update Folder
  static const String PA_UPDATE_HOLIDAY =
      "ROUNDS_PA_UPDATE_HOLIDAY"; // Update Holiday
  static const String PA_UPDATE_INSPECTION_PLAN_TYPE =
      "ROUNDS_PA_UPDATE_INSPECTION_PLAN_TYPE"; // Update Inspection Plan Type
  static const String PA_UPDATE_INSPECTION_SCHEDULE =
      "ROUNDS_PA_UPDATE_INSPECTION_SCHEDULE"; // Update Inspection Schedule
  static const String PA_UPDATE_JOB_TYPE =
      "ROUNDS_PA_UPDATE_JOB_TYPE"; // Update Job Type
  static const String PA_UPDATE_KPI = "ROUNDS_PA_UPDATE_KPI"; // Update KPI
  static const String PA_UPDATE_LOCATION =
      "ROUNDS_PA_UPDATE_LOCATION"; // Update Location
  static const String PA_UPDATE_LOC_CATEGORY =
      "ROUNDS_PA_UPDATE_LOC_CATEGORY"; // Update Location Category
  static const String PA_UPDATE_PICKLIST =
      "ROUNDS_PA_UPDATE_PICKLIST"; // Update Picklist
  static const String PA_UPDATE_PICKLIST_CODE =
      "ROUNDS_PA_UPDATE_PICKLIST_CODE"; // Update Picklist Code
  static const String PA_UPDATE_PLANT =
      "ROUNDS_PA_UPDATE_PLANT"; // Update Plant
  static const String PA_UPDATE_PLANT_SECTION =
      "ROUNDS_PA_UPDATE_PLANT_SECTION"; // Update Plant Section
  static const String PA_UPDATE_PRIORITY =
      "ROUNDS_PA_UPDATE_PRIORITY"; // Update Priority
  static const String PA_UPDATE_ROLE = "ROUNDS_PA_UPDATE_ROLE"; // Update Role
  static const String PA_UPDATE_SHIFT =
      "ROUNDS_PA_UPDATE_SHIFT"; // Update Shift
  static const String PA_UPDATE_SKIP_REASON =
      "ROUNDS_PA_UPDATE_SKIP_REASON"; // Update Skip Reason
  static const String PA_UPDATE_SYS_COND =
      "ROUNDS_PA_UPDATE_SYS_COND"; // Update System Condition
  static const String PA_UPDATE_UNIT = "ROUNDS_PA_UPDATE_UNIT"; // Update Unit
  static const String PA_UPDATE_USER = "ROUNDS_PA_UPDATE_USER"; // Update User
  static const String PA_UPDATE_USER_GROUP =
      "ROUNDS_PA_UPDATE_USER_GROUP"; // Update User Group

  static const APP_CONFIG_PATH = "assets/json/app_config.json";
  static const WEB_APP_CONFIG_PATH = "web/assets/config.json";

  static const String LOG_OUT = "LOG_OUT";
  static const String INITIALIZATION = "INITIALIZATION";
  static const String SETTINGS = "SETTINGS";
  static const String ROUNDS_PA_ADD_CILT = "ROUNDS_PA_ADD_CILT";

  //  Preferences
  static const String APPLICATION_VERSION = "APPLICATION_VERSION";
  static final String FRAMEWORK_VERSION = "FRAMEWORK_VERSION";
  static final String USER_ID = "USER_ID";

  static const String DATE_IN_DEVICE_FORMAT = "d MMM yyyy";
  static final String TIME_IN_DEVICE_FORMAT = "h:mm aa";

  static const String DATE_IN_SERVER_FORMAT = "yyyy-MM-dd";
  static const String TIME_IN_SERVER_FORMAT = "HH:mm:ss";

  static final String ACTION_ASSIGN = "ASSIGN";
  static final String ACTION_ACCEPT = "ACCEPT";
  static final String ACTION_REJECT = "REJECT";
  static final String ACTION_CLOSE = "CLOSE";
  static final String ACTION_CANCEL = "CANCEL";

  static final String FAULT_STATE_OSNO =
      "CREATED"; //	Outstanding/New Notification
  // static final String FAULT_STATE_INPR = "IN_PROGRESS"; //	InProgress
  static final String FAULT_STATE_ORAS = "ASSIGNED"; //	Order Assigned
  static final String FAULT_STATE_NOCO = "COMPLETED"; //	Completed
  // static final String FAULT_STATE_CNCL = "CANCELLED"; //	Cancelled

  static final String JOB_STATE_OSNO =
      "CREATED"; //	Outstanding/New Notification
  static final String JOB_STATE_ORAS = "ASSIGNED";
  static final String JOB_STATE_ACPT = "ACCEPTED";
  static final String JOB_STATE_RJCT = "REJECTED";
  // static final String JOB_STATE_INPR = "IN_PROGRESS"; //	Job Assigned
  static final String JOB_STATE_NOCO = "COMPLETED"; //	Completed
  // static final String JOB_STATE_CNCL = "CANCELLED"; //	Cancelled
  static const NotConnected = "Not Connected";
}
