import 'package:flutter/material.dart';
import 'package:rounds/widgets/overlay_toast.dart';

class NotificationToastManager {
  static final List<OverlayEntry> _activeToasts = [];

  static void showRefreshToast(BuildContext context,
      {required Function() onRefresh}) {
    final overlay = Overlay.of(context);
    late OverlayEntry entry;

    entry = OverlayEntry(
      builder: (context) => Positioned(
          bottom: 40,
          right: 20,
          child: OverlayToast(
              entry: entry,
              onRefresh: () {
                onRefresh.call();
                entry.remove();
              },
              onCancel: () {
                entry.remove();
              })),
    );

    overlay.insert(entry);
    _activeToasts.add(entry);

    Future.delayed(const Duration(seconds: 1), () {
      if (entry.mounted) {
        entry.remove();
        _activeToasts.remove(entry);
      }
    });
  }

  static void clearAllToasts() {
    for (final toast in List.of(_activeToasts)) {
      if (toast.mounted) toast.remove();
    }
    _activeToasts.clear();
  }
}
