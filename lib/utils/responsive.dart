import 'package:flutter/material.dart';

class ResponsiveWidget extends StatelessWidget {

  final Widget webView;

  final Widget mobileView;

  static const double breakpointMobile = 600;
  static const double breakpointTab = 800;

  const ResponsiveWidget(
      {Key? key, required this.webView, required this.mobileView})
      : super(key: key);

  static bool isMobileView(BuildContext context) {
    return MediaQuery.of(context).size.width < breakpointMobile;
  }

  static bool isWebView(BuildContext context) {
    return MediaQuery.of(context).size.width >= breakpointMobile;
  }

  static bool isLargeScreen(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    return width >= breakpointTab;
  }

  static bool isMediumScreen(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    return width < breakpointTab && width >= breakpointMobile;
  }

  static bool isMobileScreen(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    return width < breakpointMobile;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (isMobileScreen(context)) {
          return mobileView;
        } else {
          return webView;
        }
      },
    );
  }
}
