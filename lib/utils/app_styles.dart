import 'package:flutter/material.dart';
import 'package:hexcolor/hexcolor.dart';

class AppStyles {
  static const double gapSize = 30;
  static const tileGapHeight = SizedBox(height: gapSize);
  static const tileGapWidth = SizedBox(width: gapSize);
  static const _parent = TextStyle(fontSize: 20, fontWeight: FontWeight.w700);
  static final headLine1 = _parent.copyWith(fontSize: 21);
  static final headLine20_700 = _parent.copyWith(fontSize: 20);
  static final headLine3 = _parent.copyWith(fontWeight: FontWeight.w600);
  static final headLine13 = _parent.copyWith(fontSize: 13);
  static final headLine5 = _parent.copyWith(fontWeight: FontWeight.w500);
  static final headLine6 = _parent.copyWith(fontSize: 18);
  static final headLine16 = _parent.copyWith(fontSize: 16);

  static final headLine13_600 =
      _parent.copyWith(fontSize: 13, fontWeight: FontWeight.w600);
  static final headLine15_600 =
      _parent.copyWith(fontWeight: FontWeight.w600, fontSize: 15);
  static final buttonTitle16_600 = _parent.copyWith(
      fontSize: 16, fontWeight: FontWeight.w600, color: Colors.white);
  static final textStyle_14_700w = _parent.copyWith(fontSize: 14);
  static final textStyle_14_500w =
      _parent.copyWith(fontSize: 14, fontWeight: FontWeight.w500);
  static final textStyle_14_400w =
      _parent.copyWith(fontSize: 14, fontWeight: FontWeight.w400);
  static final textStyle_14_600w =
      _parent.copyWith(fontSize: 14, fontWeight: FontWeight.w600);
  static final textStyle_16_600w =
      _parent.copyWith(fontSize: 14, fontWeight: FontWeight.w600);
  static final textStyle_16_bold =
      _parent.copyWith(fontSize: 16, fontWeight: FontWeight.bold);
  static final textStyle_23_700 = _parent.copyWith(fontSize: 20);
  static const labelTextStyle =
      TextStyle(fontWeight: FontWeight.w600, fontSize: 14);
  static const labelTextBold =
      TextStyle(fontWeight: FontWeight.bold, color: Colors.black);
  static const labelValueText = TextStyle(
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      fontSize: 15,
      overflow: TextOverflow.ellipsis);
  static const labelText = TextStyle(fontSize: 16, fontWeight: FontWeight.w500);
  static final expansionTileText = _parent
      .copyWith(fontSize: 20)
      .copyWith(color: Colors.blue, fontWeight: FontWeight.w600);
  static final batchText =
      _parent.copyWith(fontSize: 20).copyWith(fontWeight: FontWeight.w600);

  static const disabledTileColor = Color.fromRGBO(211, 211, 211, 1);
  static const whiteColor = Colors.white;
  static const blackColor = Colors.white;

  static const primaryBlueColor = Color.fromARGB(255, 6, 67, 121);
  static const secondaryColor = Colors.blue;
  static final buttonBkgrdRed = Color.fromRGBO(213, 65, 53, 1);
  static final deFaultBoxDecoration = BoxDecoration(
      borderRadius: BorderRadius.circular(8),
      color: HexColor("#F6F6F7"),
      border: Border.all(color: HexColor("#E5E8EA")));
}
