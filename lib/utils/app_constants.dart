class AppConstants {
  static const String companyName = "UNVIRED";
  static const String appName = "ROUNDS";
  static const String completed = "COMPLETED";
/*  static const String created = "CREATED";*/
  static const String skipped = "SKIPPED";
  static const String modified = "M";
  static const String add = "A";
  static const String status = "Status";
  static const String search = "Search";
  static const String jobType = "Job Type";
  static const String priority = "Priority";
  static const String faultType = "Fault Type";
  static const String faults = "Faults";
  static const String addFault = "Add Fault";
  static const String notes = "Notes";
  static const String attachments = "Attachments";
  static const String info = "Info";
  static const String faultNoticedOn = "faultNoticedOn";
  static const String dueOn = "dueOn";
  static const String startDate = "startDate";
  static const String endDate = "endDate";
  static const String fault = "Fault";
  static const String ciltFault = "CiltFault";
  static const String inspFault = "InspFault";
  static const String complete = "complete";
  static const String assign = "assign";
  static const String accept = "accept";
  static const String reject = "reject";
  static const String cancel = "cancel";
  static const String review = "review";
  static const String STATE_OPEN = "OPEN";
  static const String STATE_UNASSIGNED = "UNASSIGNED";
  static const String STATE_ASSIGNED = "ASSIGNED";
  static const String STATE_REJECTED = "REJECTED";
  static const String STATE_ACCEPTED = "ACCEPTED";
  // static const String STATE_IN_PROGRESS = "IN_PROGRESS";
  static const String STATE_COMPLETED = "COMPLETED";
  // static const String STATE_CANCELLED = "CANCELLED";
  // static const String STATE_CREATED = "CREATED";
  //	CILT and Inspection task status
  static const String STATE_TASK_OPEN = "OPEN";
  static const String STATE_TASK_COMP = "COMP";

  static const String InfoMessageFailure = "FAILURE";

  static const String InfoMessageWarning = "WARNING";

  static const String InfoMessageSuccess = "SUCCESS";
}
