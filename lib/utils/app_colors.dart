import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:hexcolor/hexcolor.dart';

class AppColors {
  static Color white = HexColor("#FFFFFF");
  static Color black = HexColor("#000000");
  static Color backgroundGrey = HexColor("#e7ecef");
  static Color scaffoldBackgroundGrey = HexColor("ffffff");

  static Color yellowColor = HexColor("#FFD67E");
  static Color primaryColor = HexColor("#3F51B5");
  static Color lighterGrey = HexColor('#f3f3f3');
  static Color cardBorderGrey = HexColor('#e2e3e2');
  static Color grey = HexColor('#D3D3D3');
  static Color darkgrey = HexColor('#A9A9A9');
  static Color titleTextColor = HexColor('#343233');
  static Color subTitleTextColor = HexColor('#333132');
  static Color secondaryTextColor = HexColor('#565455');
  static Color selectedIconColor = HexColor('#343233');
  static Color lightGreenestColor = HexColor('#343233');
  static Color lightGreyestColor = HexColor('#D3D3D3');
  static Color lightPinkColor = HexColor('#90EE90');
  static Color buttonColor = HexColor('#343233');
  static Color transparent = Colors.transparent;
  static const navyblue = 0xff285FE7;
  static Color filterButtonColor = HexColor("#B9C0E4");
  static const Color faultIconOrange = Colors.orange;
  static const Color jobIconblue = Colors.lightBlueAccent;
  static Color faultColor = HexColor("#90EE90");

  static const blackTitleText = Color(0xff212121);
  static const greySubtitleText = Color(0xff7e7d7d);
  static const greenSentColor = Color(0xff2e7d32);
  static const lightGreenColor = Color(0xBB8AC459);
  static const greenColor = Colors.green;
  static const whiteColor = Color(0xffffffff);
  static const offWhiteColor = Color(0xfff2f1f6);
  static const redColor = Color(0xffff0000);
  static const redAccentColor = Color(0xFFFF5A6E);
  static const pauseAmberColor = Colors.amber;
  static const blue = Color(0xff285FE7);
  static String itemBackground = "#F6F6F7";

  static Color completedChartColor = HexColor("#218838");
  static Color unAssignedChartColor = HexColor("#B0B0B0");
  static Color assignedChartColor = HexColor("#007BFF");
  static Color acceptedChartColor = HexColor("#7ED957");
  static Color rejectedChartColor = HexColor("#DC3545");
  static Color openChartColor = HexColor("#B0B0B0");
  static Color canceledChartColor = HexColor("#DC3545");

  // Modern UI Colors for Fault Cards
  static Color modernCardBackground = HexColor("#FFFFFF");
  static Color modernCardShadow = HexColor("#000000").withOpacity(0.08);
  static Color modernListBackground = HexColor("#F8F9FA");

  // Badge Colors
  static Color highPriorityBadge = HexColor("#FF4444");
  static Color mediumPriorityBadge = HexColor("#FF8800");
  static Color lowPriorityBadge = HexColor("#00AA44");

  // Status Colors
  static Color openStatusColor = HexColor("#6C757D");
  static Color assignedStatusColor = HexColor("#007BFF");
  static Color completedStatusColor = HexColor("#28A745");

  // Text Colors for Modern UI
  static Color modernPrimaryText = HexColor("#212529");
  static Color modernSecondaryText = HexColor("#6C757D");
  static Color modernTertiaryText = HexColor("#ADB5BD");

  // Fault Type Badge Colors
  static Color mechanicalBadge = HexColor("#17A2B8");
  static Color electricalBadge = HexColor("#FFC107");
  static Color hydraulicBadge = HexColor("#DC3545");
  static Color instrumentBadge = HexColor("#6F42C1");
}
