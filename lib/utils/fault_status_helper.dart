import 'package:unvired_sdk/unvired_sdk.dart';
import '../be/FAULT_HEADER.dart';
import 'constants.dart';

/// Utility class to handle fault status-related logic and permissions
class FaultStatusHelper {
  
  /// Checks if a fault is in "Open" status (CREATED)
  /// Open faults are the only ones that allow full editing and action buttons
  static bool isFaultOpen(FAULT_HEADER faultHeader) {
    return faultHeader.status == null || 
           faultHeader.status == Constants.FAULT_STATE_OSNO; // CREATED
  }
  
  /// Checks if a fault is in "Assigned" status (job has been created)
  static bool isFaultAssigned(FAULT_HEADER faultHeader) {
    return faultHeader.status == Constants.FAULT_STATE_ORAS; // ASSIGNED
  }
  
  /// Checks if a fault is in "Completed" status
  static bool isFaultCompleted(FAULT_HEADER faultHeader) {
    return faultHeader.status == Constants.FAULT_STATE_NOCO; // COMPLETED
  }
  
  /// Checks if a fault is in read-only mode (any status other than Open)
  static bool isFaultReadOnly(FAULT_HEADER faultHeader) {
    return !isFaultOpen(faultHeader);
  }
  
  /// Checks if editing is allowed for a fault
  /// Only Open faults with proper sync status can be edited
  static bool isEditAllowed(FAULT_HEADER faultHeader) {
    // Allow editing only for Open faults with proper sync status
    if (isFaultOpen(faultHeader)) {
      // Check sync status - don't allow editing if queued, sent, or has sync errors
      return faultHeader.syncStatus == SyncStatus.none || 
             faultHeader.syncStatus == SyncStatus.error;
    }
    return false;
  }
  
  /// Checks if "Create Job" action button should be visible
  /// Only visible for Open faults that don't already have a job assigned
  static bool shouldShowCreateJobButton(FAULT_HEADER faultHeader) {
    return isFaultOpen(faultHeader) && 
           faultHeader.job_id == null &&
           (faultHeader.syncStatus == SyncStatus.none || 
            faultHeader.syncStatus == SyncStatus.error);
  }
  
  /// Checks if "Complete" action button should be visible
  /// Only visible for Open faults
  static bool shouldShowCompleteButton(FAULT_HEADER faultHeader) {
    return isFaultOpen(faultHeader) &&
           (faultHeader.syncStatus == SyncStatus.none || 
            faultHeader.syncStatus == SyncStatus.error);
  }
  
  /// Checks if "Edit" action button should be visible
  /// Same logic as isEditAllowed but separated for clarity
  static bool shouldShowEditButton(FAULT_HEADER faultHeader) {
    return isEditAllowed(faultHeader);
  }
  
  /// Checks if any action buttons should be visible
  /// Returns true if at least one action button should be shown
  static bool shouldShowActionButtons(FAULT_HEADER faultHeader) {
    return shouldShowCreateJobButton(faultHeader) || 
           shouldShowCompleteButton(faultHeader) || 
           shouldShowEditButton(faultHeader);
  }
  
  /// Gets a user-friendly status description for display
  static String getStatusDescription(FAULT_HEADER faultHeader) {
    if (isFaultOpen(faultHeader)) {
      return 'Open - Can be edited and actions can be performed';
    } else if (isFaultAssigned(faultHeader)) {
      return 'Job Assigned - Read-only mode';
    } else if (isFaultCompleted(faultHeader)) {
      return 'Completed - Read-only mode';
    } else {
      return 'Read-only mode';
    }
  }
  
  /// Gets the reason why a fault is read-only (for user messaging)
  static String getReadOnlyReason(FAULT_HEADER faultHeader) {
    if (isFaultAssigned(faultHeader)) {
      return 'This fault has a job assigned and cannot be modified.';
    } else if (isFaultCompleted(faultHeader)) {
      return 'This fault is completed and cannot be modified.';
    } else if (faultHeader.syncStatus == SyncStatus.queued) {
      return 'This fault is queued for sync and cannot be modified.';
    } else if (faultHeader.syncStatus == SyncStatus.sent) {
      return 'This fault has been sent and cannot be modified.';
    } else {
      return 'This fault is in read-only mode and cannot be modified.';
    }
  }
}
