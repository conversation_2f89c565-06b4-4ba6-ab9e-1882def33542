import 'dart:developer';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import 'dart:async';
import 'constants.dart';

class Utils {
  static const className = 'Utils';

  static Future<String> getUserId() async {
    return (await SettingsHelper().getUserName());
  }

  static bool equalsIgnoreCase(String? string1, String? string2) {
    return string1!.toLowerCase() == string2!.toLowerCase();
  }

  static String getDateInDeviceFormat(
      {int? timestamp,
      String? stringDate,
      String format = Constants.DATE_IN_DEVICE_FORMAT}) {
    late DateTime dateTime;
    if (timestamp != null) {
      dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp).toLocal();
    } else if (stringDate != null) {
      dateTime = DateTime.parse(stringDate).toLocal();
    }

    return DateFormat(format).format(dateTime).toString();
  }

  static int getTimestampFromServerDate(String date,
      {String format = Constants.DATE_IN_SERVER_FORMAT}) {
    if (isNullOrEmpty(date)) {
      return 0;
    }

    try {
      DateTime dateTime = DateTime.parse(date).toUtc();
      return dateTime.millisecondsSinceEpoch;
    } catch (e) {
      //Logger.e(e.getMessage());
    }

    return 0;
  }

  static int getTimestampFromServerDate2(String date,
      {String format = Constants.DATE_IN_SERVER_FORMAT}) {
    if (isNullOrEmpty(date)) {
      return 0;
    }

    try {
      DateTime dateTime = DateTime.parse(date).toLocal();
      return dateTime.millisecondsSinceEpoch;
    } catch (e) {
      Logger.logError('Utils', 'getTimestampFromServerDate2', e.toString());
    }

    return 0;
  }

  static bool isNullOrEmpty(String? string) {
    return string == null || string.trim().isEmpty;
  }

  static String getDateInServerFormat({int? time}) {
    DateTime date;
    if (time != null) {
      date = DateTime.fromMillisecondsSinceEpoch(time, isUtc: true);
    } else {
      date = DateTime.now().toUtc();
    }
    return DateFormat(Constants.DATE_IN_SERVER_FORMAT).format(date).toString();
  }

  static String getDateInServerFormat2({int? time}) {
    DateTime date;
    if (time != null) {
      date = DateTime.fromMillisecondsSinceEpoch(time);
    } else {
      date = DateTime.now().toLocal();
    }
    return DateFormat(Constants.DATE_IN_SERVER_FORMAT).format(date).toString();
  }

  static String getTimeInDeviceFormat(int time) {
    DateTime date =
        DateTime.fromMillisecondsSinceEpoch(time, isUtc: true).toLocal();

    String formattedTime =
        DateFormat(Constants.TIME_IN_DEVICE_FORMAT).format(date);
    Logger.logInfo(className, 'getTimeInDeviceFormat', formattedTime);

    return formattedTime;
  }

  static int getTimestampFromServerTime(
      {required final String date,
      required final String time,
      String format =
          "${Constants.DATE_IN_SERVER_FORMAT} ${Constants.TIME_IN_SERVER_FORMAT}"}) {
    if (isNullOrEmpty(date)) {
      return 0;
    }

    try {
      DateTime dateTime =
          DateFormat(format).parse('$date $time', true).toLocal();
      return dateTime.millisecondsSinceEpoch;
    } catch (e) {
      Logger.logError(className, 'getTimestampFromServerTime', e.toString());
    }

    return 0;
  }

  static int getTimestampFromServerTime2(
      {required final String date,
      required final String time,
      String format =
          "${Constants.DATE_IN_SERVER_FORMAT} ${Constants.TIME_IN_SERVER_FORMAT}"}) {
    if (isNullOrEmpty(date)) {
      return 0;
    }

    try {
      DateTime dateTime =
          DateFormat(format).parse('$date $time', true).toLocal();
      return dateTime.millisecondsSinceEpoch;
    } catch (e) {
      Logger.logError(className, 'getTimestampFromServerTime', e.toString());
    }

    return 0;
  }

  static bool isValidEndDate(
      {required String startDate, required String endDate}) {
    var dateFormat = DateFormat(Constants.DATE_IN_SERVER_FORMAT);
    try {
      return !dateFormat
          .parse(endDate)
          .toUtc()
          .isBefore(dateFormat.parse(startDate).toUtc());
    } catch (e) {
      log(e.toString());
    }

    return false;
  }

  static String getFullName({required String fName, required String lName}) {
    if (Utils.isNullOrEmpty(fName)) {
      return lName;
    }

    return ("$fName $lName").trim();
  }

  static int convertTimeStamp(
      {required final String date,
      required final String time,
      String format =
          "${Constants.DATE_IN_SERVER_FORMAT} ${Constants.TIME_IN_SERVER_FORMAT}"}) {
    if (isNullOrEmpty(date)) {
      return 0;
    }

    try {
      DateTime dateTime = DateFormat(format).parse('$date $time', true);
      return dateTime.millisecondsSinceEpoch;
    } catch (e) {
      Logger.logError(className, 'getTimestampFromServerTime', e.toString());
    }

    return 0;
  }

  static DateTime timeStampToDateTime(int millisecondsSinceEpoch) {
    return DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch);
  }

  static String getTimeInServerFormat({int? time}) {
    var dateFormat = DateFormat(Constants.TIME_IN_SERVER_FORMAT);
    DateTime date;
    if (time != null) {
      //date = DateTime(time).toUtc();
      date = DateTime.fromMillisecondsSinceEpoch(time).toUtc();
    } else {
      date = DateTime.now().toUtc();
    }
    return dateFormat.format(date);
  }

  static getString({required List<String?> obj, required String separator}) {
    String finalString = "";
    if (obj != null && separator != null) {
      for (int i = 0; i < obj.length; i++) {
        if (obj[i] != null && obj[i] is String) {
          if (!isNullOrEmpty(obj[i])) {
            if (i == 0) {
              finalString = obj[i].toString();
            } else {
              if (!isNullOrEmpty(finalString))
                finalString += (separator + obj[i].toString());
              else
                finalString = obj[i].toString();
            }
          }
        }
      }
    }
    return finalString;
  }

  static getRoundedValue({double? unRestrictedStock}) {
    return NumberFormat("#").format(unRestrictedStock);
  }

  static isPortrait({required BuildContext context}) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  static Future<String> convertUtcToLocal(
      {required String date,
      required String time,
      required String timeFormat,
      required String dateFormat}) async {
    try {
      final dateParts = date.split('-');

      final timeParts = time.split(':');

      if (dateParts.length != 3 || timeParts.length != 3) {
        throw const FormatException("Invalid date or time format");
      }

      final year = int.parse(dateParts[0]);
      final month = int.parse(dateParts[1]);
      final day = int.parse(dateParts[2]);

      final hour = timeParts[0].length == 1 ? "0${timeParts[0]}" : timeParts[0];
      final minute = int.parse(timeParts[1]);
      final second = int.parse(timeParts[2]);

      final utcDateTime =
          DateTime.utc(year, month, day, int.parse(hour), minute, second);

      final localDateTime = utcDateTime.toLocal();

      final dateFormatter = DateFormat(dateFormat);
      final timeFormatter = DateFormat(timeFormat);

      final formattedDate = dateFormatter.format(localDateTime);
      final formattedTime = timeFormatter.format(localDateTime);

      return "$formattedDate $formattedTime";
    } catch (e) {
      Logger.logError(className, 'convertUtcToLocal', e.toString());
      return "Error";
    }
  }

  static Future<bool> hasInternetConnection() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.mobile ||
          connectivityResult == ConnectivityResult.wifi || connectivityResult == ConnectivityResult.ethernet) {
        return true;
      }
    } catch (e) {
      Logger.logError("utils", 'hasInternetConnection', e.toString());
    }
    return false;
  }

  /**
   * startAT is the time
   * startON is the date 
   */
  static DateTime getTimeStamp(String startON, String startAT) {
    //startAT  e.g., "140000"
    // startON e.g., "20250323"
    final year = int.parse(startON.substring(0, 4));
    final month = int.parse(startON.substring(4, 6));
    final day = int.parse(startON.substring(6, 8));

    int parsedTime = int.parse(startAT);

    int hours = parsedTime ~/ 10000;
    int minutes = (parsedTime % 10000) ~/ 100;
    int seconds = parsedTime % 100; // Extract seconds by taking the remainder

    return DateTime(year, month, day, hours, minutes, seconds);
  }

  /// Finds either the leftmost subdomain or the base registrable domain name (without TLD)
  /// from a given URL string,
  ///
  /// It prioritizes finding the leftmost meaningful subdomain (excluding 'www').
  /// If no meaningful subdomain is found, it returns the base registrable domain name.
  ///
  /// [urlString] The URL to parse.
  /// Returns a string indicating the found leftmost subdomain or base domain name, or an error message.
  static String? findDomainFromUrl(String urlString) {
    try {
      Uri uri = Uri.parse(urlString);
      String host = uri.host;

      // Heuristic fix for malformed URLs where host contains '://'
      if (host.contains('://')) {
        int firstSchemeEnd = urlString.indexOf('://');
        if (firstSchemeEnd != -1) {
          String potentialHostPart = urlString.substring(firstSchemeEnd + 3);
          int nextSlash = potentialHostPart.indexOf('/');
          if (nextSlash != -1) {
            potentialHostPart = potentialHostPart.substring(0, nextSlash);
          }
          int nextScheme = potentialHostPart.indexOf('://');
          if (nextScheme != -1) {
            potentialHostPart = potentialHostPart.substring(0, nextScheme);
          }
          host = potentialHostPart;
          uri = Uri.parse("http://$host");
          host = uri.host;
        }
      }

      if (host.isEmpty) return null;

      // Exclude localhost and IP addresses
      final isIpAddress = RegExp(r'^\d{1,3}(\.\d{1,3}){3}$').hasMatch(host);
      if (host == 'localhost' || isIpAddress) return null;

      List<String> parts = host.split('.');
      String baseDomainName = '';
      String effectiveSubdomain = '';
      String fullSubdomain = '';

      if (parts.length >= 2) {
        baseDomainName = parts[parts.length - 2];
        fullSubdomain = parts.sublist(0, parts.length - 2).join('.');
      } else {
        baseDomainName = host;
      }

      if (fullSubdomain.isNotEmpty) {
        List<String> subdomainSegments = fullSubdomain.split('.');
        if (subdomainSegments.isNotEmpty &&
            subdomainSegments[0].toLowerCase() == 'www') {
          subdomainSegments.removeAt(0);
        }
        if (subdomainSegments.isNotEmpty) {
          effectiveSubdomain = subdomainSegments[0];
        }
      }

      // Choose prioritized output
      String result =
          effectiveSubdomain.isNotEmpty ? effectiveSubdomain : baseDomainName;

      // Exclude unwanted domain-like results
      final blocked = {'app', 'rounds'};
      if (blocked.contains(result.toLowerCase())) return null;

      return result;
    } on FormatException {
      return null;
    } catch (e) {
      return null;
    }
  }
}

String convertTimeString(int? time) {
  if (time == null) {
    return "00:00";
  }

  int hours = time ~/ 10000;
  int minutes = (time % 10000) ~/ 100;
  int seconds = time % 100;

  return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
}

DateTime convertToDateTime(String dateString) {
  if (dateString.length == 8) {
    return DateTime.parse(
        '${dateString.substring(0, 4)}-${dateString.substring(4, 6)}-${dateString.substring(6, 8)}');
  } else {
    Logger.logError("utils", 'convertToDateTime',
        "Invalid date format. Expected yyyyMMdd.");

    return DateTime.now();
  }
}

String formatDate(DateTime date) {
  // Using DateFormat to return a formatted date like "1 March 2025"
  return DateFormat('d MMMM yyyy').format(date);
}

enum ScreenType { mobile, tablet, desktop }

Widget flightShuttleBuilder(
  BuildContext flightContext,
  Animation<double> animation,
  HeroFlightDirection flightDirection,
  BuildContext fromHeroContext,
  BuildContext toHeroContext,
) {
  return DefaultTextStyle(
    style: DefaultTextStyle.of(toHeroContext).style,
    child: toHeroContext.widget,
  );
}

const types = {
  ".3gp": "video/3gpp",
  ".torrent": "application/x-bittorrent",
  ".kml": "application/vnd.google-earth.kml+xml",
  ".gpx": "application/gpx+xml",
  ".csv": "application/vnd.ms-excel",
  ".apk": "application/vnd.android.package-archive",
  ".asf": "video/x-ms-asf",
  ".avi": "video/x-msvideo",
  ".bin": "application/octet-stream",
  ".bmp": "image/bmp",
  ".c": "text/plain",
  ".class": "application/octet-stream",
  ".conf": "text/plain",
  ".cpp": "text/plain",
  ".doc": "application/msword",
  ".docx":
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ".xls": "application/vnd.ms-excel",
  ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ".exe": "application/octet-stream",
  ".gif": "image/gif",
  ".gtar": "application/x-gtar",
  ".gz": "application/x-gzip",
  ".h": "text/plain",
  ".htm": "text/html",
  ".html": "text/html",
  ".jar": "application/java-archive",
  ".java": "text/plain",
  ".jpeg": "image/jpeg",
  ".jpg": "image/jpeg",
  ".js": "application/x-javascript",
  ".log": "text/plain",
  ".m3u": "audio/x-mpegurl",
  ".m4a": "audio/mp4a-latm",
  ".m4b": "audio/mp4a-latm",
  ".m4p": "audio/mp4a-latm",
  ".m4u": "video/vnd.mpegurl",
  ".m4v": "video/x-m4v",
  ".mov": "video/quicktime",
  ".mp2": "audio/x-mpeg",
  ".mp3": "audio/x-mpeg",
  ".mp4": "video/mp4",
  ".mpc": "application/vnd.mpohun.certificate",
  ".mpe": "video/mpeg",
  ".mpeg": "video/mpeg",
  ".mpg": "video/mpeg",
  ".mpg4": "video/mp4",
  ".mpga": "audio/mpeg",
  ".msg": "application/vnd.ms-outlook",
  ".ogg": "audio/ogg",
  ".pdf": "application/pdf",
  ".png": "image/png",
  ".pps": "application/vnd.ms-powerpoint",
  ".ppt": "application/vnd.ms-powerpoint",
  ".pptx":
      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  ".prop": "text/plain",
  ".rc": "text/plain",
  ".rmvb": "audio/x-pn-realaudio",
  ".rtf": "application/rtf",
  ".sh": "text/plain",
  ".tar": "application/x-tar",
  ".tgz": "application/x-compressed",
  ".txt": "text/plain",
  ".wav": "audio/x-wav",
  ".wma": "audio/x-ms-wma",
  ".wmv": "audio/x-ms-wmv",
  ".wps": "application/vnd.ms-works",
  ".xml": "text/plain",
  ".z": "application/x-compress",
  ".zip": "application/x-zip-compressed",
  "": "*/*",
};
