import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';

class PlatformDetails {
  static final PlatformDetails _singleton = PlatformDetails._internal();

  factory PlatformDetails() {
    return _singleton;
  }

  PlatformDetails._internal();

  static bool get isDesktop =>
      defaultTargetPlatform == TargetPlatform.macOS ||
      defaultTargetPlatform == TargetPlatform.linux ||
      defaultTargetPlatform == TargetPlatform.windows;

  static bool get isMobile =>
      defaultTargetPlatform == TargetPlatform.iOS ||
      defaultTargetPlatform == TargetPlatform.android;

  static double width(BuildContext context) =>
      MediaQuery.of(context).size.width;

  static double height(BuildContext context) =>
      MediaQuery.of(context).size.height;

  static bool isTabPortraitScreen(BuildContext context) {
    if (width(context) > 600 && width(context) <= 1000) {
      return true;
    }

    if (isMobile &&
        MediaQuery.of(context).orientation == Orientation.portrait) {
      return true;
    }

    return false;
  }

  static bool isMobileScreen(BuildContext context) {
    if (width(context) <= 600) {
      return true;
    }
    return false;
  }

  static bool isMobileOrTab(BuildContext context) {
    if (isMobileScreen(context) || isTabPortraitScreen(context)) {
      return true;
    }
    return false;
  }
}
