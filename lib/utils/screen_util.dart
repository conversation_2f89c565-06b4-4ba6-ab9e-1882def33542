import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:rounds/utils/app_colors.dart';

class ScreenUtils {
  static const double SPACE_LABEL_VALUE = 4;
  static const double SPACE_LABEL_VALUE_SECTION = 8;
  static const double SPACE_DETAIL_CARD = 8;
  static const double ELEVATION_DETAIL_CARD = 2;
  static const double GENERAL_SPACE_VALUE = 24;

  static var SHORT_TEXT_MAX_LENGTH = 40;

  static getTabNavigationWidth({required double screenWidth}) {
    double width = 180;
    if (width > (screenWidth / 6)) {
      return width;
    }
    return (screenWidth / 6);
  }

  static double getTabNavigationBodyWidth({required double screenWidth}) {
    double width = 180;
    if (width > (screenWidth / 6)) {
      return screenWidth - width;
    }
    return screenWidth - (screenWidth / 6);
  }

  static getCardTitleTextStyle() {
    return const TextStyle(
      fontSize: 18,
      fontWeight: FontWeight.bold,
    );
  }

  static getLabelTextStyle() {
    return const TextStyle(
      fontSize: 16,
      color: AppColors.greySubtitleText,
    );
  }

  static getValueTextStyle() {
    return const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w400,
    );
  }

  static getValueLinkTextStyle() {
    return const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      color: Colors.blueAccent,
    );
  }

  static getListItemMargin() {
    return const EdgeInsets.symmetric(
      vertical: 5,
      horizontal: 10,
    );
  }

  static double getStatusWidth() {
    return 8;
  }

  static TextStyle getCardSubtitleTextStyle({Color? color}) {
    return TextStyle(
      fontSize: 15,
      color: color != null ? color : AppColors.greySubtitleText,
    );
  }

  static getEmptySpaceText() {
    return "          ";
  }
}
