// import 'package:ai_ml_support/providers/chat_provider.dart';
// import 'package:ai_ml_support/providers/voice_assistance_provider.dart';
// import 'package:ai_ml_support/screens/ai_ml_support_package.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart' as legacy_provider;
import 'package:rounds/helpers/db_helper.dart';
import 'package:rounds/helpers/pushNotification_helper.dart';
import 'package:rounds/l10n/l10n.dart';
import 'package:rounds/pages/login/login.dart';
import 'package:rounds/providers/fab_visibility_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unvired_settings/screens/log_viewer/provider/log_provider.dart';
import 'firebase_options.dart';

Future _firebaseBackgroundMessage(RemoteMessage message) async {
  if (message.notification != null) {}
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // await initializeAiMlSupport();
  await FabVisibilityProvider.initializeSharedPrefs();

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  if (kIsWeb) {
    await DbHelper.openIndexDb();
  }

  if (kIsWeb) {
    PushNotifications.init();
  }

  FirebaseMessaging.onBackgroundMessage(_firebaseBackgroundMessage);

  FlutterError.onError = (FlutterErrorDetails details) {
    FlutterError.presentError(details);
    debugPrint("=======================================");
    debugPrintStack(
        label: details.exception.toString(), stackTrace: details.stack);
  };

  final prefs = await SharedPreferences.getInstance();
  String? lastScreen = prefs.getString("last_screen");
  runApp(
    ProviderScope(
      child: AppWithProviders(initialRoute: lastScreen),
    ),
  );
}

class AppWithProviders extends StatelessWidget {
  final String? initialRoute;
  const AppWithProviders({super.key, this.initialRoute});

  @override
  Widget build(BuildContext context) {
    return legacy_provider.MultiProvider(
      providers: [
        // legacy_provider.ChangeNotifierProvider(
        //   create: (_) => ChatProvider(),
        // ),
        // legacy_provider.ChangeNotifierProvider(
        //   create: (_) => VoiceAssistanceProvider(),
        // ),
        legacy_provider.ChangeNotifierProvider(
          create: (_) => LoggerProvider(),
        ),
      ],
      child: MyApp(
        initialRoute: initialRoute,
      ),
    );
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key, this.initialRoute});
  final String? initialRoute;

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ],
      supportedLocales: L10n.all,
      title: 'Rounds',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: false,
        fontFamily: GoogleFonts.inter().fontFamily,
      ),
      home: getInitialScreen(),
    );
  }

  Widget getInitialScreen() {
    return const LoginPage();
  }
}
