import 'dart:async';
import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:idb_shim/idb.dart';
import 'package:rounds/helpers/db_helper.dart';
import 'package:rounds/helpers/pa_helper.dart';

import 'package:rounds/helpers/web_message_background_listener_stub.dart'
    if (dart.library.html) 'package:rounds/helpers/web_message_background_listener.dart';

class PushNotifications {
  static final _firebaseMessaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin
      _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  static final StreamController<void> _notificationProcessedController =
      StreamController<void>.broadcast();

  static Stream<void> get onNotificationProcessed =>
      _notificationProcessedController.stream;

  static late Database _db;
  static bool _isProcessing = false;

  static Future init() async {
    try {
      if (kIsWeb) {
        _db = await DbHelper.openIndexDb();
      }
      // Request permission
      NotificationSettings settings =
          await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: true,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      debugPrint('User granted permission: ${settings.authorizationStatus}');

      // Initialize foreground handler
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        debugPrint('Got a message whilst in the foreground!');
        debugPrint('Message data: ${message.toMap()}');

        if (message.notification != null) {
          debugPrint('Message notification details: ');
          debugPrint('Title: ${message.notification?.title}');
          debugPrint('Body: ${message.notification?.body}');
          debugPrint('Data: ${message.data}');

          var notificationContext = _getNotificationContext(message.data);

          if (notificationContext != null) {
            _addNotificationToQueue(
                notificationContext, message.messageId ?? "");
            processQueue();
          } else {}
        }
      });
      if (kIsWeb) {
        PushWebListener.init();
      }

      if (kIsWeb) {
        Timer.periodic(const Duration(minutes: 1), (timer) async {
          try {
            final txn = _db.transaction('notifications', idbModeReadOnly);
            final store = txn.objectStore('notifications');
            final allNotifications = await store.getAll();
            await txn.completed;
            if (allNotifications.isNotEmpty) {
              processQueue();
            }
          } catch (e) {
            debugPrint('Error during periodic notification check: $e');
          }
        });
      } else {}
    } catch (e) {
      debugPrint('Error initializing push notifications: $e');
    }
  }

  static Map<String, dynamic>? _getNotificationContext(
      Map<String, dynamic> message) {
    try {
      // Check if "data" exists in message
      if (!message.containsKey("data")) return null;

      var dataRaw = message["data"];
      var notificationData = dataRaw is String ? jsonDecode(dataRaw) : dataRaw;

      // Check if "NotificationContext" exists
      var contextRaw = notificationData["NotificationContext"];
      if (contextRaw == null ||
          (contextRaw is String && contextRaw.trim().isEmpty)) {
        return null;
      }

      // Parse NotificationContext
      return jsonDecode(contextRaw);
    } catch (e) {
      // Parsing failed
      return null;
    }
  }

  static Future<void> _addNotificationToQueue(
      Map<String, dynamic> notification, String messageId) async {
    final txn = _db.transaction('notifications', idbModeReadWrite);
    final store = txn.objectStore('notifications');

    // Ensure messageId is part of the data (required by keyPath)
    final data = {
      'messageId': messageId,
      'data': notification,
    };

    await store.put(data); // Use put to overwrite if messageId already exists
    await txn.completed;
  }

  static Future<void> processQueue() async {
    if (_isProcessing) return;
    _isProcessing = true;
    var allNotifications;

    try {
      if (_db == null) {
        debugPrint('Database not initialized');
        return;
      }

      // Use a separate transaction to fetch all notifications
      final readTxn = _db.transaction('notifications', idbModeReadOnly);
      final readStore = readTxn.objectStore('notifications');
      allNotifications = await readStore.getAll();
      await readTxn.completed;

      for (final notif in allNotifications) {
        try {
          final notifMap = Map<String, dynamic>.from(notif as Map);
          final data = notifMap['data'];
          if (data == null || (data is Map && data.isEmpty)) {
            // Optionally: remove this invalid notification from the queue
            final messageId = notif['messageId'];
            if (messageId != null) {
              final deleteTxn =
                  _db.transaction('notifications', idbModeReadWrite);
              final deleteStore = deleteTxn.objectStore('notifications');
              await deleteStore.delete(messageId);
              await deleteTxn.completed;
            }
            continue; // Skip processing
          }

          await PAHelper.downloadNotificationEntity(
              Map<String, dynamic>.from(data as Map));

          final messageId = notif['messageId'];
          if (messageId != null) {
            // ✅ Open a separate short transaction for deletion
            final deleteTxn =
                _db.transaction('notifications', idbModeReadWrite);
            final deleteStore = deleteTxn.objectStore('notifications');
            await deleteStore.delete(messageId);
            await deleteTxn.completed;
          }
        } catch (e) {
          debugPrint("Failed to process notification: $e");
          continue;
        }
      }
    } catch (e) {
      debugPrint("Error during queue processing: $e");
    } finally {
      if (allNotifications.isEmpty) {
        _isProcessing = false;
        _notificationProcessedController.add(null); // 🔔 Emit event
      } else {
        _isProcessing = false;
        processQueue();
      }
    }
  }
}
