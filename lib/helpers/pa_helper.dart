import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:http/http.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:rounds/be/FAULT_HEADER.dart';
import 'package:rounds/be/JOB_HEADER.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/models/adhoc_cilt.dart';
import 'package:rounds/utils/constants.dart';
import 'package:rounds/utils/utils.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:uuid/uuid.dart';

import '../be/CILT_EXEC_HEADER.dart';
import '../be/INSP_EXEC_HEADER.dart';
import '../utils/app_constants.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';

class PAHelper {
  static const String className = "PAHelper";

  static Future<Result> getCustomizationInSyncMode(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_CUSTOMIZING,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getCustomizationInSyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getCustomizationInAsyncMode(
      BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_CUSTOMIZING,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getCustomizationInAsyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> getAssetInAsyncMode(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_ASSET,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getAssetInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getAssetInSyncMode(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_ASSET,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getAssetInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getLocationInAsyncMode(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_LOCATION,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getLocationInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getLocationInSyncMode(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_LOCATION,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getLocationInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getCiltPlanInAsyncMode(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_CILT_PLAN,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getCiltPlanInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getCiltPlanInSyncMode(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_CILT_PLAN,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getCiltPlanInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getCiltInAsyncMode(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_CILT,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getCiltInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getCiltInSyncMode(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_CILT,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getCiltInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getInspectionPlanInAsyncMode(
      BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_INSP_PLAN,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getInspectionPlanInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getInspectionPlanInSyncMode(
      BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_INSP_PLAN,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getInspectionPlanInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getInspectionInAsyncMode(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_INSPECTION,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getInspectionInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getInspectionInSyncMode(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_INSPECTION,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getInspectionInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getFaultInAsyncMode(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_FAULT,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getFaultInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getFaultInSyncMode(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_FAULT,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getFaultInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getJobInAsyncMode(BuildContext context) async {
    String userId = await Utils.getUserId();
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_JOB,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getFaultInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getJobInSyncMode(BuildContext context) async {
    String userId = await Utils.getUserId();
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_JOB,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getFaultInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getKpiInAsyncMode(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_KPI,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getKpiInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getKpiInSyncMode(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_KPI,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getKpiInAsyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> getInspectionScheduleInAsyncMode(
      BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_GET_CILT_SCHEDULE,
      );
      return result;
    } catch (e) {
      Logger.logError(
          className, 'getInspectionScheduleInAsyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> acceptCLITExecInSyncMode(
      BuildContext context, CILT_EXEC_HEADER header, String userid) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_ACTION_ACCEPT_CILT,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'acceptCLITInAsyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> acceptINSPExecInSyncMode(
      BuildContext context, INSP_EXEC_HEADER header, String userid) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_ACTION_ACCEPT_INSPECTION,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'acceptINSPExecInSyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> addOrModifyFaultInAsyncMode(
      BuildContext context, FAULT_HEADER header) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_MODIFY_FAULT,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'addOrModifyFaultInSyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> addOrModifyFaultInSyncMode(
      BuildContext context, FAULT_HEADER header) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_MODIFY_FAULT,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'addOrModifyFaultInSyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> addOrModifyJobInAsyncMode(
      BuildContext context, JOB_HEADER header) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_MODIFY_JOB,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'addOrModifyJobInAsyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> addOrModifyJobInSyncMode(
      BuildContext context, JOB_HEADER header) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_MODIFY_JOB,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'addOrModifyJobInAsyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result?> addDocumentInSyncMode(
      BuildContext context, List<DOCUMENT_HEADER> header) async {
    try {
      Result? result;
      for (DOCUMENT_HEADER documentHeader in header) {
        result = await (SyncEngine()
              ..isAutoSave(true)
              ..isAsynchronous(false)
              ..setInputType(InputType.standard)
              ..setRequestType(RequestType.rqst))
            .send(
                umpApplicationFunctionName: Constants.PA_ADD_DOCUMENT,
                dataObject: documentHeader.toJson());
      }

      return result;
    } catch (e) {
      Logger.logError(className, 'addDocumentInSyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result?> addDocumentInAsyncMode(
      BuildContext context, List<DOCUMENT_HEADER> header) async {
    try {
      Result? result;
      for (DOCUMENT_HEADER documentHeader in header) {
        result = await (SyncEngine()
              ..isAutoSave(true)
              ..isAsynchronous(true)
              ..setInputType(InputType.standard)
              ..setRequestType(RequestType.rqst))
            .send(
                umpApplicationFunctionName: Constants.PA_ADD_DOCUMENT,
                dataObject: documentHeader.toJson());
      }

      return result;
    } catch (e) {
      Logger.logError(className, 'addDocumentInAsyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> rejectCLITInSyncMode(
      BuildContext context, CILT_EXEC_HEADER header) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_ACTION_REJECT_CILT,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'rejectCLITInAsyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> rejectINSPInSyncMode(
      BuildContext context, INSP_EXEC_HEADER header) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_ACTION_REJECT_INSPECTION,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'rejectINSPInSyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> acceptInspectionInAsyncMode(
      BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_ACTION_ACCEPT_INSPECTION,
        // dataObject: customization.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'acceptInspectionInAsyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> rejectInspectionInAsyncMode(
      BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_ACTION_REJECT_INSPECTION,
        // dataObject: customization.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'rejectInspectionInAsyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<void> doRefresh(BuildContext context) async {
    List<Future> futures = [
      PAHelper.getInspectionInAsyncMode(context),
      PAHelper.getCiltInAsyncMode(context),
      PAHelper.getFaultInAsyncMode(context),
      PAHelper.getJobInAsyncMode(context),
    ];
    try {
      // await PAHelper.requestDataDownload();
      await Future.wait(futures);
    } catch (e) {
      Logger.logError('PAHelper', 'doRefresh', e.toString());
    } finally {}
  }

  static Future<void> requestDataDownload() async {
    await SettingsHelper().requestInitialDataDownload();
  }

  static Future<Result> setUserPreferenceLogin(
      BuildContext context, Map<String, dynamic> data) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
              umpApplicationFunctionName: Constants.PA_SET_DEVICE_CONTEXT,
              dataObject: data);
      return result;
    } catch (e) {
      Logger.logError(className, 'setUserPreferenceLogin', e.toString());
      rethrow;
    }
  }

  static Future<Result> getUserPreferenceLogin(BuildContext context) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
        umpApplicationFunctionName: Constants.PA_GET_DEVICE_CONTEXT,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'getUserPreferenceLogin', e.toString());
      rethrow;
    }
  }

  static Future<Result> assignCLITExecInAsyncMode(
      BuildContext context, CILT_EXEC_HEADER header) async {
    header.status = 'ASSIGNED';
    await AppDatabaseManager()
        .update(DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, header.toJson()));

    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_ACTION_ASSIGN_CILT,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'assignCLITExecInSyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> assignINSPExecInAsyncMode(
      BuildContext context, INSP_EXEC_HEADER header) async {
    header.status = 'ASSIGNED';
    await AppDatabaseManager()
        .update(DBInputEntity(INSP_EXEC_HEADER.TABLE_NAME, header.toJson()));

    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_ACTION_ASSIGN_INSPECTION,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'assignINSPExecInAsyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> assignCLITExecInSyncMode(
      BuildContext context, CILT_EXEC_HEADER header) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_ACTION_ASSIGN_CILT,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'assignCLITExecInSyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> assignINSPExecInSyncMode(
      BuildContext context, INSP_EXEC_HEADER header) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_ACTION_ASSIGN_INSPECTION,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'assignINSPExecInSyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> modifyCiltPlanInAsyncMode(
      BuildContext context, CILT_EXEC_HEADER header) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_MODIFY_CILT_PLAN,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'modifyCiltPlan', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> modifyCiltPlanInSyncMode(
      BuildContext context, CILT_EXEC_HEADER header) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_MODIFY_CILT_PLAN,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'modifyCiltPlan', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> modifyCiltExecInAsyncMode(
      BuildContext context, CILT_EXEC_HEADER header) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_MODIFY_CILT_EXEC,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'modifyCiltExec', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> modifyCiltExecInSyncMode(
      BuildContext context, CILT_EXEC_HEADER header) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_MODIFY_CILT_EXEC,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'modifyCiltExec', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> modifyInspExecInAsyncMode(
      BuildContext context, INSP_EXEC_HEADER header) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_MODIFY_INSP_EXEC,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'modifyInspExecInAsyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> modifyInspExecInSyncMode(
      BuildContext context, INSP_EXEC_HEADER header) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_MODIFY_INSP_EXEC,
        dataObject: header.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'modifyInsExecInSyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> createAdHocCiltinSyncMode(
    BuildContext context,
    AdhocCilt adhocCilt,
  ) async {
    try {
      var data = adhocCilt.toJson();
      data["device"] = true;
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.query))
          .send(
        umpApplicationFunctionName: Constants.ROUNDS_PA_ADD_CILT,
        dataObject: data,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'createAdHocCiltinSyncMode', e.toString());
      rethrow;
    }
  }

  static Future<Result> createAdHocInspectioninSyncMode(
    BuildContext context,
    AdhocCilt adhocCilt,
  ) async {
    try {
      var data = adhocCilt.toJson();
      data["device"] = true;
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.query))
          .send(
        umpApplicationFunctionName: Constants.PA_ADD_INSPECTION,
        dataObject: data,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'createAdHocCiltinSyncMode', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<Result> updateUser(
    BuildContext context,
    USER_HEADER user,
  ) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
        umpApplicationFunctionName: Constants.PA_MODIFY_USER,
        dataObject: user.toJson(),
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'updateUser', e.toString());
      return UIHelper.showErrorDialog(context, description: e.toString());
    }
  }

  static Future<bool> isSyncAllowed() async {
    try {
      return (await SettingsHelper().getOutboxCount()) <= 0;
    } catch (e) {
      rethrow;
    }
  }

  static Future<dynamic> downloadNotificationEntity(
      Map<String, dynamic> data) async {
    try {
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.query))
          .send(
        umpApplicationFunctionName: Constants.PA_DOWNLOAD_ENTITY,
        dataObject: data,
      );
      return result;
    } catch (e) {
      Logger.logError(className, 'downloadNotificationEntity', e.toString());
    }
  }
}
