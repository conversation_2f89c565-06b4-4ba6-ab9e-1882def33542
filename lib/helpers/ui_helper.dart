import 'dart:math';
import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:rounds/icons/app_icons.dart';
import 'package:file_picker/file_picker.dart';
import 'package:rounds/utils/app_constants.dart';
import 'package:rounds/utils/utils.dart';

import '../utils/app_colors.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:intl/intl.dart';

enum WrapType {
  id,
  description,
  none,
}

class UIHelper {
  static void showSnackBar(BuildContext context, {required String message}) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text(message),
      action: SnackBarAction(
        label: AppLocalizations.of(context)!.dismiss,
        textColor: Colors.white,
        onPressed: () => ScaffoldMessenger.of(context).hideCurrentSnackBar(),
      ),
    ));
  }

  static showEamProgressDialog(BuildContext context,
      {String? title,
      Color? progressColor,
      bool barrierDismissible = false,
      bool showCancelIcon = false}) {
    if (!kIsWeb) {
      showDialog(
        context: context,
        barrierDismissible: barrierDismissible,
        builder: (BuildContext dialogContext) {
          return AlertDialog(
            content: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (title != null) ...[
                  CircularProgressIndicator(
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(
                    width: 20,
                  ),
                  Expanded(child: Text(title)),
                ] else ...[
                  CircularProgressIndicator(
                    color: Theme.of(context).primaryColor,
                  )
                ]
              ],
            ),
            actions: [
              if (showCancelIcon) ...[
                TextButton(
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).pop();
                  },
                  child: Text(AppLocalizations.of(context)!.cancel),
                )
              ]
            ],
          );
        },
      );
    } else {
      showGeneralDialog(
        context: context,
        barrierLabel: 'Label',
        barrierDismissible: false,
        pageBuilder: (_, __, ___) => Center(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 50, sigmaY: 50),
            child: Material(
              color: Colors.transparent,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (title != null) ...[
                    EamIcon(
                            iconName: EamIcon.hourglass,
                            height: 50,
                            width: 50,
                            color: Colors.white)
                        .icon(),
                    const SizedBox(
                      height: 10,
                    ),
                    Text(title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                        )),
                  ] else ...[
                    CircularProgressIndicator(
                      color: Theme.of(context).primaryColor,
                    )
                  ]
                ],
              ),
            ),
          ),
        ),
      );
    }
  }

  static showEamDialog(BuildContext context,
      {String? title,
      String? description,
      String? positiveActionLabel,
      String? negativeActionLabel,
      Function()? onPositiveClickListener,
      Function? onNegativeClickListener,
      final bool dismissible = false,
      final VoidCallback? onOKPressed}) {
    showDialog(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return dismissible;
          },
          child: AlertDialog(
            title: Text(title!),
            content: description != null ? Text(description) : null,
            actions: [
              if (negativeActionLabel != null)
                TextButton(
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).pop();

                    if (onNegativeClickListener != null) {
                      onNegativeClickListener();
                    }
                  },
                  child: Text(
                    negativeActionLabel,
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
              if (positiveActionLabel != null)
                TextButton(
                  onPressed: () {
                    if (onPositiveClickListener != null) {
                      onPositiveClickListener();
                    }
                  },
                  child: Text(
                    positiveActionLabel,
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
              if (negativeActionLabel == null && positiveActionLabel == null)
                TextButton(
                  onPressed: onOKPressed,
                  child: Text(
                    AppLocalizations.of(context)!.ok,
                    style: TextStyle(
                      color: AppColors.primaryColor,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  static String formatIdAndDescription(
    String? id,
    String? description, {
    WrapType wrap = WrapType.description, // default: wrap description
  }) {
    final trimmedId = id?.trim();
    final trimmedDesc = description?.trim();

    final hasId = trimmedId != null && trimmedId.isNotEmpty;
    final hasDesc = trimmedDesc != null && trimmedDesc.isNotEmpty;

    if (hasId && hasDesc) {
      switch (wrap) {
        case WrapType.id:
          return "($trimmedId) - $trimmedDesc";
        case WrapType.description:
          return "$trimmedId - ($trimmedDesc)";
        case WrapType.none:
          return "$trimmedId - $trimmedDesc";
      }
    } else if (hasId) {
      return wrap == WrapType.id ? "($trimmedId)" : trimmedId;
    } else if (hasDesc) {
      return wrap == WrapType.description ? "($trimmedDesc)" : trimmedDesc;
    } else {
      return "";
    }
  }

  static showErrorDialog(BuildContext context,
      {String? title,
      required String description,
      final bool dismissible = false,
      VoidCallback? onPressed}) {
    showDialog(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return dismissible;
          },
          child: AlertDialog(
            title: Text(title ?? AppLocalizations.of(context)!.alertString),
            content: Text(description),
            actions: [
              TextButton(
                onPressed: onPressed ??
                    () {
                      Navigator.of(context).pop();
                    },
                child: Text(
                  AppLocalizations.of(context)!.ok,
                  style: TextStyle(
                    color: AppColors.primaryColor,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static void showErrorDialogWithStackStrace(
    BuildContext context, {
    String? title,
    required String description,
    String? stackTrace,
    bool dismissible = true,
  }) {
    showDialog(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async => dismissible,
          child: AlertDialog(
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            title: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.red),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title ?? 'Error',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(description),
                  if (stackTrace != null && stackTrace.isNotEmpty) ...[
                    const SizedBox(height: 12),
                    const Text(
                      'Stack Trace:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      constraints: const BoxConstraints(maxHeight: 200),
                      child: SingleChildScrollView(
                        child: Text(
                          stackTrace,
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text(
                  'OK',
                  style: TextStyle(color: Colors.blue),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static showResultInfoDialog(BuildContext context,
      {String? title,
      required String description,
      final bool dismissible = false,
      final VoidCallback? onPressed}) {
    showDialog(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return dismissible;
          },
          child: AlertDialog(
            title: Text(title ?? AppLocalizations.of(context)!.info),
            content: Text(description),
            actions: [
              TextButton(
                onPressed: onPressed,
                child: Text(
                  AppLocalizations.of(context)!.ok,
                  style: TextStyle(
                    color: AppColors.primaryColor,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static showSystemErrorDialog(
    BuildContext context, {
    String? title,
    required String description,
    final bool dismissible = false,
  }) {
    showDialog(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return dismissible;
          },
          child: AlertDialog(
            title: Text(title ?? AppLocalizations.of(context)!.alertString),
            content: Text(description),
            actions: [],
          ),
        );
      },
    );
  }

  static showConfirmationDialog(
    BuildContext context, {
    String? title,
    required String description,
    final bool dismissible = false,
    required String positiveButtonString,
    VoidCallback? positiveButtonOnTap,
    VoidCallback? cancel,
  }) {
    if (!context.mounted) return; // Ensure the context is still active

    showDialog<bool>(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return dismissible;
          },
          child: AlertDialog(
            title: Text(AppLocalizations.of(context)!.alertString),
            content: Text(description),
            actions: [
              TextButton(
                onPressed: positiveButtonOnTap,
                child: Text(
                  positiveButtonString,
                  style: TextStyle(
                    color: AppColors.primaryColor,
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  AppLocalizations.of(context)!.cancel,
                  style: TextStyle(
                    color: AppColors.primaryColor,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static showConfirmationDialogWithYesOrNo(
    BuildContext context, {
    String? title,
    required String description,
    final bool dismissible = false,
    VoidCallback? yes,
    VoidCallback? no,
  }) {
    showDialog<bool>(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return dismissible;
          },
          child: AlertDialog(
            title: Text(AppLocalizations.of(context)!.alertString),
            content: Text(description),
            actions: [
              TextButton(
                onPressed: yes,
                child: Text(
                  AppLocalizations.of(context)!.yes,
                  style: TextStyle(
                    color: AppColors.primaryColor,
                  ),
                ),
              ),
              TextButton(
                onPressed: no,
                child: Text(
                  AppLocalizations.of(context)!.no,
                  style: TextStyle(
                    color: AppColors.primaryColor,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static showAttachmentsDialog(
    BuildContext context, {
    String? title,
    final bool dismissible = false,
    VoidCallback? reject,
    VoidCallback? cancel,
  }) {
    List<String> addedAttachments = [];

    showDialog(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return dismissible;
          },
          child: StatefulBuilder(
            builder: (context, setState) {
              return AlertDialog(
                contentPadding: EdgeInsets.all(5),
                content: SingleChildScrollView(
                  child: Container(
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Attachments',
                                style: TextStyle(
                                    fontWeight: FontWeight.bold, fontSize: 14),
                              ),
                              GestureDetector(
                                onTap: () async {
                                  FilePickerResult? result =
                                      await FilePicker.platform.pickFiles();
                                  if (result != null) {
                                    PlatformFile file = result.files.first;
                                    setState(() {
                                      addedAttachments.add(
                                          file.name); // Add a new empty note
                                    });
                                  }
                                },
                                child: Container(
                                  width: 20,
                                  height: 20,
                                  decoration: BoxDecoration(
                                    border: Border.all(color: AppColors.black),
                                    shape: BoxShape.circle,
                                    color: AppColors.white,
                                  ),
                                  child: Center(
                                    child: Icon(
                                      Icons.add,
                                      color: AppColors.black,
                                      size: 18,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Container(
                              width: MediaQuery.of(context).size.width,
                              decoration: BoxDecoration(
                                border:
                                    Border.all(color: Colors.grey, width: 1.0),
                                borderRadius: BorderRadius.circular(5.0),
                              ),
                              child: const Padding(
                                padding: EdgeInsets.all(5),
                                child: Text('data'),
                              )),
                          _buildAddedAttachments(
                              addedAttachments, setState, context),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  static Widget _buildAddedAttachments(List<String> addedAttachments,
      void Function(void Function()) setState, BuildContext context) {
    return Column(
      children: addedAttachments.map((attachment) {
        int index = addedAttachments.indexOf(attachment);
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 5.0),
                child: Container(
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey, width: 1.0),
                      borderRadius: BorderRadius.circular(5.0),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(5),
                      child: Row(
                        children: [
                          Expanded(child: Text(attachment)),
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                addedAttachments.removeAt(
                                    index); // Remove the note from the list
                              });
                            },
                            child: const Icon(
                              Icons.delete,
                              color: AppColors.redAccentColor,
                              size: 20,
                            ),
                          )
                        ],
                      ),
                    )),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  static showNotesDialog(
    BuildContext context, {
    String? title,
    final bool dismissible = false,
    VoidCallback? save,
  }) {
    TextEditingController commentController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return dismissible;
          },
          child: StatefulBuilder(
            builder: (context, setState) {
              return AlertDialog(
                contentPadding: EdgeInsets.all(5),
                content: SingleChildScrollView(
                  child: Container(
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Add Additional comments',
                                style: TextStyle(
                                    fontWeight: FontWeight.bold, fontSize: 14),
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          TextField(
                            style: TextStyle(fontSize: 14),
                            controller: commentController,
                            onChanged: (value) {
                              setState(() {
                                commentController.text = value;
                              });
                            },
                            maxLines: null,
                            decoration: InputDecoration(
                              hintText: 'Enter Comment',
                              border: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              focusedBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              enabledBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 10),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: save,
                    child: Text(
                      AppLocalizations.of(context)!.save,
                      style: TextStyle(
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text(
                      AppLocalizations.of(context)!.cancel,
                      style: TextStyle(
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  static showInfoDialog(
    BuildContext context, {
    String? title,
    final bool dismissible = false,
  }) {
    String textFieldValue =
        'You’ve been actively working on a Flutter app with features similar to Zomato, integrating Firebase for authentication and Realtime Database. Key elements of the app involve managing authentication with email/password and email verification via Google. You are using Riverpod for state management, leveraging StateNotifierProvider and FutureProvider.family to handle various data sources, including fetching orders by user_id from Firebase. To handle restaurant data and user interactions, you want to maintain a list of restaurants, allow users to place orders specific to their email, and store the selected restaurant via Riverpod.';

    showDialog(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return dismissible;
          },
          child: StatefulBuilder(
            builder: (context, setState) {
              return AlertDialog(
                contentPadding: EdgeInsets.all(5),
                content: SingleChildScrollView(
                  child: Container(
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Note',
                            style: TextStyle(
                                fontWeight: FontWeight.bold, fontSize: 14),
                          ),
                          SizedBox(height: 10),
                          TextField(
                            readOnly: true,
                            style: TextStyle(fontSize: 14),
                            controller:
                                TextEditingController(text: textFieldValue)
                                  ..selection = TextSelection.fromPosition(
                                    TextPosition(offset: textFieldValue.length),
                                  ),
                            onChanged: (value) {
                              setState(() {
                                textFieldValue = value;
                              });
                            },
                            maxLines: null,
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              focusedBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              enabledBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.grey)),
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 10),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  static showInfoDialogWithtitleAndDescription(
    BuildContext context, {
    required String title,
    required String description,
    final bool dismissible = false,
  }) {
    String textFieldValue = "";

    showDialog(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return dismissible;
          },
          child: AlertDialog(
            contentPadding: const EdgeInsets.all(5),
            title: Text(
              title,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
            ),
            content: Text(description,
                style:
                    const TextStyle(fontWeight: FontWeight.w200, fontSize: 12)),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  AppLocalizations.of(context)!.ok,
                  style: TextStyle(
                    color: AppColors.black,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static showReasonTextFieldDialog(BuildContext context,
      {String? title,
      final bool dismissible = false,
      VoidCallback? save,
      required Widget widget}) {
    showDialog(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return dismissible;
          },
          child: StatefulBuilder(
            builder: (context, setState) {
              return AlertDialog(
                contentPadding: EdgeInsets.all(5),
                content: SingleChildScrollView(
                  child: Container(
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Select Reason',
                                style: TextStyle(
                                    fontWeight: FontWeight.bold, fontSize: 14),
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          widget
                        ],
                      ),
                    ),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: save,
                    child: Text(
                      AppLocalizations.of(context)!.save,
                      style: TextStyle(
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text(
                      AppLocalizations.of(context)!.cancel,
                      style: TextStyle(
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  static Widget _buildAddedNotes(
      List<String> addedNotes, void Function(void Function()) setState) {
    return Column(
      children: addedNotes.map((note) {
        int index = addedNotes.indexOf(note);
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 5.0),
                child: TextField(
                  controller: TextEditingController(text: note)
                    ..selection = TextSelection.fromPosition(
                      TextPosition(offset: note.length),
                    ),
                  style: const TextStyle(fontSize: 14),
                  onChanged: (value) {
                    addedNotes[index] = value; // Update the note value
                    setState(() {});
                  },
                  maxLines: null,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.only(
                        left: 10, right: 20, top: 10, bottom: 10),
                  ),
                ),
              ),
              Positioned(
                right: 5,
                top: 8,
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      addedNotes
                          .removeAt(index); // Remove the note from the list
                    });
                  },
                  child: const Icon(
                    Icons.delete,
                    color: AppColors.redAccentColor,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  static showFaultsDialog(
    BuildContext context, {
    String? title,
    final bool dismissible = false,
    VoidCallback? reject,
    VoidCallback? cancel,
  }) {
    final List<String> dropdownOptions = ['Option 1', 'Option 2', 'Option 3'];
    String selectedFaultType = 'Option 1';
    List<String> addedFaults = [];
    showDialog(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return dismissible;
          },
          child: StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              contentPadding: EdgeInsets.all(15),
              content: SingleChildScrollView(
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          AppConstants.faults,
                          style: TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 14),
                        ),
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              addedFaults.add('');
                            });
                          },
                          child: Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              border: Border.all(color: AppColors.black),
                              shape: BoxShape.circle,
                              color: AppColors.white,
                            ),
                            child: Center(
                              child: Icon(
                                Icons.add,
                                color: AppColors.black,
                                size: 18,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey, width: 1.0),
                        borderRadius: BorderRadius.circular(5.0),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildAlignedRow('Functional Location', '12377277'),
                            _buildAlignedRow('Asset', '1234567'),
                            _buildAlignedRow('Title', 'Breaking Broke down'),
                            _buildAlignedRow(
                              AppConstants.faultType,
                              '',
                              trailingWidget: DropdownButton<String>(
                                isExpanded: true,
                                value: selectedFaultType,
                                items: dropdownOptions
                                    .map((option) => DropdownMenuItem<String>(
                                          value: option,
                                          child: Text(option),
                                        ))
                                    .toList(),
                                onChanged: (newValue) {
                                  setState(() {
                                    selectedFaultType = newValue!;
                                  });
                                },
                              ),
                            ),
                            _buildAlignedRow(
                              'Description',
                              'Embrace the journey, not just the destination. Every step teaches, every moment matters. Find joy in the small things, and let gratitude guide your path.',
                            ),
                            _buildAlignedRow(
                              AppConstants.priority,
                              '',
                              trailingWidget: DropdownButton<String>(
                                isExpanded: true,
                                value: selectedFaultType,
                                items: dropdownOptions
                                    .map((option) => DropdownMenuItem<String>(
                                          value: option,
                                          child: Text(option),
                                        ))
                                    .toList(),
                                onChanged: (newValue) {
                                  setState(() {
                                    selectedFaultType = newValue!;
                                  });
                                },
                              ),
                            ),
                            _buildAlignedRow(
                              'Failure Mode',
                              '',
                              trailingWidget: DropdownButton<String>(
                                isExpanded: true,
                                value: selectedFaultType,
                                items: dropdownOptions
                                    .map((option) => DropdownMenuItem<String>(
                                          value: option,
                                          child: Text(option),
                                        ))
                                    .toList(),
                                onChanged: (newValue) {
                                  setState(() {
                                    selectedFaultType = newValue!;
                                  });
                                },
                              ),
                            ),
                            _buildAlignedRow('Plant Section', 'Section A'),
                            _buildAlignedRow('Plant', 'Default'),
                            _buildAlignedRow('Fault Noticed on', ''),
                            _buildAlignedRow('Due on', ''),
                            _buildAlignedRow(AppConstants.attachments, ''),
                          ],
                        ),
                      ),
                    ),
                    _buildAddedFaults(addedFaults, setState),
                  ],
                ),
              ),
            );
          }),
        );
      },
    );
  }

  static Widget _buildAddedFaults(
      List<String> addedFaults, void Function(void Function()) setState) {
    final List<String> dropdownOptions = ['Option 1', 'Option 2', 'Option 3'];
    String selectedFaultType = 'Option 1';
    String selectedPriority = 'High';
    bool toggleValue = false;
    return Column(
      children: addedFaults.map((note) {
        int index = addedFaults.indexOf(note);
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey, width: 1.0),
              borderRadius: BorderRadius.circular(5.0),
            ),
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildAlignedRow('Functional Location', '12377277'),
                      _buildAlignedRow('Asset', '1234567'),
                      _buildAlignedRow('Title', 'Breaking Broke down'),
                      _buildAlignedRow(
                        'Fault Type',
                        '',
                        trailingWidget: DropdownButton<String>(
                          isExpanded: true,
                          value: selectedFaultType,
                          items: dropdownOptions
                              .map((option) => DropdownMenuItem<String>(
                                    value: option,
                                    child: Text(option),
                                  ))
                              .toList(),
                          onChanged: (newValue) {
                            setState(() {
                              selectedFaultType = newValue!;
                            });
                          },
                        ),
                      ),
                      _buildAlignedRow(
                        'Description',
                        'Embrace the journey, not just the destination. Every step teaches, every moment matters. Find joy in the small things, and let gratitude guide your path.',
                      ),
                      _buildAlignedRow(
                        AppConstants.priority,
                        '',
                        trailingWidget: DropdownButton<String>(
                          isExpanded: true,
                          value: selectedFaultType,
                          items: dropdownOptions
                              .map((option) => DropdownMenuItem<String>(
                                    value: option,
                                    child: Text(option),
                                  ))
                              .toList(),
                          onChanged: (newValue) {
                            setState(() {
                              selectedFaultType = newValue!;
                            });
                          },
                        ),
                      ),
                      _buildAlignedRow(
                        'Failure Mode',
                        '',
                        trailingWidget: DropdownButton<String>(
                          isExpanded: true,
                          value: selectedFaultType,
                          items: dropdownOptions
                              .map((option) => DropdownMenuItem<String>(
                                    value: option,
                                    child: Text(option),
                                  ))
                              .toList(),
                          onChanged: (newValue) {
                            setState(() {
                              selectedFaultType = newValue!;
                            });
                          },
                        ),
                      ),
                      _buildAlignedRow('Plant Section', 'Section A'),
                      _buildAlignedRow('Plant', 'Default'),
                      _buildAlignedRow('Fault Noticed on', ''),
                      _buildAlignedRow('Due on', ''),
                      _buildAlignedRow(
                        'Convert TO SAP Notification',
                        '',
                        trailingWidget: Container(
                            alignment: Alignment.centerLeft,
                            child: Switch(
                                value: toggleValue,
                                activeColor: AppColors.primaryColor,
                                onChanged: (bool value) {
                                  setState(() {
                                    toggleValue = value;
                                  });
                                })),
                      ),
                      _buildAlignedRow('Attachments', ''),
                    ],
                  ),
                ),
                Positioned(
                  right: 5,
                  top: 8,
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        addedFaults
                            .removeAt(index); // Remove the note from the list
                      });
                    },
                    child: const Icon(
                      Icons.delete,
                      color: AppColors.redAccentColor,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  static Widget _buildAlignedRow(String label, String value,
      {Widget? trailingWidget}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(
            flex: 2,
            child: trailingWidget ??
                Text(
                  value,
                  style: const TextStyle(fontSize: 14),
                  overflow: TextOverflow.visible,
                ),
          ),
        ],
      ),
    );
  }

  static Future<bool> showExitConfirmationDialog(
      {required BuildContext context}) async {
    bool exit = false;
    await showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Are you sure you want to exit?'),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(false);
              },
              child: Text('No'),
            ),
            TextButton(
              onPressed: () {
                exit = true;
                Navigator.of(context).pop(true);
              },
              child: Text('Yes'),
            ),
          ],
        );
      },
    );
    return exit;
  }

  static showEamDialog2(
    BuildContext context, {
    String? title,
    String? description,
    String? positiveActionLabel,
    String? negativeActionLabel,
    VoidCallback? onPositiveClickListener,
    VoidCallback? onNegativeClickListener,
    bool dismissible = false,
  }) {
    showDialog(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(title!),
          content: description != null ? Text(description) : null,
          actions: [
            if (negativeActionLabel != null)
              TextButton(
                onPressed: () {
                  onNegativeClickListener!();
                },
                child: Text(
                  negativeActionLabel,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            if (positiveActionLabel != null)
              TextButton(
                onPressed: () {
                  onPositiveClickListener!();
                },
                child: Text(
                  positiveActionLabel,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  static void showEamDialogWithOption(
    BuildContext context, {
    String? title,
    String? description,
    bool dismissible = false,
    required List<String> optionList,
    required Function(int index) onOptionSelected,
  }) {
    showDialog(
      context: context,
      barrierDismissible: dismissible,
      // false = user must tap button, true = tap outside dialog
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          scrollable: true,
          title: Text(title!),
          content: Column(
            children: [
              if (description != null) ...[
                Text(description),
                const SizedBox(
                  height: 10,
                ),
              ],
              SizedBox(
                width: double.maxFinite,
                height: 100,
                child: ListView.separated(
                  shrinkWrap: true,
                  itemCount: optionList.length,
                  separatorBuilder: (context, index) {
                    return const Divider();
                  },
                  itemBuilder: (context, index) {
                    return InkResponse(
                        onTap: () {
                          Navigator.of(context, rootNavigator: true).pop();
                          onOptionSelected(index);
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(5),
                          child: Text(optionList[index]),
                        ));
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static showEamDialogWithInput(
    BuildContext context, {
    String? title,
    String? description,
    String? positiveActionLabel,
    String? negativeActionLabel,
    Function(String res)? onPositiveClickListener,
    Function? onNegativeClickListener,
    bool dismissible = false,
    required TextEditingController controller,
    TextInputType inputType = TextInputType.text,
    String? hintText,
  }) {
    showDialog(
      context: context,
      barrierDismissible: dismissible,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(title!),
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (description != null) ...[
                Text(description),
              ],
              TextFormField(
                keyboardType: inputType,
                controller: controller,
                decoration: InputDecoration(hintText: hintText),
              ),
            ],
          ),
          actions: [
            if (negativeActionLabel != null)
              TextButton(
                onPressed: () =>
                    Navigator.of(context, rootNavigator: true).pop(),
                child: Text(
                  negativeActionLabel,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            if (positiveActionLabel != null)
              TextButton(
                onPressed: () {
                  if (onPositiveClickListener != null) {
                    onPositiveClickListener(controller.text);
                  }
                  //Navigator.pop(context);
                },
                child: Text(
                  positiveActionLabel,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  static getEAMAppBarShape() {
    return const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        bottom: Radius.circular(10),
      ),
    );
  }

  static closeDialog(BuildContext context) =>
      Navigator.of(context, rootNavigator: true).pop();

  static progressBarWidget(BuildContext context) {
    return SizedBox(
      height: 200,
      width: 200,
      child: Center(
        child: CupertinoActivityIndicator(
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  static void simpleDialog(BuildContext context,
      {String title = "", String dec = ""}) {
    UIHelper.showEamDialog(context,
        positiveActionLabel: AppLocalizations.of(context)!.ok,
        negativeActionLabel: AppLocalizations.of(context)!.cancel,
        title: title,
        description: dec, onPositiveClickListener: () {
      Navigator.of(context, rootNavigator: true).pop();
    });
  }

  TextStyle textStyle(
      {double? fontSize, FontWeight? fontWeight, Color? textColor}) {
    return TextStyle(
      fontSize: fontSize ?? 12,
      fontWeight: fontWeight,
      color: textColor ?? AppColors.titleTextColor,
    );
  }

  TextStyle mediumTextStyle(
      {double? fontSize, FontWeight? fontWeight, Color? textColor}) {
    return TextStyle(
      fontSize: fontSize ?? 14,
      fontWeight: fontWeight,
      color: textColor ?? AppColors.titleTextColor,
    );
  }

  TextStyle largeTextStyle(
      {double? fontSize, FontWeight? fontWeight, Color? textColor}) {
    return TextStyle(
      fontSize: fontSize ?? 16,
      fontWeight: fontWeight,
      color: textColor ?? AppColors.titleTextColor,
    );
  }

  TextStyle extraLargeTextStyle(
      {double? fontSize, FontWeight? fontWeight, Color? textColor}) {
    return TextStyle(
      fontSize: fontSize ?? 20,
      fontWeight: FontWeight.bold,
      color: textColor ?? AppColors.titleTextColor,
    );
  }

  Widget icon({required IconData icon, double? size}) {
    return Icon(icon, size: size ?? 30, color: AppColors.selectedIconColor);
  }

  progressDialog({required BuildContext context, required String message}) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return AlertDialog(
            title: Text("Please wait", style: textStyle(fontSize: 14)),
            content: Row(
              children: [
                Container(
                  width: 30,
                  height: 30,
                  child: Center(
                      child: CircularProgressIndicator(
                    color: AppColors.primaryColor,
                  )),
                ),
                SizedBox(width: 18),
                Expanded(
                  child: Text(message, style: textStyle(fontSize: 16)),
                )
              ],
            ),
          );
        });
  }

  static TextStyle headerStyle() {
    return const TextStyle(
        fontWeight: FontWeight.bold, fontSize: 18, color: Colors.grey);
  }

  static TextStyle faultHeaderStyle() {
    return const TextStyle(
        fontWeight: FontWeight.bold, fontSize: 16, color: Colors.grey);
  }

  static TextStyle buttonStyle() {
    return TextStyle(
        fontWeight: FontWeight.bold, fontSize: 16, color: AppColors.white);
  }

  static TextStyle labelStyle() {
    return TextStyle(
      fontSize: 14,
      color: AppColors.titleTextColor,
      fontWeight: FontWeight.w600,
      letterSpacing: 0.1,
    );
  }

  static TextStyle valueBoldStyle() {
    return TextStyle(
      fontSize: 14,
      color: AppColors.titleTextColor,
      fontWeight: FontWeight.bold,
      letterSpacing: 0.1,
    );
  }

  static TextStyle valueStyle() {
    return TextStyle(
      fontSize: 14,
      color: AppColors.titleTextColor,
      letterSpacing: 0.1,
    );
  }

  static TextStyle descriptionStyle() {
    return TextStyle(
      fontSize: 13,
      color: AppColors.titleTextColor,
      letterSpacing: 0.1,
    );
  }

  static TextStyle valueStyle14() {
    return const TextStyle(
      fontSize: 14,
      color: AppColors.greySubtitleText,
      letterSpacing: 0.1,
    );
  }

  static TextStyle textInputStyle14() {
    return const TextStyle(
      fontSize: 14,
      letterSpacing: 0.1,
    );
  }

  static TextStyle titleStyle14() {
    return TextStyle(
      fontSize: 14,
      color: AppColors.black,
      fontWeight: FontWeight.bold,
      letterSpacing: 0.1,
    );
  }

  static TextStyle titleStyle16() {
    return TextStyle(
      fontSize: 16,
      color: AppColors.black,
      fontWeight: FontWeight.bold,
      letterSpacing: 0.1,
    );
  }

  static Decoration cardDecoration({Color? cardColor}) {
    return BoxDecoration(
      color: cardColor ?? AppColors.white,
      borderRadius: BorderRadius.circular(10),
      border: Border.all(
        color: AppColors.grey,
        width: 1.0,
      ),
    );
  }

  // Modern card decoration without borders, with subtle shadow
  static Decoration modernCardDecoration({Color? cardColor, bool isSelected = false}) {
    return BoxDecoration(
      color: cardColor ?? AppColors.modernCardBackground,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: AppColors.modernCardShadow,
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
      border: isSelected ? Border.all(
        color: AppColors.primaryColor.withOpacity(0.3),
        width: 2.0,
      ) : null,
    );
  }

  // Modern text styles with Inter font
  static TextStyle modernTitleStyle({
    double fontSize = 16,
    FontWeight fontWeight = FontWeight.w600,
    Color? color,
  }) {
    return GoogleFonts.inter(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color ?? AppColors.modernPrimaryText,
      letterSpacing: -0.02,
    );
  }

  static TextStyle modernBodyStyle({
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.w400,
    Color? color,
  }) {
    return GoogleFonts.inter(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color ?? AppColors.modernSecondaryText,
      letterSpacing: -0.01,
    );
  }

  static TextStyle modernCaptionStyle({
    double fontSize = 12,
    FontWeight fontWeight = FontWeight.w400,
    Color? color,
  }) {
    return GoogleFonts.inter(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color ?? AppColors.modernTertiaryText,
      letterSpacing: 0,
    );
  }

  static Decoration fieldDecoration() {
    return BoxDecoration(
      border: Border.all(
        color: AppColors.cardBorderGrey,
        width: 1, // Border width
      ),
      borderRadius: BorderRadius.circular(5.0),
    );
  }

  static EdgeInsetsGeometry allPaddingOf8() {
    return const EdgeInsets.all(8.0);
  }

  static EdgeInsetsGeometry allPaddingOf10() {
    return const EdgeInsets.all(10.0);
  }

  static EdgeInsetsGeometry horizontalPaddingOf12() {
    return const EdgeInsets.symmetric(horizontal: 12.0);
  }

  static EdgeInsetsGeometry horizontalPaddingOf18() {
    return const EdgeInsets.symmetric(horizontal: 18.0);
  }

  static EdgeInsetsGeometry allPaddingOf18() {
    return const EdgeInsets.all(18.0);
  }

  static EdgeInsetsGeometry columnFieldPadding() {
    return const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0);
  }

  static EdgeInsetsGeometry columnFieldOnlyVerticalPadding() {
    return const EdgeInsets.symmetric(vertical: 8.0);
  }

  static EdgeInsetsGeometry columnFieldOnlyVerticalPadding10() {
    return const EdgeInsets.symmetric(vertical: 10);
  }

  static EdgeInsetsGeometry columnFieldOnlhorizontalPadding() {
    return const EdgeInsets.symmetric(horizontal: 6.0);
  }

  static EdgeInsetsGeometry columnFieldOnlhorizontalPadding6() {
    return const EdgeInsets.symmetric(horizontal: 6.0);
  }

  static Widget sizedBox10() {
    return const SizedBox(height: 10);
  }

  static Widget sizedBox8({double? height}) {
    return SizedBox(height: height ?? 8);
  }

  static Widget sizedBox20() {
    return const SizedBox(height: 20);
  }

  // static String getFaultType(String type) {
  //   switch (type) {
  //     case "1":
  //       return "Break Down";
  //     case "2":
  //       return "Safety";
  //     case "3":
  //       return "Malfunction";
  //     case "4":
  //       return "Chipping";
  //     default:
  //       return '';
  //   }
  // }

  // static String getJobType(String type) {
  //   switch (type) {
  //     case "PM01":
  //       return "Maintenance order";
  //     case "PM02":
  //       return "Cleaning order";
  //     default:
  //       return "Default";
  //   }
  // }

  // static String getFailureMode(String type) {
  //   switch (type) {
  //     case "AUT":
  //       return "Automatic";
  //     case "ELE":
  //       return "Electrical";
  //     case "MECH":
  //       return "Mechanical";
  //     case "PNEU":
  //       return "Pneumatic";
  //     case "Pump":
  //       return "Pump Failure";
  //     default:
  //       return '';
  //   }
  // }

/*
  static String getPriority(String priority) {
    switch (priority) {
      case "1":
        return "High";
      case "2":
        return "Medium";
      case "3":
        return "Low";
      default:
        return "";
    }
  }
*/

  static getStatusString(String status) {
    switch (status) {
      case "CREATED":
        return "Open";

      case "ASSIGNED":
        return "Job Assigned";

      case "COMPLETED":
        return "Completed";

      default:
        return "Open";
    }
  }

  static HexColor getFaultStatusColor(String status) {
    switch (status) {
      case "CREATED":
        return HexColor("#FF8488");

      case "ASSIGNED":
        return HexColor("#FFD67E");

      case "COMPLETED":
        return HexColor("#90EE90");

      default:
        return HexColor("#FF8488");
    }
  }

  static getJobStatusString(String status) {
    switch (status) {
      case "CREATED":
        return "Open";

      case "ASSIGNED":
        return "User Assigned";

      case "ACCEPTED":
        return "Job Accepted";

      case "REJECTED":
        return "Rejected";

      case "COMPLETED":
        return "Completed";

      default:
        return "Open";
    }
  }

  static HexColor getJobCreationStatusColor(String status) {
    switch (status) {
      case "CREATED":
        return HexColor("#FF8488");

      case "ASSIGNED":
        return HexColor("#FFD67E");

      case "ACCEPTED":
        return HexColor("#90EE90");

      case "REJECTED":
        return HexColor("#FF8488");

      case "COMPLETED":
        return HexColor("#90EE90");

      case "REJECTED":
        return HexColor("#FF8488");
      default:
        return HexColor("#FF8488");
    }
  }

/*
  static Color getStatusColor(String status) {
    switch (status) {
      case "1":
        return Colors.red[300]!;
      case "2":
        return Colors.blue[300]!;
      case "3":
        return Colors.green[300]!;
      default:
        return Colors.grey[300]!; // Fallback color for unknown statuses
    }
  }
*/

  static HexColor getPriorityColorByCode(String status) {
    switch (status) {
      case "1":
        return HexColor("#FF8488");
      case "2":
        return HexColor("#FFD67E");
      case "3":
        return HexColor("#90EE90");
      /* case "Overdue":
        return HexColor("#367E18");
      default:
        return HexColor("#367E18");*/
      default:
        return HexColor("#FFFFFF");
    }
  }

  static HexColor getPriorityColor(String status) {
    switch (status) {
      case "High":
        return HexColor("#FF8488");
      case "Highest":
        return HexColor("#FF8488");
      case "Medium":
        return HexColor("#FFD67E");
      case "Low":
        return HexColor("#90EE90");
      case "Overdue":
        return HexColor("#367E18");
      default:
        return HexColor("#FFFFFF");
/*        default:
        return HexColor("#367E18");*/
    }
  }

  static String formatDate(String dateString) {
    int year = int.parse(dateString.substring(0, 4));
    int month = int.parse(dateString.substring(4, 6));
    int day = int.parse(dateString.substring(6, 8));

    String monthName = "";
    switch (month) {
      case 1:
        monthName = "Jan";
        break;
      case 2:
        monthName = "Feb";
        break;
      case 3:
        monthName = "Mar";
        break;
      case 4:
        monthName = "Apr";
        break;
      case 5:
        monthName = "May";
        break;
      case 6:
        monthName = "Jun";
        break;
      case 7:
        monthName = "Jul";
        break;
      case 8:
        monthName = "Aug";
        break;
      case 9:
        monthName = "Sep";
        break;
      case 10:
        monthName = "Oct";
        break;
      case 11:
        monthName = "Nov";
        break;
      case 12:
        monthName = "Dec";
        break;
      default:
        monthName = "";
    }

    return "$day $monthName $year";
  }

  static Widget buildLabelAndValueAsWidgetOfFault({
    bool readOnly = false,
    bool enabled = true,
    required String label,
    required String value,
    required bool isTextFieldRequiredAsValueWidget,
    bool suffixIcon = false,
    bool isTimerSuffixRequired = false,
    GestureTapCallback? onTap,
    GestureTapCallback? onSuffixIconTap,
    GestureTapCallback? onTimerSuffixIconTap,
    required TextEditingController controller,
    ValueChanged<String>? onChanged,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return Padding(
      padding: UIHelper.columnFieldOnlyVerticalPadding10(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: UIHelper.labelStyle(),
          ),
          isTextFieldRequiredAsValueWidget
              ? getEditFaultWidgetTextField(
                  readOnly: readOnly,
                  label: label,
                  onTap: onTap,
                  controller: controller,
                  enabled: enabled,
                  suffixIcon: suffixIcon,
                  onSuffixIconTap: onSuffixIconTap,
                  isTimerSuffixRequired: isTimerSuffixRequired,
                  onTimerSuffixIconTap: onTimerSuffixIconTap,
                  onChanged: onChanged,
                  inputFormatters: inputFormatters)
              : Text(
                  value,
                  style: UIHelper.valueStyle14(),
                ),
        ],
      ),
    );
  }

  static Widget buildLabelAndValueAsWidgetOfProfileScreen({
    bool readOnly = false,
    required String label,
    required String value,
    bool isTimerSuffixRequired = false,
    GestureTapCallback? onTap,
    GestureTapCallback? onSuffixIconTap,
    GestureTapCallback? onTimerSuffixIconTap,
    required TextEditingController controller,
    ValueChanged<String>? onChanged,
    bool isPassword = false,
    Widget? customSuffixIcon,
    bool showText = false,
  }) {
    return Padding(
      padding: UIHelper.columnFieldOnlyVerticalPadding10(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: UIHelper.labelStyle(),
          ),
          getEditProfileScreenTextField(
              readOnly: readOnly,
              label: label,
              onTap: onTap,
              controller: controller,
              enabled: !readOnly,
              isPassword: isPassword,
              showText: showText,
              customSuffixIcon: customSuffixIcon,
              onSuffixIconTap: onSuffixIconTap,
              isTimerSuffixRequired: isTimerSuffixRequired,
              onTimerSuffixIconTap: onTimerSuffixIconTap,
              onChanged: onChanged),
        ],
      ),
    );
  }

  static Widget buildLabelAndValueAsWidgetOfJob({
    required BuildContext context,
    bool readOnly = false,
    required String label,
    required String value,
    required bool isTextFieldRequiredAsValueWidget,
    bool suffixIcon = false,
    bool isTimerSuffixRequired = false,
    GestureTapCallback? onTap,
    GestureTapCallback? onSuffixIconTap,
    GestureTapCallback? onTimerSuffixIconTap,
    required TextEditingController controller,
    ValueChanged<String>? onChanged,
  }) {
    return Padding(
      padding: UIHelper.columnFieldOnlyVerticalPadding10(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: UIHelper.labelStyle(),
          ),
          isTextFieldRequiredAsValueWidget
              ? getEditJobWidgetTextField(
                  readOnly: readOnly,
                  label: label,
                  onTap: onTap,
                  controller: controller,
                  enabled: !readOnly,
                  suffixIcon: suffixIcon,
                  onSuffixIconTap: onSuffixIconTap,
                  isTimerSuffixRequired: isTimerSuffixRequired,
                  onTimerSuffixIconTap: onTimerSuffixIconTap,
                  onChanged: onChanged,
                  context: context)
              : Text(
                  value,
                  style: UIHelper.valueStyle14(),
                ),
        ],
      ),
    );
  }

  static Widget buildLabelAndValueAsWidgetOfFaultChoiceAndDropDown(
      {required String label,
      required Widget value,
      required bool isTextFieldRequiredAsValueWidget}) {
    return Padding(
      padding: UIHelper.columnFieldOnlyVerticalPadding10(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: UIHelper.labelStyle(),
          ),
          isTextFieldRequiredAsValueWidget ? value : Container(),
        ],
      ),
    );
  }

  static Widget buildLabelAndValueAsWidgetOfUserPreferenceDropDown(
      {required String label,
      required Widget value,
      required bool isTextFieldRequiredAsValueWidget}) {
    return Padding(
      padding: UIHelper.columnFieldOnlyVerticalPadding10(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                label,
                style: UIHelper.labelStyle(),
              ),
              const SizedBox(width: 3),
              const Text(
                '*',
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.redColor,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.1,
                ),
              ),
            ],
          ),
          isTextFieldRequiredAsValueWidget ? value : Container(),
        ],
      ),
    );
  }

  static Widget buildLabelAndValueAsWidgetOfFaultActivity(
      {required bool isTextFieldRequiredAsValueWidget,
      required String readOnlyText,
      required TextEditingController controller}) {
    final lines = readOnlyText.split('\n');
    final dateTime = lines.isNotEmpty ? lines[0] : '';
    final details = lines.length > 1 ? lines.sublist(1).join('\n') : '';
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.grey, width: 1),
        borderRadius: BorderRadius.circular(20),
      ),
      padding: const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            text: TextSpan(
              children: [
                TextSpan(text: '$dateTime\n', style: UIHelper.valueStyle14()),
                TextSpan(text: '$details', style: UIHelper.valueStyle14()),
              ],
            ),
          ),
          TextField(
            controller: controller,
            maxLines: null,
            style: UIHelper.textInputStyle14(),
            readOnly: isTextFieldRequiredAsValueWidget,
            decoration: const InputDecoration(
              border: InputBorder.none,
            ),
          ),
        ],
      ),
    );
  }
  // static Widget buildLabelAndValueAsWidgetOfFaultActivity(
  //     {required String label,
  //     required String value,
  //     required bool isTextFieldRequiredAsValueWidget}) {
  //   TextEditingController controller = TextEditingController();
  //   controller.text = value;
  //   return Padding(
  //     padding: UIHelper.columnFieldOnlyVerticalPadding10(),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Text(
  //           label,
  //           style: UIHelper.labelStyle(),
  //         ),
  //         isTextFieldRequiredAsValueWidget
  //             ? getEditFaultWidgetTextField(
  //                 label: label, controller: controller)
  //             : Text(
  //                 value,
  //                 style: UIHelper.valueStyle14(),
  //               ),
  //       ],
  //     ),
  //   );
  // }

  static Widget buildLabelAndValueOfLocationDetailsOfFault(
      {required String label, required String value}) {
    TextEditingController controller = TextEditingController();
    controller.text = value;
    return Padding(
      padding: UIHelper.columnFieldOnlyVerticalPadding10(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [getLocTextField(label: label, controller: controller)],
      ),
    );
  }

  static Widget buildLabelAndValueOfFault(
      {String? label, required Widget trailingWidget}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          label != null
              ? Row(
                  children: [
                    Expanded(
                      child: Text(label, style: UIHelper.labelStyle()),
                    ),
                  ],
                )
              : Container(),
          label != null
              ? const SizedBox(
                  height: 3,
                )
              : Container(),
          trailingWidget
        ],
      ),
    );
  }

  static Widget getEditFaultWidgetTextField({
    bool readOnly = false,
    required String label,
    required TextEditingController controller,
    ValueChanged<String>? onChanged,
    GestureTapCallback? onTap,
    bool suffixIcon = false,
    bool? enabled = true,
    bool isTimerSuffixRequired = false,
    GestureTapCallback? onSuffixIconTap,
    GestureTapCallback? onTimerSuffixIconTap,
    double? borderRadius,
    int? maxline,
    bool isRequiredCustomSuffixIcon = false,
    Widget? customSuffixIcon,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return TextFormField(
      style: UIHelper.valueStyle14(),
      controller: controller,
      onChanged: onChanged,
      enabled: enabled,
      onTap: onTap,
      maxLines: maxline,
      readOnly: readOnly,
      inputFormatters: inputFormatters,
      decoration: InputDecoration(
          contentPadding: const EdgeInsets.only(left: 5),
          border: OutlineInputBorder(
              gapPadding: 2,
              borderRadius:
                  BorderRadius.all(Radius.circular(borderRadius ?? 5)),
              borderSide:
                  BorderSide(color: AppColors.cardBorderGrey, width: 1)),
          enabledBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(color: AppColors.cardBorderGrey, width: 1)),
          focusedBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(color: AppColors.cardBorderGrey, width: 1)),
          suffixIcon: suffixIcon
              ? isRequiredCustomSuffixIcon
                  ? customSuffixIcon
                  : isTimerSuffixRequired
                      ? InkWell(
                          onTap: onTimerSuffixIconTap, child: Icon(Icons.timer))
                      : InkWell(
                          onTap: onSuffixIconTap, child: Icon(Icons.date_range))
              : null),
    );
  }

  static Widget getEditProfileScreenTextField(
      {bool readOnly = false,
      required String label,
      required TextEditingController controller,
      ValueChanged<String>? onChanged,
      GestureTapCallback? onTap,
      bool? enabled = true,
      bool isTimerSuffixRequired = false,
      GestureTapCallback? onSuffixIconTap,
      GestureTapCallback? onTimerSuffixIconTap,
      double? borderRadius,
      int? maxline,
      bool isRequiredCustomSuffixIcon = false,
      bool isPassword = false,
      Widget? customSuffixIcon,
      bool showText = false}) {
    return TextFormField(
      style: UIHelper.valueStyle14(),
      controller: controller,
      onChanged: onChanged,
      enabled: enabled,
      onTap: onTap,
      maxLines: 1,
      readOnly: readOnly,
      obscureText: isPassword && !showText,
      enableSuggestions: isPassword,
      autocorrect: isPassword,
      decoration: InputDecoration(
          contentPadding: const EdgeInsets.only(left: 5),
          border: OutlineInputBorder(
              gapPadding: 2,
              borderRadius:
                  BorderRadius.all(Radius.circular(borderRadius ?? 5)),
              borderSide:
                  BorderSide(color: AppColors.cardBorderGrey, width: 1)),
          enabledBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(color: AppColors.cardBorderGrey, width: 1)),
          focusedBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(color: AppColors.cardBorderGrey, width: 1)),
          suffixIcon: customSuffixIcon),
    );
  }

  static Widget getEditJobWidgetTextField(
      {bool readOnly = false,
      required BuildContext context,
      required String label,
      required TextEditingController controller,
      ValueChanged<String>? onChanged,
      GestureTapCallback? onTap,
      bool suffixIcon = false,
      bool? enabled = true,
      bool isTimerSuffixRequired = false,
      GestureTapCallback? onSuffixIconTap,
      GestureTapCallback? onTimerSuffixIconTap,
      double? borderRadius}) {
    return TextFormField(
      style: UIHelper.valueStyle14(),
      controller: controller,
      onChanged: onChanged,
      enabled: enabled,
      onTap: onTap,
      // maxLines: null,
      readOnly: readOnly,
      decoration: InputDecoration(
          contentPadding: EdgeInsets.only(left: 5),
          border: OutlineInputBorder(
              gapPadding: 2,
              borderRadius:
                  BorderRadius.all(Radius.circular(borderRadius ?? 5)),
              borderSide:
                  BorderSide(color: AppColors.cardBorderGrey, width: 1)),
          enabledBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(color: AppColors.cardBorderGrey, width: 1)),
          focusedBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(color: AppColors.cardBorderGrey, width: 1)),
          suffixIcon: suffixIcon
              ? isTimerSuffixRequired
                  ? InkWell(
                      onTap: onTimerSuffixIconTap, child: Icon(Icons.timer))
                  : InkWell(
                      onTap: onSuffixIconTap, child: Icon(Icons.date_range))
              : null),
    );
  }

  static Widget getActivityCommentFaultWidgetTextField(
      {required String label,
      required TextEditingController controller,
      ValueChanged<String>? onChanged,
      bool suffixIcon = false,
      GestureTapCallback? onTap}) {
    return TextFormField(
      style: UIHelper.valueStyle14(),
      controller: controller,
      onChanged: onChanged,
      maxLines: null,
      readOnly: false,
      decoration: InputDecoration(
          contentPadding: EdgeInsets.only(left: 5),
          border: OutlineInputBorder(
              gapPadding: 2,
              borderRadius: BorderRadius.circular(20),
              borderSide: BorderSide(color: AppColors.grey, width: 1)),
          enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.grey, width: 1)),
          focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.grey, width: 1)),
          suffixIcon: suffixIcon
              ? InkWell(onTap: onTap, child: Icon(Icons.date_range))
              : null),
    );
  }

  static Widget getLocTextField(
      {required String label,
      required TextEditingController controller,
      ValueChanged<String>? onChanged,
      bool suffixIcon = false,
      GestureTapCallback? onTap}) {
    return TextFormField(
      style: UIHelper.valueStyle14(),
      controller: controller,
      onChanged: onChanged,
      // maxLines: null,
      readOnly: false,
      decoration: InputDecoration(
          label: Text(
            label,
            style: UIHelper.labelStyle(),
          ),
          contentPadding: EdgeInsets.only(left: 5),
          border: OutlineInputBorder(
              gapPadding: 2,
              borderRadius: BorderRadius.all(Radius.circular(5)),
              borderSide:
                  BorderSide(color: AppColors.cardBorderGrey, width: 1)),
          enabledBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(color: AppColors.cardBorderGrey, width: 1)),
          focusedBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(color: AppColors.cardBorderGrey, width: 1)),
          suffixIcon: suffixIcon
              ? InkWell(onTap: onTap, child: Icon(Icons.date_range))
              : null),
    );
  }

  static Widget getFaultTextField(
      {required String label,
      required TextEditingController controller,
      ValueChanged<String>? onChanged,
      bool suffixIcon = false,
      GestureTapCallback? onTap}) {
    return TextFormField(
      style: UIHelper.valueStyle(),
      controller: controller,
      onChanged: onChanged,
      readOnly: false,
      decoration: InputDecoration(
          label: Text(label, style: UIHelper.labelStyle()),
          contentPadding: EdgeInsets.only(left: 5),
          border: OutlineInputBorder(
              gapPadding: 2,
              borderRadius: BorderRadius.all(Radius.circular(5)),
              borderSide:
                  BorderSide(color: AppColors.cardBorderGrey, width: 1)),
          enabledBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(color: AppColors.cardBorderGrey, width: 1)),
          focusedBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(color: AppColors.cardBorderGrey, width: 1)),
          suffixIcon: suffixIcon
              ? InkWell(onTap: onTap, child: const Icon(Icons.date_range))
              : null),
    );
  }

  static int? generateRandomId() {
    int? id;
    id = DateTime.now().millisecondsSinceEpoch;
    return id;
/*    int? data;
    final random = Random();
    data = (10000 + random.nextInt(90000)).toInt();
    return data;*/
  }

  String toCamelCase(String input) {
    if (input.isEmpty) return '';
    return input[0].toUpperCase() + input.substring(1).toLowerCase();
  }

  bool isValidPassword(String password) {
    final RegExp passwordRegex = RegExp(
        r'^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#\$%^&*()-_=+<>?])[A-Za-z\d!@#\$%^&*()-_=+<>?]{8,}$');
    return passwordRegex.hasMatch(password);
  }

  bool isValidEmail(String email) {
    final RegExp emailRegex = RegExp(
        r"^(?!.*@.*@)[a-zA-Z0-9.!_\-#$&%',*+=/?^|{}~]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");
    return emailRegex.hasMatch(email);
  }

  static List<String> decodeBitwise(int value) {
    Map<int, String> permissions = {
      16: 'Assign',
      8: 'Create',
      4: 'Display',
      2: 'Execute',
      0: 'Delete'
    };

    List<String> result = [];

    permissions.forEach((bit, name) {
      if ((value & bit) != 0) {
        result.add(name);
      }
    });

    return result;
  }

  static bool isAssign(int value) => (value & 16) != 0;
  static bool isCreate(int value) => (value & 8) != 0;
  static bool isDisplay(int value) => (value & 4) != 0;
  static bool isExecute(int value) => (value & 2) != 0;
  static bool isDelete(int value) => (value & 1) != 0;

  Map<String, bool> checkOperationPermissions(int rolePermissions) {
    return {
      'assignment': (rolePermissions & 16) != 0,
      'create': (rolePermissions & 8) != 0,
      'display': (rolePermissions & 4) != 0,
      'execution': (rolePermissions & 2) != 0,
      'delete': (rolePermissions & 1) != 0,
    };
  }

  // ScreenSize getScreenSize(BuildContext context, BoxConstraints constraints) {
  //   double width = constraints.maxWidth;
  //   Orientation orientation = MediaQuery.of(context).orientation;

  //   if (width >= 1024) {
  //     return ScreenSize.desktop;
  //   } else if (width >= 600) {
  //     if (orientation == Orientation.portrait) {
  //       // Tablet in portrait mode → treat as mobile
  //       return ScreenSize.mobile;
  //     } else {
  //       // Tablet in landscape mode → treat as tablet
  //       return ScreenSize.desktop;
  //     }
  //   } else {
  //     if (orientation == Orientation.landscape) {
  //       // Mobile in landscape → treat as tablet
  //       return ScreenSize.desktop;
  //     } else {
  //       return ScreenSize.mobile;
  //     }
  //   }
  // }

  ScreenType getScreenType(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final width = mediaQuery.size.width;
    final orientation = mediaQuery.orientation;

    if (width >= 1024) {
      return ScreenType.desktop;
    } else if (orientation == Orientation.landscape && width >= 600) {
      // Treat landscape phones as desktop
      return ScreenType.desktop;
    } else if (width >= 600) {
      return ScreenType.tablet;
    } else {
      return ScreenType.mobile;
    }
  }
}
