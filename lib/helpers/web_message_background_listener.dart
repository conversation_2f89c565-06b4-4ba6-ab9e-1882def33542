// import 'dart:html';

// void listenToWebServiceWorkerMessages(
//     Function(Map<String, dynamic>) onMessage) {
//   window.onMessage.listen((event) {
//     final data = event.data;

//     if (data is Map && data['type'] == 'NEW_NOTIFICATION') {
//       onMessage(Map<String, dynamic>.from(data['payload']));
//     }
//   });
// }

// Only included for web platform
import 'dart:async';
import 'dart:convert';
import 'dart:html';
import 'package:logger/logger.dart';

import 'package:rounds/helpers/pushNotification_helper.dart';

class PushWebListener {
  static final _controller = StreamController<Map<String, dynamic>>.broadcast();

  static Stream<Map<String, dynamic>> get stream => _controller.stream;

  static void init() {
    try {
      final channel = BroadcastChannel('sw-messages');

      channel.onMessage.listen((MessageEvent event) {
        try {
          final rawData = event.data;
          final data = rawData is String ? jsonDecode(rawData) : rawData;
          _controller.add(Map<String, dynamic>.from(data));
          PushNotifications.processQueue();
        } catch (e) {
          Logger.logError('PushWebListener', 'init', e.toString());
        }
      });
    } catch (e) {
      Logger.logError('PushWebListener', 'init', e.toString());
    }
  }
}
