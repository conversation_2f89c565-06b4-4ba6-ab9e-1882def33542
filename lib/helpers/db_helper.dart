import 'dart:convert';
import 'dart:typed_data';
import 'package:idb_shim/idb_browser.dart';
import 'package:collection/collection.dart';
import 'package:http/http.dart';
import 'package:idb_shim/idb.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/ABCINDICATOR_HEADER.dart';
import 'package:rounds/be/APP_SETTING_HEADER.dart';
import 'package:rounds/be/ASSET_CATEGORY_HEADER.dart';
import 'package:rounds/be/ASSET_DOCUMENT.dart';
import 'package:rounds/be/ASSET_HEADER.dart';
import 'package:rounds/be/CILT_EXEC_ACTION.dart';
import 'package:rounds/be/CILT_EXEC_DOC.dart';
import 'package:rounds/be/CILT_EXEC_HEADER.dart';
import 'package:rounds/be/CILT_EXEC_TASK.dart';
import 'package:rounds/be/CILT_PLAN_DOC.dart';
import 'package:rounds/be/CILT_PLAN_HEADER.dart';
import 'package:rounds/be/CILT_TASK.dart';
import 'package:rounds/be/DOCUMENT_ATTACHMENT.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:rounds/be/FAILURE_MODE_HEADER.dart';
import 'package:rounds/be/FAULT_ACTION.dart';
import 'package:rounds/be/FAULT_DOCUMENT.dart';
import 'package:rounds/be/FAULT_HEADER.dart';
import 'package:rounds/be/FAULT_TYPE_HEADER.dart';
import 'package:rounds/be/INSPECTION_PLAN_HEADER.dart';
import 'package:rounds/be/KPI_HEADER.dart';
import 'package:rounds/be/INSPECTION_SECTION.dart';
import 'package:rounds/be/JOB_HEADER.dart';
import 'package:rounds/be/LOCATION_HEADER.dart';
import 'package:rounds/be/PICKLIST_CODE.dart';
import 'package:rounds/be/PICKLIST_HEADER.dart';
import 'package:rounds/be/PRIORITY_HEADER.dart';
import 'package:rounds/be/SHIFT_HEADER.dart';
import 'package:rounds/be/SKIP_REASON_HEADER.dart';
import 'package:rounds/be/SYSTEM_CONDITION_HEADER.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/utils/constants.dart';

import 'package:unvired_sdk/unvired_sdk.dart';
import '../be/CILT_EXEC_SEC.dart';
import '../be/CILT_SECTION.dart';
import '../be/CILT_TASK_DOC.dart';
import '../be/INSPECTION_PLAN_DOC.dart';
import '../be/INSPECTION_TASK.dart';
import '../be/INSPECTION_TASK_DOC.dart';
import '../be/INSP_EXEC_ACTION.dart';
import '../be/INSP_EXEC_DOC.dart';
import '../be/INSP_EXEC_HEADER.dart';
import '../be/INSP_EXEC_SEC.dart';
import '../be/INSP_EXEC_TASK.dart';
import '../be/JOBTYPE_HEADER.dart';
import '../be/JOB_ACTION.dart';
import '../be/JOB_DOCUMENT.dart';
import '../be/LOCATION_CATEGORY_HEADER.dart';
import '../be/LOCATION_DOCUMENT.dart';
import '../be/PLANT_HEADER.dart';
import '../be/PLANT_SECTION_HEADER.dart';
import '../be/ROLE_HEADER.dart';
import '../be/USER_HEADER.dart';
import '../be/USER_PLANT.dart';
import '../be/USER_PLANT_SECTION.dart';
import '../utils/app_constants.dart';

class DbHelper {
  static const sourceClass = 'DbHelper';

  static Future<List<APP_SETTING_HEADER>> getAppSettingHeader() async {
    List<APP_SETTING_HEADER> appSettingHeaderList = [];
    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(APP_SETTING_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        APP_SETTING_HEADER header = APP_SETTING_HEADER.fromJson(result);
        appSettingHeaderList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getAppSettingHeader', e.toString());
    }

    return appSettingHeaderList;
  }

  static Future<List<Map<String, dynamic>>> getExecutionMode() async {
    List<Map<String, dynamic>> tableInfoList = [];
    try {
      List<Map<String, dynamic>> tableNames =
          await AppDatabaseManager().execute(
        "SELECT name FROM sqlite_master WHERE type = 'table'",
      );

      for (var tableNameMap in tableNames) {
        String tableName = tableNameMap['name'] as String;

        int rowCount = await AppDatabaseManager()
            .execute(
              "SELECT COUNT(*) FROM '$tableName'",
            )
            .then((result) => result.first['COUNT(*)'] as int);

        tableInfoList.add({
          'tableName': tableName,
          'rowCount': rowCount,
        });
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getExecutionMode', e.toString());
    }

    return tableInfoList;
  }

  static Future<List<CILT_EXEC_HEADER>> getCiltExecHeaderList(
      String plantId, List<String> plantSec, String shift) async {
    List<CILT_EXEC_HEADER> ciltHeaderList = [];
    String plantSecValues = plantSec.map((e) => "'$e'").join(',');
    SHIFT_HEADER? shiftHeader = await getShiftHeader(shift, plantId);
    try {
      String query =
          "${CILT_EXEC_HEADER.FIELD_PLANT_ID}='$plantId' AND ${CILT_EXEC_HEADER.FIELD_PLANT_SEC_ID} IN ($plantSecValues) AND ${CILT_EXEC_HEADER.FIELD_SHIFT}='${shiftHeader!.shift_code.toString()}'";

      List results = await AppDatabaseManager().select(
          DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, {})
            ..setWhereClause(query));

      for (var result in results) {
        ciltHeaderList.add(CILT_EXEC_HEADER.fromJson(result));
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getCiltExecHeaderList', e.toString());
    }

    return ciltHeaderList;
  }

  static Future<SHIFT_HEADER?> getShiftHeader(
      String shift, String plantID) async {
    SHIFT_HEADER? shiftHeader;

    try {
      String query =
          '${SHIFT_HEADER.FIELD_PLANT_ID}="$plantID" AND ${SHIFT_HEADER.FIELD_SHIFT_CODE}="$shift"';
      List results = await AppDatabaseManager().select(
          DBInputEntity(SHIFT_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        shiftHeader = SHIFT_HEADER.fromJson(result);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getShiftHeader', e.toString());
    }

    return shiftHeader;
  }

  static Future<SHIFT_HEADER?> getShiftHeaderByName(String shift) async {
    SHIFT_HEADER? shiftHeader;

    try {
      String query = '${SHIFT_HEADER.FIELD_SHIFT_NAME}="$shift"';
      List results = await AppDatabaseManager().select(
          DBInputEntity(SHIFT_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        shiftHeader = SHIFT_HEADER.fromJson(result);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getShiftHeader', e.toString());
    }

    return shiftHeader;
  }

  static Future<CILT_EXEC_HEADER?> getCiltExecHeader(
      CILT_EXEC_HEADER cilt_exec_header) async {
    CILT_EXEC_HEADER? ciltHeader;

    try {
      String query =
          '${CILT_EXEC_HEADER.FIELD_CILT_ID}="${cilt_exec_header.cilt_id}"';
      List results = await AppDatabaseManager().select(
          DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, {})
            ..setWhereClause(query));

      for (var result in results) {
        ciltHeader = CILT_EXEC_HEADER.fromJson(result);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getCiltExecHeader', e.toString());
    }

    return ciltHeader;
  }

  static Future<CILT_EXEC_HEADER?> getCiltExeHeaderByCiltId(
      String ciltId) async {
    CILT_EXEC_HEADER? ciltHeader;

    try {
      String query = '${CILT_EXEC_HEADER.FIELD_CILT_ID}=$ciltId';
      List results = await AppDatabaseManager().select(
          DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, {})
            ..setWhereClause(query));

      for (var result in results) {
        ciltHeader = CILT_EXEC_HEADER.fromJson(result);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getCiltExeHeaderByCiltId', e.toString());
    }

    return ciltHeader;
  }

  static Future<INSP_EXEC_HEADER?> getInspectionExeHeaderByInspId(
      String inspId) async {
    INSP_EXEC_HEADER? inspHeader;

    try {
      String query = '${INSP_EXEC_HEADER.FIELD_INSP_ID}=$inspId';
      List results = await AppDatabaseManager().select(
          DBInputEntity(INSP_EXEC_HEADER.TABLE_NAME, {})
            ..setWhereClause(query));

      for (var result in results) {
        inspHeader = INSP_EXEC_HEADER.fromJson(result);
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getInspectionExeHeaderByInspId', e.toString());
    }

    return inspHeader;
  }

  static Future<INSP_EXEC_HEADER?> getInspectionExeHeaderByInspIdPlanId(
      INSP_EXEC_HEADER header) async {
    INSP_EXEC_HEADER? inspHeader;

    try {
      String query =
          '${INSP_EXEC_HEADER.FIELD_INSP_ID}=${header.insp_id} AND ${INSP_EXEC_HEADER.FIELD_PLAN_ID}=${header.plan_id} AND (${INSP_EXEC_HEADER.FIELD_PLANT_ID}="${header.plant_id}" AND ${INSP_EXEC_HEADER.FIELD_PLANT_SEC_ID} = "${header.plant_sec_id}" AND ${INSP_EXEC_HEADER.FIELD_SHIFT}="${header.shift}")';
      List results = await AppDatabaseManager().select(
          DBInputEntity(INSP_EXEC_HEADER.TABLE_NAME, {})
            ..setWhereClause(query));

      for (var result in results) {
        inspHeader = INSP_EXEC_HEADER.fromJson(result);
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getInspectionExeHeaderByInspIdPlanId', e.toString());
    }

    return inspHeader;
  }

  static Future<INSP_EXEC_HEADER?> getInspectionExecHeader(
      INSP_EXEC_HEADER inspectionHeaderData) async {
    INSP_EXEC_HEADER? inspectionHeader;
    try {
      String query =
          '${INSP_EXEC_HEADER.FIELD_INSP_ID}="${inspectionHeaderData.insp_id}"';
      List results = await AppDatabaseManager().select(
          DBInputEntity(INSP_EXEC_HEADER.TABLE_NAME, {})
            ..setWhereClause(query));

      for (var result in results) {
        inspectionHeader = INSP_EXEC_HEADER.fromJson(result);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getInspectionExecHeader', e.toString());
    }

    return inspectionHeader;
  }

  static Future<List<INSP_EXEC_HEADER>> getInspectionExecHeaderList(
      String plantId, List<String> plantSec, String shift) async {
    List<INSP_EXEC_HEADER> inspectionHeaderList = [];

    String plantSecValues = plantSec.map((e) => "'$e'").join(',');
    SHIFT_HEADER? shiftHeader = await getShiftHeader(shift, plantId);
    try {
      String query =
          '${INSP_EXEC_HEADER.FIELD_PLANT_ID}="$plantId" AND ${INSP_EXEC_HEADER.FIELD_PLANT_SEC_ID} IN ($plantSecValues) AND ${INSP_EXEC_HEADER.FIELD_SHIFT}="${shiftHeader?.shift_code.toString()}"';

      List results = await AppDatabaseManager().select(
          DBInputEntity(INSP_EXEC_HEADER.TABLE_NAME, {})
            ..setWhereClause(query));

      for (var result in results) {
        inspectionHeaderList.add(INSP_EXEC_HEADER.fromJson(result));
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getInspectionHeaderExecList', e.toString());
    }

    return inspectionHeaderList;
  }

  static Future<List<FAULT_HEADER>> getFaultListHeader() async {
    List<FAULT_HEADER> faultListHeader = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(FAULT_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        faultListHeader.add(FAULT_HEADER.fromJson(result));
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getInspectionListHeader', e.toString());
    }

    return faultListHeader;
  }

  static Future<List<CILT_PLAN_HEADER>> getCiltPlanListHeader() async {
    List<CILT_PLAN_HEADER> ciltPlanHeaderList = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(CILT_PLAN_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        ciltPlanHeaderList.add(CILT_PLAN_HEADER.fromJson(result));
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getCiltPlanListHeader', e.toString());
    }

    return ciltPlanHeaderList;
  }

  static Future<List<CILT_SECTION>> getCiltSectionPlanListHeader(
      String id) async {
    List<CILT_SECTION> ciltPlanSectionHeaderList = [];

    try {
      String query = 'PLAN_ID = $id';
      List results = await AppDatabaseManager().select(
          DBInputEntity(CILT_SECTION.TABLE_NAME, {})..setWhereClause(query));
      for (var result in results) {
        ciltPlanSectionHeaderList.add(CILT_SECTION.fromJson(result));
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getCiltSectionPlanListHeader', e.toString());
    }

    return ciltPlanSectionHeaderList;
  }

  static Future<List<CILT_TASK>> getCiltTaskPlanListHeader(
      String planId, String sectionId) async {
    List<CILT_TASK> ciltPlanTaskHeaderList = [];

    try {
      String query =
          '${CILT_TASK.FIELD_PLAN_ID} = $planId AND ${CILT_TASK.FIELD_SECTION_ID} = $sectionId';
      List results = await AppDatabaseManager().select(
          DBInputEntity(CILT_TASK.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        ciltPlanTaskHeaderList.add(CILT_TASK.fromJson(result));
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getCiltTaskPlanListHeader', e.toString());
    }

    return ciltPlanTaskHeaderList;
  }

  static Future<List<CILT_EXEC_HEADER>> getAllCiltExecHeaderList() async {
    List<CILT_EXEC_HEADER> ciltExecHeaderList = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, {}));
      for (var result in results) {
        ciltExecHeaderList.add(CILT_EXEC_HEADER.fromJson(result));
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getAllCiltExecHeaderList', e.toString());
    }

    return ciltExecHeaderList;
  }

  static Future<List<CILT_EXEC_TASK>> getAllCiltExecTaskList() async {
    List<CILT_EXEC_TASK> ciltExecTaskList = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(CILT_EXEC_TASK.TABLE_NAME, {}));
      for (var result in results) {
        ciltExecTaskList.add(CILT_EXEC_TASK.fromJson(result));
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getAllCiltExecTaskList', e.toString());
    }

    return ciltExecTaskList;
  }

  static Future<List<INSP_EXEC_TASK>> getAllInspExecTaskList() async {
    List<INSP_EXEC_TASK> inspExecTaskList = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(INSP_EXEC_TASK.TABLE_NAME, {}));
      for (var result in results) {
        if (result[INSP_EXEC_TASK.FIELD_BLOB_VALUE] is Uint8List) {
          result[INSP_EXEC_TASK.FIELD_BLOB_VALUE] =
              base64Encode(result[INSP_EXEC_TASK.FIELD_BLOB_VALUE]);
        }
        inspExecTaskList.add(INSP_EXEC_TASK.fromJson(result));
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getAllInspExecTaskList', e.toString());
    }

    return inspExecTaskList;
  }

  static Future<CILT_EXEC_TASK?> getCiltExecTaskByCiltHeaderCiltId(
      String id, String taskno) async {
    CILT_EXEC_TASK? ciltExecTask;

    try {
      List results = await AppDatabaseManager().select(DBInputEntity(
          CILT_EXEC_TASK.TABLE_NAME, {})
        ..setWhereClause(
            '${CILT_EXEC_TASK.FIELD_CILT_ID}=$id AND ${CILT_EXEC_TASK.FIELD_TASK_ID}=$taskno'));
      if (results.isNotEmpty) {
        for (var result in results) {
          ciltExecTask = CILT_EXEC_TASK.fromJson(result);
        }
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getCiltExecTaskByCiltHeaderCiltId', e.toString());
    }

    return ciltExecTask;
  }

  static Future<INSP_EXEC_TASK?> getInspExecTaskByInspHeaderInspId(
      String id, String taskno) async {
    INSP_EXEC_TASK? inspExecTask;

    try {
      List results = await AppDatabaseManager().select(DBInputEntity(
          INSP_EXEC_TASK.TABLE_NAME, {})
        ..setWhereClause(
            '${INSP_EXEC_TASK.FIELD_INSP_ID}=$id AND ${INSP_EXEC_TASK.FIELD_TASK_ID}=$taskno'));
      if (results.isNotEmpty) {
        for (var result in results) {
          if (result[INSP_EXEC_TASK.FIELD_BLOB_VALUE] is Uint8List) {
            result[INSP_EXEC_TASK.FIELD_BLOB_VALUE] =
                base64Encode(result[INSP_EXEC_TASK.FIELD_BLOB_VALUE]);
          }
          inspExecTask = INSP_EXEC_TASK.fromJson(result);
        }
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getInspExecTaskByInspHeaderInspId', e.toString());
    }

    return inspExecTask;
  }

  static Future<List<CILT_EXEC_TASK>> getCiltExecHeaderListByCiltIdAndStatus(
      String id,
      {String? status}) async {
    List<CILT_EXEC_TASK> ciltExecHeader = [];

    try {
      List results = await AppDatabaseManager().select(DBInputEntity(
          CILT_EXEC_TASK.TABLE_NAME, {})
        ..setWhereClause(status == null
            ? '${CILT_EXEC_TASK.FIELD_CILT_ID}=$id'
            : "${CILT_EXEC_TASK.FIELD_CILT_ID}=$id AND ${CILT_EXEC_TASK.FIELD_STATUS}= '$status'"));
      if (results.isNotEmpty) {
        for (var result in results) {
          ciltExecHeader.add(CILT_EXEC_TASK.fromJson(result));
        }
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getCiltExecHeaderListByCiltIdAndStatus', e.toString());
    }

    return ciltExecHeader;
  }

  static Future<List<INSP_EXEC_TASK>> getInspExecHeaderListByInspIdAndStatus(
      String id,
      {String? status}) async {
    List<INSP_EXEC_TASK> inspExecHeader = [];

    try {
      List results = await AppDatabaseManager().select(DBInputEntity(
          INSP_EXEC_TASK.TABLE_NAME, {})
        ..setWhereClause(status == null
            ? '${INSP_EXEC_TASK.FIELD_INSP_ID}=$id'
            : "${INSP_EXEC_TASK.FIELD_INSP_ID}=$id AND ${INSP_EXEC_TASK.FIELD_STATUS}= '$status'"));
      if (results.isNotEmpty) {
        for (var result in results) {
          if (result[INSP_EXEC_TASK.FIELD_BLOB_VALUE] is Uint8List) {
            result[INSP_EXEC_TASK.FIELD_BLOB_VALUE] =
                base64Encode(result[INSP_EXEC_TASK.FIELD_BLOB_VALUE]);
          }
          inspExecHeader.add(INSP_EXEC_TASK.fromJson(result));
        }
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getInspExecHeaderListByInspIdAndStatus', e.toString());
    }

    return inspExecHeader;
  }

  static Future<List<CILT_EXEC_SEC>> getCiltExecSecListByCiltId(
      String id) async {
    List<CILT_EXEC_SEC> ciltExecSec = [];

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(CILT_EXEC_SEC.TABLE_NAME, {})
            ..setWhereClause('${CILT_EXEC_SEC.FIELD_CILT_ID}=$id'));
      if (results.isNotEmpty) {
        for (var result in results) {
          ciltExecSec.add(CILT_EXEC_SEC.fromJson(result));
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getCiltExecSecListByCiltId', e.toString());
    }

    return ciltExecSec;
  }

  static Future<List<INSP_EXEC_SEC>> getInspectionExecSecListByInspId(
      int id) async {
    List<INSP_EXEC_SEC> inspExecSec = [];

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(INSP_EXEC_SEC.TABLE_NAME, {})
            ..setWhereClause('${INSP_EXEC_SEC.FIELD_INSP_ID}=$id'));
      if (results.isNotEmpty) {
        for (var result in results) {
          inspExecSec.add(INSP_EXEC_SEC.fromJson(result));
        }
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getInspectionExecSecListByInspId', e.toString());
    }

    return inspExecSec;
  }

  static Future<List<CILT_EXEC_DOC>> getCiltExecDocumentByCiltId(
      String id) async {
    List<CILT_EXEC_DOC> ciltExecDoc = [];

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(CILT_EXEC_DOC.TABLE_NAME, {})
            ..setWhereClause('${CILT_EXEC_DOC.FIELD_CILT_ID}=$id'));
      if (results.isNotEmpty) {
        for (var result in results) {
          ciltExecDoc.add(CILT_EXEC_DOC.fromJson(result));
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getCiltExecDocumentByCiltId', e.toString());
    }

    return ciltExecDoc;
  }

  static Future<List<CILT_TASK>> getCiltTaskPlanListHeaderByPlanId(
      String planId) async {
    List<CILT_TASK> ciltPlanTaskHeaderList = [];

    try {
      String query = '${CILT_TASK.FIELD_PLAN_ID} = $planId';
      List results = await AppDatabaseManager().select(
          DBInputEntity(CILT_TASK.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        ciltPlanTaskHeaderList.add(CILT_TASK.fromJson(result));
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getCiltTaskPlanListHeaderByPlanId', e.toString());
    }
    return ciltPlanTaskHeaderList;
  }

  static Future<List<INSPECTION_TASK>> getInspectionTaskPlanListHeaderByPlanId(
      String planId,
      {bool onlyParent = false}) async {
    List<INSPECTION_TASK> inspectionPlanTaskHeaderList = [];
    try {
      String query;

      if (onlyParent) {
        query =
            '${INSPECTION_TASK.FIELD_PLAN_ID} = $planId AND (${INSPECTION_TASK.FIELD_DEPENDENT} IS NULL OR ${INSPECTION_TASK.FIELD_DEPENDENT} != "true")';
      } else {
        query = '${INSPECTION_TASK.FIELD_PLAN_ID} = $planId';
      }

      List results = await AppDatabaseManager().select(
          DBInputEntity(INSPECTION_TASK.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        inspectionPlanTaskHeaderList.add(INSPECTION_TASK.fromJson(result));
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getInspectionTaskPlanListHeaderByPlanId', e.toString());
    }
    return inspectionPlanTaskHeaderList;
  }

  static Future<List<CILT_TASK>> getNumberOfCiltTasks(String planId) async {
    List<CILT_TASK> ciltPlanTaskHeaderList = [];

    try {
      String query = '${CILT_TASK.FIELD_PLAN_ID} = $planId';
      List results = await AppDatabaseManager().select(
          DBInputEntity(CILT_TASK.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        ciltPlanTaskHeaderList.add(CILT_TASK.fromJson(result));
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getNumberOfCiltTasks', e.toString());
    }

    return ciltPlanTaskHeaderList;
  }

  static Future<List<INSPECTION_TASK>> getNumberOfInspTasks(
      String planId) async {
    List<INSPECTION_TASK> inspPlanTaskHeaderList = [];

    try {
      String query = '${INSPECTION_TASK.FIELD_PLAN_ID} = $planId';
      List results = await AppDatabaseManager().select(
          DBInputEntity(INSPECTION_TASK.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        inspPlanTaskHeaderList.add(INSPECTION_TASK.fromJson(result));
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getNumberOfInspTasks', e.toString());
    }

    return inspPlanTaskHeaderList;
  }

  static Future<void> deleteCiltExecHeader(CILT_EXEC_HEADER ciltHeader) async {
    try {
      await AppDatabaseManager().delete(
          DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, ciltHeader.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, 'deleteCiltPlanListHeader', e.toString());
    }
  }

  static Future<List<INSPECTION_PLAN_HEADER>>
      getInspectionPlanListHeader() async {
    List<INSPECTION_PLAN_HEADER> inspectionPlanHeaderList = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(INSPECTION_PLAN_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        inspectionPlanHeaderList.add(INSPECTION_PLAN_HEADER.fromJson(result));
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getInspectionPlanListHeader', e.toString());
    }

    return inspectionPlanHeaderList;
  }

  static Future<String?> gettingTitle(String id) async {
    String? title;
    String query =
        'SELECT ${CILT_PLAN_HEADER.FIELD_TITLE} FROM CILT_PLAN_HEADER WHERE PLAN_ID = $id';

    try {
      List results = await AppDatabaseManager().execute(query);

      for (var result in results) {
        title = result['TITLE'];
      }
    } catch (e) {
      Logger.logError(sourceClass, 'gettingTitle', e.toString());
    }

    return title;
  }

  Future<void> taskUpdate(
      {required CILT_TASK task,
      String? status,
      required CILT_EXEC_HEADER ciltHeader,
      String? skipreason,
      String? reasonCode,
      String? p_mode,
      String? comment}) async {
    try {
      String query =
          '${CILT_EXEC_HEADER.FIELD_PLAN_ID}= ${task.plan_id.toString()}';
      List header = await AppDatabaseManager().select(
          DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, {})
            ..setWhereClause(query));
      if (header.isNotEmpty) {
        for (Map<String, dynamic> data in header) {
          CILT_EXEC_HEADER cilt_header = CILT_EXEC_HEADER.fromJson(data);
          if (cilt_header.cilt_id == ciltHeader.cilt_id) {
            String query1 =
                '${CILT_EXEC_HEADER.FIELD_CILT_ID}= "${cilt_header.cilt_id.toString()}"';

            List execute = await AppDatabaseManager().select(
                DBInputEntity(CILT_EXEC_TASK.TABLE_NAME, {})
                  ..setWhereClause(query1));
            if (execute.isNotEmpty) {
              for (Map<String, dynamic> exec in execute) {
                CILT_EXEC_TASK execHeader = CILT_EXEC_TASK.fromJson(exec);
                if (execHeader.task_id.toString() == task.task_id.toString() &&
                    execHeader.section_id == task.section_id) {
                  execHeader.status = status;
                  execHeader.reason = reasonCode;
                  execHeader.skip_comments = skipreason;
                  execHeader.comments = comment ?? execHeader.comments;
                  execHeader.p_mode = p_mode;
                  await AppDatabaseManager().update(DBInputEntity(
                      CILT_EXEC_TASK.TABLE_NAME, execHeader.toJson()));
                }
              }
            }
          }
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, 'taskUpdate', e.toString());
    }
  }

  Future<void> taskInspectionUpdate(
      {required INSPECTION_TASK task,
      String? status,
      required INSP_EXEC_HEADER inspHeader,
      String? reasonCode,
      String? skipreason,
      String? p_mode,
      String? comment,
      String? dataString,
      double? dataDouble,
      Uint8List? dataBloc,
      String? isSkipped,
      String? isIrrelevant,
      String? isDiscrepant,
      bool isSkippingSection = false}) async {
    try {
      String query =
          '${INSP_EXEC_HEADER.FIELD_PLAN_ID}= ${task.plan_id.toString()}';
      List header = await AppDatabaseManager().select(
          DBInputEntity(INSP_EXEC_HEADER.TABLE_NAME, {})
            ..setWhereClause(query));
      if (header.isNotEmpty) {
        for (Map<String, dynamic> data in header) {
          INSP_EXEC_HEADER insp_header = INSP_EXEC_HEADER.fromJson(data);
          if (insp_header.insp_id == inspHeader.insp_id) {
            String query1 =
                '${INSP_EXEC_HEADER.FIELD_INSP_ID}= "${insp_header.insp_id.toString()}"';

            List execute = await AppDatabaseManager().select(
                DBInputEntity(INSP_EXEC_TASK.TABLE_NAME, {})
                  ..setWhereClause(query1));
            if (execute.isNotEmpty) {
              for (Map<String, dynamic> exec in execute) {
                if (exec[INSP_EXEC_TASK.FIELD_BLOB_VALUE] is Uint8List) {
                  exec[INSP_EXEC_TASK.FIELD_BLOB_VALUE] =
                      base64Encode(exec[INSP_EXEC_TASK.FIELD_BLOB_VALUE]);
                }
                INSP_EXEC_TASK execHeader = INSP_EXEC_TASK.fromJson(exec);

                if (execHeader.task_id.toString() == task.task_id.toString() &&
                    execHeader.section_id == task.section_id) {
                  execHeader.status = status ?? execHeader.status;
                  execHeader.reason = reasonCode;
                  execHeader.skip_comments = skipreason;
                  execHeader.comments = comment ?? execHeader.comments;
                  execHeader.p_mode = p_mode;
                  execHeader.is_skipped = isSkipped;
                  execHeader.is_irrelevant = isIrrelevant;
                  execHeader.is_discrepant = isDiscrepant;
                  if (!isSkippingSection) {
                    if (dataString != null) {
                      execHeader.str_value = dataString;
                    }
                  } else {
                    execHeader.str_value = null;
                  }
                  if (!isSkippingSection) {
                    if (dataDouble != null) {
                      execHeader.num_value = dataDouble;
                    }
                  } else {
                    execHeader.num_value = null;
                  }
                  if (!isSkippingSection) {
                    if (dataBloc != null) {
                      execHeader.blob_value = base64Encode(dataBloc);
                    }
                  } else {
                    execHeader.blob_value = null;
                  }
                  await AppDatabaseManager().update(DBInputEntity(
                    INSP_EXEC_TASK.TABLE_NAME,
                    execHeader.toJson(),
                  ));
                }
              }
            }
          }
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, 'taskInspectionUpdate', e.toString());
    }
  }

  static deleteCiltSection(
      List<CILT_TASK> tasks, List<CILT_SECTION> cilt_section) async {
    for (CILT_TASK task in tasks) {
      String query =
          "${CILT_TASK.FIELD_SECTION_ID} = '${task.section_id}' AND ${CILT_TASK.FIELD_PLAN_ID} = '${task.plan_id}' AND ${CILT_TASK.FIELD_TASK_ID} = '${task.task_id}'";
      try {
        bool result = await AppDatabaseManager().delete(
            DBInputEntity(CILT_TASK.TABLE_NAME, task.toJson())
              ..setWhereClause(query));
      } catch (e) {
        Logger.logError(sourceClass, 'deleteCiltSection', e.toString());
      }
    }
    try {
      for (var data in cilt_section) {
        await AppDatabaseManager()
            .delete(DBInputEntity(CILT_SECTION.TABLE_NAME, data.toJson()));
      }
      List list = await AppDatabaseManager()
          .select(DBInputEntity(CILT_TASK.TABLE_NAME, {}));
    } catch (e) {
      Logger.logError(sourceClass, 'deleteCiltSection', e.toString());
    }
  }

  static Future<List<FAULT_HEADER>> getFaultHeaderListByPlanId(
      String plantId, String? faultId) async {
    List<FAULT_HEADER> faultTypeList = [];
    String query = '${FAULT_HEADER.FIELD_PLANT_ID} = $plantId ';
    try {
      if (faultId != null) {
        query =
            '${FAULT_HEADER.FIELD_PLANT_ID} = $plantId AND ${FAULT_HEADER.FIELD_FAULT_ID} = $faultId ';
      }
      List results = await AppDatabaseManager().select(
          DBInputEntity(FAULT_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        FAULT_HEADER header = FAULT_HEADER.fromJson(result);
        faultTypeList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFaultHeaderListByPlanId', e.toString());
    }

    return faultTypeList;
  }

  static Future<List<FAULT_HEADER>> getFaultHeaderList(String plantId,
      {bool initial = false}) async {
    List<FAULT_HEADER> faultTypeList = [];
    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(FAULT_HEADER.TABLE_NAME, {})
            ..setWhereClause('${FAULT_HEADER.FIELD_PLANT_ID} = "$plantId"'));

      for (var result in results) {
        FAULT_HEADER header = FAULT_HEADER.fromJson(result);
        if (!initial) {
          if (header.description == null ||
              header.fault_type == null ||
              header.failure_mode == null ||
              header.priority == null ||
              header.location_id == null ||
              header.req_end == null) {
            String deleteQuery =
                'DELETE FROM ${FAULT_HEADER.TABLE_NAME} WHERE ${FAULT_HEADER.FIELD_FAULT_ID} = ${header.fault_id}';
            await AppDatabaseManager().execute(deleteQuery);
            continue;
          }
        }
        faultTypeList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFaultTypeList', e.toString());
    }

    return faultTypeList;
  }

  static Future<List<FAULT_ACTION>> getFaultActions() async {
    List<FAULT_ACTION> faultAction = [];
    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(FAULT_ACTION.TABLE_NAME, {}));

      for (var result in results) {
        FAULT_ACTION header = FAULT_ACTION.fromJson(result);
        faultAction.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFaultTypeList', e.toString());
    }

    return faultAction;
  }

  static Future<List<FAULT_DOCUMENT>> getFaultDocuments() async {
    List<FAULT_DOCUMENT> faultDocList = [];
    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(FAULT_DOCUMENT.TABLE_NAME, {}));

      for (var result in results) {
        FAULT_DOCUMENT header = FAULT_DOCUMENT.fromJson(result);
        faultDocList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFaultTypeList', e.toString());
    }

    return faultDocList;
  }

  static Future<FAULT_DOCUMENT?> getFaultDocumentsByFaultId(
      String faultId, String docID) async {
    FAULT_DOCUMENT? faultDoc;
    try {
      List results = await AppDatabaseManager().select(DBInputEntity(
          FAULT_DOCUMENT.TABLE_NAME, {})
        ..setWhereClause(
            '${FAULT_DOCUMENT.FIELD_FAULT_ID}="${faultId}" AND ${FAULT_DOCUMENT.FIELD_DOC_ID}="${docID}"'));

      for (var result in results) {
        FAULT_DOCUMENT doc = FAULT_DOCUMENT.fromJson(result);
        faultDoc = doc;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFaultTypeList', e.toString());
    }

    return faultDoc;
  }

  static Future<JOB_DOCUMENT?> getJobSingleDocumentsByJobId(
      String jobId, String docID) async {
    JOB_DOCUMENT? jobDoc;
    try {
      List results = await AppDatabaseManager().select(DBInputEntity(
          JOB_DOCUMENT.TABLE_NAME, {})
        ..setWhereClause(
            '${JOB_DOCUMENT.FIELD_JOB_ID}="$jobId" AND ${JOB_DOCUMENT.FIELD_DOC_ID}="$docID"'));

      for (var result in results) {
        JOB_DOCUMENT doc = JOB_DOCUMENT.fromJson(result);
        jobDoc = doc;
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getJobSingleDocumentsByJobId', e.toString());
    }

    return jobDoc;
  }

  static Future<List<JOB_DOCUMENT>> getJobDocuments() async {
    List<JOB_DOCUMENT> jobDocList = [];
    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(JOB_DOCUMENT.TABLE_NAME, {}));

      for (var result in results) {
        JOB_DOCUMENT header = JOB_DOCUMENT.fromJson(result);
        jobDocList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getJobDocuments', e.toString());
    }

    return jobDocList;
  }

  static Future<List<FAULT_HEADER>> getFilteredFaultHeaderList(
    List<String> typeList,
    List<String> priorityList,
    List<String> statusList,
  ) async {
    List<FAULT_HEADER> faultTypeList = [];
    List<String> conditions = [];
    if (typeList.isNotEmpty) {
      String faultTypeQuery =
          "${FAULT_HEADER.FIELD_FAULT_TYPE} IN (${typeList.map((e) => "'$e'").join(", ")})";
      conditions.add(faultTypeQuery);
    }

    if (priorityList.isNotEmpty) {
      String priorityQuery =
          "${FAULT_HEADER.FIELD_PRIORITY} IN (${priorityList.map((e) => "'$e'").join(", ")})";
      conditions.add(priorityQuery);
    }

    if (statusList.isNotEmpty) {
      String statusQuery =
          "${FAULT_HEADER.FIELD_STATUS} IN (${statusList.map((e) => "'${e.toUpperCase()}'").join(", ")})";
      conditions.add(statusQuery);
    }
    String query = conditions.isNotEmpty ? conditions.join(" AND ") : "1=1";
    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(FAULT_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        FAULT_HEADER header = FAULT_HEADER.fromJson(result);
        faultTypeList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFilteredFaultHeaderList', e.toString());
    }

    return faultTypeList;
  }

  static Future<List<FAULT_HEADER>> getSearchedFaultHeaderList(String data,
      {required String plantId, required List<String> plantSec}) async {
    List<FAULT_HEADER> faultTypeList = [];
    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(FAULT_HEADER.TABLE_NAME, {}));
      for (var result in results) {
        FAULT_HEADER header = FAULT_HEADER.fromJson(result);
        faultTypeList.add(header);
      }

      List<FAULT_TYPE_HEADER> faultTypes = await DbHelper.getFaultTypeList();
      List<PRIORITY_HEADER> priorityList = await getPriorityList();
      List<USER_HEADER> userHeaders = await getUsersData(plantId, plantSec);

      if (faultTypes.isEmpty) {
        return [];
      }
      if (priorityList.isEmpty) {
        return [];
      }

      if (userHeaders.isEmpty) {
        return [];
      }

      final lowerCaseQuery = cleanText(data);

      return faultTypeList.where((item) {
        final normalizedDescription = cleanText(item.description.toString());
        final normalizedFaultId = cleanText(item.fault_id.toString());

        FAULT_TYPE_HEADER? faultType;
        PRIORITY_HEADER? priorityHeader;
        USER_HEADER? userHeader;

        for (var element in faultTypes) {
          if (element.fault_code.toString() == item.fault_type.toString()) {
            faultType = element;
            break;
          }
        }
        for (var element in priorityList) {
          if (element.priority_code.toString() == item.priority.toString()) {
            priorityHeader = element;
            break;
          }
        }

        for (var element in userHeaders) {
          if (element.user_id.toString() == item.reported_by.toString()) {
            userHeader = element;
            break;
          }
        }

        faultType ??= FAULT_TYPE_HEADER(fault_code: '');
        priorityHeader ??= PRIORITY_HEADER(priority_code: '');
        userHeader ??= USER_HEADER(user_id: '');

        final normalizedFaultType = cleanText(faultType.description ?? '');
        final normalizedPriority = cleanText(priorityHeader.description ?? '');
        final normalizedDate =
            cleanText(UIHelper.formatDate(item.reported_on.toString()));
        final normalizedUser = cleanText(UIHelper().toCamelCase(
            '${userHeader.first_name.toString()} ${userHeader.last_name.toString()}'));

        return normalizedDescription.contains(lowerCaseQuery) ||
            normalizedFaultId.contains(lowerCaseQuery) ||
            normalizedFaultType.contains(lowerCaseQuery) ||
            normalizedPriority.contains(lowerCaseQuery) ||
            normalizedDate.contains(lowerCaseQuery) ||
            normalizedUser.contains(lowerCaseQuery);
      }).toList();
    } catch (e) {
      Logger.logError(sourceClass, 'getSearchedFaultHeaderList', e.toString());
    }

    return faultTypeList.isEmpty ? [] : faultTypeList;
  }

  static Future<List<FAULT_HEADER>> getFilteredAndSearchedFaultHeaderList({
    required List<String> typeList,
    required List<String> priorityList,
    required List<String> statusList,
    required String searchQuery,
    required String plantId,
    required List<String> plantSec,
  }) async {
    List<FAULT_HEADER> faultTypeList = [];

    // Build SQL WHERE clause
    List<String> conditions = [];
    if (typeList.isNotEmpty) {
      conditions.add(
          "${FAULT_HEADER.FIELD_FAULT_TYPE} IN (${typeList.map((e) => "'$e'").join(", ")})");
    }
    if (priorityList.isNotEmpty) {
      conditions.add(
          "${FAULT_HEADER.FIELD_PRIORITY} IN (${priorityList.map((e) => "'$e'").join(", ")})");
    }
    if (statusList.isNotEmpty) {
      conditions.add(
          "${FAULT_HEADER.FIELD_STATUS} IN (${statusList.map((e) => "'${e.toUpperCase()}'").join(", ")})");
    }
    String query = conditions.isNotEmpty ? conditions.join(" AND ") : "1=1";

    try {
      // Step 1: Filter
      List results = await AppDatabaseManager().select(
        DBInputEntity(FAULT_HEADER.TABLE_NAME, {})..setWhereClause(query),
      );

      for (var result in results) {
        faultTypeList.add(FAULT_HEADER.fromJson(result));
      }

      // Step 2: Search (if searchQuery is not empty)
      if (searchQuery.trim().isNotEmpty) {
        final faultTypes = await DbHelper.getFaultTypeList();
        final priorityHeaders = await getPriorityList();
        final userHeaders = await getUsersData(plantId, plantSec);

        final lowerCaseQuery = cleanText(searchQuery);

        faultTypeList = faultTypeList.where((item) {
          final normalizedDescription = cleanText(item.description.toString());
          final normalizedFaultId = cleanText(item.fault_id.toString());

          final faultType = faultTypes.firstWhere(
            (f) => f.fault_code.toString() == item.fault_type.toString(),
            orElse: () => FAULT_TYPE_HEADER(fault_code: ''),
          );

          final priority = priorityHeaders.firstWhere(
            (p) => p.priority_code.toString() == item.priority.toString(),
            orElse: () => PRIORITY_HEADER(priority_code: ''),
          );

          final user = userHeaders.firstWhere(
            (u) => u.user_id.toString() == item.reported_by.toString(),
            orElse: () => USER_HEADER(user_id: ''),
          );

          final normalizedFaultType = cleanText(faultType.description ?? '');
          final normalizedPriority = cleanText(priority.description ?? '');
          final normalizedDate =
              cleanText(UIHelper.formatDate(item.reported_on.toString()));
          final normalizedUser = cleanText(
              UIHelper().toCamelCase('${user.first_name} ${user.last_name}'));

          return normalizedDescription.contains(lowerCaseQuery) ||
              normalizedFaultId.contains(lowerCaseQuery) ||
              normalizedFaultType.contains(lowerCaseQuery) ||
              normalizedPriority.contains(lowerCaseQuery) ||
              normalizedDate.contains(lowerCaseQuery) ||
              normalizedUser.contains(lowerCaseQuery);
        }).toList();
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getFilteredAndSearchedFaultHeaderList', e.toString());
    }

    return faultTypeList;
  }

  static String cleanText(String text) {
    return text.replaceAll(RegExp(r'\s+'), ' ').trim().toLowerCase();
  }

  static Future<List<JOB_HEADER>> getSearchedJobCreationHeaderList(String data,
      {required String plantId, required List<String> plantSec}) async {
    List<JOB_HEADER> jobTypeList = [];
    try {
      jobTypeList = await getJobHeaderList(plantId);
      List<JOBTYPE_HEADER> jobTypes = await DbHelper.getJobTypeList();
      List<USER_HEADER> userHeaders = await getUsersData(plantId, plantSec);
      List<PRIORITY_HEADER> priorityList = await getPriorityList();

      if (jobTypes.isEmpty) {
        return [];
      }
      if (priorityList.isEmpty) {
        return [];
      }

      if (userHeaders.isEmpty) {
        return [];
      }

      final lowerCaseQuery = cleanText(data);

      return jobTypeList.where((item) {
        final normalizedDescription = cleanText(item.description.toString());
        final normalizedJobId = cleanText(item.job_id.toString());

        JOBTYPE_HEADER? jobType;
        PRIORITY_HEADER? priorityHeader;
        USER_HEADER? userHeader;

        for (var element in jobTypes) {
          if (element.job_type.toString() == item.job_type.toString()) {
            jobType = element;
            break;
          }
        }
        for (var element in priorityList) {
          if (element.priority_code.toString() == item.priority.toString()) {
            priorityHeader = element;
            break;
          }
        }

        for (var element in userHeaders) {
          if (element.user_id.toString() == item.assigned_to.toString()) {
            userHeader = element;
            break;
          }
        }

        jobType ??= JOBTYPE_HEADER(job_type: '');
        priorityHeader ??= PRIORITY_HEADER(priority_code: '');
        userHeader ??= USER_HEADER(user_id: '');

        final normalizedJobType = cleanText(jobType.description ?? '');
        final normalizedPriority = cleanText(priorityHeader.description ?? '');
        final normalizedDate =
            cleanText(UIHelper.formatDate(item.start_date.toString()));
        final normalizedUser = cleanText(UIHelper().toCamelCase(
            '${userHeader.first_name.toString()} ${userHeader.last_name.toString()}'));

        return normalizedDescription.contains(lowerCaseQuery) ||
            normalizedJobId.contains(lowerCaseQuery) ||
            normalizedJobType.contains(lowerCaseQuery) ||
            normalizedPriority.contains(lowerCaseQuery) ||
            normalizedDate.contains(lowerCaseQuery) ||
            normalizedUser.contains(lowerCaseQuery);
      }).toList();
    } catch (e) {
      Logger.logError(
          sourceClass, 'getSearchedJobCreationHeaderList', e.toString());
    }

    return jobTypeList;
  }

  static Future<List<JOB_HEADER>> getFilteredJobCreationHeaderList(
      List<String> typeList,
      List<String> priorityList,
      List<String> statusList,
      String plant) async {
    List<JOB_HEADER> jobTypeList = [];
    List<String> conditions = [];

    if (typeList.isNotEmpty) {
      String faultTypeQuery =
          "${JOB_HEADER.FIELD_JOB_TYPE} IN (${typeList.map((e) => "'$e'").join(", ")})";
      conditions.add(faultTypeQuery);
    }

    if (priorityList.isNotEmpty) {
      String priorityQuery =
          "${JOB_HEADER.FIELD_PRIORITY} IN (${priorityList.map((e) => "'$e'").join(", ")})";
      conditions.add(priorityQuery);
    }

    if (statusList.isNotEmpty) {
      String statusQuery =
          "${JOB_HEADER.FIELD_STATUS} IN (${statusList.map((e) => "'${e.toUpperCase()}'").join(", ")})";
      conditions.add(statusQuery);
    }
    String query = conditions.isNotEmpty ? conditions.join(" AND ") : "1=1";

    String combinedQuery =
        ' ${JOB_HEADER.FIELD_PLANT_ID} = "$plant" AND $query';

    try {
      USER_HEADER? user = await DbHelper.getUser();
      List results = await AppDatabaseManager().select(
          DBInputEntity(JOB_HEADER.TABLE_NAME, {})
            ..setWhereClause(combinedQuery));

      for (var result in results) {
        JOB_HEADER header = JOB_HEADER.fromJson(result);
        if (header.status != null && header.assigned_to != user!.user_id) {
          if ((header.status != Constants.JOB_STATE_OSNO) &&
              header.assigned_to != user.user_id) {
            continue;
          }
        }
        jobTypeList.add(header);
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getFilteredJobCreationHeaderList', e.toString());
    }

    return jobTypeList;
  }

  static Future<List<JOB_HEADER>> getFilteredAndSearchedJobHeaders({
    required String plantId,
    required List<String> plantSec,
    List<String> typeList = const [],
    List<String> priorityList = const [],
    List<String> statusList = const [],
    String searchText = '',
  }) async {
    List<JOB_HEADER> jobList = [];
    List<String> conditions = [];

    if (typeList.isNotEmpty) {
      conditions.add(
          "${JOB_HEADER.FIELD_JOB_TYPE} IN (${typeList.map((e) => "'$e'").join(", ")})");
    }

    if (priorityList.isNotEmpty) {
      conditions.add(
          "${JOB_HEADER.FIELD_PRIORITY} IN (${priorityList.map((e) => "'$e'").join(", ")})");
    }

    if (statusList.isNotEmpty) {
      conditions.add(
          "${JOB_HEADER.FIELD_STATUS} IN (${statusList.map((e) => "'${e.toUpperCase()}'").join(", ")})");
    }

    String whereClause = '${JOB_HEADER.FIELD_PLANT_ID} = "$plantId"';
    if (conditions.isNotEmpty) {
      whereClause += " AND ${conditions.join(" AND ")}";
    }

    try {
      final user = await DbHelper.getUser();
      final jobTypes = await DbHelper.getJobTypeList();
      final userHeaders = await getUsersData(plantId, plantSec);
      final priorityHeaders = await getPriorityList();

      final results = await AppDatabaseManager().select(
        DBInputEntity(JOB_HEADER.TABLE_NAME, {})..setWhereClause(whereClause),
      );

      jobList = results.map((e) => JOB_HEADER.fromJson(e)).toList();

      if (searchText.trim().isEmpty) return jobList;

      // In-memory search
      final query = cleanText(searchText);

      var jobIdExists = jobList.any((header) => header.job_id == 1279);

      Logger.logInfo('JobHeaderListNotifier', 'fetchJobHeaderList',
          'Job ID 1279 exists: $jobIdExists');

      return jobList.where((item) {
        final description = cleanText(item.description.toString());
        final jobId = cleanText(item.job_id.toString());

        final jobType = jobTypes.firstWhere((j) => j.job_type == item.job_type,
            orElse: () => JOBTYPE_HEADER(job_type: ''));

        final priority = priorityHeaders.firstWhere(
            (p) => p.priority_code == item.priority,
            orElse: () => PRIORITY_HEADER(priority_code: ''));

        final assignedUser = userHeaders.firstWhere(
            (u) => u.user_id == item.assigned_to,
            orElse: () => USER_HEADER(user_id: ''));

        final jobTypeDesc = cleanText(jobType.description.toString());
        final priorityDesc = cleanText(priority.description.toString());
        final date = cleanText(UIHelper.formatDate(item.start_date.toString()));
        final userName = cleanText(UIHelper().toCamelCase(
            '${assignedUser.first_name ?? ''} ${assignedUser.last_name ?? ''}'));

        return description.contains(query) ||
            jobId.contains(query) ||
            jobTypeDesc.contains(query) ||
            priorityDesc.contains(query) ||
            date.contains(query) ||
            userName.contains(query);
      }).toList();
    } catch (e) {
      Logger.logError(
          sourceClass, 'getFilteredAndSearchedJobHeaders', e.toString());
      return [];
    }
  }

  static Future<FAULT_HEADER?> getFaultHeader(
      String plantId, String? faultId) async {
    FAULT_HEADER? faultHeader;
    String query =
        '${FAULT_HEADER.FIELD_PLANT_ID} = $plantId AND ${FAULT_HEADER.FIELD_FAULT_ID} = $faultId';
    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(FAULT_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        faultHeader = FAULT_HEADER.fromJson(result[0]);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFaultTypeList', e.toString());
    }

    return faultHeader;
  }

  static Future<FAULT_HEADER?> getFaultHeaderByTaskNo(String taskNo) async {
    FAULT_HEADER? faultHeader;
    String query = '${FAULT_HEADER.FIELD_CILT_TASK_ID} = $taskNo';
    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(FAULT_HEADER.TABLE_NAME, {})..setWhereClause(query));
      if (results.isNotEmpty) {
        for (Map<String, dynamic> result in results) {
          faultHeader = FAULT_HEADER.fromJson(result);
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFaultHeaderByTaskNo', e.toString());
    }

    return faultHeader;
  }

  static Future<FAULT_HEADER?> getFaultHeaderByTaskNoForInsp(
      String taskNo) async {
    FAULT_HEADER? faultHeader;
    String query = '${FAULT_HEADER.FIELD_INSP_TASK_ID} = $taskNo';
    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(FAULT_HEADER.TABLE_NAME, {})..setWhereClause(query));
      if (results.isNotEmpty) {
        for (Map<String, dynamic> result in results) {
          faultHeader = FAULT_HEADER.fromJson(result);
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFaultHeaderByTaskNo', e.toString());
    }

    return faultHeader;
  }

  static Future<FAULT_HEADER?> getFaultHeaderByFaultId(String faultId) async {
    FAULT_HEADER? faultHeader;
    String query = "${FAULT_HEADER.FIELD_FAULT_ID} = '$faultId'";
    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(FAULT_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        faultHeader = FAULT_HEADER.fromJson(result);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFaultHeaderByFaultId', e.toString());
    }

    return faultHeader;
  }

  static Future<FAULT_HEADER?> getFaultHeaderByJobId(String jobId) async {
    FAULT_HEADER? faultHeader;
    String query = "${FAULT_HEADER.FIELD_FAULT_ID} = '$jobId'";
    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(FAULT_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        faultHeader = FAULT_HEADER.fromJson(result);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFaultHeaderByFaultId', e.toString());
    }

    return faultHeader;
  }

  static Future<FAULT_HEADER?> updateFault(FAULT_HEADER faultHeader) async {
    try {
      await AppDatabaseManager()
          .update(DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, 'updateFault', e.toString());
    }

    return faultHeader;
  }

  static Future<List<PRIORITY_HEADER>> getPriorityList() async {
    List<PRIORITY_HEADER> priorityList = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(PRIORITY_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        PRIORITY_HEADER header = PRIORITY_HEADER.fromJson(result);
        priorityList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getPriorityList', e.toString());
    }
    return priorityList;
  }

  static Future<PRIORITY_HEADER?> getPriority(String data) async {
    PRIORITY_HEADER? priority;

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(PRIORITY_HEADER.TABLE_NAME, {})
            ..setWhereClause('${PRIORITY_HEADER.FIELD_DESCRIPTION}="$data"'));

      for (var result in results) {
        PRIORITY_HEADER header = PRIORITY_HEADER.fromJson(result);
        priority = header;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getPriority', e.toString());
    }
    return priority;
  }

  static Future<List<SKIP_REASON_HEADER>> getSkipReasonList() async {
    List<SKIP_REASON_HEADER> skipReasonList = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(SKIP_REASON_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        SKIP_REASON_HEADER header = SKIP_REASON_HEADER.fromJson(result);
        skipReasonList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getSkipReasonList', e.toString());
    }
    return skipReasonList;
  }

  static Future<SKIP_REASON_HEADER?> getSkipReason(String data) async {
    SKIP_REASON_HEADER? priority;

    try {
      List results = await AppDatabaseManager().select(DBInputEntity(
          SKIP_REASON_HEADER.TABLE_NAME, {})
        ..setWhereClause('${SKIP_REASON_HEADER.FIELD_DESCRIPTION}="$data"'));

      for (var result in results) {
        SKIP_REASON_HEADER header = SKIP_REASON_HEADER.fromJson(result);
        priority = header;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getSkipReason', e.toString());
    }
    return priority;
  }

  static Future<List<FAULT_TYPE_HEADER>> getFaultTypeList() async {
    List<FAULT_TYPE_HEADER> faultTypeList = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(FAULT_TYPE_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        FAULT_TYPE_HEADER header = FAULT_TYPE_HEADER.fromJson(result);
        faultTypeList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFaultTypeList', e.toString());
    }
    return faultTypeList;
  }

  static Future<FAULT_TYPE_HEADER?> getFaultType(String data) async {
    FAULT_TYPE_HEADER? faultType;

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(FAULT_TYPE_HEADER.TABLE_NAME, {})
            ..setWhereClause('${FAULT_TYPE_HEADER.FIELD_DESCRIPTION}="$data"'));

      for (var result in results) {
        FAULT_TYPE_HEADER header = FAULT_TYPE_HEADER.fromJson(result);
        faultType = header;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFaultType', e.toString());
    }
    return faultType;
  }

  static Future<List<JOBTYPE_HEADER>> getJobTypeList() async {
    List<JOBTYPE_HEADER> jobTypeList = [];

    try {
      // Filter out inactive job types (INACTIVE != "X")
      String whereClause = '${JOBTYPE_HEADER.FIELD_INACTIVE} != "X" OR ${JOBTYPE_HEADER.FIELD_INACTIVE} IS NULL';
      List results = await AppDatabaseManager()
          .select(DBInputEntity(JOBTYPE_HEADER.TABLE_NAME, {})..setWhereClause(whereClause));

      for (var result in results) {
        JOBTYPE_HEADER header = JOBTYPE_HEADER.fromJson(result);
        jobTypeList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getJobTypeList', e.toString());
    }
    return jobTypeList;
  }

  static Future<JOBTYPE_HEADER?> getJobType(String data) async {
    JOBTYPE_HEADER? faultType;

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(JOBTYPE_HEADER.TABLE_NAME, {})
            ..setWhereClause('${JOBTYPE_HEADER.FIELD_DESCRIPTION}="$data"'));

      for (var result in results) {
        JOBTYPE_HEADER header = JOBTYPE_HEADER.fromJson(result);
        faultType = header;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getJobType', e.toString());
    }
    return faultType;
  }

  static Future<List<FAILURE_MODE_HEADER>> getFaultModeList() async {
    List<FAILURE_MODE_HEADER> faultModeList = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(FAILURE_MODE_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        FAILURE_MODE_HEADER header = FAILURE_MODE_HEADER.fromJson(result);
        faultModeList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFaultModeList', e.toString());
    }
    return faultModeList;
  }

  static Future<FAILURE_MODE_HEADER?> getFaultMode(String data) async {
    FAILURE_MODE_HEADER? faultMode;

    try {
      List results = await AppDatabaseManager().select(DBInputEntity(
          FAILURE_MODE_HEADER.TABLE_NAME, {})
        ..setWhereClause('${FAILURE_MODE_HEADER.FIELD_DESCRIPTION}="$data"'));

      for (var result in results) {
        FAILURE_MODE_HEADER header = FAILURE_MODE_HEADER.fromJson(result);
        faultMode = header;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFaultMode', e.toString());
    }
    return faultMode;
  }

  static Future<List<DOCUMENT_HEADER>> getDocumentHeaders() async {
    List<DOCUMENT_HEADER> docHeaderList = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        DOCUMENT_HEADER header = DOCUMENT_HEADER.fromJson(result);
        docHeaderList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getDocumentHeaders', e.toString());
    }
    return docHeaderList;
  }

  static Future<List<DOCUMENT_HEADER>> getDocumentHeadersByFid(
      String fid) async {
    List<DOCUMENT_HEADER> docHeaderList = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        DOCUMENT_HEADER header = DOCUMENT_HEADER.fromJson(result);
        docHeaderList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getDocumentHeaders', e.toString());
    }
    return docHeaderList;
  }

  static Future<DOCUMENT_HEADER?> getDocumentHeaderById(String docId) async {
    DOCUMENT_HEADER? documentHeader;
    try {
      List results = await AppDatabaseManager().select(
        DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, {})
          ..setWhereClause('${DOCUMENT_HEADER.FIELD_DOC_ID}="$docId"'),
      );
      if (results.isNotEmpty) {
        documentHeader = DOCUMENT_HEADER.fromJson(results.first);
      }
      return documentHeader;
    } catch (e) {
      Logger.logError(sourceClass, 'getDocumentHeaderById', e.toString());
    }
    return documentHeader;
  }

  static Future<List<DOCUMENT_ATTACHMENT>> getDocumentAttachments() async {
    List<DOCUMENT_ATTACHMENT> docAttachmentList = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(DOCUMENT_ATTACHMENT.TABLE_NAME, {}));

      for (var result in results) {
        DOCUMENT_ATTACHMENT header = DOCUMENT_ATTACHMENT.fromJson(result);
        docAttachmentList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getDocumentAttachments', e.toString());
    }
    return docAttachmentList;
  }

  static Future<List<DOCUMENT_HEADER>> getDocumentHeadersByDocId(
      String docId) async {
    List<DOCUMENT_HEADER> docHeaderList = [];

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, {})
            ..setWhereClause('${DOCUMENT_HEADER.FIELD_DOC_ID}=$docId}'));

      for (var result in results) {
        DOCUMENT_HEADER header = DOCUMENT_HEADER.fromJson(result);
        docHeaderList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getDocumentHeaders', e.toString());
    }
    return docHeaderList;
  }

  static Future<DOCUMENT_ATTACHMENT?> getDocumentAttachmentsByUid(
      String uid) async {
    DOCUMENT_ATTACHMENT? docAttachmentList;

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(DOCUMENT_ATTACHMENT.TABLE_NAME, {})
            ..setWhereClause('${DOCUMENT_ATTACHMENT.FIELD_UID}="$uid"'));

      for (var result in results) {
        DOCUMENT_ATTACHMENT header = DOCUMENT_ATTACHMENT.fromJson(result);
        docAttachmentList = header;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getDocumentAttachments', e.toString());
    }
    return docAttachmentList;
  }

  static Future<DOCUMENT_HEADER?> getDocumentHeadersByDocsId(
      String docID) async {
    DOCUMENT_HEADER? docHeaderList;

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, {})
            ..setWhereClause('${DOCUMENT_HEADER.FIELD_DOC_ID}="$docID"'));

      for (var result in results) {
        DOCUMENT_HEADER header = DOCUMENT_HEADER.fromJson(result);
        docHeaderList = header;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getDocumentHeaderssByDocId', e.toString());
    }
    return docHeaderList;
  }

  static Future<FAULT_ACTION?> getFaultActionByFaultId(String faultId) async {
    FAULT_ACTION? faultAction;

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(FAULT_ACTION.TABLE_NAME, {})
            ..setWhereClause('${FAULT_ACTION.FIELD_FAULT_ID}="$faultId"'));
      for (var result in results) {
        FAULT_ACTION header = FAULT_ACTION.fromJson(result);
        faultAction = header;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFaultActionByFaultId', e.toString());
    }
    return faultAction;
  }

  static Future<JOB_ACTION?> getJobActionByJobId(String jobId) async {
    JOB_ACTION? jobAction;

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(JOB_ACTION.TABLE_NAME, {})
            ..setWhereClause('${JOB_ACTION.FIELD_JOB_ID}="$jobId"'));
      for (var result in results) {
        JOB_ACTION header = JOB_ACTION.fromJson(result);
        jobAction = header;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getJobActionByJobId', e.toString());
    }
    return jobAction;
  }

  static Future<List<FAULT_DOCUMENT>> getFaultDocumentByFaultId(
      String faultId) async {
    List<FAULT_DOCUMENT> faultDoc = [];

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(FAULT_DOCUMENT.TABLE_NAME, {})
            ..setWhereClause('${FAULT_DOCUMENT.FIELD_FAULT_ID}="$faultId"'));

      for (var result in results) {
        FAULT_DOCUMENT header = FAULT_DOCUMENT.fromJson(result);
        faultDoc.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getDocumentAttachments', e.toString());
    }
    return faultDoc;
  }

  static Future<List<JOB_DOCUMENT>> getJobDocumentByJobId(String jobId) async {
    List<JOB_DOCUMENT> jobDoc = [];

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(JOB_DOCUMENT.TABLE_NAME, {})
            ..setWhereClause('${JOB_DOCUMENT.FIELD_JOB_ID}="$jobId"'));

      for (var result in results) {
        JOB_DOCUMENT header = JOB_DOCUMENT.fromJson(result);
        jobDoc.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getJobDocumentByJobId', e.toString());
    }
    return jobDoc;
  }

  static Future<List<DOCUMENT_ATTACHMENT>> getDocumentAttachmentsByFid(
      String fid) async {
    List<DOCUMENT_ATTACHMENT> attachments = [];

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(DOCUMENT_ATTACHMENT.TABLE_NAME, {})
            ..setWhereClause('$FieldFid="$fid"'));

      for (var result in results) {
        DOCUMENT_ATTACHMENT attachment = DOCUMENT_ATTACHMENT.fromJson(result);
        attachments.add(attachment);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getDocumentByUId', e.toString());
    }
    return attachments;
  }

  static Future<JOB_HEADER?> getJobHeaderById(String id) async {
    JOB_HEADER? jobHeader;

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(JOB_HEADER.TABLE_NAME, {})
            ..setWhereClause('${JOB_HEADER.FIELD_JOB_ID}="$id"'));

      for (var result in results) {
        JOB_HEADER header = JOB_HEADER.fromJson(result);
        jobHeader = header;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getJobHeaderById', e.toString());
    }
    return jobHeader;
  }

  static Future<JOB_HEADER?> getJobHeaderByFautlId(String id) async {
    JOB_HEADER? jobHeader;

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(JOB_HEADER.TABLE_NAME, {})
            ..setWhereClause('${JOB_HEADER.FIELD_FAULT_ID}="$id"'));

      for (var result in results) {
        JOB_HEADER header = JOB_HEADER.fromJson(result);
        jobHeader = header;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getJobHeaderById', e.toString());
    }
    return jobHeader;
  }

  static Future<List<PLANT_HEADER>> getAllPlantsList() async {
    List<PLANT_HEADER> plants = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(PLANT_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        PLANT_HEADER header = PLANT_HEADER.fromJson(result);
        plants.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getPlantsList', e.toString());
    }
    return plants;
  }

  static Future<USER_HEADER?> getUser() async {
    USER_HEADER? userHeader;
    String user = await SettingsHelper().getUserName();
    String query = '${USER_HEADER.FIELD_USER_ID}="${user.trim()}"';

    try {
      List results = await AppDatabaseManager().select(
        DBInputEntity(USER_HEADER.TABLE_NAME, {})..setWhereClause(query),
      );

      for (var result in results) {
        USER_HEADER header = USER_HEADER.fromJson(result);
        userHeader = header;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getUser', e.toString());
    }

    return userHeader;
  }

  static Future<ROLE_HEADER?> getRole() async {
    ROLE_HEADER? roleHeader;
    String? role;
    USER_HEADER? user = await getUser();
    if (user != null) {
      role = user.role;
    }
    if (role != null) {
      String query = '${ROLE_HEADER.FIELD_ROLE_ID}="$role"';
      try {
        List results = await AppDatabaseManager().select(
            DBInputEntity(ROLE_HEADER.TABLE_NAME, {})..setWhereClause(query));

        for (var result in results) {
          ROLE_HEADER header = ROLE_HEADER.fromJson(result);
          roleHeader = header;
        }
      } catch (e) {
        Logger.logError(sourceClass, 'getRole', e.toString());
      }
    }
    return roleHeader;
  }

  static Future<List<SYSTEM_CONDITION_HEADER>> getSystemConditions() async {
    List results = await AppDatabaseManager()
        .select(DBInputEntity(SYSTEM_CONDITION_HEADER.TABLE_NAME, {}));
    return results.map((e) => SYSTEM_CONDITION_HEADER.fromJson(e)).toList();
  }

  static Future<List<USER_HEADER>> searchUserByName(String searchString) async {
    List<USER_HEADER> filteredUsers = [];
    try {
      String query =
          '${USER_HEADER.FIELD_FIRST_NAME} LIKE "%$searchString%" OR ${USER_HEADER.FIELD_LAST_NAME} LIKE "%$searchString%"';
      List results = await AppDatabaseManager().select(
          DBInputEntity(USER_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        if (result[USER_HEADER.FIELD_THUMBNAIL] is Uint8List) {
          result[USER_HEADER.FIELD_THUMBNAIL] =
              base64Encode(result[USER_HEADER.FIELD_THUMBNAIL]);
        }
        USER_HEADER user = USER_HEADER.fromJson(result);
        filteredUsers.add(user);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'searchUserByName', e.toString());
    }
    return filteredUsers;
  }

  static Future<List<USER_PLANT>> getPlantLists(userId) async {
    List<USER_PLANT> plants = [];
    String query = "${USER_PLANT.FIELD_USER_ID} = UPPER('${userId}')";

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(USER_PLANT.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {}

      for (var result in results) {
        USER_PLANT header = USER_PLANT.fromJson(result);
        plants.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getPlantList', e.toString());
    }
    return plants;
  }

  static Future<PLANT_HEADER?> getUserPlant(plantId) async {
    PLANT_HEADER? plants;
    String query = '${PLANT_HEADER.FIELD_PLANT_ID}="$plantId"';
    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(PLANT_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        PLANT_HEADER header = PLANT_HEADER.fromJson(result);
        plants = header;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getUserPlantsList', e.toString());
    }
    return plants;
  }

  static Future<List<PLANT_SECTION_HEADER>> getPlantsSectionList() async {
    List<PLANT_SECTION_HEADER> plantsSection = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(PLANT_SECTION_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        PLANT_SECTION_HEADER header = PLANT_SECTION_HEADER.fromJson(result);
        plantsSection.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getPlantsSectionList', e.toString());
    }
    return plantsSection;
  }

  static Future<List<PLANT_SECTION_HEADER>> getUserPlantsSectionList(
      String plantId) async {
    List<PLANT_SECTION_HEADER> plantsSection = [];

    try {
      String query = '${PLANT_SECTION_HEADER.FIELD_PLANT_ID}="$plantId"';
      List results = await AppDatabaseManager().select(
          DBInputEntity(PLANT_SECTION_HEADER.TABLE_NAME, {})
            ..setWhereClause(query));

      for (var result in results) {
        PLANT_SECTION_HEADER header = PLANT_SECTION_HEADER.fromJson(result);
        plantsSection.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getUserPlantsSectionList', e.toString());
    }
    return plantsSection;
  }

  static Future<List<ASSET_HEADER>> getAssetHeaderList(String plantId) async {
    List<ASSET_HEADER> assetList = [];
    // Filter out inactive assets (INACTIVE != "X") and match plant ID
    String query = "${ASSET_HEADER.FIELD_PLANT_ID}='$plantId' AND (${ASSET_HEADER.FIELD_INACTIVE} != 'X' OR ${ASSET_HEADER.FIELD_INACTIVE} IS NULL)";

    try {
      final results = await AppDatabaseManager().select(
          DBInputEntity(ASSET_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        ASSET_HEADER header = ASSET_HEADER.fromJson(result);
        assetList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getAssetHeaderList', e.toString());
    }
    return assetList;
  }

  static Future<List<KPI_HEADER>> getKPIHeaderList(String plantId) async {
    List<KPI_HEADER> kpiList = [];
    String query = "${KPI_HEADER.FIELD_PLANT_ID}='$plantId'";

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(KPI_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        KPI_HEADER header = KPI_HEADER.fromJson(result);
        kpiList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'geKPIHeaderList', e.toString());
    }
    return kpiList;
  }

  static Future<KPI_HEADER?> getKPIHeaderByKpiId(
      {required String plantId, required String kpiId}) async {
    KPI_HEADER? kpi;
    String query =
        "${KPI_HEADER.FIELD_PLANT_ID}='$plantId' AND ${KPI_HEADER.FIELD_KPI_ID}='$kpiId'";

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(KPI_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        KPI_HEADER header = KPI_HEADER.fromJson(result);
        kpi = header;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getKPIHeaderByKpiId', e.toString());
    }
    return kpi;
  }

  static Future<List<LOCATION_HEADER>> getLocationHeaderList(
      String plantId) async {
    List<LOCATION_HEADER> location_list = [];
    // Filter out inactive locations (INACTIVE != "X") and match plant ID
    String query = "${LOCATION_HEADER.FIELD_PLANT_ID}='$plantId' AND (${LOCATION_HEADER.FIELD_INACTIVE} != 'X' OR ${LOCATION_HEADER.FIELD_INACTIVE} IS NULL)";
    try {
      final results = await AppDatabaseManager().select(
          DBInputEntity(LOCATION_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        LOCATION_HEADER header = LOCATION_HEADER.fromJson(result);
        location_list.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getLocationHeaderList', e.toString());
    }
    return location_list;
  }

  static Future<List<JOB_HEADER>> getJobHeaderList(String plant,
      {bool initial = false}) async {
    List<JOB_HEADER> jobHeaders = [];

    try {
      USER_HEADER? user = await DbHelper.getUser();
      String query =
          'SELECT * FROM ${JOB_HEADER.TABLE_NAME} WHERE ${JOB_HEADER.FIELD_PLANT_ID} = "$plant"';
      List results = await AppDatabaseManager().execute(query);

      for (var result in results) {
        JOB_HEADER header = JOB_HEADER.fromJson(result);
        if (!initial) {
          if (header.description == null ||
              header.job_type == null ||
              header.details == null ||
              header.assigned_to == null ||
              header.end_date == null) {
            String deleteQuery =
                'DELETE FROM ${JOB_HEADER.TABLE_NAME} WHERE ${JOB_HEADER.FIELD_JOB_ID} = ${header.job_id}';
            await AppDatabaseManager().execute(deleteQuery);
            continue;
          }
        }
        jobHeaders.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getJobHeaderList', e.toString());
    }
    return jobHeaders;
  }

  static Future<List<ASSET_DOCUMENT>> getAssetDocument(
      ASSET_HEADER asset_header) async {
    List<ASSET_DOCUMENT> docList = [];

    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(ASSET_DOCUMENT.TABLE_NAME, {})
            ..setWhereClause(
                "${ASSET_DOCUMENT.FIELD_ASSET_NO} = ${asset_header.asset_no}"));

      for (var result in results) {
        ASSET_DOCUMENT header = ASSET_DOCUMENT.fromJson(result);
        docList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getAssetDocument', e.toString());
    }
    return docList;
  }

  static Future<List<LOCATION_DOCUMENT>> getFlocDocument(
      LOCATION_HEADER floc) async {
    List<LOCATION_DOCUMENT> docList = [];

    try {
      List results = await AppDatabaseManager().select(DBInputEntity(
          LOCATION_DOCUMENT.TABLE_NAME, {})
        ..setWhereClause(
            "${LOCATION_DOCUMENT.FIELD_LOCATION_ID}='${floc.location_id}'"));

      for (var result in results) {
        LOCATION_DOCUMENT header = LOCATION_DOCUMENT.fromJson(result);
        docList.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getFlocDocument', e.toString());
    }
    return docList;
  }

  static Future<List<INSPECTION_SECTION>> getInspectionSectionPlanListHeader(
      String id) async {
    List<INSPECTION_SECTION> inspectionPlanSectionHeaderList = [];

    try {
      String query = 'PLAN_ID = $id';
      List results = await AppDatabaseManager().select(
          DBInputEntity(INSPECTION_SECTION.TABLE_NAME, {})
            ..setWhereClause(query));
      for (var result in results) {
        inspectionPlanSectionHeaderList
            .add(INSPECTION_SECTION.fromJson(result));
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getInspectionSectionPlanListHeader', e.toString());
    }

    return inspectionPlanSectionHeaderList;
  }

  static Future<List<INSPECTION_TASK>> getInspectionTaskPlanListHeader(
      String planId, String sectionId) async {
    List<INSPECTION_TASK> inspectionPlanTaskHeaderList = [];

    try {
      String query =
          '${INSPECTION_TASK.FIELD_PLAN_ID} = $planId AND ${INSPECTION_TASK.FIELD_SECTION_ID} = $sectionId AND (${INSPECTION_TASK.FIELD_DEPENDENT} IS NULL OR ${INSPECTION_TASK.FIELD_DEPENDENT} != "true")';

      List results = await AppDatabaseManager().select(
          DBInputEntity(INSPECTION_TASK.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        inspectionPlanTaskHeaderList.add(INSPECTION_TASK.fromJson(result));
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getInspectionTaskPlanListHeader', e.toString());
    }

    return inspectionPlanTaskHeaderList;
  }

  static Future<List<INSPECTION_TASK>>
      getInspectionTaskPlanListHeaderWithoutSection(String planId) async {
    List<INSPECTION_TASK> inspectionPlanTaskHeaderList = [];

    try {
      String query =
          '${INSPECTION_TASK.FIELD_PLAN_ID} = $planId AND (${INSPECTION_TASK.FIELD_DEPENDENT} IS NULL OR ${INSPECTION_TASK.FIELD_DEPENDENT} != "true")';

      List results = await AppDatabaseManager().select(
          DBInputEntity(INSPECTION_TASK.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        inspectionPlanTaskHeaderList.add(INSPECTION_TASK.fromJson(result));
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getInspectionTaskPlanListHeader', e.toString());
    }

    return inspectionPlanTaskHeaderList;
  }

  static Future<List<INSPECTION_TASK>> getInspectionTaskPlanListHeaderAll(
      String planId, String sectionId) async {
    List<INSPECTION_TASK> inspectionPlanTaskHeaderList = [];

    try {
      String query =
          '${INSPECTION_TASK.FIELD_PLAN_ID} = $planId AND ${INSPECTION_TASK.FIELD_SECTION_ID} = $sectionId';

      List results = await AppDatabaseManager().select(
          DBInputEntity(INSPECTION_TASK.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        inspectionPlanTaskHeaderList.add(INSPECTION_TASK.fromJson(result));
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getInspectionTaskPlanListHeaderAll', e.toString());
    }

    return inspectionPlanTaskHeaderList;
  }

  static Future<List<INSP_EXEC_TASK>> getInspectionExeTaskPlanListHeaderAll(
      int inspId, String sectionId) async {
    List<INSP_EXEC_TASK> inspExecTaskHeaderList = [];

    try {
      String query =
          '${INSP_EXEC_TASK.FIELD_INSP_ID} = $inspId AND ${INSP_EXEC_TASK.FIELD_SECTION_ID} = $sectionId';

      List results = await AppDatabaseManager().select(
          DBInputEntity(INSP_EXEC_TASK.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        inspExecTaskHeaderList.add(INSP_EXEC_TASK.fromJson(result));
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getInspectionExeTaskPlanListHeaderAll', e.toString());
    }

    return inspExecTaskHeaderList;
  }

  static Future<List<INSPECTION_TASK>> getInspectionTaskPlanListHeaderDependent(
      String planId, INSPECTION_TASK task) async {
    List<INSPECTION_TASK> inspectionPlanTaskHeaderList = [];

    try {
      String query =
          '${INSPECTION_TASK.FIELD_PLAN_ID} = $planId AND ${INSPECTION_TASK.FIELD_SECTION_ID} = ${task.section_id} AND ${INSPECTION_TASK.FIELD_DEPENDENT} == "true"  AND ${INSPECTION_TASK.FIELD_DEP_TASK_ID} = ${task.task_id}';

      List results = await AppDatabaseManager().select(
          DBInputEntity(INSPECTION_TASK.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        inspectionPlanTaskHeaderList.add(INSPECTION_TASK.fromJson(result));
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getInspectionTaskPlanListHeaderDependent',
          e.toString());
    }

    return inspectionPlanTaskHeaderList;
  }

  static Future<List<SHIFT_HEADER>> getShiftList() async {
    List<SHIFT_HEADER> shifts = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(SHIFT_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        SHIFT_HEADER header = SHIFT_HEADER.fromJson(result);
        shifts.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getShiftList', e.toString());
    }
    return shifts;
  }

  static Future<List<SHIFT_HEADER>> getUserShiftList(String plantId) async {
    List<SHIFT_HEADER> shifts = [];

    try {
      String query = '${SHIFT_HEADER.FIELD_PLANT_ID}="$plantId"';
      List results = await AppDatabaseManager().select(
          DBInputEntity(SHIFT_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        SHIFT_HEADER header = SHIFT_HEADER.fromJson(result);
        shifts.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getUserShiftList', e.toString());
    }
    return shifts;
  }

  static Future<List<String>> getLocationList(String plantId) async {
    List<String> locations = [];

    String query = "${LOCATION_HEADER.FIELD_PLANT_ID}='$plantId'";
    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(LOCATION_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        LOCATION_HEADER header = LOCATION_HEADER.fromJson(result);
        locations.add(header.location_id.toString());
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getLocationList', e.toString());
    }
    return locations;
  }

  static Future<LOCATION_HEADER?> getLocationUsingParentLocFromAsset(
      String plantId) async {
    LOCATION_HEADER? location;

    String query = "${LOCATION_HEADER.FIELD_LOCATION_ID}='$plantId'";
    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(LOCATION_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        LOCATION_HEADER header = LOCATION_HEADER.fromJson(result);
        location = header;
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getLocationUsingParentLocFromAsset', e.toString());
    }
    return location;
  }

  static Future<List<String>> getAssetList(String plantId) async {
    List<String> assets = [];

    String query = "${ASSET_HEADER.FIELD_PLANT_ID}='$plantId'";
    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(ASSET_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        ASSET_HEADER header = ASSET_HEADER.fromJson(result);
        assets.add(header.asset_no.toString());
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getAssetList', e.toString());
    }
    return assets;
  }

  static Future<List<ASSET_HEADER>> getAssetLocList(String loc) async {
    List<ASSET_HEADER> assets = [];
    String query = "${ASSET_HEADER.FIELD_PARENT_LOC_ID}='$loc'";
    try {
      List results = await AppDatabaseManager().select(
          DBInputEntity(ASSET_HEADER.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        ASSET_HEADER header = ASSET_HEADER.fromJson(result);
        assets.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getAssetLocList', e.toString());
    }
    return assets;
  }

  static Future<List<ASSET_CATEGORY_HEADER>> getAssetCategoryList() async {
    List<ASSET_CATEGORY_HEADER> assets = [];
    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(ASSET_CATEGORY_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        ASSET_CATEGORY_HEADER header = ASSET_CATEGORY_HEADER.fromJson(result);
        assets.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getAssetCategoryList', e.toString());
    }
    return assets;
  }

  static Future<List<ABCINDICATOR_HEADER>> getABCCategoryList() async {
    List<ABCINDICATOR_HEADER> assets = [];
    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(ABCINDICATOR_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        ABCINDICATOR_HEADER header = ABCINDICATOR_HEADER.fromJson(result);
        assets.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getABCCategoryList', e.toString());
    }
    return assets;
  }

  static Future<List<LOCATION_CATEGORY_HEADER>>
      getLocationCategoryList() async {
    List<LOCATION_CATEGORY_HEADER> locations = [];
    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(LOCATION_CATEGORY_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        LOCATION_CATEGORY_HEADER header =
            LOCATION_CATEGORY_HEADER.fromJson(result);
        locations.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getLocationCategoryList', e.toString());
    }
    return locations;
  }

  Future<void> markIrrelevantDependentTasks({
    required INSPECTION_PLAN_HEADER planHeader,
    required INSP_EXEC_HEADER execHeader,
  }) async {
    final planId = planHeader.plan_id!;
    final inspId = execHeader.insp_id!;
    final allPlanTasks = await DbHelper.getInspectionTaskPlanListHeaderByPlanId(
        planId.toString());

    List<INSP_EXEC_TASK> irreleventExecTasks = await getAllIrrelevantExecTasks(
        execHeader: execHeader, planHeader: planHeader);

    for (INSP_EXEC_TASK execTask in irreleventExecTasks) {
      if (execTask.status == AppConstants.STATE_TASK_COMP) {
        continue;
      }

      ///removded now

      final taskMeta =
          allPlanTasks.firstWhereOrNull((t) => t.task_id == execTask.task_id);

      await DbHelper().taskInspectionUpdate(
          task: taskMeta!,
          status: AppConstants.STATE_TASK_COMP,
          inspHeader: execHeader,
          reasonCode: '',
          skipreason: '',
          p_mode: "M",
          comment: '',
          dataString: null,
          dataDouble: null,
          dataBloc: null,
          isIrrelevant: "true");
    }
  }

  Future<List<INSP_EXEC_TASK>> getAllIrrelevantExecTasks({
    required INSPECTION_PLAN_HEADER planHeader,
    required INSP_EXEC_HEADER execHeader,
  }) async {
    final planId = planHeader.plan_id!;
    final inspId = execHeader.insp_id!;
    final List<INSP_EXEC_TASK> allIrrelevantExecTasks = [];

    final allSections =
        await DbHelper.getInspectionSectionPlanListHeader(planId.toString());

    for (final section in allSections) {
      final allPlanTasks =
          await DbHelper.getInspectionTaskPlanListHeaderByPlanId(
              planId.toString());

      final allExecTasks =
          await DbHelper.getInspectionExecTaskPlanListHeaderAll(
        inspId.toString(),
        section.section_id.toString(),
      );

      final parentTasks = allPlanTasks.where((t) => t.dependent != 'true');

      for (final parent in parentTasks) {
        final parentExec =
            allExecTasks.firstWhereOrNull((e) => e.task_id == parent.task_id);
        if (parentExec == null) continue;

        final result = await _getIrrelevantExecTasksRecursive(
          parentTask: parent,
          parentExecTask: parentExec,
          allPlanTasks: allPlanTasks,
          allExecTasks: allExecTasks,
          plantId: planId.toString(),
        );

        allIrrelevantExecTasks.addAll(result);
      }
    }

    return allIrrelevantExecTasks;
  }

  Future<List<INSP_EXEC_TASK>> _getIrrelevantExecTasksRecursive({
    required INSPECTION_TASK parentTask,
    required INSP_EXEC_TASK parentExecTask,
    required List<INSPECTION_TASK> allPlanTasks,
    required List<INSP_EXEC_TASK> allExecTasks,
    required String plantId,
  }) async {
    final List<INSP_EXEC_TASK> irrelevantExecTasks = [];

    String? selectedValue;

    if (parentTask.task_type == 'b') {
      selectedValue = parentExecTask.str_value;
    } else if (parentTask.task_type == 'e') {
      selectedValue = parentExecTask.num_value?.toString();
    } else if (parentTask.task_type == 'o') {
      final headers = await DbHelper.getKPIHeaderList(plantId);
      final kpiHeader = headers.firstWhereOrNull(
        (k) =>
            k.plant_id == plantId &&
            k.kpi_id.toString() == parentTask.kpi_id.toString(),
      );

      if (kpiHeader != null) {
        selectedValue = ['2', '3', '4'].contains(kpiHeader.kpi_type)
            ? parentExecTask.num_value?.toString()
            : parentExecTask.str_value;
      }
    }

    if (parentTask.task_type.toString() == 'n') {
      selectedValue = parentExecTask.str_value;
    }
    if (parentTask.task_type.toString() == 'h') {
      selectedValue = parentExecTask.num_value?.toString();
    } else if (parentTask.task_type.toString() == 'i') {
      selectedValue = parentExecTask.str_value;
    } else if (parentTask.task_type.toString() == 'c') {
      selectedValue = parentExecTask.str_value;
    } else if (parentTask.task_type.toString() == 'k') {
      selectedValue = parentExecTask.str_value;
    } else if (parentTask.task_type.toString() == 'g') {
      selectedValue = parentExecTask.blob_value;
    } else if (parentTask.task_type.toString() == 'f') {
      selectedValue = parentExecTask.str_value;
    } else if (parentTask.task_type.toString() == 'l') {
      selectedValue = parentExecTask.num_value?.toString();
    } else if (parentTask.task_type.toString() == 'j') {
      selectedValue = parentExecTask.num_value?.toString();
    } else if (parentTask.task_type.toString() == 'd') {
      selectedValue = parentExecTask.num_value?.toString();
      ;
    } else if (parentTask.task_type.toString() == 'm') {
      selectedValue = parentExecTask.num_value?.toString();
    }

    if (selectedValue == null) return [];

    // Process all immediate dependents of this parentTask
    final dependents = allPlanTasks.where(
      (t) => t.dependent == 'true' && t.dep_task_id == parentTask.task_id,
    );

    for (final dep in dependents) {
      final shouldShow = shouldShowTask(
        actual: selectedValue,
        expected: dep.dep_cond_val,
        operatorCode: dep.dep_cond_code,
      );

      final depExec =
          allExecTasks.firstWhereOrNull((e) => e.task_id == dep.task_id);

      if (!shouldShow) {
        // Task is irrelevant
        if (depExec != null) {
          irrelevantExecTasks.add(depExec);
        }

        // Continue finding children of this irrelevant task
        final nested = await _getIrrelevantChildrenRecursive(
          parentTask: dep,
          allPlanTasks: allPlanTasks,
          allExecTasks: allExecTasks,
          plantId: plantId,
        );
        irrelevantExecTasks.addAll(nested);
      } else if (depExec != null) {
        // Task is relevant, but it may have children that are not
        final childNested = await _getIrrelevantExecTasksRecursive(
          parentTask: dep,
          parentExecTask: depExec,
          allPlanTasks: allPlanTasks,
          allExecTasks: allExecTasks,
          plantId: plantId,
        );
        irrelevantExecTasks.addAll(childNested);
      }
    }

    return irrelevantExecTasks;
  }

  Future<List<INSP_EXEC_TASK>> _getIrrelevantChildrenRecursive({
    required INSPECTION_TASK parentTask,
    required List<INSPECTION_TASK> allPlanTasks,
    required List<INSP_EXEC_TASK> allExecTasks,
    required String plantId,
  }) async {
    final List<INSP_EXEC_TASK> childrenIrrelevant = [];

    final children = allPlanTasks.where(
      (t) => t.dependent == 'true' && t.dep_task_id == parentTask.task_id,
    );

    for (final child in children) {
      final childExec =
          allExecTasks.firstWhereOrNull((e) => e.task_id == child.task_id);
      if (childExec != null) {
        childrenIrrelevant.add(childExec);
      }

      final nested = await _getIrrelevantChildrenRecursive(
        parentTask: child,
        allPlanTasks: allPlanTasks,
        allExecTasks: allExecTasks,
        plantId: plantId,
      );
      childrenIrrelevant.addAll(nested);
    }

    return childrenIrrelevant;
  }

  bool shouldShowTask({
    required String? actual,
    required String? expected,
    required String? operatorCode,
  }) {
    if (actual == null || expected == null || operatorCode == null) {
      return false;
    }

    final actualVal = actual;
    final expectedVal = expected;
    final op = operatorCode;

    late List<String> expectedValues;
    try {
      final parsed = jsonDecode(expected);
      if (parsed is List) {
        expectedValues = parsed.map((e) => e.toString()).toList();
      } else {
        expectedValues = [parsed.toString()];
      }
    } catch (_) {
      expectedValues = [expected];
    }

    switch (op) {
      case 'if':
        return expectedValues.contains(actualVal);
      case 'di':
        return !expectedValues.contains(actualVal);
      case 'e':
        return actualVal == expectedVal;

      case 'ne':
        return actualVal != expectedVal;

      case 'gt':
        return double.tryParse(actualVal) != null &&
            double.tryParse(expectedVal) != null &&
            double.parse(actualVal) > double.parse(expectedVal);

      case 'ng':
        return double.tryParse(actualVal) != null &&
            double.tryParse(expectedVal) != null &&
            double.parse(actualVal) <= double.parse(expectedVal);

      case 'lt':
        return double.tryParse(actualVal) != null &&
            double.tryParse(expectedVal) != null &&
            double.parse(actualVal) < double.parse(expectedVal);

      case 'nl':
        return double.tryParse(actualVal) != null &&
            double.tryParse(expectedVal) != null &&
            double.parse(actualVal) >= double.parse(expectedVal);

      case 'bt':
        final parts = expectedVal.split('-');
        if (parts.length == 2) {
          final min = double.tryParse(parts[0]);
          final max = double.tryParse(parts[1]);
          final actualNum = double.tryParse(actualVal);
          if (min != null && max != null && actualNum != null) {
            return actualNum >= min && actualNum <= max;
          }
        }
        return false;

      case 'nb':
        final parts = expectedVal.split('-');
        if (parts.length == 2) {
          final min = double.tryParse(parts[0]);
          final max = double.tryParse(parts[1]);
          final actualNum = double.tryParse(actualVal);
          if (min != null && max != null && actualNum != null) {
            return actualNum < min || actualNum > max;
          }
        }
        return false;

      default:
        // fallback to equals
        return actualVal == expectedVal;
    }
  }

  static Future<List<USER_HEADER>> getUsersData(
      String plantId, List<String> plantSec) async {
    List<USER_HEADER> users = [];
    try {
      List<String> usersData = await getUserPlantSection(plantId, plantSec);
      String plantSecValues = usersData.map((e) => "'$e'").join(',');
      String query = "${USER_HEADER.FIELD_USER_ID} IN ($plantSecValues)";

      List results = await AppDatabaseManager().select(
          DBInputEntity(USER_HEADER.TABLE_NAME, {})..setWhereClause(query));
      for (var user in results) {
        users.add(USER_HEADER.fromJson(user));
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getUsersData', e.toString());
    }
    return users;
  }

  static Future<List<String>> getUserPlantSection(
      String plantId, List<String> plantSec) async {
    List<String> users = [];
    String plantSecValues = plantSec.map((e) => "'$e'").join(',');
    try {
      String query =
          "SELECT * FROM ${USER_PLANT_SECTION.TABLE_NAME} WHERE ${USER_PLANT_SECTION.FIELD_PLANT_ID}='$plantId' AND ${USER_PLANT_SECTION.FIELD_PLANT_SEC_ID} IN ($plantSecValues)";
      List results = await AppDatabaseManager().execute((query));
      for (var user in results) {
        users.add(USER_PLANT_SECTION.fromJson(user).user_id.toString());
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getUserPlantSection', e.toString());
    }
    return users;
  }

  static Future<void> updateCilt(CILT_EXEC_HEADER cilt_exec_header) async {
    try {
      String query =
          '${CILT_EXEC_HEADER.FIELD_PLAN_ID}= ${cilt_exec_header.plan_id.toString()}';
      List header = await AppDatabaseManager().select(
          DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, {})
            ..setWhereClause(query));
      if (header.isNotEmpty) {
        for (Map<String, dynamic> data in header) {
          CILT_EXEC_HEADER ciltheader = CILT_EXEC_HEADER.fromJson(data);
          String query1 =
              '${CILT_EXEC_HEADER.FIELD_CILT_ID}= ${ciltheader.cilt_id.toString()}';
          await AppDatabaseManager().update(DBInputEntity(
              CILT_EXEC_HEADER.TABLE_NAME, cilt_exec_header.toJson()));
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, 'taskUpdate', e.toString());
    }
  }

  static Future<void> updateInsp(INSP_EXEC_HEADER insp_exec_header) async {
    try {
      String query =
          '${INSP_EXEC_HEADER.FIELD_PLAN_ID}= ${insp_exec_header.plan_id.toString()}';
      List header = await AppDatabaseManager().select(
          DBInputEntity(INSP_EXEC_HEADER.TABLE_NAME, {})
            ..setWhereClause(query));
      if (header.isNotEmpty) {
        for (Map<String, dynamic> data in header) {
          INSP_EXEC_HEADER inspHeader = INSP_EXEC_HEADER.fromJson(data);
          String query1 =
              '${INSP_EXEC_HEADER.FIELD_INSP_ID}= ${inspHeader.insp_id.toString()}';
          await AppDatabaseManager().update(DBInputEntity(
              INSP_EXEC_HEADER.TABLE_NAME, insp_exec_header.toJson()));
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, 'updateInsp', e.toString());
    }
  }

  static insertDocumentsHeader(DOCUMENT_HEADER header) async {
    try {
      var results = await AppDatabaseManager()
          .insert(DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, header.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, 'insertDocumentsHeader', e.toString());
    }
  }

  static updateDocumentsHeader(DOCUMENT_HEADER header) async {
    try {
      var results = await AppDatabaseManager()
          .update(DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, header.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, 'updateDocumentsHeader', e.toString());
    }
  }

  static insertDocumentAttachment(DOCUMENT_ATTACHMENT attachment) async {
    try {
      await AppDatabaseManager().insert(
          DBInputEntity(DOCUMENT_ATTACHMENT.TABLE_NAME, attachment.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, 'insertDocumentAttachment', e.toString());
    }
  }

  static updateDocumentAttachment(DOCUMENT_ATTACHMENT attachment) async {
    try {
      await AppDatabaseManager().update(
          DBInputEntity(DOCUMENT_ATTACHMENT.TABLE_NAME, attachment.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, 'insertDocumentAttachment', e.toString());
    }
  }

  static insertCiltExeDoc(CILT_EXEC_DOC ciltDocument) async {
    try {
      var result = await AppDatabaseManager().insert(
          DBInputEntity(CILT_EXEC_DOC.TABLE_NAME, ciltDocument.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, 'insertCiltExeDoc', e.toString());
    }
  }

  static updateCiltExeDoc(CILT_EXEC_DOC ciltDocument) async {
    try {
      await AppDatabaseManager().update(
          DBInputEntity(CILT_EXEC_DOC.TABLE_NAME, ciltDocument.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, 'updateCiltExeDoc', e.toString());
    }
  }

  static insertInspExeDoc(INSP_EXEC_DOC inspDocument) async {
    try {
      var result = await AppDatabaseManager().insert(
          DBInputEntity(INSP_EXEC_DOC.TABLE_NAME, inspDocument.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, 'insertInspExeDoc', e.toString());
    }
  }

  static updateInspExeDoc(INSP_EXEC_DOC inspDocument) async {
    try {
      await AppDatabaseManager().update(
          DBInputEntity(INSP_EXEC_DOC.TABLE_NAME, inspDocument.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, 'updateInspExeDoc', e.toString());
    }
  }

  static Future<List<DOCUMENT_HEADER>> getCiltTaskDocHeaders(
      CILT_TASK ciltTask) async {
    List<CILT_TASK_DOC> ciltTaskDocs = [];
    List<DOCUMENT_HEADER> documentHeaders = [];

    try {
      List result = await AppDatabaseManager().select(DBInputEntity(
          CILT_TASK_DOC.TABLE_NAME, {})
        ..setWhereClause(
            "${CILT_TASK_DOC.FIELD_PLAN_ID} = '${ciltTask.plan_id}' AND ${CILT_TASK_DOC.FIELD_TASK_ID}='${ciltTask.task_id}' AND ${CILT_TASK_DOC.FIELD_SECTION_ID}='${ciltTask.section_id}'"));

      for (var data in result) {
        CILT_TASK_DOC ciltTaskDoc = CILT_TASK_DOC.fromJson(data);
        ciltTaskDocs.add(ciltTaskDoc);
      }
      List<DOCUMENT_HEADER> docList = await DbHelper.getDocumentHeaders();
      documentHeaders = docList.where((doc) {
        return ciltTaskDocs
            .any((ciltTaskDoc) => ciltTaskDoc.doc_id == doc.doc_id);
      }).toList();
    } catch (e) {
      Logger.logError(sourceClass, 'getCiltTaskDocHeaders', e.toString());
    }
    return documentHeaders;
  }

  static Future<List<DOCUMENT_HEADER>> getInspectionTaskDocHeaders(
      INSPECTION_TASK inspTask) async {
    List<INSPECTION_TASK_DOC> inspTaskDocs = [];
    List<DOCUMENT_HEADER> documentHeaders = [];

    try {
      List result = await AppDatabaseManager().select(DBInputEntity(
          INSPECTION_TASK_DOC.TABLE_NAME, {})
        ..setWhereClause(
            "${INSPECTION_TASK_DOC.FIELD_PLAN_ID} = '${inspTask.plan_id}' AND ${INSPECTION_TASK_DOC.FIELD_TASK_ID}='${inspTask.task_id}' AND ${INSPECTION_TASK_DOC.FIELD_SECTION_ID}='${inspTask.section_id}'"));

      for (var data in result) {
        INSPECTION_TASK_DOC inspTaskDoc = INSPECTION_TASK_DOC.fromJson(data);
        inspTaskDocs.add(inspTaskDoc);
      }
      List<DOCUMENT_HEADER> docList = await DbHelper.getDocumentHeaders();
      documentHeaders = docList.where((doc) {
        return inspTaskDocs.any((taskDoc) => taskDoc.doc_id == doc.doc_id);
      }).toList();
    } catch (e) {
      Logger.logError(sourceClass, 'getInspectionTaskDocHeaders', e.toString());
    }
    return documentHeaders;
  }

  static Future<List<DOCUMENT_HEADER>> getPlanDocHeader(
      CILT_PLAN_HEADER ciltPlan) async {
    List<CILT_PLAN_DOC> ciltPlanDocs = [];
    List<DOCUMENT_HEADER> documentHeaders = [];
    try {
      List result = await AppDatabaseManager().select(
          DBInputEntity(CILT_PLAN_DOC.TABLE_NAME, {})
            ..setWhereClause(
                "${CILT_PLAN_DOC.FIELD_PLAN_ID} = '${ciltPlan.plan_id}'"));
      for (var data in result) {
        CILT_PLAN_DOC ciltPlanDoc = CILT_PLAN_DOC.fromJson(data);
        ciltPlanDocs.add(ciltPlanDoc);
      }
      List<DOCUMENT_HEADER> docList = await DbHelper.getDocumentHeaders();
      documentHeaders = docList.where((doc) {
        return ciltPlanDocs
            .any((ciltTaskDoc) => ciltTaskDoc.doc_id == doc.doc_id);
      }).toList();
    } catch (e) {
      Logger.logError(sourceClass, 'getCiltPlankDocHeaders', e.toString());
    }
    return documentHeaders;
  }

  static Future<List<DOCUMENT_HEADER>> getPlanDocInspectionHeader(
      INSPECTION_PLAN_HEADER inspPlan) async {
    List<INSPECTION_PLAN_DOC> inspPlanDocs = [];
    List<DOCUMENT_HEADER> documentHeaders = [];
    try {
      List result = await AppDatabaseManager().select(DBInputEntity(
          INSPECTION_PLAN_DOC.TABLE_NAME, {})
        ..setWhereClause(
            "${INSPECTION_PLAN_DOC.FIELD_PLAN_ID} = '${inspPlan.plan_id}'"));
      for (var data in result) {
        INSPECTION_PLAN_DOC ciltPlanDoc = INSPECTION_PLAN_DOC.fromJson(data);
        inspPlanDocs.add(ciltPlanDoc);
      }
      List<DOCUMENT_HEADER> docList = await DbHelper.getDocumentHeaders();
      documentHeaders = docList.where((doc) {
        return inspPlanDocs
            .any((inspTaskDoc) => inspTaskDoc.doc_id == doc.doc_id);
      }).toList();
    } catch (e) {
      Logger.logError(sourceClass, 'getPlanDocInspectionHeader', e.toString());
    }
    return documentHeaders;
  }

  static Future<List<DOCUMENT_HEADER>> getPlanAndCiltTaskDocHeader(
      CILT_PLAN_HEADER ciltPlan, CILT_TASK ciltTask) async {
    List<DOCUMENT_HEADER> documentHeaders = [];
    try {
      List<DOCUMENT_HEADER> taskDcuments =
          await getCiltTaskDocHeaders(ciltTask);
      documentHeaders.addAll(taskDcuments);
      List<DOCUMENT_HEADER> planDocuments = await getPlanDocHeader(ciltPlan);
      documentHeaders.addAll(planDocuments);
    } catch (e) {
      Logger.logError(
          sourceClass, 'getCiltPlanAndTaskDocHeaders', e.toString());
    }
    return documentHeaders;
  }

  static deleteDocumentHeader(DOCUMENT_HEADER addedAttachment) async {
    try {
      await AppDatabaseManager().delete(
          DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, addedAttachment.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, 'deleteDocumentHeader', e.toString());
    }
  }

  static updateCiltExecSection(CILT_SECTION sectionData, String? reason,
      String? reasonCode, CILT_EXEC_HEADER cilt_exec_header) async {
    try {
      String query = "";
      List sections = await AppDatabaseManager().select(DBInputEntity(
          CILT_EXEC_SEC.TABLE_NAME, {})
        ..setWhereClause(
            "${CILT_EXEC_SEC.FIELD_SECTION_ID} = ${sectionData.section_id} AND ${CILT_EXEC_SEC.FIELD_CILT_ID} = ${cilt_exec_header.cilt_id}"));

      for (var section in sections) {
        CILT_EXEC_SEC ciltExecSec = CILT_EXEC_SEC.fromJson(section);
        ciltExecSec.reason = reasonCode;
        if (ciltExecSec.section_id == sectionData.section_id) {
          ciltExecSec.skip_comments = reason;
          await AppDatabaseManager().update(
              DBInputEntity(CILT_EXEC_SEC.TABLE_NAME, ciltExecSec.toJson()));
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, 'updateCiltExecSection', e.toString());
    }
  }

  static updateInspExecSection(
      INSPECTION_SECTION sectionData,
      String? reasonCode,
      INSP_EXEC_HEADER insp_exec_header,
      String? skipreason) async {
    try {
      String query = "";
      List sections = await AppDatabaseManager().select(DBInputEntity(
          INSP_EXEC_SEC.TABLE_NAME, {})
        ..setWhereClause(
            "${INSP_EXEC_SEC.FIELD_SECTION_ID} = ${sectionData.section_id} AND ${INSP_EXEC_SEC.FIELD_INSP_ID} = ${insp_exec_header.insp_id}"));

      for (var section in sections) {
        INSP_EXEC_SEC inspExecSec = INSP_EXEC_SEC.fromJson(section);
        inspExecSec.reason = reasonCode;
        if (inspExecSec.section_id == sectionData.section_id) {
          inspExecSec.skip_comments = skipreason;
          await AppDatabaseManager().update(
              DBInputEntity(INSP_EXEC_SEC.TABLE_NAME, inspExecSec.toJson()));
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, 'updateInspExecSection', e.toString());
    }
  }

  static Future<INSPECTION_SECTION?> getInspectionSection(
      String section, String planId) async {
    try {
      INSPECTION_SECTION? sectionData;
      List sections = await AppDatabaseManager().select(DBInputEntity(
          INSPECTION_SECTION.TABLE_NAME, {})
        ..setWhereClause(
            "${INSPECTION_SECTION.FIELD_SECTION_ID} = $section AND ${INSPECTION_SECTION.FIELD_PLAN_ID} = $planId"));

      for (var section in sections) {
        INSPECTION_SECTION inspSec = INSPECTION_SECTION.fromJson(section);
        sectionData = inspSec;
      }
      return sectionData;
    } catch (e) {
      Logger.logError(sourceClass, 'getInspectionSection', e.toString());
    }
    return null;
  }

  static Future<INSPECTION_SECTION?> getInspectionSectionByPlanId(
      String planId) async {
    try {
      INSPECTION_SECTION? sectionData;
      List sections = await AppDatabaseManager().select(
          DBInputEntity(INSPECTION_SECTION.TABLE_NAME, {})
            ..setWhereClause("${INSPECTION_SECTION.FIELD_PLAN_ID} = $planId"));

      for (var section in sections) {
        INSPECTION_SECTION inspSec = INSPECTION_SECTION.fromJson(section);
        sectionData = inspSec;
      }
      return sectionData;
    } catch (e) {
      Logger.logError(
          sourceClass, 'getInspectionSectionByPlanId', e.toString());
    }
    return null;
  }

  static Future<void> sectionUpdate(
      CILT_SECTION section, String reason, String? comment) async {
    try {
      String query =
          '${CILT_EXEC_HEADER.FIELD_PLAN_ID}= ${section.plan_id.toString()}';
      List header = await AppDatabaseManager().select(
          DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, {})
            ..setWhereClause(query));
      if (header.isNotEmpty) {
        for (Map<String, dynamic> data in header) {
          CILT_EXEC_HEADER ciltheader = CILT_EXEC_HEADER.fromJson(data);
          String query1 =
              '${CILT_EXEC_HEADER.FIELD_CILT_ID}= ${ciltheader.cilt_id.toString()}';

          List execute = await AppDatabaseManager().select(
              DBInputEntity(CILT_EXEC_SEC.TABLE_NAME, {})
                ..setWhereClause(query1));
          if (execute.isNotEmpty) {
            for (Map<String, dynamic> exec in execute) {
              CILT_EXEC_SEC execHeader = CILT_EXEC_SEC.fromJson(exec);
              if (execHeader.section_id.toString() ==
                  section.section_id.toString()) {
                execHeader.reason = reason;
                execHeader.skip_comments = comment;
                await AppDatabaseManager().update(DBInputEntity(
                    CILT_EXEC_SEC.TABLE_NAME, execHeader.toJson()));
              }
            }
          }
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, 'sectionUpdate', e.toString());
    }
  }

  static insertCiltTaskDoc(CILT_TASK_DOC task_document) async {
    try {
      await AppDatabaseManager().insert(
          DBInputEntity(CILT_TASK_DOC.TABLE_NAME, task_document.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, 'insertCiltTaskDoc', e.toString());
    }
  }

  static updateCiltTaskDoc(CILT_TASK_DOC task_document) async {
    try {
      await AppDatabaseManager().update(
          DBInputEntity(CILT_TASK_DOC.TABLE_NAME, task_document.toJson()));

      List list = await AppDatabaseManager()
          .select(DBInputEntity(CILT_TASK_DOC.TABLE_NAME, {}));
    } catch (e) {
      Logger.logError(sourceClass, 'updateCiltTaskDoc', e.toString());
    }
  }

  static Future<void> updateCiltExecTask(CILT_EXEC_TASK task) async {
    try {
      task.p_mode = "";
      var result = await AppDatabaseManager()
          .update(DBInputEntity(CILT_EXEC_TASK.TABLE_NAME, task.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, 'updateCiltExecTask', e.toString());
    }
  }

  static Future<void> updateInspExecTask(INSP_EXEC_TASK task) async {
    try {
      task.p_mode = "";
      var result = await AppDatabaseManager()
          .update(DBInputEntity(INSP_EXEC_TASK.TABLE_NAME, task.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, 'updateInspExecTask', e.toString());
    }
  }

  static Future<ASSET_HEADER> getAssetByAssetno(int asset_no) async {
    ASSET_HEADER? asset_header;
    try {
      List list = await AppDatabaseManager().select(
          DBInputEntity(ASSET_HEADER.TABLE_NAME, {})
            ..setWhereClause("${ASSET_HEADER.FIELD_ASSET_NO} = $asset_no"));
      asset_header = ASSET_HEADER.fromJson(list[0]);
    } catch (e) {
      Logger.logError(sourceClass, 'updateCiltExecTask', e.toString());
    }
    return asset_header!;
  }

  static Future<List<CILT_TASK>> getIncompleteCiltTasks(
      CILT_EXEC_HEADER cilt_exec_header) async {
    List<CILT_TASK> ciltTasks = [];

    try {
      String query =
          '${CILT_EXEC_TASK.FIELD_CILT_ID}="${cilt_exec_header.cilt_id}" AND (${CILT_EXEC_TASK.FIELD_STATUS} IS NULL OR ${CILT_EXEC_TASK.FIELD_STATUS} != "${AppConstants.STATE_TASK_COMP}")';

      List results = await AppDatabaseManager().select(
          DBInputEntity(CILT_EXEC_TASK.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        CILT_EXEC_TASK execTask = CILT_EXEC_TASK.fromJson(result);
        String taskQuery = '${CILT_TASK.FIELD_TASK_ID}="${execTask.task_id}"';
        List taskResults = await AppDatabaseManager().select(
            DBInputEntity(CILT_TASK.TABLE_NAME, {})..setWhereClause(taskQuery));

        for (var taskResult in taskResults) {
          ciltTasks.add(CILT_TASK.fromJson(taskResult));
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getIncompleteCiltTasks', e.toString());
    }

    return ciltTasks;
  }

  static Future<List<INSPECTION_TASK>> getIncompleteInspectionTasks(
      INSP_EXEC_HEADER insp_exec_header) async {
    List<INSPECTION_TASK> inspTasks = [];

    try {
      String query =
          '${INSP_EXEC_TASK.FIELD_INSP_ID}="${insp_exec_header.insp_id}" AND (${INSP_EXEC_TASK.FIELD_STATUS} IS NULL OR ${INSP_EXEC_TASK.FIELD_STATUS} != "${AppConstants.STATE_TASK_COMP}")';

      List results = await AppDatabaseManager().select(
          DBInputEntity(INSP_EXEC_TASK.TABLE_NAME, {})..setWhereClause(query));

      for (var result in results) {
        if (result[INSP_EXEC_TASK.FIELD_BLOB_VALUE] is Uint8List) {
          result[INSP_EXEC_TASK.FIELD_BLOB_VALUE] =
              base64Encode(result[INSP_EXEC_TASK.FIELD_BLOB_VALUE]);
        }
        INSP_EXEC_TASK execTask = INSP_EXEC_TASK.fromJson(result);
        String taskQuery =
            '${INSPECTION_TASK.FIELD_TASK_ID}="${execTask.task_id}"';
        List taskResults = await AppDatabaseManager().select(
            DBInputEntity(INSPECTION_TASK.TABLE_NAME, {})
              ..setWhereClause(taskQuery));

        for (var taskResult in taskResults) {
          inspTasks.add(INSPECTION_TASK.fromJson(taskResult));
        }
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getIncompleteInspectionTasks', e.toString());
    }

    return inspTasks;
  }

  static Future<List<INSP_EXEC_TASK>> getInspectionExecTaskList() async {
    List<INSP_EXEC_TASK> inspTasks = [];

    try {
      List results = await AppDatabaseManager()
          .select(DBInputEntity(INSP_EXEC_TASK.TABLE_NAME, {}));
      for (var result in results) {
        if (result[INSP_EXEC_TASK.FIELD_BLOB_VALUE] is Uint8List) {
          result[INSP_EXEC_TASK.FIELD_BLOB_VALUE] =
              base64Encode(result[INSP_EXEC_TASK.FIELD_BLOB_VALUE]);
        }
        INSP_EXEC_TASK execTask = INSP_EXEC_TASK.fromJson(result);
        inspTasks.add(execTask);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getInspectionExecTaskList', e.toString());
    }

    return inspTasks;
  }

  static Future<List<INSP_EXEC_TASK>> getInspectionExecTaskPlanListHeaderAll(
      String inspId, String sectionId) async {
    List<INSP_EXEC_TASK> inspectionPlanTaskHeaderList = [];
    try {
      String query =
          '${INSP_EXEC_TASK.FIELD_INSP_ID} = $inspId AND ${INSP_EXEC_TASK.FIELD_SECTION_ID} = $sectionId';
      List results = await AppDatabaseManager().select(
          DBInputEntity(INSP_EXEC_TASK.TABLE_NAME, {})..setWhereClause(query));
      for (var result in results) {
        inspectionPlanTaskHeaderList.add(INSP_EXEC_TASK.fromJson(result));
      }
    } catch (e) {
      Logger.logError(
          sourceClass, 'getInspectionExecTaskPlanListHeaderAll', e.toString());
    }
    return inspectionPlanTaskHeaderList;
  }

  static Future<INSP_EXEC_TASK?> getInspectionExecTaskByTask(
      String sectionId, String taskId) async {
    INSP_EXEC_TASK? inspTasks;

    try {
      String query =
          '${INSP_EXEC_TASK.FIELD_SECTION_ID}="$sectionId" && ${INSP_EXEC_TASK.FIELD_INSP_TASK_ID}="$taskId"';

      List results = await AppDatabaseManager().select(
          DBInputEntity(INSP_EXEC_TASK.TABLE_NAME, {})..setWhereClause(query));
      for (var result in results) {
        if (result[INSP_EXEC_TASK.FIELD_BLOB_VALUE] is Uint8List) {
          result[INSP_EXEC_TASK.FIELD_BLOB_VALUE] =
              base64Encode(result[INSP_EXEC_TASK.FIELD_BLOB_VALUE]);
        }
        INSP_EXEC_TASK execTask = INSP_EXEC_TASK.fromJson(result);
        inspTasks = execTask;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getInspectionExecTaskList', e.toString());
    }

    return inspTasks;
  }

  static Future<List<DOCUMENT_HEADER>> getCiltExecDocByCiltidAndTaskId(
      CILT_EXEC_TASK task, CILT_EXEC_HEADER header) async {
    List<CILT_EXEC_DOC> ciltExecDoc = [];
    List<DOCUMENT_HEADER> docList = [];

    try {
      String query =
          '${CILT_EXEC_DOC.FIELD_CILT_ID}="${header.cilt_id}" AND ${CILT_EXEC_DOC.FIELD_CILT_TASK_ID}="${task.cilt_task_id}"';

      List results = await AppDatabaseManager().select(
          DBInputEntity(CILT_EXEC_DOC.TABLE_NAME, {})..setWhereClause(query));
      for (var result in results) {
        ciltExecDoc.add(CILT_EXEC_DOC.fromJson(result));
      }
      List<DOCUMENT_HEADER> list = await DbHelper.getDocumentHeaders();
      docList = list
          .where((element) =>
              ciltExecDoc.any((element1) => element1.doc_id == element.doc_id))
          .toList();
    } catch (e) {
      Logger.logError(
          sourceClass, 'getCiltExecDocByCiltidAndTaskId', e.toString());
    }
    return docList;
  }

  static Future<List<DOCUMENT_HEADER>> getInspectionExecDocByInspidAndTaskId(
      INSP_EXEC_TASK task, INSP_EXEC_HEADER header) async {
    List<INSP_EXEC_DOC> inspExecDoc = [];
    List<DOCUMENT_HEADER> docList = [];

    try {
      String query =
          '${INSP_EXEC_DOC.FIELD_INSP_ID}="${header.insp_id}" AND ${INSP_EXEC_DOC.FIELD_INSP_TASK_ID}="${task.insp_task_id}"';

      List results = await AppDatabaseManager().select(
          DBInputEntity(INSP_EXEC_DOC.TABLE_NAME, {})..setWhereClause(query));
      for (var result in results) {
        inspExecDoc.add(INSP_EXEC_DOC.fromJson(result));
      }
      List<DOCUMENT_HEADER> list = await DbHelper.getDocumentHeaders();
      docList = list
          .where((element) =>
              inspExecDoc.any((element1) => element1.doc_id == element.doc_id))
          .toList();
    } catch (e) {
      Logger.logError(
          sourceClass, 'getInspectionExecDocByInspidAndTaskId', e.toString());
    }
    return docList;
  }

  static USER_HEADER? getAssignUser(List<USER_HEADER> userList, String user) {
    for (var userHeader in userList) {}
    USER_HEADER? userData;
    userData = userList.firstWhereOrNull((element) => element.user_id == user);
    return userData;
  }

  static Future<void> deleteAllDataOnPalntChange() async {
    try {
      List<String> tablesToDelete = [
        'ASSET_CATEGORY_HEADER',
        'ASSET_DOCUMENT',
        'ASSET_HEADER',
        'CALENDAR_HEADER',
        'ABCINDICATOR_HEADER',
        'APP_SETTING_HEADER',
        'CALENDAR_HOLIDAY',
        'CILT_EXEC_ACTION',
        'CILT_EXEC_DOC',
        'CILT_EXEC_HEADER',
        'CILT_EXEC_SEC',
        'CILT_EXEC_TASK',
        'CILT_PLAN_DOC',
        'CILT_PLAN_HEADER',
        'CILT_SCHED_HEADER',
        'CILT_SECTION',
        'CILT_TASK',
        'CILT_TASK_DOC',
        'COUNTRY_HEADER',
        'DOCUMENT_ATTACHMENT',
        'DOCUMENT_HEADER',
        'FAILURE_MODE_HEADER',
        'FAULT_ACTION',
        'FAULT_DOCUMENT',
        'FAULT_HEADER',
        'FAULT_TYPE_HEADER',
        'FOLDER_HEADER',
        'INSP_EXEC_ACTION',
        'INSP_EXEC_DOC',
        'INSP_EXEC_HEADER',
        'INSP_EXEC_SEC',
        'INSP_EXEC_TASK',
        'INSP_PLAN_TYPE_HEADER',
        'INSP_SCHED_HEADER',
        'INSPECTION_PLAN_DOC',
        'INSPECTION_PLAN_HEADER',
        'INSPECTION_SECTION',
        'INSPECTION_TASK',
        'INSPECTION_TASK_DOC',
        'JOB_ACTION',
        'JOB_DOCUMENT',
        'JOB_HEADER',
        'JOBTYPE_HEADER',
        'KPI_HEADER',
        'LOCATION_CATEGORY_HEADER',
        'LOCATION_DOCUMENT',
        'LOCATION_HEADER',
        'PICKLIST_CODE',
        'PICKLIST_HEADER',
        'STATE_HEADER',
        'PLANT_HEADER',
        'PLANT_SECTION_HEADER',
        'USER_GROUP_HEADER',
        'PRIORITY_HEADER',
        'ROLE_HEADER',
        'SHIFT_HEADER',
        'UNIT_HEADER',
        'SKIP_REASON_HEADER',
      ];

      for (String table in tablesToDelete) {
        await AppDatabaseManager().delete(DBInputEntity(table, {}));
      }
    } catch (e) {
      Logger.logError(sourceClass, 'deleteAllDataOnPalntChange', e.toString());
    }
  }

  static Future<void> updateUserHeader(USER_HEADER userHeader) async {
    try {
      await AppDatabaseManager()
          .update(DBInputEntity(USER_HEADER.TABLE_NAME, userHeader.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, 'updateUserHeader', e.toString());
    }
  }

  static Future<List<PICKLIST_HEADER>> getPickListHeaderList() async {
    List<PICKLIST_HEADER> pickListHeader = [];
    try {
      final results = await AppDatabaseManager()
          .select(DBInputEntity(PICKLIST_HEADER.TABLE_NAME, {}));

      for (var result in results) {
        PICKLIST_HEADER header = PICKLIST_HEADER.fromJson(result);
        pickListHeader.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getPickListHeaderList', e.toString());
    }
    return pickListHeader;
  }

  static Future<List<PICKLIST_CODE>> getPickListCodeList() async {
    List<PICKLIST_CODE> pickListCode = [];
    try {
      final results = await AppDatabaseManager()
          .select(DBInputEntity(PICKLIST_CODE.TABLE_NAME, {}));

      for (var result in results) {
        PICKLIST_CODE header = PICKLIST_CODE.fromJson(result);
        pickListCode.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getPickListCodeList', e.toString());
    }
    return pickListCode;
  }

  static Future<List<PICKLIST_CODE>> getPickListCodeListById(String id) async {
    List<PICKLIST_CODE> pickListCode = [];
    try {
      final results = await AppDatabaseManager().select(
          DBInputEntity(PICKLIST_CODE.TABLE_NAME, {})
            ..setWhereClause('${PICKLIST_CODE.FIELD_PICKLIST_ID} = "$id"'));

      for (var result in results) {
        PICKLIST_CODE header = PICKLIST_CODE.fromJson(result);
        pickListCode.add(header);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getPickListCode', e.toString());
    }
    return pickListCode;
  }

  static IdbFactory? idbFactory = getIdbFactory();

  static Future<Database> openIndexDb() async {
    if (idbFactory == null) {
      throw Exception('idbFactory is not initialized');
    }
    return await idbFactory!.open('rounds', version: 1,
        onUpgradeNeeded: (VersionChangeEvent e) {
      final db = e.database;
      if (!db.objectStoreNames.contains('attachments')) {
        db.createObjectStore('attachments'); // UID as key
      }
      if (!db.objectStoreNames.contains('notifications')) {
        db.createObjectStore('notifications', keyPath: 'messageId');
      }
    });
  }

  Future<void> saveAttachmentinIndexDbByUid(
      String uid, String base64Image) async {
    final db = await openIndexDb();
    final txn = db.transaction('attachments', idbModeReadWrite);
    final store = txn.objectStore('attachments');

    await store.put(base64Image, uid); // UID as key
    await txn.completed;
  }

  Future<String?> getAttachmentFromIndexDbByUid(String uid) async {
    final db = await openIndexDb();
    final txn = db.transaction('attachments', idbModeReadOnly);
    final store = txn.objectStore('attachments');

    final image = await store.getObject(uid);
    await txn.completed;
    return image as String?;
  }

  Future<void> deleteAttachmentFromIndexDbForUid(String uid) async {
    final db = await openIndexDb();
    final txn = db.transaction('attachments', idbModeReadWrite);
    final store = txn.objectStore('attachments');
    await store.delete(uid);
    await txn.completed;
  }

  Future<void> acceptCiltAction(CILT_EXEC_ACTION action) async {
    List data = await AppDatabaseManager().select(
        DBInputEntity(CILT_EXEC_ACTION.TABLE_NAME, {})
          ..setWhereClause(
              '${CILT_EXEC_ACTION.FIELD_CILT_ID} = ${action.cilt_id}'));
    if (data.isNotEmpty) {
      CILT_EXEC_ACTION actionInDB = CILT_EXEC_ACTION.fromJson(data[0]);
      action.lid = actionInDB.lid;
      await AppDatabaseManager()
          .update(DBInputEntity(CILT_EXEC_ACTION.TABLE_NAME, action.toJson()));
    } else {
      await AppDatabaseManager()
          .insert(DBInputEntity(CILT_EXEC_ACTION.TABLE_NAME, action.toJson()));
    }
  }

  Future<void> acceptInspAction(INSP_EXEC_ACTION action) async {
    List data = await AppDatabaseManager().select(
        DBInputEntity(INSP_EXEC_ACTION.TABLE_NAME, {})
          ..setWhereClause(
              '${INSP_EXEC_ACTION.FIELD_INSP_ID} = ${action.insp_id}'));
    if (data.isNotEmpty) {
      INSP_EXEC_ACTION actionInDB = INSP_EXEC_ACTION.fromJson(data[0]);
      action.lid = actionInDB.lid;
      await AppDatabaseManager()
          .update(DBInputEntity(INSP_EXEC_ACTION.TABLE_NAME, action.toJson()));
    } else {
      await AppDatabaseManager()
          .insert(DBInputEntity(INSP_EXEC_ACTION.TABLE_NAME, action.toJson()));
    }
  }

  Future<List<INSPECTION_TASK>> getAllValidNestedDependentsA(
    String plantId,
    INSPECTION_TASK parentTask,
    String? selectedValue,
    List<INSP_EXEC_TASK> executionTasks,
    Set<String> existingTaskIds,
  ) async {
    List<INSPECTION_TASK> result = [];
    final dependents = await DbHelper.getInspectionTaskPlanListHeaderDependent(
      parentTask.plan_id.toString(),
      parentTask,
    );
    for (final dependent in dependents) {
      final shouldAdd = shouldShowTask(
        actual: selectedValue,
        expected: dependent.dep_cond_val,
        operatorCode: dependent.dep_cond_code,
      );
      if (shouldAdd &&
          !existingTaskIds.contains(dependent.task_id.toString())) {
        result.add(dependent);
        existingTaskIds.add(dependent.task_id.toString());
        final childExecTask = executionTasks.firstWhereOrNull((e) =>
            e.task_id == dependent.task_id &&
            e.section_id == dependent.section_id);
        String? childSelectedValue;
        if (dependent.task_type == "b") {
          childSelectedValue = childExecTask?.str_value;
        } else if (dependent.task_type == "e") {
          childSelectedValue = childExecTask?.num_value?.toString();
        } else if (dependent.task_type == "m") {
          childSelectedValue = childExecTask?.num_value?.toString();
        } else if (dependent.task_type == "o") {
          final headers = await DbHelper.getKPIHeaderList(plantId);
          final kpiHeader = headers.firstWhereOrNull(
            (element) =>
                element.plant_id == plantId &&
                element.kpi_id.toString() == dependent.kpi_id.toString(),
          );
          if (kpiHeader != null &&
              (kpiHeader.kpi_type == '2' ||
                  kpiHeader.kpi_type == '3' ||
                  kpiHeader.kpi_type == '4')) {
            childSelectedValue = childExecTask?.num_value?.toString();
          } else {
            childSelectedValue = childExecTask?.str_value;
          }
        }
        final nested = await getAllValidNestedDependentsA(
          plantId,
          dependent,
          childSelectedValue,
          executionTasks,
          existingTaskIds,
        );
        result.addAll(nested);
      }
    }
    return result;
  }
}
