import 'url_detector.dart'; // Import the abstract interface

class MobileUrlDetector implements UrlDetector {
  @override
  String getCurrentUrl() {
    // For mobile, you might return an empty string or a custom scheme
    // or handle deep linking/app links if that's what you're detecting.
    // For general URL detection, it's usually not applicable in the same way.
    return '';
  }

  @override
  Map<String, String> getQueryParameters() {
    return {};
  }

  @override
  String getFragment() {
    return '';
  }
}

// Override the factory for mobile platform
UrlDetector getUrlDetector() => MobileUrlDetector();
