import 'dart:html' as html;
import 'package:flutter/foundation.dart'; // Needed for kIsWeb in this example if used

import 'url_detector.dart'; // Import the abstract interface

class WebUrlDetector implements UrlDetector {
  @override
  String getCurrentUrl() {
    return html.window.location.href;
  }

  @override
  Map<String, String> getQueryParameters() {
    final uri = Uri.parse(html.window.location.href);
    return uri.queryParameters;
  }

  @override
  String getFragment() {
    final uri = Uri.parse(html.window.location.href);
    return uri.fragment;
  }
}

// Override the factory for web platform
UrlDetector getUrlDetector() {
  if (kIsWeb) {
    // Good practice to double-check, though the import will handle it
    return WebUrlDetector();
  }
  throw UnsupportedError('WebUrlDetector is only available on web platform.');
}
