class AppUrl {
  final String name;
  String url;
  final bool isDefault;

  AppUrl({
    this.name = '',
    this.url = '',
    this.isDefault = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': this.name,
      'url': this.url,
      'default': this.isDefault,
    };
  }

  factory AppUrl.fromMap(Map<String, dynamic> map) {
    String name = map.containsKey('name')
        ? map['name'] as String
        : map['ump_comment'] as String;
    String url = map.containsKey('url')
        ? map['url'] as String
        : map['ump_url'] as String;
    bool isDefault = map.containsKey('isDefault')
        ? map['isDefault'] as bool
        : map['use_as_default'] as bool;

    return AppUrl(
      name: name,
      url: url,
      isDefault: isDefault,
    );
  }

  @override
  List<Object?> get props => [name, url];
}
