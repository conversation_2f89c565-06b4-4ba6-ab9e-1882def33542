class AdhocCilt {
  final String planId;
  final String shift;
  final String priority;
  final String startOn;
  final String startAt;
  final String endAt;
  final String assignedTo;

  AdhocCilt({
    required this.planId,
    required this.shift,
    required this.priority,
    required this.startOn,
    required this.startAt,
    required this.endAt,
    required this.assignedTo,
  });

  Map<String, dynamic> toJson() {
    return {
      'planId': planId,
      'shift': shift,
      'priority': priority,
      'startOn': startOn,
      'startAt': startAt,
      'endAt': endAt,
      'assignedTo': assignedTo,
    };
  }
}
