class MenuActionItem {
  final String value;
  final String name;

  const MenuActionItem({
    required this.value,
    required this.name,
  });

  Map<String, dynamic> toMap() {
    return {
      'value': this.value,
      'name': this.name,
    };
  }

  factory MenuActionItem.fromMap(Map<String, dynamic> map) {
    return MenuActionItem(
      value: map['value'] as String,
      name: map['name'] as String,
    );
  }
}
