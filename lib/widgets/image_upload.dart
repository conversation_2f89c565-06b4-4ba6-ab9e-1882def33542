/*
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:open_file_plus/open_file_plus.dart';
import 'package:rounds/be/DOCUMENT_ATTACHMENT.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/widgets/image_viewer_screen.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../pages/job/fault/video_player_screen.dart';
import '../providers/attachments/attachment_provider.dart';
import '../providers/fault/fault_header_provider.dart';
import '../utils/app_colors.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

class FileOrImageUploadWidget extends ConsumerStatefulWidget {
  final bool isOnlyDisplay;
  const FileOrImageUploadWidget({required this.isOnlyDisplay, super.key});
  @override
  _FileOrImageUploadWidgetState createState() =>
      _FileOrImageUploadWidgetState();
}

class _FileOrImageUploadWidgetState
    extends ConsumerState<FileOrImageUploadWidget> {
  static const sourceClass = 'FileOrImageUploadWidget';
  bool isUploading = false;
  bool isHovered = false;
  double uploadProgress = 0.0;
  double containerHeightOfImage = 80;
  int selectedImageIndex = -1;
  int hoveredIndices = -1;

  Future<void> pickFile() async {
    final documentHeaderProviderData =
        ref.watch(documentHeaderProvider.notifier);
    final documentAttachmentProviderData =
        ref.watch(documentAttachmentProvider.notifier);
    final result = await FilePicker.platform.pickFiles(type: FileType.any);
    final faultHeader = ref.watch(faultHeaderProvider.notifier).state;
    if (result != null && result.files.single.path != null) {
      for (var data in result.files) {
        File fileData = File(data.path!);
        String? docId = generate32BitDocId();
        String fileName = path.basename(fileData.path);
        String filePath = fileData.path;
        String fileDocType = fileData.path.split('.').last.toUpperCase();
        DOCUMENT_HEADER documentHeader = DOCUMENT_HEADER(
            doc_id: docId,
            doc_type: fileDocType,
            file_name: fileName,
            title: fileName,
            mime_type: 'application/${fileDocType.toLowerCase()}');
        documentHeader.fid = faultHeader.lid;
        await documentHeaderProviderData.insertDocumentHeaders(documentHeader);
        // documentHeaderProviderData.listOfDocumentHeaders(documentHeader);

        // await AppDatabaseManager().insert(DBInputEntity(
        //     DOCUMENT_HEADER.TABLE_NAME, documentHeader.toJson()));
        DOCUMENT_ATTACHMENT documentAttachment = DOCUMENT_ATTACHMENT(uid: '');
        documentAttachment.fid = documentHeader.lid;
        documentAttachment.uid = docId;
        documentAttachment.local_path = filePath;
        documentAttachment.file_name = fileName;
        documentAttachment.mime_type =
            'application/${fileDocType.toLowerCase()}';
        documentAttachment.external_url = "";
        documentAttachment.url_requires_auth = "";
        documentAttachment.attachment_status = AttachmentStatusSavedForUpload;
        await documentAttachmentProviderData
            .insertDocumentAttachments(documentAttachment);
        //  documentAttachmentProviderData
        //     .listOfDocumentAttachments(documentAttachment);
      }
      await documentHeaderProviderData.fetchDocumentHeaders();
      await documentAttachmentProviderData.fetchDocumentAttachments();

      selectedImageIndex = documentAttachmentProviderData.state.length - 1;

      uploadFile(documentAttachmentProviderData.state, selectedImageIndex);
    }
  }

  String? generate32BitDocId() {
    var uuid = Uuid();
    return uuid.v4();
  }

  Future<void> uploadFile(
      List<DOCUMENT_ATTACHMENT> attachments, int index) async {
    if (index < 0 || index >= attachments.length) return;

    setState(() {
      isUploading = true;
      uploadProgress = 0.0;
    });

    for (int i = 1; i <= 10; i++) {
      await Future.delayed(Duration(milliseconds: 300));
      setState(() {
        uploadProgress = i / 10.0;
      });
    }

    setState(() {
      isUploading = false;
      selectedImageIndex = -1;
      isHovered = !isHovered;
    });
  }

  void removeFile(
      {required DOCUMENT_ATTACHMENT file, required int index}) async {
    try {
      final docHeader = ref.watch(documentHeaderProvider.notifier).state;
      final documentAttachmentProviderData =
          ref.watch(documentAttachmentProvider.notifier);

      DOCUMENT_HEADER? headerData;
      for (var header in docHeader) {
        if (header.doc_id == file.uid) {
          headerData = header;
          break;
        }
      }
      await AppDatabaseManager()
          .delete(DBInputEntity(DOCUMENT_ATTACHMENT.TABLE_NAME, file.toJson()));
      if (headerData != null) {
        await AppDatabaseManager().delete(
            DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, headerData.toJson()));
      }
      await documentAttachmentProviderData.fetchDocumentAttachments();
      setState(() {
        isHovered = false;
        hoveredIndices = -1;
      });
    } catch (e) {
      Logger.logError(sourceClass, 'remvoeFile', e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    final documentAttachmentProviderData =
        ref.watch(documentAttachmentProvider.notifier).state;

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        if (!widget.isOnlyDisplay)
          GestureDetector(
            onTap: pickFile,
            child: Container(
              height: containerHeightOfImage,
              width: containerHeightOfImage,
              decoration: UIHelper.cardDecoration(cardColor: Colors.grey[100]),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: AppColors.primaryColor,
                        width: 1.0,
                      ),
                    ),
                    child: Center(
                      child: Icon(
                        Icons.add,
                        color: AppColors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: List.generate(
                documentAttachmentProviderData.length,
                (index) => buildImageContainer(
                    documentAttachmentProviderData[index], index),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget buildImageContainer(DOCUMENT_ATTACHMENT attachment, int index) {
    File? file =
        attachment.local_path != null ? File(attachment.local_path!) : null;
    String? fileExtension = file?.path.split('.').last.toLowerCase();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Stack(
        children: [
          GestureDetector(
            onTap: () {
              if (isHovered) {
                setState(() {
                  isHovered = !isHovered;
                });
              }
              if (file != null) {
                if (fileExtension == 'jpg' ||
                    fileExtension == 'jpeg' ||
                    fileExtension == 'png' ||
                    fileExtension == 'gif') {
                  Navigator.of(context).push(MaterialPageRoute(
                    builder: (context) =>
                        ImageViewerScreen(attachment: attachment),
                  ));
                }
                if (fileExtension == 'mp4' ||
                    fileExtension == 'avi' ||
                    fileExtension == 'mov' ||
                    fileExtension == 'mkv') {
                  _openVideo(file);
                } else {
                  _openFile(file);
                }
              }
            },
            onLongPressStart: (_) {
              setState(() {
                isHovered = !isHovered;
                hoveredIndices = index;
              });
            },
            child: Container(
              height: containerHeightOfImage,
              width: containerHeightOfImage,
              decoration: BoxDecoration(
                image: file != null ? getFileDisplayWidget(file) : null,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: AppColors.grey, width: 1.0),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  if (isUploading && index == selectedImageIndex)
                    Container(
                      decoration: UIHelper.cardDecoration(
                        cardColor: isUploading
                            ? Colors.grey[400]?.withOpacity(0.7)
                            : file != null
                                ? Colors.transparent
                                : Colors.grey[400],
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            AppLocalizations.of(context)!.upload,
                            style:
                                TextStyle(color: AppColors.white, fontSize: 14),
                          ),
                          UIHelper.sizedBox8(),
                          LinearProgressIndicator(
                            borderRadius: BorderRadius.circular(10),
                            value: uploadProgress,
                            backgroundColor: AppColors.white,
                            valueColor:
                                AlwaysStoppedAnimation(AppColors.greenColor),
                          ),
                        ],
                      ),
                    )
                  else
                    (hoveredIndices == index)
                        ? Container(
                            height: 80,
                            width: 80,
                            decoration: BoxDecoration(
                              color: isHovered
                                  ? Colors.grey[400]?.withOpacity(0.7)
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Center(
                              child: Visibility(
                                visible: isHovered,
                                child: InkWell(
                                  onTap: () {
                                    removeFile(file: attachment, index: index);
                                  },
                                  child: const Icon(
                                    Icons.delete,
                                    color: AppColors.whiteColor,
                                    size: 30,
                                  ),
                                ),
                              ),
                            ))
                        : Container(),
                ],
              ),
            ),
          ),
          if (widget.isOnlyDisplay)
            const Positioned(
                top: 0,
                left: 0,
                child: Icon(
                  Icons.cloud_download_outlined,
                  color: AppColors.greenColor,
                ))
        ],
      ),
    );
  }

  DecorationImage getFileDisplayWidget(File file) {
    String fileExtension = path.extension(file.path).replaceFirst('.', '');

    if (fileExtension == 'jpg' ||
        fileExtension == 'jpeg' ||
        fileExtension == 'png' ||
        fileExtension == 'gif') {
      return DecorationImage(
        image: FileImage(file),
        fit: BoxFit.cover, // Adjust fit as needed
      );
    }

    if (fileExtension == 'mp4' ||
        fileExtension == 'avi' ||
        fileExtension == 'mov' ||
        fileExtension == 'mkv') {
      return const DecorationImage(
        image: AssetImage('assets/icon/task_icons/video.png'),
      );
    }

    return const DecorationImage(
      image: AssetImage('assets/icon/task_icons/pdff.png'),
    );
  }

  void _openFile(File file) async {
    try {
      final result = await OpenFile.open(file.path);
      if (result.type == ResultType.done) {
        Logger.logInfo(
            sourceClass, 'openFile', 'File opened successfully: ${file.path}');
      } else {
        Logger.logInfo(
            sourceClass, 'openFile', 'Failed to open file: ${file.path}');
      }
    } catch (e) {
      Logger.logInfo(sourceClass, 'openFile', e.toString());
    }
  }

  void _openVideo(File file) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoPlayerScreen(file: file),
      ),
    );
  }
}
*/
