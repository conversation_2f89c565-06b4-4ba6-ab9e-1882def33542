import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:rounds/models/intractive_Item_Model.dart';
import 'package:rounds/providers/calendar_provider.dart';
import 'package:rounds/utils/app_colors.dart';

import '../pages/inspection/inspection_screen.dart';

class HorizontalCalendar extends ConsumerStatefulWidget {
  @override
  _HorizontalCalendarState createState() => _HorizontalCalendarState();
}

class _HorizontalCalendarState extends ConsumerState<HorizontalCalendar> {
  late DateTime _selectedDate;
  final List<DateTime> _weekDates = [];

  @override
  void initState() {
    _selectedDate = DateTime.now();
    final rawString = DateFormat('yyyyMMdd').format(DateTime.now());

    final cleaned = rawString.replaceAll(RegExp(r'[^0-9]'), '');

    try {
      if (cleaned.length >= 8) {
        final year = int.parse(cleaned.substring(0, 4));
        final month = int.parse(cleaned.substring(4, 6));
        final day = int.parse(cleaned.substring(6, 8));
        _selectedDate = DateTime(year, month, day);
      }
    } catch (e) {
      _selectedDate = DateTime.now();
    }

    super.initState();
    _updateWeekDates();
  }

  @override
  void didUpdateWidget(HorizontalCalendar oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateWeekDates();
  }

  void _updateWeekDates() {
    _weekDates.clear();
    for (int i = -1; i <= 5; i++) {
      _weekDates.add(DateTime.now().add(Duration(days: i)));
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 65,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: _weekDates.map((date) {
          bool isSelected = date.day == _selectedDate.day &&
              date.month == _selectedDate.month &&
              date.year == _selectedDate.year;

          return InkWell(
            borderRadius: BorderRadius.circular(20), // Rounded splash
            onTap: () {
              setState(() {
                _selectedDate = date;
              });

              // Update the selected date in the provider
              ref.read(selectedDateProvider.notifier).state =
                  DateFormat('yyyyMMdd').format(date);
              roundDetailViewNotifier.value =
                  InteractiveItemModel(type: '', data: {});
            },
            child: Container(
              width: 45,
              padding: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: isSelected ? AppColors.primaryColor : AppColors.white,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected
                      ? AppColors.primaryColor
                      : AppColors.cardBorderGrey,
                  width: 1.0,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    DateFormat('EEE').format(date),
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.black54,
                      fontSize: 11,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.w400,
                    ),
                  ),
                  const SizedBox(height: 3),
                  Text(
                    date.day.toString(),
                    style: TextStyle(
                      fontSize: isSelected ? 16 : 12,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Colors.white : Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
