import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class CameraCaptureScreen extends StatefulWidget {
  final Function(Uint8List imageBytes) onImageCaptured;
  final Function(XFile file)onImageCapturedFile;

  const CameraCaptureScreen({Key? key, required this.onImageCaptured, required this.onImageCapturedFile})
      : super(key: key);

  @override
  State<CameraCaptureScreen> createState() => _CameraCaptureScreenState();
}

class _CameraCaptureScreenState extends State<CameraCaptureScreen> {
  List<CameraDescription> _availableCameras = [];
  CameraController? _controller;
  CameraDescription? _selectedCamera;
  bool _isLoading = true;
  bool _isCameraReady = false;

  @override
  void initState() {
    super.initState();
    _fetchCameras();
  }

  Future<void> _fetchCameras() async {
    try {
      _availableCameras = await availableCameras();
      if (_availableCameras.isNotEmpty) {
        _selectedCamera = _availableCameras.first;
        await _initializeCamera(_selectedCamera!);
      }
    } catch (e) {
      debugPrint("Camera fetch error: $e");
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _initializeCamera(CameraDescription camera) async {
    _isCameraReady = false;
    setState(() {});
    _controller?.dispose();

    _controller = CameraController(
      camera,
      ResolutionPreset.high,
      enableAudio: false,
    );

    try {
      await _controller!.initialize();
      _isCameraReady = true;
    } catch (e) {
      debugPrint("Camera init error: $e");
    }

    if (mounted) setState(() {});
  }

  Future<void> _captureImage() async {
    try {
      if (_controller != null && _controller!.value.isInitialized) {
        final XFile file = await _controller!.takePicture();
        final bytes = await file.readAsBytes();
        widget.onImageCaptured(bytes);
        widget.onImageCapturedFile(file);
        Navigator.pop(context);
      }
    } catch (e) {
      debugPrint("Capture error: $e");
    }
  }

  void _onCameraSwitch(CameraDescription? newCamera) async {
    if (newCamera != null && newCamera != _selectedCamera) {
      _selectedCamera = newCamera;
      await _initializeCamera(_selectedCamera!);
    }
  }

  Widget _cameraPreviewWidget() {
    if (!_isCameraReady ||
        _controller == null ||
        !_controller!.value.isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    return ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: AspectRatio(
            aspectRatio: _controller!.value.aspectRatio,
            child: CameraPreview(_controller!)));
  }

  String _cameraName(CameraDescription cam) {
    // switch (cam.lensDirection) {
    //   case CameraLensDirection.front:
    //     return "Front Camera";
    //   case CameraLensDirection.back:
    //     return "Back Camera";
    //   case CameraLensDirection.external:
    //     return "External Camera";
    //   default:
    //     return "Camera";
    // }
    return cam.name;
  }

  @override
  void dispose() {
    // _controller?.pausePreview();
    // _controller?.debugCheckIsDisposed();
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _availableCameras.isEmpty
              ? const Center(
                  child: Text("No camera found! Connect a camera"),
                )
              : Stack(
                  children: [
                    Positioned.fill(child: _cameraPreviewWidget()),
                    Positioned(
                      top: 10,
                      left: 20,
                      child: ClipOval(
                        child: Material(
                          color: Colors.black54,
                          child: InkWell(
                            onTap: () => Navigator.of(context).pop(),
                            child: const SizedBox(
                              width: 40,
                              height: 40,
                              child: Icon(
                                Icons.arrow_back,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    // if (_availableCameras.length > 1)
                    Positioned(
                      top: 20,
                      right: 20,
                      child: Container(
                        constraints: const BoxConstraints(
                          maxHeight: 30,
                          minHeight: 30,
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.white30, width: 1),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<CameraDescription>(
                            value: _selectedCamera,
                            dropdownColor: Colors.grey[900],
                            isDense: true,
                            icon: const Icon(Icons.arrow_drop_down,
                                size: 16, color: Colors.white),
                            style: const TextStyle(
                                color: Colors.white, fontSize: 12),
                            alignment: Alignment.center,
                            onChanged: _onCameraSwitch,
                            items: _availableCameras
                                .map((cam) => DropdownMenuItem(
                                      value: cam,
                                      child: Text(
                                        _cameraName(cam),
                                        style: const TextStyle(fontSize: 12),
                                      ),
                                    ))
                                .toList(),
                          ),
                        ),
                      ),
                    ),

                    Positioned(
                      bottom: 30,
                      left: 0,
                      right: 0,
                      child: Center(
                        child: FloatingActionButton(
                          onPressed: _captureImage,
                          child: const Icon(Icons.camera_alt),
                        ),
                      ),
                    ),
                  ],
                ),
    );
  }
}
