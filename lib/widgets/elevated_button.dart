import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/utils/app_colors.dart';
//#1668af

class CustomElevatedButton extends ConsumerWidget {
  const CustomElevatedButton({
    super.key,
    required this.title,
    required this.ontap,
    this.isLoading = false,
  });

  final String title;
  final VoidCallback ontap;
  final bool isLoading; // Add isLoading parameter

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: 45,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white),
        color:  isLoading ? AppColors.black.withOpacity(0.4) : AppColors.buttonColor,
      ),
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: isLoading ? Colors.grey : Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onPressed: isLoading ? null : ontap,
        child: isLoading
            ? const Row(
                // mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Spacer(flex: 3,),
                  SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  ),
                  Spacer(flex: 1,),
                  Text(
                    'Loading...',
                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500, fontSize: 16, letterSpacing: 0.5),
                  ),
                  Spacer(flex: 4,),
                ],
              )
            : Text(
                title,
                style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w500, fontSize: 16, letterSpacing: 0.5),
              ),
      ),
    );
  }
}
