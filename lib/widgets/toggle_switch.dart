import 'package:flutter/material.dart';

class RoundSegmentedSwitch extends StatefulWidget {
  final String firstLabel;
  final String secondLabel;
  final Function(bool)
      onChanged; // false for first option, true for second option
  final bool value;
  final Color activeColor;
  final Color inactiveColor;
  final Color backgroundColor;
  final Color textColor;
  final double height;
  final double borderRadius;

  const RoundSegmentedSwitch({
    Key? key,
    required this.firstLabel,
    required this.secondLabel,
    required this.onChanged,
    this.value = false,
    this.backgroundColor = const Color(0xff3F51B5),
    this.activeColor = Colors.white,
    this.inactiveColor = const Color(0xff3F51B5),
    this.textColor = Colors.white,
    this.height = 45,
    this.borderRadius = 25,
  }) : super(key: key);

  @override
  State<RoundSegmentedSwitch> createState() => _RoundSegmentedSwitchState();
}

class _RoundSegmentedSwitchState extends State<RoundSegmentedSwitch> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // First Option Toggle
          _buildToggleItem(
            label: widget.firstLabel,
            isSelected: widget.value,
            onTap: () {
              if (widget.value) {
                widget.onChanged(false);
              }
            },
          ),
          // Second Option Toggle
          _buildToggleItem(
            label: widget.secondLabel,
            isSelected: !widget.value,
            onTap: () {
              if (!widget.value) {
                widget.onChanged(true);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildToggleItem({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Container(
          height: widget.height,
          decoration: BoxDecoration(
            color: !isSelected ? widget.activeColor : widget.inactiveColor,
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
          margin: const EdgeInsets.all(2),
          child: Center(
            child: Text(
              label,
              style: TextStyle(
                color: isSelected ? widget.activeColor : widget.inactiveColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class ToggleSwitch extends StatefulWidget {
  final bool value;
  final ValueChanged<bool> onChanged;
  final String activeText;
  final String inactiveText;
  final Color activeColor;
  final Color inactiveColor;
  final Color thumbColor;
  final Color textColor;

  const ToggleSwitch({
    Key? key,
    required this.value,
    required this.onChanged,
    this.activeText = 'ON',
    this.inactiveText = 'OFF',
    this.activeColor = const Color(0xFF8B3DFF),
    this.inactiveColor = Colors.grey,
    this.thumbColor = Colors.white,
    this.textColor = Colors.white,
  }) : super(key: key);

  @override
  State<ToggleSwitch> createState() => _ToggleSwitchState();
}

class _ToggleSwitchState extends State<ToggleSwitch>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      value: widget.value ? 1.0 : 0.0,
      vsync: this,
    );
    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeInOut);
  }

  @override
  void didUpdateWidget(ToggleSwitch oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.onChanged(!widget.value);
      },
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return Container(
            width: 76, // Increased width to accommodate both texts
            height: 20,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: widget.value ? widget.activeColor : widget.inactiveColor,
            ),
            child: Stack(
              children: [
                // ON Text
                Positioned(
                  left: 8,
                  top: 0,
                  bottom: 0,
                  child: Opacity(
                    opacity: widget.value ? 1.0 : 0.0,
                    child: Center(
                      child: Text(
                        widget.activeText,
                        style: TextStyle(
                          color: widget.textColor,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
                // OFF Text
                Positioned(
                  right: 16,
                  top: 0,
                  bottom: 0,
                  child: Opacity(
                    opacity: widget.value ? 0.0 : 1.0,
                    child: Center(
                      child: Text(
                        widget.inactiveText,
                        style: TextStyle(
                          color: widget.textColor,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
                // Thumb
                Positioned(
                  left: _animation.value * 50, // Adjusted travel distance
                  child: Container(
                    width: 28,
                    height: 17,
                    margin:
                        const EdgeInsets.symmetric(horizontal: 2, vertical: 2),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: widget.thumbColor,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
