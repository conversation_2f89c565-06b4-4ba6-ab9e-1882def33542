import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:flutter/scheduler.dart';

class CameraVideoCaptureScreen extends StatefulWidget {
  final Duration maxDuration;
  final Function(Uint8List bytes, String fileExtension) onVideoCaptured;
  final Function(XFile file) onVideoCapturedFile;

  const CameraVideoCaptureScreen({
    Key? key,
    required this.maxDuration,
    required this.onVideoCapturedFile,
    required this.onVideoCaptured,
  }) : super(key: key);

  @override
  State<CameraVideoCaptureScreen> createState() =>
      _CameraVideoCaptureScreenState();
}

class _CameraVideoCaptureScreenState extends State<CameraVideoCaptureScreen> {
  List<CameraDescription> _availableCameras = [];
  CameraController? _controller;
  CameraDescription? _selectedCamera;
  bool _isLoading = true;
  bool _isRecording = false;
  Stopwatch _stopwatch = Stopwatch();
  late final Ticker _ticker;

  @override
  void initState() {
    super.initState();
    _fetchCameras();
    _ticker = Ticker((_) {
      if (mounted && _isRecording) setState(() {});
    })
      ..start();
  }

  Future<void> _fetchCameras() async {
    try {
      _availableCameras = await availableCameras();
      if (_availableCameras.isNotEmpty) {
        _selectedCamera = _availableCameras.first;
        await _initializeCamera(_selectedCamera!);
      }
    } catch (e) {
      debugPrint("Camera fetch error: $e");
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _initializeCamera(CameraDescription camera) async {
    _controller?.dispose();
    _controller = CameraController(
      camera,
      ResolutionPreset.high,
      enableAudio: true,
    );
    try {
      await _controller!.initialize();
    } catch (e) {
      debugPrint("Camera init error: $e");
    }
    if (mounted) setState(() {});
  }

  Future<void> _startRecording() async {
    if (_controller == null || !_controller!.value.isInitialized) return;

    try {
      await _controller!.startVideoRecording();
      _stopwatch.reset();
      _stopwatch.start();
      setState(() => _isRecording = true);

      // Auto-stop after max duration
      Future.delayed(widget.maxDuration, () {
        if (_isRecording) _stopRecording();
      });
    } catch (e) {
      debugPrint("Start recording error: $e");
    }
  }

  Future<void> _stopRecording() async {
    try {
      final file = await _controller!.stopVideoRecording();
      _stopwatch.stop();
      setState(() => _isRecording = false);

      final bytes = await file.readAsBytes();

      widget.onVideoCaptured(bytes, '.mp4');
      widget.onVideoCapturedFile(file);
      if (mounted) Navigator.pop(context);
    } catch (e) {
      debugPrint("Stop recording error: $e");
    }
  }

  void _onCameraSwitch(CameraDescription? newCamera) async {
    if (newCamera != null && newCamera != _selectedCamera) {
      _selectedCamera = newCamera;
      await _initializeCamera(_selectedCamera!);
    }
  }

  String _cameraName(CameraDescription cam) {
    // switch (cam.lensDirection) {
    //   case CameraLensDirection.front:
    //     return "Front Camera";
    //   case CameraLensDirection.back:
    //     return "Back Camera";
    //   case CameraLensDirection.external:
    //     return "External Camera";
    //   default:
    //     return "Camera";
    // }
    return cam.name;
  }

  @override
  void dispose() {
    _controller?.dispose();
    _ticker.dispose();
    super.dispose();
  }

  Widget _buildRecordingIndicator() {
    final elapsed = _stopwatch.elapsed;
    final timeText =
        "${elapsed.inMinutes.toString().padLeft(2, '0')}:${(elapsed.inSeconds % 60).toString().padLeft(2, '0')}";

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(Icons.fiber_manual_record, color: Colors.red),
        const SizedBox(width: 4),
        Text(
          timeText,
          style:
              const TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final isInitialized = _controller?.value.isInitialized ?? false;

    return Scaffold(
      // appBar: AppBar(
      //   title: const Text("Record Video"),
      //   actions: _availableCameras.length > 1
      //       ? [
      //           Padding(
      //             padding: const EdgeInsets.symmetric(horizontal: 8.0),
      //             child: DropdownButtonHideUnderline(
      //               child: DropdownButton<CameraDescription>(
      //                 value: _selectedCamera,
      //                 dropdownColor: Colors.white,
      //                 iconEnabledColor: Colors.white,
      //                 onChanged: _onCameraSwitch,
      //                 items: _availableCameras.map((camera) {
      //                   return DropdownMenuItem(
      //                     value: camera,
      //                     child: Text(_cameraName(camera)),
      //                   );
      //                 }).toList(),
      //               ),
      //             ),
      //           ),
      //         ]
      //       : null,
      // ),

      body: _isLoading || !isInitialized
          ? const Center(child: CircularProgressIndicator())
          : _availableCameras.isEmpty
              ? const Center(
                  child: Text("No camera found! Connect a camera"),
                )
              : Stack(
                  children: [
                    Positioned.fill(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: CameraPreview(_controller!),
                      ),
                    ),
                    Positioned(
                      top: 10,
                      left: 20,
                      child: ClipOval(
                        child: Material(
                          color: Colors.black54,
                          child: InkWell(
                            onTap: () => Navigator.of(context).pop(),
                            child: const SizedBox(
                              width: 40,
                              height: 40,
                              child: Icon(
                                Icons.arrow_back,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                    // if (_availableCameras.length > 1)
                    Positioned(
                      top: 20,
                      right: 20,
                      child: Container(
                        constraints: const BoxConstraints(
                          maxHeight: 30,
                          minHeight: 30,
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.white30, width: 1),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<CameraDescription>(
                            value: _selectedCamera,
                            dropdownColor: Colors.grey[900],
                            isDense: true,
                            icon: const Icon(Icons.arrow_drop_down,
                                size: 16, color: Colors.white),
                            style: const TextStyle(
                                color: Colors.white, fontSize: 12),
                            alignment: Alignment.center,
                            onChanged: _onCameraSwitch,
                            items: _availableCameras
                                .map((cam) => DropdownMenuItem(
                                      value: cam,
                                      child: Text(
                                        _cameraName(cam),
                                        style: const TextStyle(fontSize: 12),
                                      ),
                                    ))
                                .toList(),
                          ),
                        ),
                      ),
                    ),

                    if (_isRecording)
                      Positioned(
                        top: 20,
                        left: 20,
                        child: _buildRecordingIndicator(),
                      ),
                    Positioned(
                      bottom: 30,
                      left: 0,
                      right: 0,
                      child: Center(
                        child: FloatingActionButton(
                          backgroundColor:
                              _isRecording ? Colors.grey : Colors.red,
                          onPressed:
                              _isRecording ? _stopRecording : _startRecording,
                          child:
                              Icon(_isRecording ? Icons.stop : Icons.videocam),
                        ),
                      ),
                    ),
                  ],
                ),
    );
  }
}
