import 'package:flutter/material.dart';
import 'package:rounds/utils/app_colors.dart';

class SkipButton extends StatelessWidget {
  final bool isActive;
  final VoidCallback onPressed;
  final double width;
  final double height;
  final String? skipped;

  const SkipButton({
    Key? key,
    this.isActive = true,
    required this.onPressed,
    this.width = 200,
    this.height = 60,
    this.skipped,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Colors based on active state
    final backgroundColor = isActive ? AppColors.redAccentColor : Colors.grey;
    final circleColor =
        isActive ? const Color(0xFFE8F1FF) : const Color(0xFFE0E0E0);
    final iconColor =
        isActive ? AppColors.redAccentColor : Colors.grey.shade600;

    return InkWell(
      onTap: isActive ? onPressed : null,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(30),
        ),
        child: <PERSON><PERSON>(
          children: [
            // Text part
            Positioned.fill(
              child: Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding: EdgeInsets.only(left: width * 0.15),
                  child: Text(
                    skipped ?? 'SKIP',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: height * 0.4,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),

            // Circle with arrow
            Positioned(
              right: 0,
              top: 0,
              bottom: 0,
              child: Container(
                width: height,
                height: height,
                decoration: BoxDecoration(
                  color: circleColor,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.skip_next_rounded,
                        color: iconColor,
                        size: height * 0.65,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
