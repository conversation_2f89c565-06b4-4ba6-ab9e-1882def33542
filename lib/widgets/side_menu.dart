import 'package:flutter/material.dart';

class MenuItem {
  final IconData icon;
  final String label;

  MenuItem({required this.icon, required this.label});
}

class SideMenu extends StatefulWidget {
  final Function(int) onMenuItemSelected;

  const SideMenu({
    Key? key,
    required this.onMenuItemSelected,
  }) : super(key: key);

  @override
  _SideMenuState createState() => _SideMenuState();
}

class _SideMenuState extends State<SideMenu> {
  int _selectedIndex = 0;

  // Define the menu items in a list
  final List<MenuItem> _menuItems = [
    MenuItem(icon: Icons.home, label: 'Home'),
    MenuItem(icon: Icons.location_on, label: 'Locations'),
    MenuItem(icon: Icons.business, label: 'CILT'),
    MenuItem(icon: Icons.lightbulb, label: 'Inception'),
    MenuItem(icon: Icons.notifications, label: 'Notification'),
    MenuItem(icon: Icons.task, label: 'Tasks'),
    MenuItem(icon: Icons.settings, label: 'Settings'),
  ];

  void _onMenuItemTap(int index) {
    setState(() {
      _selectedIndex = index;
    });
    widget.onMenuItemSelected(index);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 110,
      color: Colors.black,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: _menuItems.asMap().entries.map((entry) {
          int index = entry.key;
          MenuItem item = entry.value;
          return _buildMenuItem(
            icon: item.icon,
            label: item.label,
            index: index,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String label,
    required int index,
  }) {
    bool isSelected = _selectedIndex == index;

    return GestureDetector(
      onTap: () => _onMenuItemTap(index),
      child: AnimatedContainer(
        duration: Duration(milliseconds: 300),
        color:
            isSelected ? Color.fromARGB(255, 117, 21, 146) : Colors.transparent,
        padding: EdgeInsets.symmetric(vertical: 16, horizontal: 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white),
            SizedBox(height: 8),
            FittedBox(
              child: Text(
                label,
                maxLines: 1,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
