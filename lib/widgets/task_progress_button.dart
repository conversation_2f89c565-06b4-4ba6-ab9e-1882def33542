import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

class TaskProgressButton extends StatefulWidget {
  @override
  _TaskProgressButtonState createState() => _TaskProgressButtonState();
}

class _TaskProgressButtonState extends State<TaskProgressButton> {
  bool start = true;
  List<String> tasks = ['Task 1', 'Task 2', 'Task 3', 'Task 4', 'Task 5'];
  int completedTasks = 0;
  double get completionPercentage => (completedTasks / tasks.length) * 100;

  void completeTask() {
    start = false;
    if (completedTasks < tasks.length) {
      setState(() {
        completedTasks++;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: ElevatedButton(
        onPressed: completeTask,
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
          backgroundColor: AppColors.primaryColor,

          ///check
          padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 16),
          textStyle: const TextStyle(fontSize: 16),
        ),
        child: Text(
          start
              ? 'Start'
              : completedTasks == tasks.length
                  ? 'Submit'
                  : '${completionPercentage.toStringAsFixed(0)}% Task Completed',
          style: const TextStyle(fontSize: 16),
        ),
      ),
    );
  }
}
