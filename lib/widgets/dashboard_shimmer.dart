import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/PLANT_HEADER.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/models/intractive_Item_Model.dart';
import 'package:rounds/pages/assets/assets.dart';
import 'package:rounds/pages/dashboard/dashboard.dart';
import 'package:rounds/pages/dashboard/widgets/top_header.dart';
import 'package:rounds/pages/fault/fault_screen.dart';
import 'package:rounds/pages/inspection/inspection_screen.dart';
import 'package:rounds/pages/job_creation/job_creation_screen.dart';
import 'package:rounds/pages/login/login.dart';
import 'package:rounds/pages/login/login_state/login_state.dart';
import 'package:rounds/providers/assets/asset_provider.dart';
import 'package:rounds/providers/assets/floc_provider.dart';
import 'package:rounds/providers/assets/kpi_provider.dart';
import 'package:rounds/providers/cilt/cilt_header_provider.dart';
import 'package:rounds/providers/fault/fault_header_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:unvired_settings/main.dart';

class DashboardShimmer extends ConsumerWidget {
  DashboardShimmer({Key? key}) : super(key: key);

  Future<List<PLANT_HEADER>> _fetchPlantList(WidgetRef ref) async {
    var data = await ref.read(plantListProvider.notifier).fetchPlantsList();
    return data;
  }

  _headerTitle(BuildContext context, WidgetRef ref) {
    String plantName = ref
            .watch(plantListProvider)
            .firstWhereOrNull(
                (plant) => plant.plant_id == ref.watch(plantProvider))
            ?.plant_name ??
        "";

    plantName = "$plantName (${ref.watch(plantProvider)})";
    return InkWell(
      onTap: () {
        _handleTap(context);
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.you_are_at,
            style: const TextStyle(color: Colors.black, fontSize: 12),
          ),
          SizedBox(
            width: MediaQuery.of(context).size.width * 0.36,
            child: Text(
              plantName,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                  fontSize: 15,
                  letterSpacing: 0.5),
            ),
          )
        ],
      ),
    );
  }

  int _tapCount = 0;
  DateTime? _lastTapTime;
  void _handleTap(BuildContext context) {
    final now = DateTime.now();

    if (_lastTapTime == null ||
        now.difference(_lastTapTime!) > Duration(seconds: 2)) {
      _tapCount = 1;
    } else {
      _tapCount++;
    }

    _lastTapTime = now;

    if (_tapCount == 7) {
      _tapCount = 0;
      _navigateToSettingsPage(context);
    }
  }

  _navigateToSettingsPage(context) {
    Navigator.push(
        context,
        CupertinoPageRoute(
            builder: ((context) => Settings(
                  themeData: ThemeData(useMaterial3: false),
                )))).then((value) {});
  }

  List<BottomNavigationBarItem> buildBottomNavigationBarItems(
      int selectedIndex) {
    final items = [
      {'icon': Icons.home, 'label': 'Dashboard'},
      {'icon': Icons.assignment, 'label': 'Rounds'},
      {'icon': Icons.error_outline, 'label': 'Fault'},
      {'icon': 'assets/icon/setting.svg', 'label': 'Job'},
      {'icon': Icons.inventory, 'label': 'Asset'},
    ];

    return items.asMap().entries.map((entry) {
      int idx = entry.key;
      var item = entry.value;
      return BottomNavigationBarItem(
        icon: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          decoration: BoxDecoration(
            color: selectedIndex == idx
                ? Colors.white.withOpacity(0.2)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.all(8),
          child: buildIconWidget(item['icon']),
        ),
        activeIcon: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          decoration: BoxDecoration(
            color: selectedIndex == idx
                ? Colors.white.withOpacity(0.2)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.all(8),
          child: buildIconWidgett(item['icon']),
        ),
        label: item['label'] as String,
      );
    }).toList();
  }

  Widget buildIconWidget(dynamic icon) {
    if (icon is IconData) {
      return Icon(icon);
    } else if (icon is String) {
      return SvgPicture.asset(
        icon,
        height: 20,
        width: 20,
      );
    }
    return const SizedBox.shrink();
  }

  Widget buildIconWidgett(dynamic icon) {
    if (icon is IconData) {
      return Icon(
        icon,
      );
    } else if (icon is String) {
      return SvgPicture.asset(
        icon,
        height: 20,
        width: 20,
        color: ThemeData().colorScheme.primary,
      );
    }
    return const SizedBox.shrink();
  }

  void _onItemTapped(int index, WidgetRef ref) {
    final plant = ref.watch(plantProvider);
    final plantSection = ref.watch(plantSectionProvider);
    final shift = ref.watch(shiftProvider);
    final searchProvider = ref.read(roundsSearchProvider.notifier).state;
    ref.read(bottomNavIndexProvider.notifier).state = index;
    ref
        .read(filteredCiltProvider.notifier)
        .filter(plant, plantSection, shift, searchProvider, ref);
    ref.read(flocHeaderProvider.notifier).getLocHeaderList(plant);
    ref.read(assetHeaderProvider.notifier).getAssetHeaderList(plant);
    ref.read(kpiHeaderProvider.notifier).getKPIHeaderList(plant);
    roundDetailViewNotifier.value = InteractiveItemModel(
      type: "",
      data: {"type": "", "index": null},
    );
    faultDetailViewNotifier.value = InteractiveItemModel(
      type: "",
      data: {"type": "", "index": null},
    );
    jobDetailViewNotifier.value = InteractiveItemModel(
      type: "",
      data: {"type": "", "index": null},
    );
    assetDetailViewNotifier.value = InteractiveItemModel(
      type: "",
      data: {"type": "", "index": null},
    );
    /*  calendarKey.currentState?.checkAndRefreshIfMidnightPassed();*/
  }

  //Logout
  void onLogOut(BuildContext context, WidgetRef ref) {
    UIHelper.showConfirmationDialogWithYesOrNo(context,
        description: AppLocalizations.of(context)!.alert_msg_to_clear_data,
        yes: () {
      Navigator.pop(context);
      clearData(context, ref);
    }, no: () {
      Navigator.pop(context);
    });
  }

  clearData(BuildContext context, WidgetRef ref) async {
    try {
      await SettingsHelper().clearData();
    } catch (e) {
      Logger.logError('ProfileScreen', 'clearData', e.toString());
    }
    final prefs = await SharedPreferences.getInstance();
    // Preserve userName and userEmail before clearing
    final userName = prefs.getString('userName');
    final userEmail = prefs.getString('userEmail');
    await prefs.clear();
    if (kIsWeb) {
      if (userName != null) {
        await prefs.setString('userName', userName);
      }
      if (userEmail != null) {
        await prefs.setString('userEmail', userEmail);
      }
    }
    ref.read(loginStateProvider.notifier).setLoginState(LoginState.domain);
    Future.delayed(Duration(milliseconds: 300), () {
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (Route<dynamic> route) => false,
      );
    });
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(bottomNavIndexProvider);
    return Scaffold(
      body: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.backgroundGrey,
            ),
            child: SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: MediaQuery.of(context).size.height,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    children: [
                      Expanded(
                        child: NavigationRail(
                          selectedIndex: selectedIndex,
                          onDestinationSelected: (int index) =>
                              _onItemTapped(index, ref),
                          backgroundColor: AppColors.backgroundGrey,
                          labelType: NavigationRailLabelType.all,
                          destinations:
                              buildBottomNavigationBarItems(selectedIndex)
                                  .map((item) {
                            return NavigationRailDestination(
                              icon: item.icon,
                              selectedIcon: item.activeIcon ?? item.icon,
                              label: Text(item.label ?? ''),
                            );
                          }).toList(),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: Column(
                          children: [
                            IconButton(
                                splashRadius: 20,
                                focusColor:
                                    AppColors.primaryColor.withOpacity(0.5),
                                highlightColor:
                                    AppColors.primaryColor.withOpacity(0.5),
                                splashColor:
                                    AppColors.primaryColor.withOpacity(0.5),
                                icon: const Icon(Icons.logout),
                                tooltip: 'Logout',
                                onPressed: () => onLogOut(context, ref)),
                            const Text('Logout'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Expanded(child: getDashBoardShimmer(context, ref)),
        ],
      ),
    );
  }

  Column getDashBoardShimmer(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(
              horizontal: 18,
              vertical: UIHelper().getScreenType(context) == ScreenType.desktop
                  ? 8
                  : 0),
          child: FutureBuilder(
            future: _fetchPlantList(ref),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return buildAppBarShimmer(); // Show nothing while loading
              } else if (snapshot.hasError) {
                return buildAppBarShimmer();
              } else {
                return TopHeader(
                  titleWidget: _headerTitle(context, ref),
                );
              }
            },
          ),
        ),
        SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Top row - Inspections and CILT
              Row(
                children: [
                  Expanded(child: _buildDashboardCard('Inspections')),
                  const SizedBox(width: 16),
                  Expanded(child: _buildDashboardCard('CILT')),
                ],
              ),
              const SizedBox(height: 16),
              // Bottom row - Faults and Jobs
              Row(
                children: [
                  Expanded(child: _buildDashboardCard('Faults')),
                  const SizedBox(width: 16),
                  Expanded(child: _buildDashboardCard('Jobs')),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildAppBarShimmer() {
    return Container(
      height: 56, // Standard AppBar height
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // Leading icon shimmer
          _buildShimmerItem(
            child: Container(
              width: 40,
              height: 40,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
            ),
          ),
          const SizedBox(width: 16),
          // Title shimmer
          _buildShimmerItem(
            child: Container(
              width: 150,
              height: 20,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
          const Spacer(),
          // Optional trailing action shimmer
          _buildShimmerItem(
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardCard(String title) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          _buildShimmerItem(
            child: Container(
              width: title.length * 8.0,
              height: 18,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
          const SizedBox(height: 24),
          // Main content area with chart and stats
          Row(
            children: [
              // Left side - Chart
              Expanded(
                flex: 5,
                child: Center(
                  child: _buildShimmerItem(
                    child: Container(
                      width: 200,
                      height: 200,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: _buildShimmerItem(
                          child: Container(
                            width: 40,
                            height: 20,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Right side - Stats list
              Expanded(
                flex: 1,
                child: _buildStatsList(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildStatItem(),
        const SizedBox(height: 12),
        _buildStatItem(),
        const SizedBox(height: 12),
        _buildStatItem(),
        const SizedBox(height: 12),
        _buildStatItem(),
        const SizedBox(height: 12),
        _buildStatItem(),
        const SizedBox(height: 12),
        _buildStatItem(),
      ],
    );
  }

  Widget _buildStatItem() {
    return Row(
      children: [
        // Color indicator
        _buildShimmerItem(
          child: Container(
            width: 8,
            height: 30,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(1.5),
            ),
          ),
        ),
        const SizedBox(width: 8),
        // Label
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildShimmerItem(
                child: Container(
                  width: double.infinity,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              _buildShimmerItem(
                child: Container(
                  width: 20,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerItem({required Widget child}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      period: const Duration(milliseconds: 1500),
      child: child,
    );
  }
}
