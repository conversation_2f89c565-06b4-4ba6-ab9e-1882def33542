import 'package:flutter/material.dart';
import 'package:rounds/utils/app_colors.dart';

class OverlayToast extends StatelessWidget {
  const OverlayToast({
    super.key,
    required this.entry,
    required this.onRefresh,
    required this.onCancel,
  });

  final OverlayEntry entry;
  final Function()? onRefresh;
  final Function()? onCancel;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: AppColors.primaryColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 10,
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.info_outline, color: Colors.white),
            const SizedBox(width: 10),
            const Text(
              "New data available",
              style: TextStyle(color: Colors.white),
            ),
            const SizedBox(width: 12),
            TextButton(
              onPressed: onRefresh,
              child: const Text(
                "Refresh",
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: onCancel,
              child: const Text(
                "Cancel",
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
