import 'package:flutter/cupertino.dart';

class QROverlayPainter extends CustomPainter {
  final Color borderColor;
  final double borderWidth;

  QROverlayPainter({required this.borderColor, required this.borderWidth});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = borderColor
      ..strokeWidth = borderWidth
      ..style = PaintingStyle.stroke;

    const cornerSize = 30.0;

    // Top Left
    canvas.drawLine(Offset(0, 0), Offset(cornerSize, 0), paint);
    canvas.drawLine(Offset(0, 0), Offset(0, cornerSize), paint);

    // Top Right
    canvas.drawLine(
        Offset(size.width - cornerSize, 0), Offset(size.width, 0), paint);
    canvas.drawLine(
        Offset(size.width, 0), Offset(size.width, cornerSize), paint);

    // Bottom Left
    canvas.drawLine(
        Offset(0, size.height), Offset(0, size.height - cornerSize), paint);
    canvas.drawLine(
        Offset(0, size.height), Offset(cornerSize, size.height), paint);

    // Bottom Right
    canvas.drawLine(Offset(size.width, size.height - cornerSize),
        Offset(size.width, size.height), paint);
    canvas.drawLine(Offset(size.width - cornerSize, size.height),
        Offset(size.width, size.height), paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
