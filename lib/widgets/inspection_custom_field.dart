import 'package:flutter/material.dart';
import '../../../utils/app_colors.dart';

class InspCustomTextFieldWidget extends StatefulWidget {
  final String label;
  final TextEditingController controller;
  final ValueChanged<String>? onChanged;
  final String? dataString;
  final Widget? suffixIcon;
  final Color? borderColor;
  final int? maxlines;
  final VoidCallback? onSuffixIcon;
  final bool? readOnly;
  final TextInputType? keyboardType;
  final TextStyle? textStyle;
  final FocusNode? focusNode; GestureTapCallback? onTap;


  InspCustomTextFieldWidget(
      {super.key,
      required this.controller,
      this.onChanged,
      this.dataString,
      required this.label,
      this.suffixIcon,
      this.borderColor,
      this.maxlines,
      this.onSuffixIcon,
      this.readOnly,
      this.keyboardType,
      this.textStyle,
      this.focusNode,this.onTap});

  @override
  _InspCustomTextFieldWidgetState createState() =>
      _InspCustomTextFieldWidgetState();
}

class _InspCustomTextFieldWidgetState extends State<InspCustomTextFieldWidget> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 15, bottom: 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: SizedBox(
              height: widget.maxlines != null ? 40 : null,
              child: TextFormField(
                style: widget.textStyle,
                controller: widget.controller,
                focusNode: widget.focusNode,
                onChanged: widget.onChanged,
                maxLines: widget.maxlines,
                readOnly: widget.readOnly ?? false,
                onTap: widget.onTap,
                keyboardType: widget.keyboardType,
                decoration: InputDecoration(
                    hintText: widget.label,
                    hintStyle: TextStyle(color: AppColors.greySubtitleText),
                    border: UnderlineInputBorder(
                        // gapPadding: 2,
                        borderSide: BorderSide(
                            color: widget.borderColor ?? AppColors.grey,
                            width: 1.5)),
                    enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(
                            color: widget.borderColor ?? AppColors.grey,
                            width: 1.5)),
                    focusedBorder: UnderlineInputBorder(
                        borderSide: BorderSide(
                            color: widget.borderColor ?? AppColors.grey,
                            width: 1.5)),
                    suffixIcon: IconButton(
                        onPressed: widget.onSuffixIcon,
                        icon: widget.suffixIcon ?? Container())),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
