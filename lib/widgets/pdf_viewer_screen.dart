import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pdfx/pdfx.dart';

class PdfViewerScreen extends ConsumerStatefulWidget {
  final String filePath;
  PdfViewerScreen({required this.filePath});

  @override
  _PdfViewerScreenWidgetState createState() => _PdfViewerScreenWidgetState();
}

class _PdfViewerScreenWidgetState extends ConsumerState<PdfViewerScreen> {
  late PdfControllerPinch _pdfController;

  @override
  void initState() {
    super.initState();
    _pdfController = PdfControllerPinch(
      document: PdfDocument.openFile(widget.filePath),
    );
  }

  @override
  void dispose() {
    _pdfController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: PdfViewPinch(
          controller: _pdfController,
        ),
      ),
    );
  }
}
