import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class HorizontalCalendar extends StatefulWidget {
  @override
  _HorizontalCalendarState createState() => _HorizontalCalendarState();
}

class _HorizontalCalendarState extends State<HorizontalCalendar> {
  DateTime selectedDate = DateTime.now();
  ScrollController _scrollController = ScrollController();
  List<DateTime> weekDates = [];

  @override
  void initState() {
    super.initState();
    // Create the list of 7 days (3 before, current day, 3 after)
    for (int i = -3; i <= 3; i++) {
      weekDates.add(selectedDate.add(Duration(days: i)));
    }
    // Auto scroll to center the current day (index 3)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      double initialOffset = 60.0 * 3; // 3 days before the current date
      _scrollController.jumpTo(initialOffset);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50, // Customize this height based on your layout
      child: ListView.builder(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        itemCount: weekDates.length, // Limited to only 7 days
        itemBuilder: (context, index) {
          DateTime date = weekDates[index];
          bool isSelected = date.day == selectedDate.day &&
              date.month == selectedDate.month &&
              date.year == selectedDate.year;

          return GestureDetector(
            onTap: () {
              setState(() {
                selectedDate = date;
              });
            },
            child: Container(
              width: 40, // Customize the width for each day
              margin: EdgeInsets.symmetric(horizontal: 5),
              decoration: BoxDecoration(
                color: isSelected ? Colors.blue : Colors.white,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  if (isSelected)
                    BoxShadow(
                      color: Colors.blueAccent.withOpacity(0.5),
                      spreadRadius: 2,
                      blurRadius: 5,
                    ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    DateFormat('EEE')
                        .format(date), // Displays day abbreviation (Mon, Tue)
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.black,
                    ),
                  ),
                  SizedBox(height: 5),
                  Text(
                    date.day.toString(), // Displays the day number
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Colors.white : Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
