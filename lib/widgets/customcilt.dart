// import 'package:flutter/material.dart';
// import 'package:hexcolor/hexcolor.dart';
// import 'package:rounds/utils/app_colors.dart';



// class CiltIndicator extends StatelessWidget {
//   final Color textColor;
//   final double? boxSize; // Optional fixed square size
//   final int type; // Optional fixed square size

//   const  CiltIndicator({
//     super.key,
//     this.textColor = Colors.white,
//     this.boxSize, // If null, size is dynamic
//     required this.type, // If null, size is dynamic
//   });

//   @override
//   Widget build(BuildContext context) {
//     String binary = type.toRadixString(2).padLeft(4, '0');
//     return LayoutBuilder(
//       builder: (context, constraints) {
//         // Calculate dynamic spacing and size
//         double calculatedSize =
//             (constraints.maxWidth - (3 * 8)) / 4; // Default spacing 8px
//         double size = boxSize ?? calculatedSize;
//         double spacing = size * 0.1; // Spacing is 10% of boxSize
//         size = size > 0 ? size : 40; // Prevent negative or zero sizes

//         return Row(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             _buildIndicatorBox('C', binary.substring(0,1) == "1", size),
//             SizedBox(width: spacing),
//             _buildIndicatorBox('I',  binary.substring(1,2) == "1", size),
//             SizedBox(width: spacing),
//             _buildIndicatorBox('L',  binary.substring(2,3) == "1", size),
//             SizedBox(width: spacing),
//             _buildIndicatorBox('T', binary.substring(3,4) == "1", size),
//           ],
//         );
//       },
//     );
//   }

//   Widget _buildIndicatorBox(String text,bool isActive, double size) {
//     double fontSize = size * 0.7;
//     Color color=HexColor("b1ce4d");
//       switch(text){
//         case 'C':
//           color = HexColor("b1ce4d");
//           break;
//         case 'I':
//           color = HexColor("ee622e");
//           break;
//         case 'L':
//           color = HexColor("faaf53");
//           break;
//         case 'T':
//           color = HexColor("8ECCF6");
//           break;
//       }
//     return Container(
//       width: size,
//       height: size, // Always a square
//       alignment: Alignment.center,
//       decoration: BoxDecoration(
//         color: isActive ? color : AppColors.grey,
//         borderRadius:
//         BorderRadius.circular(size * 0.2), // 20% radius of box size
//       ),
//       child: Text(
//         text,
//         style: TextStyle(
//           color: textColor,
//           fontSize: fontSize, // Dynamic font size
//           fontWeight: FontWeight.bold,
//         ),
//       ),
//     );
//   }
// }
