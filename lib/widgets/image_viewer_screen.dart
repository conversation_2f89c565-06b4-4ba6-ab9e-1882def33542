import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/be/DOCUMENT_ATTACHMENT.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../providers/fault/fault_header_provider.dart';
import '../services/app_notifier.dart';
import '../utils/app_colors.dart';

class ImageViewerScreen extends ConsumerStatefulWidget {
  final DOCUMENT_ATTACHMENT attachment;
  ImageViewerScreen({required this.attachment});

  @override
  _ImageViewerScreenWidgetState createState() =>
      _ImageViewerScreenWidgetState();
}

class _ImageViewerScreenWidgetState extends ConsumerState<ImageViewerScreen> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    String? file = widget.attachment.local_path != null
        ? widget.attachment.local_path!
        : null;

    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: IconButton(onPressed: (){
          Navigator.pop(context);
        }, icon: Icon(Icons.arrow_back_ios, color: Colors.black,)),
        elevation: 0.0,
      ),
      body: SafeArea(
        child: Center(
            child: file != null
                ? InteractiveViewer(
                    child: Image.file(
                      File(file),
                      fit: BoxFit.cover,
                    ),
                  )
                : Container()),
      ),
    );
  }
}
