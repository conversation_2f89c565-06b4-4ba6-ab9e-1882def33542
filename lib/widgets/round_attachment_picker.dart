import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logger/Logger.dart';
import 'package:open_file_plus/open_file_plus.dart';
import 'package:rounds/be/DOCUMENT_ATTACHMENT.dart';
import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:rounds/helpers/db_helper.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/utils/utils.dart';
import 'package:rounds/widgets/camera_capture.dart';
import 'package:rounds/widgets/camera_video_capture.dart';
import 'package:rounds/widgets/file_renderer/file_renderer.dart';
import 'package:rounds/widgets/video_player_screen.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:rounds/widgets/image_viewer_screen.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';
import 'dart:typed_data';
import 'package:unvired_sdk/unvired_sdk.dart';

/// A widget that allows users to pick attachments for a round.
///
/// This widget provides a user interface for selecting and attaching files
/// or other media to a round. It can be used in various contexts where
/// attachment functionality is required.
///
/// Example usage:
///
/// ```dart
/// return RoundsAttachmentPicker(
///       onAttachmentPicked: (result) async {
///
///       },
///      isGridView: false,
///       attachments: attachmentList
///           .map((e) => DocumentAttachmentContainer(
///                 attachment: e,
///                 onDelete: (value) {
///                   print("delete");
///                 },
///                 onTap: (value) {},
///               ))
///           .toList());
/// )
/// ```
///
/// The `onAttachmentSelected` callback is triggered when an attachment is
/// selected by the user.
class RoundsAttachmentPicker extends StatelessWidget {
  final Function(FilePickerResult) onAttachmentPicked;
  final List<DocumentAttachmentContainer> attachments;
  final Axis scrollDirection;
  final RoundsAttachmentPickerViewType viewType;
  final int? gridCrossAxisCount;
  final bool isAddButtonRequired;

  const RoundsAttachmentPicker({
    Key? key,
    required this.onAttachmentPicked,
    required this.attachments,
    this.isAddButtonRequired = true,
    scrollDirection = Axis.horizontal,
    this.viewType = RoundsAttachmentPickerViewType.list,
    this.gridCrossAxisCount = 4,
  })  : scrollDirection = RoundsAttachmentPickerViewType.grid == viewType
            ? Axis.vertical
            : scrollDirection,
        super(key: key);

  void _showImagePickerSheet(BuildContext context) {
    if (UIHelper().getScreenType(context) == ScreenType.desktop) {
      // Assuming desktop screen width is greater than 600
      showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                    leading: const Icon(Icons.camera_alt, color: Colors.blue),
                    title: const Text("Camera"),
                    onTap: () async {
                      Navigator.pop(context);
                      if (kIsWeb || Platform.isWindows) {
                        if (UIHelper().getScreenType(context) ==
                            ScreenType.desktop) {}
                        await showDialog(
                            context: context,
                            builder: (rootDialogContext) => Dialog(
                                backgroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20.0),
                                ),
                                child: ConstrainedBox(
                                    constraints: const BoxConstraints(
                                      maxWidth: 600,
                                      maxHeight: 400,
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(20.0),
                                      child: CameraCaptureScreen(
                                        onImageCapturedFile: (file) async {
                                          if (!kIsWeb) {
                                            onAttachmentPicked(
                                                FilePickerResult([
                                              PlatformFile(
                                                name: path.basename(file.path),
                                                path: file.path,
                                                size: await file.length(),
                                              )
                                            ]));
                                          }
                                        },
                                        onImageCaptured: (bytes) {
                                          final file = PlatformFile(
                                            name: "captured_image.jpg",
                                            bytes: bytes,
                                            size: bytes.length,
                                          );
                                          if (kIsWeb) {
                                            onAttachmentPicked(
                                                FilePickerResult([file]));
                                          }
                                        },
                                      ),
                                    ))));
                      } else {
                        final file = await ImagePicker()
                            .pickImage(source: ImageSource.camera);
                        if (file != null) {
                          onAttachmentPicked(FilePickerResult([
                            PlatformFile(
                              name: path.basename(file.path),
                              path: file.path,
                              size: await file.length(),
                            )
                          ]));
                        }
                      }
                    }),
                ListTile(
                    leading: const Icon(Icons.videocam, color: Colors.red),
                    title: const Text("Video"),
                    onTap: () async {
                      Navigator.pop(context);
                      if (kIsWeb || Platform.isWindows) {
                        await showDialog(
                            context: context,
                            builder: (rootDialogContext) => Dialog(
                                backgroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20.0),
                                ),
                                child: ConstrainedBox(
                                    constraints: const BoxConstraints(
                                      maxWidth: 600,
                                      maxHeight: 400,
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(20.0),
                                      child: CameraVideoCaptureScreen(
                                        maxDuration: const Duration(seconds: 5),
                                        onVideoCapturedFile: (file) async {
                                          if (!kIsWeb) {
                                            onAttachmentPicked(
                                                FilePickerResult([
                                              PlatformFile(
                                                name: path.basename(file.path),
                                                path: file.path,
                                                size: await file.length(),
                                              )
                                            ]));
                                          }
                                        },
                                        onVideoCaptured:
                                            (bytes, fileExtension) {
                                          final file = PlatformFile(
                                            name: "capture_video$fileExtension",
                                            bytes: bytes,
                                            size: bytes.length,
                                          );
                                          if (kIsWeb) {
                                            onAttachmentPicked(
                                                FilePickerResult([file]));
                                          }
                                        },
                                      ),
                                    ))));
                      } else {
                        final file = await ImagePicker().pickVideo(
                            source: ImageSource.camera,
                            maxDuration: const Duration(seconds: 5));
                        if (file != null) {
                          onAttachmentPicked(FilePickerResult([
                            PlatformFile(
                              name: path.basename(file.path),
                              path: file.path,
                              size: await file.length(),
                            )
                          ]));
                        }
                      }
                    }),
                ListTile(
                  leading: const Icon(Icons.file_present_rounded,
                      color: Colors.green),
                  title: const Text("Browse"),
                  onTap: () async {
                    Navigator.pop(context);
                    final result = await FilePicker.platform
                        .pickFiles(type: FileType.image, withData: true);
                    if (result != null) {
                      onAttachmentPicked(result);
                    }
                  },
                ),
              ],
            ),
          );
        },
      );
    } else {
      showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) {
          return Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.camera_alt, color: Colors.blue),
                  title: const Text("Camera"),
                  onTap: () async {
                    Navigator.pop(context);
                    if (kIsWeb) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => CameraCaptureScreen(
                            onImageCapturedFile: (file) async {
                              if (!kIsWeb) {
                                onAttachmentPicked(FilePickerResult([
                                  PlatformFile(
                                    name: path.basename(file.path),
                                    path: file.path,
                                    size: await file.length(),
                                  )
                                ]));
                              }
                            },
                            onImageCaptured: (bytes) {
                              final file = PlatformFile(
                                name: "captured_image.jpg",
                                bytes: bytes,
                                size: bytes.length,
                              );
                              onAttachmentPicked(FilePickerResult([file]));
                            },
                          ),
                        ),
                      );
                    } else {
                      final file = await ImagePicker()
                          .pickImage(source: ImageSource.camera);
                      if (file != null) {
                        onAttachmentPicked(FilePickerResult([
                          PlatformFile(
                            name: path.basename(file.path),
                            path: file.path,
                            size: await file.length(),
                          )
                        ]));
                      }
                    }
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.videocam, color: Colors.red),
                  title: const Text("Video"),
                  onTap: () async {
                    Navigator.pop(context);
                    if (kIsWeb) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => CameraVideoCaptureScreen(
                            maxDuration: const Duration(seconds: 5),
                            onVideoCapturedFile: (file) async {
                              if (!kIsWeb) {
                                onAttachmentPicked(FilePickerResult([
                                  PlatformFile(
                                    name: path.basename(file.path),
                                    path: file.path,
                                    size: await file.length(),
                                  )
                                ]));
                              }
                            },
                            onVideoCaptured: (bytes, fileName) {
                              final file = PlatformFile(
                                name: fileName,
                                bytes: bytes,
                                size: bytes.length,
                              );
                              if (kIsWeb) {
                                onAttachmentPicked(FilePickerResult([file]));
                              }
                            },
                          ),
                        ),
                      );
                    } else {
                      final file = await ImagePicker().pickVideo(
                          source: ImageSource.camera,
                          maxDuration: const Duration(seconds: 5));
                      if (file != null) {
                        onAttachmentPicked(FilePickerResult([
                          PlatformFile(
                            name: path.basename(file.path),
                            path: file.path,
                            size: await file.length(),
                          )
                        ]));
                      }
                    }
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.file_present_rounded,
                      color: Colors.green),
                  title: const Text("Browse"),
                  onTap: () async {
                    Navigator.pop(context);
                    final result = await FilePicker.platform
                        .pickFiles(type: FileType.image);
                    if (result != null && result.files.single.path != null) {
                      onAttachmentPicked(result);
                    }
                  },
                ),
              ],
            ),
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final content = RoundsAttachmentPickerViewType.grid == viewType
        ? GridView.builder(
            shrinkWrap: true,
            scrollDirection: scrollDirection,
            gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
              maxCrossAxisExtent: 120, // adjust as needed
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
              childAspectRatio: 1, // adjust based on design
            ),
            itemCount: isAddButtonRequired
                ? attachments.length + 1
                : attachments.length,
            itemBuilder: (context, index) {
              if (isAddButtonRequired) {
                if (index == 0) {
                  return isAddButtonRequired
                      ? GestureDetector(
                          onTap: () => _showImagePickerSheet(context),
                          child: Container(
                            height: 80,
                            width: 80,
                            decoration: UIHelper.cardDecoration(
                                cardColor: Colors.grey[100]),
                            child: const Icon(Icons.add,
                                color: Colors.black, size: 30),
                          ),
                        )
                      : null;
                }
                return attachments[index - 1];
              } else {
                return attachments[index];
              }
            },
          )
        : Row(
            children: [
              isAddButtonRequired
                  ? GestureDetector(
                      onTap: () => _showImagePickerSheet(context),
                      child: Container(
                        height: 80,
                        width: 80,
                        decoration: UIHelper.cardDecoration(
                            cardColor: Colors.grey[100]),
                        child: const Icon(Icons.add,
                            color: Colors.black, size: 30),
                      ),
                    )
                  : const SizedBox(),
              const SizedBox(
                width: 5.0,
              ),
              Expanded(
                child: ListView.builder(
                    shrinkWrap: true,
                    scrollDirection: scrollDirection,
                    itemCount: attachments.length,
                    itemBuilder: (context, index) => Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 5.0),
                          child: attachments[index],
                        )),
              ),
            ],
          );

    return SizedBox(
      height: RoundsAttachmentPickerViewType.grid == viewType ? null : 80,
      child: content,
    );
  }
}

class DocumentAttachmentContainer extends StatefulWidget {
  final DOCUMENT_ATTACHMENT attachment;
  final Function(DOCUMENT_ATTACHMENT)? onDelete;
  final Function(DOCUMENT_ATTACHMENT)? onTap;
  final bool isUploading;
  final bool isFromPlanner;
  final double? uploadProgress;
  final double height;
  final double width;

  const DocumentAttachmentContainer({
    Key? key,
    required this.attachment,
    required this.isFromPlanner,
    this.onDelete,
    this.onTap,
    this.isUploading = false,
    this.uploadProgress,
    // this.height = 80,
    // this.width = 80,
    this.height = double.infinity,
    this.width = double.infinity,
  }) : super(key: key);

  @override
  State<DocumentAttachmentContainer> createState() =>
      _DocumentAttachmentContainerState();
}

class _DocumentAttachmentContainerState
    extends State<DocumentAttachmentContainer> {
  bool isHovered = false;

  void _openFile(File file) async {
    try {
      final result = await OpenFile.open(file.path);
      if (result.type == ResultType.done) {
        Logger.logInfo("CiltTaskCard", 'openFile',
            'File opened successfully: ${file.path}');
      } else {
        Logger.logInfo(
            "CiltTaskCard", 'openFile', 'Failed to open file: ${file.path}');
      }
    } catch (e) {
      Logger.logInfo("CiltTaskCard", 'openFile', e.toString());
    }
  }

  void _openVideo(File file) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoPlayerScreen(file: file),
      ),
    );
  }

  Widget getFileDisplayWidget(DOCUMENT_ATTACHMENT attachment) {
    if (attachment.local_path == null) {
      // return _getWebFileDisplayWidget(attachment);
      return Image.asset(
        'assets/icon/loading.gif',
        scale: 5.0,
        fit: BoxFit.scaleDown,
      );
    }

    final filePath = attachment.local_path!;
    final fileExtension =
        path.extension(filePath).toLowerCase().replaceFirst('.', '');

    if (kIsWeb) {
      return _getWebFileDisplayWidget(attachment);
    } else {
      return _getMobileFileDisplayWidget(filePath, fileExtension);
    }
  }

  Widget _getWebFileDisplayWidget(DOCUMENT_ATTACHMENT attachment) {
    if (attachment.fid == null || attachment.file_name == null) {
      return const Icon(Icons.attachment, size: 50);
    }

    final fileExtension = path
        .extension(attachment.file_name!)
        .toLowerCase()
        .replaceFirst('.', '');

    return FutureBuilder<Uint8List>(
      future: !widget.isFromPlanner
          ? DbHelper.getDocumentHeaderById(attachment.uid ?? "")
              .then((header) async {
              String? data = await DbHelper()
                  .getAttachmentFromIndexDbByUid(header?.doc_id ?? "");
              return Uint8List.fromList(base64Decode(data ?? ""));
            })
          : SyncEngine().downloadAttachmentAndGetBytes(attachment.uid ?? ""),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        } else if (snapshot.hasError || !snapshot.hasData) {
          Logger.logInfo("DocumentAttachmentContainer", 'downloadError',
              snapshot.error?.toString() ?? "No data received");
          return const Icon(Icons.broken_image, size: 50);
        } else {
          final bytes = snapshot.data!;

          if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
              .contains(fileExtension)) {
            return Image.memory(
              bytes,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return const Icon(Icons.image, size: 50);
              },
            );
          } else if (['mp4', 'avi', 'mov', 'mkv', 'webm']
              .contains(fileExtension)) {
            return Image.asset(
              'assets/icon/task_icons/video.png',
              fit: BoxFit.scaleDown,
            );
          } else if (fileExtension == 'pdf') {
            return Image.asset(
              'assets/icon/task_icons/pdff.png',
              fit: BoxFit.scaleDown,
            );
          } else {
            return const Icon(Icons.insert_drive_file, size: 50);
          }
        }
      },
    );
  }

  Widget _getMobileFileDisplayWidget(String filePath, String fileExtension) {
    final file = File(filePath);

    if (!file.existsSync()) {
      return Image.asset(
        'assets/icon/file-corrupted.png',
        fit: BoxFit.scaleDown,
        scale: 7.0,
      );
    }

    if (['jpg', 'jpeg', 'png', 'gif'].contains(fileExtension)) {
      return Image.file(file, fit: BoxFit.cover);
    }

    if (['mp4', 'avi', 'mov', 'mkv'].contains(fileExtension)) {
      return Image.asset(
        'assets/icon/task_icons/video.png',
        fit: BoxFit.scaleDown,
      );
    }

    return Image.asset(
      'assets/icon/task_icons/pdff.png',
      fit: BoxFit.scaleDown,
    );
  }

  Future<Uint8List?> getAttachmentBytes(String? uid,
      {required bool isFromPlanner}) async {
    if (uid == null || uid.isEmpty) return null;

    // Check local storage first (for both cases)
    final localData = await DbHelper().getAttachmentFromIndexDbByUid(uid);
    if (localData != null && localData.isNotEmpty) {
      return Uint8List.fromList(base64Decode(localData));
    }

    if (isFromPlanner) {
      // From planner: fetch from network and save locally
      final bytes = await SyncEngine().downloadAttachmentAndGetBytes(uid);
      final base64String = base64Encode(bytes);
      await DbHelper().saveAttachmentinIndexDbByUid(uid, base64String);
      return Uint8List.fromList(bytes);
    } else {
      // Not from planner: get doc header first to find actual docId
      final header = await DbHelper.getDocumentHeaderById(uid);
      final docId = header?.doc_id;
      if (docId == null) return null;

      if (kIsWeb) {
        final localData = await DbHelper().getAttachmentFromIndexDbByUid(docId);
        if (localData != null && localData.isNotEmpty) {
          return Uint8List.fromList(base64Decode(localData));
        }
      }

      // Fetch from cloud using original UID (if docId differs, still use UID to download)
      final bytes = await SyncEngine().downloadAttachmentAndGetBytes(uid);
      final base64String = base64Encode(bytes);
      await DbHelper().saveAttachmentinIndexDbByUid(
          docId ?? uid, base64String); // Prefer docId
      return Uint8List.fromList(bytes);
    }
  }

  Future<void> showDocumentInNewTab(DOCUMENT_ATTACHMENT attachment) async {
    if (attachment.fid == null) return;

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      final bytes = await getAttachmentBytes(attachment.uid,
          isFromPlanner: widget.isFromPlanner);

      // Determine MIME type based on file extension
      final ext = path
          .extension(attachment.file_name ?? '')
          .toLowerCase()
          .replaceFirst('.', '');
      // final mimeType = _getMimeTypeFromExtension(ext);
      if (mounted) {
        Navigator.of(context).pop(); //
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => Container(
                    color: AppColors.white,
                    child: FileRenderer(
                      bytes: bytes!,
                      mimeType: _getMimeTypeFromExtension(ext),
                    ),
                  )),
        );
      }
    } catch (e) {
      Navigator.of(context).pop();
      Logger.logError("WebFilePreview", "Error showing document", e.toString());
    }
  }

  String _getMimeTypeFromExtension(String ext) {
    switch (ext) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return 'image/$ext';
      case 'pdf':
        return 'application/pdf';
      case 'mp4':
      case 'webm':
        return 'video/$ext';
      case 'txt':
        return 'text/plain';
      case 'html':
        return 'text/html';
      case 'json':
        return 'application/json';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      default:
        return 'application/octet-stream';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GestureDetector(
            onTap: () async {
              widget.onTap?.call(widget.attachment);
              if (!kIsWeb) {
                File? file = widget.attachment.local_path != null
                    ? File(widget.attachment.local_path!)
                    : null;
                if (file?.existsSync() ?? false) {
                  String? fileExtension =
                      file?.path.split('.').last.toLowerCase();
                  if (isHovered) {
                    setState(() {
                      isHovered = !isHovered;
                    });
                  }
                  if (file != null) {
                    if (fileExtension == 'jpg' ||
                        fileExtension == 'jpeg' ||
                        fileExtension == 'png' ||
                        fileExtension == 'gif') {
                      if (kIsWeb) {
                        final appBaseUrl = URLService.getApplicationUrl(
                            (await AuthenticationService()
                                    .getSelectedAccount())!
                                .getUrl());
                        // final fileUrl = "$appBaseUrl/attachment/${widget.attachment.fid}";
                      } else {
                        Navigator.of(context).push(MaterialPageRoute(
                            builder: (context) => ImageViewerScreen(
                                attachment: widget.attachment)));
                      }
                    } else if (fileExtension == 'mp4' ||
                        fileExtension == 'avi' ||
                        fileExtension == 'mov' ||
                        fileExtension == 'mkv') {
                      if (kIsWeb) {
                        final appBaseUrl = URLService.getApplicationUrl(
                            (await AuthenticationService()
                                    .getSelectedAccount())!
                                .getUrl());
                      } else {
                        _openVideo(file);
                      }
                    } else {
                      if (kIsWeb) {
                        final appBaseUrl = URLService.getApplicationUrl(
                            (await AuthenticationService()
                                    .getSelectedAccount())!
                                .getUrl());
                      } else {
                        _openFile(file);
                      }
                    }
                  }
                } else {
                  UIHelper.showSnackBar(context,
                      message: "Downloading the file");
                }
              } else {
                showDocumentInNewTab(widget.attachment);
              }
            },
            onLongPressStart: (_) {
              setState(() {
                isHovered = true;
              });
            },
            child: Container(
              height: widget.height,
              width: widget.width,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: AppColors.grey, width: 1.0),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: SizedBox(
                      height: widget.height,
                      width: widget.width,
                      child: kIsWeb
                          ? _getWebFileDisplayWidget(widget.attachment)
                          : getFileDisplayWidget(widget.attachment),
                    ),
                  ),
                  if (widget.isUploading)
                    Container(
                      decoration: UIHelper.cardDecoration(
                        cardColor: Colors.grey[400]?.withOpacity(0.7),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            AppLocalizations.of(context)!.upload,
                            style:
                                TextStyle(color: AppColors.white, fontSize: 14),
                          ),
                          UIHelper.sizedBox8(),
                          if (widget.uploadProgress != null)
                            LinearProgressIndicator(
                              borderRadius: BorderRadius.circular(10),
                              value: widget.uploadProgress,
                              backgroundColor: AppColors.white,
                              valueColor: const AlwaysStoppedAnimation(
                                  AppColors.greenColor),
                            ),
                        ],
                      ),
                    ),
                  widget.isFromPlanner
                      ? const SizedBox()
                      : Positioned(
                          bottom: 2,
                          right: 2,
                          child: CircleAvatar(
                            radius: 10,
                            backgroundColor: AppColors.redAccentColor,
                            child: InkWell(
                              onTap: () {
                                widget.onDelete?.call(widget.attachment);
                                setState(() {
                                  isHovered = false;
                                });
                              },
                              child: const Icon(
                                Icons.close,
                                color: AppColors.whiteColor,
                                size: 20,
                              ),
                            ),
                          ),
                        )
                ],
              ),
            )),
        if (widget.isFromPlanner)
          Positioned(
              top: 3,
              left: 5,
              child: CircleAvatar(
                radius: 12,
                backgroundColor: Colors.grey.withOpacity(0.4),
                child: Icon(
                  Icons.cloud_download_outlined,
                  size: 16,
                  color: AppColors.primaryColor,
                ),
              ))
      ],
    );
  }
}

enum RoundsAttachmentPickerViewType {
  grid,
  list,
}
