import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/app_styles.dart';

class CustomCardForTexFields extends StatefulWidget {
  final String? title;
  final Widget child;

  const CustomCardForTexFields({
    super.key,
    this.title,
    required this.child,
  });

  @override
  State<CustomCardForTexFields> createState() => _CustomCardForTexFieldsState();
}

class _CustomCardForTexFieldsState extends State<CustomCardForTexFields> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        widget.title != null && widget.title!.isNotEmpty
            ? Text(
                widget.title!,
                style: AppStyles.headLine16,
              )
            : const SizedBox(),
        widget.title == null || widget.title!.isEmpty
            ? const SizedBox()
            : const SizedBox(height: 10),
        Container(
          width: double.maxFinite,
          decoration: BoxDecoration(
            color: const Color.fromRGBO(213, 218, 221, 0.5),
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: widget.child,
          ),
        )
      ],
    );
  }
}

class CustomTextField extends StatefulWidget {
  final String labelName;
  final TextEditingController controller;
  final bool isRequiredField;
  final String hintText;
  final double height;
  final int maxLines;
  final TextInputType? keyBoardType;
  final bool? readOnly;
  final bool? enable;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? stopTypedCallback;
  final Function(String)? onFieldSubmitted;
  final bool isPassword;
  final List<TextInputFormatter>? inputFormatter;
  final bool isSearch;
  final Function()? searchOnTap;
  final int? maxLength;
  final FocusNode? focusNode;
  final Color? labelColor;
  final bool? autoFocus;
  final Function()? onTap;

  const CustomTextField(
      {Key? key,
      required this.labelName,
      this.labelColor,
      required this.isRequiredField,
      required this.hintText,
      required this.height,
      required this.maxLines,
      this.keyBoardType,
      required this.controller,
      this.onChanged,
      this.onFieldSubmitted,
      this.stopTypedCallback,
      this.isPassword = false,
      this.readOnly = false,
      this.enable = true,
      this.inputFormatter,
      this.isSearch = false,
      this.searchOnTap,
      this.maxLength = 0,
      this.focusNode,
      this.autoFocus,
      this.onTap})
      : super(key: key);

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  bool _showText = false;

  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTyping);
  }

  @override
  void dispose() {
    // widget.controller.dispose();
    // if (mounted) {
    _debounceTimer?.cancel();
    // }

    super.dispose();
  }

  void _onTyping() {
    if (_debounceTimer?.isActive ?? false) {
      _debounceTimer!.cancel();
    }
    _debounceTimer = Timer(const Duration(milliseconds: 500), _onStoppedTyping);
  }

  void _onStoppedTyping() {
    if (mounted) {
      setState(() {
        if (widget.stopTypedCallback != null) {
          widget.stopTypedCallback!(widget.controller.text);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              widget.labelName,
              style: AppStyles.labelTextStyle
                  .copyWith(color: widget.labelColor ?? Colors.black),
            ),
            widget.isRequiredField
                ? const Text(
                    "*",
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.w700,
                    ),
                  )
                : const SizedBox(),
          ],
        ),
        // const SizedBox(height: 8),
        const SizedBox(height: 12),
        Container(
          height: widget.height,
          width: double.maxFinite,
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: AppColors.grey,
            ),
          ),
          child: TextFormField(
            onTap: widget.onTap,
            onFieldSubmitted: widget.onFieldSubmitted,
            autofocus: widget.autoFocus != null ? widget.autoFocus! : false,
            focusNode: widget.focusNode,
            maxLength: widget.maxLength != 0 ? 15 : null,
            enabled: widget.enable,
            inputFormatters: widget.inputFormatter ?? [],
            keyboardType: widget.keyBoardType,
            readOnly: widget.readOnly!,
            controller: widget.controller,
            maxLines: widget.maxLines,
            onChanged: widget.onChanged,
            obscureText: widget.isPassword && !_showText,
            enableSuggestions: !widget.isPassword,
            autocorrect: !widget.isPassword,
            decoration: InputDecoration(
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderSide: BorderSide.none,
                borderRadius: BorderRadius.circular(10.0),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 10.0),
              hintStyle: const TextStyle(
                fontSize: 14,
                color: Color.fromRGBO(144, 144, 144, 1),
              ),
              hintText: widget.hintText,
              suffixIcon: _buildSuffixIcon(),
              //counter: SizedBox.shrink(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSuffixIcon() {
    if (widget.isPassword) {
      return IconButton(
        onPressed: () {
          setState(() {
            _showText = !_showText;
          });
        },
        icon: Icon(
          _showText ? Icons.visibility : Icons.visibility_off,
          color: AppColors.buttonColor,
        ),
      );
    } else {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.isSearch) _buildSearchIcon(),
          if (widget.controller.text.isNotEmpty) _buildClearIcon(),
        ],
      );
    }
  }

  Widget _buildSearchIcon() {
    return InkWell(
      onTap: widget.isSearch ? widget.searchOnTap : () {},
      child: const SizedBox(
        height: 40,
        width: 50,
        child: Icon(
          Icons.search,
          // size: 30,
          color: AppStyles.primaryBlueColor,
        ),
      ),
    );
  }

  Widget _buildClearIcon() {
    return IconButton(
      onPressed: () {
        setState(() {
          widget.controller.clear();
          if (widget.onChanged != null) {
            widget.onChanged!("");
          }
          if (widget.stopTypedCallback != null) {
            widget.stopTypedCallback!("");
          }
        });
      },
      icon: Icon(
        Icons.close,
        // size: 30,
        color: widget.enable! ? AppStyles.primaryBlueColor : null,
      ),
    );
  }
}
