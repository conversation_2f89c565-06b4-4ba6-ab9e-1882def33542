import 'dart:typed_data';
import 'dart:html' as html;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:ui' as ui show platformViewRegistry;

class FileRenderer extends StatefulWidget {
  final Uint8List bytes;
  final String mimeType;

  const FileRenderer({required this.bytes, required this.mimeType, Key? key})
      : super(key: key);

  @override
  State<FileRenderer> createState() => _FileRendererState();
}

class _FileRendererState extends State<FileRenderer> {
  late final String _objectUrl;

  @override
  void initState() {
    super.initState();
    _objectUrl = html.Url.createObjectUrlFromBlob(
      html.Blob([widget.bytes], widget.mimeType),
    );

    if (widget.mimeType == 'application/pdf' ||
        widget.mimeType.startsWith('audio/') ||
        widget.mimeType.startsWith('video/')) {
      // ignore: undefined_prefixed_name
      ui.platformViewRegistry.registerViewFactory(
        _objectUrl,
        (int viewId) {
          if (widget.mimeType == 'application/pdf') {
            return html.IFrameElement()
              ..src = _objectUrl
              ..style.border = 'none'
              ..style.width = '100%'
              ..style.height = '100%';
          } else if (widget.mimeType.startsWith('audio/')) {
            return html.AudioElement()
              ..src = _objectUrl
              ..controls = true
              ..autoplay = false;
          } else if (widget.mimeType.startsWith('video/')) {
            return html.VideoElement()
              ..src = _objectUrl
              ..controls = true
              ..style.width = '100%'
              ..style.height = '100%'
              ..style.objectFit = 'contain'
              ..style.border = 'none'
              ..style.display = 'block'
              ..style.margin = '0 auto';
          }
          throw UnimplementedError('Unsupported MIME type for view.');
        },
      );
    }
  }

  @override
  void dispose() {
    html.Url.revokeObjectUrl(_objectUrl);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.mimeType.startsWith('image/')) {
      return Image.memory(
        widget.bytes,
        errorBuilder: (context, error, stackTrace) {
          return const Icon(Icons.image, size: 50);
        },
      );
    }

    if (widget.mimeType == 'application/pdf' ||
        widget.mimeType.startsWith('audio/') ||
        widget.mimeType.startsWith('video/')) {
      return HtmlElementView(viewType: _objectUrl);
    }

    // Generic download button for other types
    return ElevatedButton.icon(
      icon: const Icon(Icons.download),
      label: const Text('Download file'),
      onPressed: () {
        final anchor = html.AnchorElement(href: _objectUrl)
          ..download = 'file.${_getExtensionFromMime(widget.mimeType)}'
          ..click();
      },
    );
  }

  String _getExtensionFromMime(String mime) {
    final parts = mime.split('/');
    return parts.length == 2 ? parts[1] : 'bin';
  }
}
