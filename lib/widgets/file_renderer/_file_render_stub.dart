import 'dart:typed_data';
import 'package:flutter/material.dart';

class File<PERSON>enderer extends StatelessWidget {
  final Uint8List bytes;
  final String mimeType;

  const FileRenderer({required this.bytes, required this.mimeType, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (mimeType.startsWith('image/')) {
      if (bytes.isNotEmpty) {
        return Image.memory(bytes);
      } else {
        Image.asset(
          'assets/icon/file-corrupted.png',
        );
      }
    }

    return const Center(
      child: Text(
        'File preview not supported on this platform.',
        style: TextStyle(color: Colors.grey),
      ),
    );
  }
}
