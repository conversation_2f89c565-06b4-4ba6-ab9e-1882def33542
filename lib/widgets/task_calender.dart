import 'dart:math';

import 'package:flutter/material.dart';
import 'package:rounds/be/FAULT_HEADER.dart';
import 'package:rounds/be/JOB_HEADER.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/utils.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

class TaskCalender extends StatelessWidget {
  const TaskCalender({super.key, required this.taskDataSource});

  final TaskDataSource taskDataSource;

  @override
  Widget build(BuildContext context) {
    return SfCalendar(
      showNavigationArrow: true,
      showCurrentTimeIndicator: true,
      view: CalendarView.month,
      headerStyle: CalendarHeaderStyle(
          textStyle: TextStyle(
        color: AppColors.primaryColor,
        fontWeight: FontWeight.bold,
        fontSize: 20,
      )),
      viewHeaderStyle: const ViewHeaderStyle(
        dayTextStyle:
            TextStyle(fontWeight: FontWeight.w500, color: Colors.black),
      ),
      cellBorderColor: Colors.transparent,
      dataSource: taskDataSource,
      todayHighlightColor: AppColors.primaryColor,
      selectionDecoration: BoxDecoration(
        color: Colors.transparent,
        border: Border.all(color: AppColors.primaryColor, width: 2),
        borderRadius: const BorderRadius.all(Radius.circular(4)),
        shape: BoxShape.rectangle,
      ),
      onSelectionChanged: (calendarSelectionDetails) =>
          {Navigator.of(context).pop(calendarSelectionDetails.date)},
      monthViewSettings: const MonthViewSettings(
          appointmentDisplayMode: MonthAppointmentDisplayMode.indicator),
    );
  }
}

class TaskDataSource extends CalendarDataSource {
  TaskDataSource(List<CalenderTask> source) {
    appointments = source;
  }

  @override
  DateTime getStartTime(int index) {
    return appointments![index].reported_on;
  }

  @override
  DateTime getEndTime(int index) {
    return appointments![index].endAt;
  }

  @override
  String getSubject(int index) {
    return appointments![index].eventName;
  }

  @override
  Color getColor(int index) {
    return appointments![index].background;
  }

  @override
  bool isAllDay(int index) {
    return appointments![index].isAllDay;
  }
}

class CalenderTask {
  CalenderTask(
      {required this.eventName,
      required this.reported_on,
      required this.endAt,
      required this.background,
      required this.isAllDay});

  String eventName;
  DateTime reported_on;
  DateTime endAt;
  Color background;
  bool isAllDay;

  factory CalenderTask.fromFaultHeader(FAULT_HEADER faultHeader) {
    return CalenderTask(
        eventName: faultHeader.description ?? "",
        reported_on: convertToDateTime(faultHeader.reported_on.toString()),
        endAt: convertToDateTime(faultHeader.reported_on.toString()),
        background: Colors.orange,
        isAllDay: false);
  }

  factory CalenderTask.fromJobHeader(JOB_HEADER jobHeader) {
    return CalenderTask(
        eventName: jobHeader.description ?? "",
        reported_on: convertToDateTime(jobHeader.start_date.toString()),
        endAt: convertToDateTime(jobHeader.start_date.toString()),
        background: AppColors.primaryColor,
        isAllDay: false);
  }
}
