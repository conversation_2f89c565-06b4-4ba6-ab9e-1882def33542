import 'dart:math';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class Rounds<PERSON>ie<PERSON>hart extends StatefulWidget {
  final List<RoundsPieChartData> data;
  final Widget? centerWidget;
  const RoundsPieChart({
    super.key,
    required this.data,
    this.centerWidget,
  });

  @override
  State<StatefulWidget> createState() => RoundsPieChartState();
}

class RoundsPieChartState extends State<RoundsPieChart>
    with SingleTickerProviderStateMixin {
  int touchedIndex = 0;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) => Stack(
              alignment: Alignment.center,
              children: [
                CustomPaint(
                  size: const Size(200, 200), // Match your chart size
                  painter: _CircleShadowPainter(),
                ),
                PieChart(
                  PieChartData(
                    pieTouchData: PieTouchData(),
                    borderData: FlBorderData(
                      show: false,
                    ),
                    sectionsSpace: 4,
                    centerSpaceRadius: 40,
                    sections: showingSections(_animation.value),
                  ),
                ),
                widget.centerWidget ?? const SizedBox(),
              ],
            ),
          ),
        ),
        SingleChildScrollView(child: _buildLegend()),
      ],
    );
  }

  List<PieChartSectionData> showingSections(double animationValue) {
    return List.generate(widget.data.length, (i) {
      final isTouched = i == touchedIndex;
      final fontSize = 10.0 * animationValue;
      // Scale radius by animation value for entrance effect
      final radius = (isTouched ? 70.0 : 60.0) * animationValue;

      return PieChartSectionData(
        color: widget.data[i].color,
        value: widget.data[i].value,
        showTitle: false,
        // title: "${widget.data[i].title}\n(${widget.data[i].value.toInt()})",

        radius: radius,
        titleStyle: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          // shadows: const [Shadow(color: Colors.black)],
        ),
        borderSide: BorderSide(
          color: isTouched ? Colors.white : Colors.transparent,
          width: 2,
        ),

        titlePositionPercentageOffset: 1.40,
      );
    });
  }

  // Widget _buildLegend() {
  //   return Padding(
  //     padding: const EdgeInsets.only(top: 16.0),
  //     child: Wrap(
  //       spacing: 12,
  //       runSpacing: 4,
  //       alignment: WrapAlignment.center,
  //       children: widget.data.map((data) {
  //         return Row(
  //           mainAxisSize: MainAxisSize.min,
  //           children: [
  //             Container(
  //               width: 14,
  //               height: 14,
  //               decoration: BoxDecoration(
  //                 color: data.color,
  //                 shape: BoxShape.circle,
  //               ),
  //             ),
  //             const SizedBox(width: 6),
  //             Text("${data.title} (${data.value.toInt()})",
  //                 style: const TextStyle(fontSize: 12)),
  //           ],
  //         );
  //       }).toList(),
  //     ),
  //   );
  // }

  Widget _buildLegend() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widget.data.map((data) {
        return Column(
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Modern line indicator instead of circle
                Container(
                  width: 3,
                  height: 35,
                  decoration: BoxDecoration(
                    color: data.color,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      data.title,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      data.value.toInt().toString(),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: data.color,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 8)
          ],
        );
      }).toList(),
    );
  }
}

class RoundsPieChartData {
  final String title;
  final double value;
  final Color color;

  RoundsPieChartData({
    required this.title,
    required this.value,
    required this.color,
  });
}

class _CircleShadowPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.1)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 20); // Soft shadow

    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width / 2, size.height / 2) - 10;

    canvas.drawCircle(center, radius, shadowPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
