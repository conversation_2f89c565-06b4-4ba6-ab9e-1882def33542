import 'package:flutter/material.dart';
import 'package:rounds/utils/app_colors.dart';
import 'dart:math' as math;

import 'package:rounds/widgets/chart_widget.dart';

class FlippableCountWidget extends StatefulWidget {
  final Widget icon;
  final String title;
  final int count;
  final Widget detailsWidget;

  const FlippableCountWidget({
    Key? key,
    required this.icon,
    required this.title,
    required this.count,
    required this.detailsWidget,
  }) : super(key: key);

  @override
  _FlippableCountWidgetState createState() => _FlippableCountWidgetState();
}

class _FlippableCountWidgetState extends State<FlippableCountWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool _showFrontSide = true;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOutQuad),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleSide() {
    if (_showFrontSide) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
    setState(() {
      _showFrontSide = !_showFrontSide;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: GestureDetector(
        onTap: _toggleSide,
        child: AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            final rotationValue = _animation.value * math.pi;
            final isFrontVisible = _animation.value <= 0.5;

            return Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..setEntry(3, 2, 0.001)
                ..rotateY(rotationValue),
              child: isFrontVisible
                  ? _buildFrontSide()
                  : Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.identity()..rotateY(math.pi),
                      child: _buildBackSide(),
                    ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildFrontSide() {
    return Container(
      height: 85,
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 6.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Container(
                //   padding: const EdgeInsets.all(8),
                //   decoration: BoxDecoration(
                //     color: AppColors.primaryColor.withOpacity(0.1),
                //     borderRadius: BorderRadius.circular(12),
                //   ),
                //   child: widget.icon,
                // ),
                // const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.title,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.count.toString(),
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBackSide() {
    return Container(
      height: 85,
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 6.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: widget.detailsWidget,
          ),
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.arrow_back,
                size: 16,
                color: AppColors.primaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Enum to define expansion directions
enum ExpansionDirection {
  topToBottom,
  bottomToTop,
  topLeftToBottomRight,
  bottomRightToTopLeft,
  topRightToBottomLeft,
  bottomLeftToTopRight,
}

// Data model for each card
class DashboardCardItem {
  final String title;
  final int count;
  final Widget? icon;
  final Widget? detailsWidget;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? valueColor;
  final ExpansionDirection expansionDirection;

  DashboardCardItem({
    required this.title,
    required this.count,
    this.icon,
    this.detailsWidget,
    this.backgroundColor,
    this.textColor,
    this.valueColor,
    this.expansionDirection = ExpansionDirection.topToBottom,
  });
}

class ExpandableDashboardCards extends StatefulWidget {
  final List<DashboardCardItem> items;
  final double collapsedHeight;
  final EdgeInsets padding;
  final EdgeInsets cardMargin;
  final Duration animationDuration;
  final int columnsPerRow;

  const ExpandableDashboardCards({
    Key? key,
    required this.items,
    this.collapsedHeight = 85,
    this.padding = const EdgeInsets.all(12.0),
    this.cardMargin =
        const EdgeInsets.symmetric(vertical: 8.0, horizontal: 6.0),
    this.animationDuration = const Duration(milliseconds: 500),
    this.columnsPerRow = 2,
  }) : super(key: key);

  @override
  _ExpandableDashboardCardsState createState() =>
      _ExpandableDashboardCardsState();
}

class _ExpandableDashboardCardsState extends State<ExpandableDashboardCards>
    with TickerProviderStateMixin {
  int? _expandedCardIndex;

  late final List<AnimationController> _controllers;
  late final List<Animation<Offset>> _animations;

  @override
  void initState() {
    super.initState();

    _controllers = [];
    _animations = [];

    for (int i = 0; i < widget.items.length; i++) {
      final item = widget.items[i];

      final controller = AnimationController(
        duration: widget.animationDuration,
        vsync: this,
      );

      // Determine the direction of the slide
      Offset beginOffset;
      switch (item.expansionDirection) {
        case ExpansionDirection.topToBottom:
          beginOffset = const Offset(0, -1);
          break;
        case ExpansionDirection.bottomToTop:
          beginOffset = const Offset(0, 1);
          break;
        case ExpansionDirection.topLeftToBottomRight:
          beginOffset = const Offset(-1, -1);
          break;
        case ExpansionDirection.bottomRightToTopLeft:
          beginOffset = const Offset(1, 1);
          break;
        case ExpansionDirection.topRightToBottomLeft:
          beginOffset = const Offset(1, -1);
          break;
        case ExpansionDirection.bottomLeftToTopRight:
          beginOffset = const Offset(-1, 1);
          break;
      }

      final animation = Tween<Offset>(
        begin: beginOffset,
        end: Offset.zero,
      ).animate(
        CurvedAnimation(
          parent: controller,
          curve: Curves.easeInOut,
        ),
      );

      _controllers.add(controller);
      _animations.add(animation);
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Calculate how many rows we need based on items count and columns per row
    final int totalRows = (widget.items.length / widget.columnsPerRow).ceil();

    // When only one card is shown and it's expanded, make it fill the width
    if (widget.items.length == 1 ||
        (_expandedCardIndex != null && widget.items.length > 0)) {
      // If there's only one card or a card is expanded
      return Padding(
        padding: widget.padding,
        child: LayoutBuilder(builder: (context, constraints) {
          if (widget.items.length == 1) {
            // If there's only one card
            return _buildCard(0, _expandedCardIndex == 0, constraints.maxWidth);
          } else {
            // If a card is expanded
            final rowIndex = _expandedCardIndex! ~/ widget.columnsPerRow;
            final colIndex = _expandedCardIndex! % widget.columnsPerRow;

            return Column(
              children: List.generate(totalRows, (i) {
                if (i == rowIndex) {
                  // This is the row with the expanded card
                  return _buildCard(
                      _expandedCardIndex!, true, constraints.maxWidth);
                } else {
                  // Other rows are hidden when a card is expanded
                  return const SizedBox();
                }
              }),
            );
          }
        }),
      );
    }

    // Normal grid layout for multiple cards when none is expanded
    return Padding(
      padding: widget.padding,
      child: LayoutBuilder(builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;

        return Column(
          children: List.generate(totalRows, (rowIndex) {
            // For each row, create a Row widget with cards
            return Row(
              children: List.generate(widget.columnsPerRow, (colIndex) {
                final itemIndex = rowIndex * widget.columnsPerRow + colIndex;

                // Calculate the card width
                final cardWidth = (availableWidth -
                        (widget.cardMargin.horizontal *
                            (widget.columnsPerRow - 1.5))) /
                    widget.columnsPerRow;

                // Check if this index is valid for our items list
                if (itemIndex < widget.items.length) {
                  return _buildCard(
                      itemIndex, _expandedCardIndex == itemIndex, cardWidth);
                } else {
                  // Return an empty spacer for empty slots to maintain grid layout
                  return SizedBox(width: cardWidth);
                }
              }),
            );
          }),
        );
      }),
    );
  }

  Widget _buildCard(int index, bool isExpanded, double cardWidth) {
    if (index >= widget.items.length) {
      return SizedBox(width: cardWidth);
    }

    final item = widget.items[index];
    final cardColor = item.backgroundColor ?? Colors.white;
    final textColor = item.textColor ?? Colors.black87;
    final valueColor = item.valueColor ?? Theme.of(context).primaryColor;

    return Container(
      width: isExpanded ? double.infinity : cardWidth,
      constraints: isExpanded
          ? const BoxConstraints(
              minHeight: 120, maxHeight: 450) // adjust as needed
          : BoxConstraints(
              maxHeight: widget.collapsedHeight * 3,
            ),
      child: Stack(
        alignment: Alignment.topLeft,
        children: [
          GestureDetector(
            onTap: () => _toggleCard(index),
            child: AnimatedBuilder(
              animation: _controllers[index],
              builder: (context, child) {
                final cardHeight = isExpanded
                    ? null // Let it be determined by content when expanded
                    : widget.collapsedHeight +
                        (_controllers[index].value * 200);

                return Container(
                  height: isExpanded
                      ? null
                      : cardHeight, // No fixed height when expanded
                  width: isExpanded ? double.infinity : null,
                  constraints: isExpanded
                      ? null // Remove constraints to allow content to define height
                      : BoxConstraints(maxHeight: widget.collapsedHeight * 3),
                  margin: widget.cardMargin,
                  decoration: BoxDecoration(
                    color: cardColor,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: isExpanded
                      ? ConstrainedBox(
                          constraints:
                              const BoxConstraints(minHeight: 100), // required!
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: item.detailsWidget!,
                          ),
                        )
                      : Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Title & count block
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Flexible(
                                      child: Text(
                                        item.title,
                                        style: TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.bold,
                                          color: textColor,
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        textScaler: const TextScaler.linear(1.0), // Prevent text scaling
                                      ),
                                    ),
                                    const SizedBox(height: 2),
                                    Flexible(
                                      child: Text(
                                        item.count.toString(),
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                          color: valueColor,
                                        ),
                                        textScaler: const TextScaler.linear(1.0), // Prevent text scaling
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.expand_more,
                                color: valueColor,
                              ),
                            ],
                          ),
                        ),
                );
              },
            ),
          ),
          isExpanded
              ? IconButton(
                  style: IconButton.styleFrom(
                    backgroundColor: AppColors.primaryColor.withOpacity(0.1),
                  ),
                  onPressed: () => _toggleCard(index),
                  icon: Icon(
                    Icons.arrow_back,
                    color: AppColors.primaryColor,
                  ))
              : const SizedBox.shrink()
        ],
      ),
    );
  }

  void _toggleCard(int index) {
    setState(() {
      if (_expandedCardIndex != null && _expandedCardIndex != index) {
        _controllers[_expandedCardIndex!]
            .reverse(); // Reverse the previous animation
      }

      if (_expandedCardIndex == index) {
        // Reverse the current animation when it's collapsed
        _controllers[index].reverse();
        _expandedCardIndex = null;
      } else {
        // Expand the current card
        _expandedCardIndex = index;
        _controllers[index].forward(from: 0.0); // Start the animation
      }
    });
  }
}

// ================================================

class DesktopDashboardCardItem {
  final String title;
  final int count;
  final Widget? icon;
  final Widget? detailsWidget;
  final Color? backgroundColor;
  final Color? titleColor;
  final Color? valueColor;

  DesktopDashboardCardItem({
    required this.title,
    required this.count,
    this.icon,
    this.detailsWidget,
    this.backgroundColor,
    this.titleColor,
    this.valueColor,
  });
}

class DesktopDashboardCards extends StatelessWidget {
  final List<DesktopDashboardCardItem> items;
  final double cardHeight;
  final EdgeInsets padding;
  final EdgeInsets cardMargin;
  final int crossAxisCount;
  final double childAspectRatio;
  final double crossAxisSpacing;
  final double mainAxisSpacing;

  const DesktopDashboardCards({
    Key? key,
    required this.items,
    this.cardHeight = 240,
    this.padding = const EdgeInsets.all(12.0),
    this.cardMargin = const EdgeInsets.all(4.0),
    this.crossAxisCount = 2,
    this.childAspectRatio = 2.2,
    this.crossAxisSpacing = 12.0,
    this.mainAxisSpacing = 12.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: GridView.builder(
        shrinkWrap: true,
        physics: const BouncingScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          childAspectRatio: childAspectRatio,
          // crossAxisSpacing: crossAxisSpacing,
          // mainAxisSpacing: mainAxisSpacing,
        ),
        itemCount: items.length,
        itemBuilder: (context, index) => _buildCard(context, index),
      ),
    );
  }

  Widget _buildCard(BuildContext context, int index) {
    final item = items[index];
    final cardColor = item.backgroundColor ?? Colors.white;
    final titleColor = item.titleColor ?? Colors.black87;
    final valueColor = item.valueColor ?? Theme.of(context).primaryColor;

    return Container(
      margin: cardMargin,
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // Header section with title and count
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: cardColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Title & count block
                  Expanded(
                    child: Row(
                      children: [
                        // if (item.icon != null) ...[
                        //   item.icon!,
                        //   const SizedBox(width: 8),
                        // ],
                        Expanded(
                          child: Text(
                            item.title,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: titleColor,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Content section with details widget
          Expanded(
            flex: 4,
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
              child: item.detailsWidget != null
                  ? Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16.0, vertical: 8.0),
                      child: item.detailsWidget,
                    )
                  : const Center(
                      child: Text("No details available"),
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
