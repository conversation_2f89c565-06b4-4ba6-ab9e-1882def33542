const barcodeHtmlString = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <title></title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        canvas.drawing, canvas.drawingBuffer {
            position: absolute;
            left: 0;
            top: 0;
        }
        html, body {
            margin: 0;
            padding: 0;
            height: 200%;
            overflow: hidden; 
        }
        #reader {
            width: 100%;
            height: 18.8.....%;
        }
        
    </style>
    <script src="https://unpkg.com/html5-qrcode@2.0.13/dist/html5-qrcode.min.js"></script>
</head>
<body>
    <!-- Div to show the scanner -->
    <div id="reader"></div>

    <script>
        const html5QrCode = new Html5Qrcode("reader");
        const qrCodeSuccessCallback = (decodedText, decodedResult) => {
            SubmitCallback(decodedText)
        };

        // Support multiple barcode formats
        const config = {
            fps: 10,
            qrbox: { width: 250, height: 150 },
            formatsToSupport: [
                Html5QrcodeSupportedFormats.QR_CODE,
                Html5QrcodeSupportedFormats.AZTEC,
                Html5QrcodeSupportedFormats.CODABAR,
                Html5QrcodeSupportedFormats.CODE_39,
                Html5QrcodeSupportedFormats.CODE_93,
                Html5QrcodeSupportedFormats.CODE_128,
                Html5QrcodeSupportedFormats.DATA_MATRIX,
                Html5QrcodeSupportedFormats.EAN_13,
                Html5QrcodeSupportedFormats.EAN_8,
                Html5QrcodeSupportedFormats.ITF,
                Html5QrcodeSupportedFormats.PDF_417,
                Html5QrcodeSupportedFormats.UPC_A,
                Html5QrcodeSupportedFormats.UPC_E
            ]
        };

        html5QrCode.start({ facingMode: "environment" }, config, qrCodeSuccessCallback);
    </script>
</body>
</html>
''';
