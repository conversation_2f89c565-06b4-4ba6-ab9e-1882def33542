import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:path/path.dart' as p;
import 'package:permission_handler/permission_handler.dart';
import 'package:webview_windows/webview_windows.dart';
import 'package:webviewx_plus/webviewx_plus.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'package:rounds/widgets/scanner/scanner_html.dart';

class BarcodeScannerWidget extends StatefulWidget {
  final String? title;
  final Function(String result, bool isScanned)? resultCallback;

  const BarcodeScannerWidget(
    this.resultCallback, {
    Key? key,
    this.title,
  }) : super(key: key);

  @override
  State<BarcodeScannerWidget> createState() => _BarcodeScannerWidgetState();
}

class _BarcodeScannerWidgetState extends State<BarcodeScannerWidget> {
  WebViewXController? webViewXController;

  late final WebviewController _controller;
  bool _isPermissionGranted = false;
  bool _isInit = false;
  String? _error;

  bool get _isWindows => !kIsWeb && Platform.isWindows;

  @override
  void initState() {
    super.initState();

    if (_isWindows) {
      _controller = WebviewController();
      _initPlatform();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    webViewXController?.dispose();
    super.dispose();
  }

  Future<void> _initPlatform() async {
    try {
      _isPermissionGranted = await Permission.camera.request().isGranted;
      await _controller.initialize();
      final fileUrl = getAssetFileUrl(asset: "assets/barcode/barcode.html");
      await _controller.loadUrl(fileUrl);
      _controller.webMessage.listen((event) {
        // The JS side must post messages as either a string or as a map with a 'methodName'
        if (event is Map &&
            event['methodName'] == "successCallback" &&
            event['data'] is String) {
          widget.resultCallback!(event['data'], true);
        } else if (event is String) {
          widget.resultCallback!(event, false);
        }
      });

      setState(() => _isInit = true);
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isInit = true;
      });
    }
  }

  /// Converts a Flutter asset path to file URL suitable for webview_windows.
  String getAssetFileUrl({required String asset}) {
    final assetFile = p.join(
      p.dirname(Platform.resolvedExecutable),
      'data',
      'flutter_assets',
      asset,
    );
    return Uri.file(assetFile).toString();
  }

  /// Handles camera permission prompt when requested by WebView
  Future<WebviewPermissionDecision> _onPermissionRequested(
    String url,
    WebviewPermissionKind kind,
    bool isUserInitiated,
  ) async {
    if (kind == WebviewPermissionKind.camera && !_isPermissionGranted) {
      final granted = await showDialog<WebviewPermissionDecision>(
        context: context,
        builder: (c) => AlertDialog(
          title: const Text('Camera Permission Requested'),
          content:
              const Text('This feature needs camera access to scan barcodes.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(c, WebviewPermissionDecision.deny),
              child: const Text('Deny'),
            ),
            TextButton(
              onPressed: () =>
                  Navigator.pop(c, WebviewPermissionDecision.allow),
              child: const Text('Allow'),
            ),
          ],
        ),
      );
      _isPermissionGranted = (granted == WebviewPermissionDecision.allow);
      return granted ?? WebviewPermissionDecision.none;
    }
    // Already granted, just allow
    return WebviewPermissionDecision.allow;
  }

  @override
  Widget build(BuildContext context) {
    if (kIsWeb) {
      // Use WebViewX for web
      return SizedBox(
        width: 300,
        height: 300,
        child: WebViewX(
          width: 300,
          height: 300,
          javascriptMode: JavascriptMode.unrestricted,
          initialContent: barcodeHtmlString,
          initialSourceType: SourceType.html,
          onWebViewCreated: (controller) {
            webViewXController = controller;
          },
          dartCallBacks: {
            DartCallback(
              name: 'SubmitCallback',
              callBack: (msg) {
                widget.resultCallback?.call(msg.toString(), true);
              },
            ),
          },
        ),
      );
    } else if (!kIsWeb && (Platform.isAndroid || Platform.isIOS)) {
      // Use native QR scanner for mobile platforms
      return _MobileQrScannerWidget(
        resultCallback: widget.resultCallback,
      );
    } else if (_isWindows) {
      // Windows platform: use webview_windows
      if (_error != null) {
        return Center(
            child: Text('Error: $_error',
                style: const TextStyle(color: Colors.red)));
      }
      if (!_isInit) {
        return const Center(child: CircularProgressIndicator());
      }
      return SizedBox(
        width: 300,
        height: 300,
        child: Webview(
          _controller,
          permissionRequested: (url, kind, initiated) =>
              _onPermissionRequested(url, kind, initiated),
          width: 300,
          height: 300,
        ),
      );
    } else {
      return const Center(
          child: Text('Barcode scanning is not supported on this platform.'));
    }
  }
}

class _MobileQrScannerWidget extends StatefulWidget {
  final Function(String result, bool isScanned)? resultCallback;

  const _MobileQrScannerWidget({
    Key? key,
    this.resultCallback,
  }) : super(key: key);

  @override
  State<_MobileQrScannerWidget> createState() => _MobileQrScannerWidgetState();
}

class _MobileQrScannerWidgetState extends State<_MobileQrScannerWidget> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  bool isScanning = true;
  bool hasPermission = false;
  bool isCheckingPermission = true;

  @override
  void initState() {
    super.initState();
    _checkCameraPermission();
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  Future<void> _checkCameraPermission() async {
    final status = await Permission.camera.status;
    if (status.isGranted) {
      setState(() {
        hasPermission = true;
        isCheckingPermission = false;
      });
    } else if (status.isDenied) {
      final result = await Permission.camera.request();
      setState(() {
        hasPermission = result.isGranted;
        isCheckingPermission = false;
      });
    } else {
      setState(() {
        hasPermission = false;
        isCheckingPermission = false;
      });
    }
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) {
      if (isScanning && scanData.code != null) {
        isScanning = false; // Prevent multiple scans
        widget.resultCallback?.call(scanData.code!, true);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
      height: 300,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: isCheckingPermission
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : !hasPermission
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.camera_alt_outlined,
                          size: 60,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Camera permission required',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Please grant camera permission to scan QR codes and barcodes',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _checkCameraPermission,
                          child: const Text('Grant Permission'),
                        ),
                      ],
                    ),
                  )
                : QRView(
                    key: qrKey,
                    onQRViewCreated: _onQRViewCreated,
                    formatsAllowed: [
                      BarcodeFormat.qrcode,
                      BarcodeFormat.aztec,
                      BarcodeFormat.dataMatrix,
                      BarcodeFormat.pdf417,
                      BarcodeFormat.code39,
                      BarcodeFormat.code93,
                      BarcodeFormat.code128,
                      BarcodeFormat.ean8,
                      BarcodeFormat.ean13,
                      BarcodeFormat.itf,
                      BarcodeFormat.upcE,
                      BarcodeFormat.codabar,
                    ],
                    overlay: QrScannerOverlayShape(
                      borderColor: Colors.blue,
                      borderRadius: 10,
                      borderLength: 30,
                      borderWidth: 10,
                      cutOutSize: 250,
                    ),
                  ),
      ),
    );
  }
}
