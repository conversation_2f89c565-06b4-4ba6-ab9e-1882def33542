import 'dart:async';

import 'package:flutter/material.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/utils/app_colors.dart';

class PendingAttachmentCount extends StatefulWidget {
  final int count;
  final double size;
  final Duration duration;

  const PendingAttachmentCount({
    Key? key,
    required this.count,
    this.size = 50.0,
    this.duration = const Duration(milliseconds: 300),
  }) : super(key: key);

  @override
  State<PendingAttachmentCount> createState() => _PendingAttachmentCountState();
}

class _PendingAttachmentCountState extends State<PendingAttachmentCount>
    with SingleTickerProviderStateMixin {
  bool _expanded = false;

  late Timer _iconSwapTimer;
  bool _showDownload = false;

  @override
  void initState() {
    super.initState();
    _iconSwapTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted && widget.count > 0) {
        setState(() {
          _showDownload = !_showDownload;
        });
      }
    });
  }

  @override
  void dispose() {
    _iconSwapTimer.cancel();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _expanded = !_expanded;
      if (_expanded) {
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final double iconSize = widget.size * 0.5;

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 400),
      switchInCurve: Curves.easeOutBack,
      switchOutCurve: Curves.easeIn,
      transitionBuilder: (child, animation) {
        return ScaleTransition(
          scale: animation,
          child: FadeTransition(
            opacity: animation,
            child: child,
          ),
        );
      },
      child: widget.count == 0
          ? const SizedBox.shrink()
          : Tooltip(
              message: "Downloading Attachments (${widget.count})",
              child: GestureDetector(
                onTap: _toggleExpanded,
                child: AnimatedContainer(
                  duration: widget.duration,
                  curve: Curves.easeInOut,
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 6,
                        offset: const Offset(0, 3),
                      )
                    ],
                  ),
                  height: widget.size,
                  width: _expanded ? widget.size * 2 : widget.size,
                  child: Row(
                    mainAxisAlignment: _expanded
                        ? MainAxisAlignment.spaceEvenly
                        : MainAxisAlignment.center,
                    children: [
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          const CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2.0,
                          ),
                          AnimatedSwitcher(
                            duration: const Duration(milliseconds: 400),
                            switchInCurve: Curves.easeInOut,
                            switchOutCurve: Curves.easeInOut,
                            transitionBuilder: (child, animation) =>
                                FadeTransition(
                              opacity: animation,
                              child: ScaleTransition(
                                  scale: animation, child: child),
                            ),
                            child: Icon(
                              _showDownload ? Icons.download : Icons.attachment,
                              key: ValueKey(_showDownload),
                              color: Colors.white,
                              size: iconSize,
                            ),
                          ),
                        ],
                      ),
                      AnimatedSize(
                        duration: widget.duration,
                        curve: Curves.easeInOut,
                        child: _expanded
                            ? Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, right: 8.0),
                                child: Text(
                                  '${widget.count}',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                  ),
                                ),
                              )
                            : const SizedBox.shrink(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}
