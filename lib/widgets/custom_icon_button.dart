import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/utils/app_colors.dart';

class CustomIconButton extends ConsumerWidget {
  const CustomIconButton({
    super.key,
    required this.ontap,
    this.focusNode,
    this.isLoading = false,
  });

  final VoidCallback ontap;
  final bool isLoading;
  final FocusNode? focusNode;

  void _handleKeyEvent(RawKeyEvent event) {
    if (event.runtimeType == RawKeyDownEvent &&
        (event.logicalKey == LogicalKeyboardKey.enter ||
            event.logicalKey == LogicalKeyboardKey.numpadEnter)) {
      ontap();
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return RawKeyboardListener(
      focusNode: focusNode ?? FocusNode(),
      onKey: _handleKeyEvent,
      child: Container(
        height: 50,
        width: 50,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white),
          color: isLoading
              ? AppColors.black.withOpacity(0.4)
              : AppColors.buttonColor,
        ),
        margin: const EdgeInsets.symmetric(vertical: 10),
        child: IconButton(
          style: IconButton.styleFrom(
            backgroundColor: isLoading ? Colors.grey : Colors.transparent,
            shadowColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          onPressed: isLoading ? null : ontap,
          icon: isLoading
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                )
              : const Icon(
                  Icons.chevron_right,
                  size: 30,
                  color: Colors.white,
                ),
        ),
      ),
    );
  }
}
