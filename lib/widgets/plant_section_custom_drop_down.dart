/*
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/be/PLANT_SECTION_HEADER.dart';

import '../helpers/ui_helper.dart';
import '../pages/profile/profile_screen.dart';
import '../providers/fault/fault_header_provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../utils/app_colors.dart';

class PlantSectionCustomDropDown extends ConsumerStatefulWidget {
  const PlantSectionCustomDropDown({super.key});

  @override
  ConsumerState<PlantSectionCustomDropDown> createState() =>
      _PlantSectionCustomDropDownState();
}

class _PlantSectionCustomDropDownState
    extends ConsumerState<PlantSectionCustomDropDown> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: getDropdown(),
    );
  }

  bool isPlantSectionDropdownOpen = false;
  List<String> selectedPlantSection = [];

  Widget getDropdown() {
    final isEdit = ref.watch(editProfileScreenProvider.notifier).state;

    final plant = ref.watch(plantProvider.notifier).state;

    final plantSections = ref
        .watch(plantSectionListProvider.notifier)
        .state
        .where((element) => element.plant_id == plant)
        .toList();

    final plantSectionValidation =
        ref.watch(plantSectionValidationsProvider.notifier);

    return Container(
      decoration: UIHelper.fieldDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: isEdit
                ? null
                : () {
                    setState(() {
                      isPlantSectionDropdownOpen = !isPlantSectionDropdownOpen;
                    });
                  },
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 10.0, vertical: 12.0),
              child: Row(
                children: [
                  Expanded(
                    child: selectedPlantSection.isNotEmpty
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: selectedPlantSection.map((sectionId) {
                              final section = plantSections.firstWhere(
                                (e) => e.section_id == sectionId,
                                orElse: () => PLANT_SECTION_HEADER(
                                    plant_id: '',
                                    section_id: ''), // create dummy if needed
                              );
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(section.section_id ?? '',
                                      style: UIHelper.valueBoldStyle()),
                                  Text(section.description ?? '',
                                      style: UIHelper.descriptionStyle()),
                                ],
                              );
                            }).toList(),
                          )
                        : Text(AppLocalizations.of(context)!.select,
                            style: UIHelper.valueStyle()),
                  ),
                  isEdit
                      ? Icon(
                          Icons.arrow_drop_down,
                          color: AppColors.grey,
                        )
                      : Icon(
                          isPlantSectionDropdownOpen
                              ? Icons.arrow_drop_up
                              : Icons.arrow_drop_down,
                        ),
                ],
              ),
            ),
          ),
          if (isPlantSectionDropdownOpen)
            Container(
              color: AppColors.white,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: plantSections.length,
                itemBuilder: (context, index) {
                  final option = plantSections[index];
                  final isSelected =
                      selectedPlantSection.contains(option.section_id);

                  return CheckboxListTile(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 5.0),
                    activeColor: AppColors.primaryColor,
                    title: Padding(
                      padding: const EdgeInsets.only(left: 5.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(option.section_id ?? '',
                              style: UIHelper.valueBoldStyle()),
                          Padding(
                            padding: const EdgeInsets.only(top: 1.0),
                            child: Text(option.description ?? '',
                                style: UIHelper.descriptionStyle()),
                          ),
                        ],
                      ),
                    ),
                    value: isSelected,
                    onChanged: isEdit
                        ? null
                        : (checked) {
                            setState(() {
                              if (checked == true) {
                                selectedPlantSection.add(option.section_id!);
                              } else {
                                selectedPlantSection.remove(option.section_id!);
                              }

                              if (selectedPlantSection.isEmpty) {
                                plantSectionValidation
                                    .getPlantSectionValidation(true);
                              } else {
                                plantSectionValidation
                                    .clearPlantSectionValidation();
                              }

                              ref
                                  .read(plantSectionProvider.notifier)
                                  .setPlantSection(selectedPlantSection);
                            });
                          },
                  );
                },
              ),
            ),
        ],
      ),
    );
  }
}
*/
