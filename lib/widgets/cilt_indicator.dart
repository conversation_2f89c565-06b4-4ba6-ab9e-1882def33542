import 'package:flutter/material.dart';

import '../utils/app_colors.dart';

class CiltColor {
  final Color cColor;
  final Color iColor;
  final Color lColor;
  final Color tColor;

  /// Creates an instance of `CiltColor` from a given CILT code.
  ///
  /// The `fromCiltCode` constructor initializes the object using the provided
  /// CILT code, which is a int representation of the code. This constructor
  /// parses the CILT code and sets the corresponding Color properties for C I L T.
  ///
  /// Example usage:
  /// ```dart
  /// var instance = CiltColor.fromCiltCode('your_cilt_code_here');
  /// ```
  CiltColor.fromCiltCode(int code)
      : cColor = (code & 8) != 0 ? const Color(0xffA2C930) : Colors.grey,
        iColor = (code & 4) != 0 ? const Color(0xffED561D) : Colors.grey,
        lColor = (code & 2) != 0 ? const Color(0xffFBA53A) : Colors.grey,
        tColor = (code & 1) != 0 ? const Color(0xff5CB7EC) : Colors.grey;
}

enum CiltIndicatorViewType {
  grid,
  list,
}

class CiltIndicator extends StatelessWidget {
  final CiltColor colors;
  final Color textColor;
  final double? boxSize;
  final bool faultIcon;
  final CiltIndicatorViewType viewType; // New parameter

  const CiltIndicator({
    super.key,
    required this.colors,
    this.textColor = Colors.white,
    this.boxSize,
    this.faultIcon = false,
    this.viewType = CiltIndicatorViewType.list, // Default view is list
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double calculatedSize = (constraints.maxWidth - (3 * 8)) / 4;
        double size = boxSize ?? calculatedSize;
        double spacing = size * 0.1;
        size = size > 0 ? size : 40;

        List<Widget> indicators = faultIcon
            ? [_buildIndicatorBox('!', AppColors.faultIconOrange, size)]
            : [
                _buildIndicatorBox('C', colors.cColor, size),
                _buildIndicatorBox('I', colors.iColor, size),
                _buildIndicatorBox('L', colors.lColor, size),
                _buildIndicatorBox('T', colors.tColor, size),
              ];

        return viewType == CiltIndicatorViewType.grid
            ? Wrap(
                spacing: spacing,
                runSpacing: spacing,
                children: indicators.map((e) {
                  return SizedBox(width: size, height: size, child: e);
                }).toList(),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: indicators
                    .expand((e) => [e, SizedBox(width: spacing)])
                    .toList()
                  ..removeLast(),
              );
      },
    );
  }

  Widget _buildIndicatorBox(String text, Color color, double size) {
    double fontSize = size * 0.7;

    return Container(
      width: size,
      height: size,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(size * 0.2),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
