import 'package:flutter/material.dart';

import '../helpers/ui_helper.dart';
import '../utils/app_colors.dart';

class CustomDropdown<T> extends StatefulWidget {
  final List<T> items;
  final String Function(T) itemLabel;
  final ValueChanged<T?>? onChanged;
  final bool isMultiSelect;
  final List<T>? selectedItems;
  final String hint;
  final bool showSearch;

  const CustomDropdown({
    Key? key,
    required this.items,
    required this.itemLabel,
    this.onChanged,
    this.isMultiSelect = false,
    this.selectedItems,
    this.hint = "Select",
    this.showSearch = true,
  }) : super(key: key);

  @override
  _CustomDropdownState<T> createState() => _CustomDropdownState<T>();
}

class _CustomDropdownState<T> extends State<CustomDropdown<T>> {
  late List<T> _filteredItems;
  List<T> _selectedItems = [];
  String _searchText = "";

  @override
  void initState() {
    super.initState();
    _filteredItems = widget.items;
    _selectedItems = widget.selectedItems ?? [];
  }

  @override
  void didUpdateWidget(covariant CustomDropdown<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.items != widget.items) {
      setState(() {
        _filteredItems = widget.items;
      });
    }
  }

  void _showDropdown(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12.0)),
      ),
      isScrollControlled: true,
      constraints: BoxConstraints(
        maxWidth: 600,
      ),
      builder: (context) {
        return StatefulBuilder(builder: (context, setState) {
          double initialHeight = 0.5;
          return DraggableScrollableSheet(
              expand: false,
              initialChildSize: initialHeight,
              minChildSize: 0.1,
              maxChildSize: 1.0,
              builder: (context, scrollController) {
                return SafeArea(
                  child: Container(
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (widget.showSearch)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 10.0),
                              child: SizedBox(
                                height: kMinInteractiveDimension,
                                width: 580,
                                child: TextField(
                                  style: UIHelper.valueStyle(),
                                  textAlign: TextAlign.start,
                                  cursorColor: AppColors.primaryColor,
                                  decoration: InputDecoration(
                                    contentPadding: const EdgeInsets.symmetric(
                                        vertical: 12.0, horizontal: 10.0),
                                    hintText: "Search...",
                                    border: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(5.0),
                                        borderSide: BorderSide(
                                            color: AppColors.primaryColor,
                                            width: 1)),
                                    enabledBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(5.0),
                                        borderSide: BorderSide(
                                            color: AppColors.primaryColor,
                                            width: 1)),
                                    focusedBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(5.0),
                                        borderSide: BorderSide(
                                            color: AppColors.primaryColor,
                                            width: 1)),
                                  ),
                                  onChanged: (query) {
                                    setState(() {
                                      _searchText = query;

                                      if (query.isEmpty) {
                                        _filteredItems = widget.items;
                                      } else {
                                        _filteredItems = widget.items
                                            .where((item) => widget
                                                .itemLabel(item)
                                                .toLowerCase()
                                                .contains(query.toLowerCase()))
                                            .toList();
                                      }
                                    });
                                  },
                                ),
                              ),
                            ),
                          SizedBox(height: 10),
                          Expanded(
                            child: ListView.builder(
                              shrinkWrap: true,
                              itemCount: _filteredItems.length,
                              itemBuilder: (context, index) {
                                final item = _filteredItems[index];
                                final isSelected =
                                    _selectedItems.contains(item);
                                return InkWell(
                                  onTap: () {
                                    setState(() {
                                      _selectedItems = [item];
                                      widget.onChanged?.call(item);
                                      Navigator.pop(context);
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 12),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                            child: itemLabel(item as String)),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              });
        });
      },
    );
  }

  Widget itemLabel(String item) {
    final parts = item.split('\n');
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: '${parts[0]}\n',
            style: UIHelper.valueBoldStyle(),
          ),
          if (parts.length > 1)
            TextSpan(
              text: parts[1],
              style: UIHelper.descriptionStyle(),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: UIHelper.fieldDecoration(),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: InkWell(
          onTap: () => _showDropdown(context),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5.0),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Padding(
                      padding: const EdgeInsets.only(left: 5.0),
                      child: _selectedItems.isNotEmpty
                          ? RichText(
                              text: TextSpan(
                                style: UIHelper.valueStyle(),
                                children: _selectedItems
                                    .asMap()
                                    .entries
                                    .expand((entry) {
                                  final item = (entry.value as String?) ?? '';
                                  final parts = item.split('\n');
                                  final spans = <TextSpan>[
                                    TextSpan(
                                      text: parts[0] + '\n',
                                      style: UIHelper.valueBoldStyle(),
                                    ),
                                    if (parts.length > 1)
                                      TextSpan(
                                        text: parts[1],
                                        style: UIHelper.descriptionStyle(),
                                      ),
                                  ];
                                  if (entry.key != _selectedItems.length - 1) {
                                    spans.add(const TextSpan(text: ', '));
                                  }
                                  return spans;
                                }).toList(),
                              ),
                              overflow: TextOverflow.ellipsis,
                            )
                          : Padding(
                              padding: const EdgeInsets.all(6.0),
                              child: Text(
                                widget.hint,
                                style: UIHelper.valueStyle(),
                                overflow: TextOverflow.ellipsis,
                              ),
                            )),
                ),
                const Icon(Icons.arrow_drop_down,
                    size: 23), // Reduced icon size
              ],
            ),
          ),
        ),
      ),
    );
  }
}
