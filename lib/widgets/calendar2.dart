import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:math' as math;

class VerticalCalendar extends StatefulWidget {
  @override
  _VerticalCalendarState createState() => _VerticalCalendarState();
}

class _VerticalCalendarState extends State<VerticalCalendar> {
  DateTime selectedDate = DateTime.now();
  ScrollController _scrollController = ScrollController();
  List<DateTime> weekDates = [];

  @override
  void initState() {
    super.initState();
    // Create the list of 7 days (3 before, current day, 3 after)
    for (int i = -3; i <= 3; i++) {
      weekDates.add(selectedDate.add(Duration(days: i)));
    }
    // Auto scroll to center the current day (index 3)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      double initialOffset = 60.0 * 3; // 3 days before the current date
      _scrollController.jumpTo(initialOffset);
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 40,
      height: 40, // Set a fixed height for the calendar
      child: ListView.builder(
        controller: _scrollController,
        scrollDirection: Axis.vertical,
        itemCount: weekDates.length, // Limited to only 7 days
        itemBuilder: (context, index) {
          DateTime date = weekDates[index];
          bool isSelected = date.day == selectedDate.day &&
              date.month == selectedDate.month &&
              date.year == selectedDate.year;

          // Calculate angle and scale for cylindrical effect
          double angle = (index - 3) * 0.2; // Adjust angle based on index
          double scale =
              1.0 - (0.1 * (index - 3).abs()); // Make scaling gentler

          // Ensure minimum scale to keep the text visible
          scale = math.max(0.8, scale); // Set a minimum scale of 0.8

          return GestureDetector(
            onTap: () {
              setState(() {
                selectedDate = date;
              });
            },
            child: Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..setEntry(3, 2, 0.001) // Perspective effect
                ..rotateX(angle), // Rotate on X-axis for cylindrical effect
              child: Container(
                height: 35, // Customize height
                margin: EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.blue : Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    if (isSelected)
                      BoxShadow(
                        color: Colors.blueAccent.withOpacity(0.5),
                        spreadRadius: 1,
                        blurRadius: 3,
                      ),
                  ],
                ),
                child: Transform.scale(
                  scale: scale, // Apply scaling for depth
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        DateFormat('EEE')
                            .format(date), // Day abbreviation (Mon, Tue)
                        style: TextStyle(
                          color: isSelected ? Colors.white : Colors.black,
                          fontSize:
                              scale * 14, // Adjust font size based on scale
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      SizedBox(width: 4),
                      Text(
                        date.day.toString(), // Day number
                        style: TextStyle(
                          fontSize:
                              scale * 20, // Adjust font size based on scale
                          fontWeight: FontWeight.bold,
                          color: isSelected ? Colors.white : Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
