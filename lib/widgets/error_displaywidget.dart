import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

class ErrorDisplayWidget extends StatefulWidget {
  final FlutterErrorDetails details;

  const ErrorDisplayWidget({Key? key, required this.details}) : super(key: key);

  @override
  State<ErrorDisplayWidget> createState() => _ErrorDisplayWidgetState();
}

class _ErrorDisplayWidgetState extends State<ErrorDisplayWidget> {
  bool _showStackTrace = false;

  @override
  Widget build(BuildContext context) {
    final error = widget.details.exceptionAsString();
    final stackTrace =
        widget.details.stack?.toString() ?? 'No stack trace available';
    return Material(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SelectableRegion(
          focusNode: FocusNode(),
          selectionControls: materialTextSelectionControls,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Error: Something went wrong',
                  style: Theme.of(context)
                      .textTheme
                      .headline6
                      ?.copyWith(color: Colors.red),
                ),
                const SizedBox(height: 12),
                Text(
                  'Error Message:',
                  style: Theme.of(context).textTheme.subtitle1,
                ),
                Text(error, style: const TextStyle(color: Colors.red)),
                const SizedBox(height: 12),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _showStackTrace = !_showStackTrace;
                    });
                  },
                  child: Row(
                    children: [
                      Icon(
                        _showStackTrace ? Icons.expand_less : Icons.expand_more,
                        color: Colors.blue,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'Stack Trace',
                        style: TextStyle(
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                if (_showStackTrace) ...[
                  const SizedBox(height: 8),
                  Text(
                    stackTrace,
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
