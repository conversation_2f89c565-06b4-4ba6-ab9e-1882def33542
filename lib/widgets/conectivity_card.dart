import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:rounds/providers/server_connection_provider.dart';
import 'package:rounds/utils/app_styles.dart';
import 'package:rounds/utils/constants.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class ConnectivityCard extends ConsumerWidget implements PreferredSizeWidget {
  final double preferredHeightWhenConnected;
  final double preferredHeightWhenNotConnected;
  final double maxHeight; // Maximum height for the card

  const ConnectivityCard({
    Key? key,
    this.preferredHeightWhenConnected = kToolbarHeight,
    this.maxHeight = 40, // Maximum height for the card
    this.preferredHeightWhenNotConnected = 40,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connection = ref.watch(serverConnectionProvider);

    return FutureBuilder<ConnectivityResult>(
      future: Connectivity().checkConnectivity(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox.shrink(); // Hide while checking connectivity
        }

        final connectivityResult = snapshot.data;
        if (connectivityResult == ConnectivityResult.none) {
          // No internet connection
          return Container(
            height: maxHeight,
            color: HexColor("#EC665C"),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Icon(
                  Icons.cloud,
                  color: Colors.white,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(context)!.offline,
                  style:
                      AppStyles.textStyle_16_600w.copyWith(color: Colors.white),
                ),
              ],
            ),
          );
        }

        // Internet is active, check server connection
        return connection.when(
          data: (status) {
            final bool isConnected = status != Constants.NotConnected;
            if (isConnected) {
              return const SizedBox.shrink();
            } else {
              return Container(
                height: maxHeight,
                color: HexColor("#EC665C"),
                child: Center(
                  child: Text(
                    "Server Not Reachable",
                    style: AppStyles.textStyle_16_600w
                        .copyWith(color: Colors.white),
                  ),
                ),
              );
            }
          },
          loading: () => const SizedBox.shrink(), // Hide while loading
          error: (err, _) => Container(
            height: maxHeight,
            color: HexColor("#EC665C"),
            child: Center(
              child: Text(
                "Server Not Reachable",
                style:
                    AppStyles.textStyle_16_600w.copyWith(color: Colors.white),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(preferredHeightWhenConnected);
}
