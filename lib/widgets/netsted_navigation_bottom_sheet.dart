import 'package:flutter/material.dart';

class NestedNavigationBottomSheet extends StatelessWidget {
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<
      NavigatorState>(); // key is necessary to access the NavigatorState

  NestedNavigationBottomSheet({super.key, required this.child});
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false, // Initially disable system back gestures
        onPopInvoked: (didPop) async {
          if (didPop) {
            return; // Pop already happened, do nothing
          }
          final NavigatorState? childNavigator = navigatorKey.currentState;
          if (childNavigator != null && childNavigator.canPop()) {
            childNavigator.pop();
          } else {
            Navigator.of(context).pop();
          }
        },
        child: child);
  }
}
