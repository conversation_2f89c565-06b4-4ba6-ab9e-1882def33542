//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "FAULT".
*/	
class FAULT_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "FAULT_HEADER";
	
	// Unique Id of an Fault
	static const String FIELD_FAULT_ID = "FAULT_ID";

	// Fault Type 
	static const String FIELD_FAULT_TYPE = "FAULT_TYPE";

	// No desc available
	static const String FIELD_FAILURE_MODE = "FAILURE_MODE";

	// Priority 1=Highest , 2=High , 3=Medium , 4= Low , 5=Lowest
	static const String FIELD_PRIORITY = "PRIORITY";

	// Detailed Description of Fault 
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// No desc available
	static const String FIELD_PLANT_ID = "PLANT_ID";

	// Funcational location Id
	static const String FIELD_LOCATION_ID = "LOCATION_ID";

	// Equipment Number 
	static const String FIELD_ASSET_NO = "ASSET_NO";

	// Status of Fault  =  Open , Inprogress , Close 
	static const String FIELD_STATUS = "STATUS";

	// No desc available
	static const String FIELD_INSP_TASK_ID = "INSP_TASK_ID";

	// No desc available
	static const String FIELD_CILT_TASK_ID = "CILT_TASK_ID";

	// Reported By = Person Name or Number who Reported 
	static const String FIELD_REPORTED_BY = "REPORTED_BY";

	// Fault Reported Date
	static const String FIELD_REPORTED_ON = "REPORTED_ON";

	// No desc available
	static const String FIELD_REQ_START = "REQ_START";

	// No desc available
	static const String FIELD_REQ_END = "REQ_END";

	// No desc available
	static const String FIELD_MALF_START = "MALF_START";

	// No desc available
	static const String FIELD_MALF_END = "MALF_END";

	// No desc available
	static const String FIELD_BREAKDOWN = "BREAKDOWN";

	// No desc available
	static const String FIELD_JOB_ID = "JOB_ID";

	// ERP Fault Id 
	static const String FIELD_EXTERNAL_ID = "EXTERNAL_ID";

	// ERP Source
	static const String FIELD_EXT_SOURCE = "EXT_SOURCE";

	// No desc available
	static const String FIELD_CREATED_BY = "CREATED_BY";

	// No desc available
	static const String FIELD_CREATED_ON = "CREATED_ON";

	// No desc available
	static const String FIELD_CHANGED_BY = "CHANGED_BY";

	// No desc available
	static const String FIELD_CHANGED_ON = "CHANGED_ON";

	// Detailed Description 
	static const String FIELD_DETAILS = "DETAILS";

	// No desc available
	static const String FIELD_P_MODE = "P_MODE";
	
    int? _fault_id;
    String? _fault_type;
    String? _failure_mode;
    String? _priority;
    String? _description;
    String? _plant_id;
    String? _location_id;
    int? _asset_no;
    String? _status;
    int? _insp_task_id;
    int? _cilt_task_id;
    String? _reported_by;
    int? _reported_on;
    int? _req_start;
    int? _req_end;
    int? _malf_start;
    int? _malf_end;
    String? _breakdown;
    int? _job_id;
    String? _external_id;
    String? _ext_source;
    String? _created_by;
    int? _created_on;
    String? _changed_by;
    int? _changed_on;
    String? _details;
    String? _p_mode;
	
	FAULT_HEADER({
			required fault_id,
			fault_type,
			failure_mode,
			priority,
			description,
			plant_id,
			location_id,
			asset_no,
			status,
			insp_task_id,
			cilt_task_id,
			reported_by,
			reported_on,
			req_start,
			req_end,
			malf_start,
			malf_end,
			breakdown,
			job_id,
			external_id,
			ext_source,
			created_by,
			created_on,
			changed_by,
			changed_on,
			details,
			p_mode}) :
			_fault_id = fault_id,
			_fault_type = fault_type,
			_failure_mode = failure_mode,
			_priority = priority,
			_description = description,
			_plant_id = plant_id,
			_location_id = location_id,
			_asset_no = asset_no,
			_status = status,
			_insp_task_id = insp_task_id,
			_cilt_task_id = cilt_task_id,
			_reported_by = reported_by,
			_reported_on = reported_on,
			_req_start = req_start,
			_req_end = req_end,
			_malf_start = malf_start,
			_malf_end = malf_end,
			_breakdown = breakdown,
			_job_id = job_id,
			_external_id = external_id,
			_ext_source = ext_source,
			_created_by = created_by,
			_created_on = created_on,
			_changed_by = changed_by,
			_changed_on = changed_on,
			_details = details,
			_p_mode = p_mode {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	FAULT_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_fault_id = json[FIELD_FAULT_ID]; 
		_fault_type = json[FIELD_FAULT_TYPE]; 
		_failure_mode = json[FIELD_FAILURE_MODE]; 
		_priority = json[FIELD_PRIORITY]; 
		_description = json[FIELD_DESCRIPTION]; 
		_plant_id = json[FIELD_PLANT_ID]; 
		_location_id = json[FIELD_LOCATION_ID]; 
		_asset_no = json[FIELD_ASSET_NO]; 
		_status = json[FIELD_STATUS]; 
		_insp_task_id = json[FIELD_INSP_TASK_ID]; 
		_cilt_task_id = json[FIELD_CILT_TASK_ID]; 
		_reported_by = json[FIELD_REPORTED_BY]; 
		_reported_on = json[FIELD_REPORTED_ON]; 
		_req_start = json[FIELD_REQ_START]; 
		_req_end = json[FIELD_REQ_END]; 
		_malf_start = json[FIELD_MALF_START]; 
		_malf_end = json[FIELD_MALF_END]; 
		_breakdown = json[FIELD_BREAKDOWN]; 
		_job_id = json[FIELD_JOB_ID]; 
		_external_id = json[FIELD_EXTERNAL_ID]; 
		_ext_source = json[FIELD_EXT_SOURCE]; 
		_created_by = json[FIELD_CREATED_BY]; 
		_created_on = json[FIELD_CREATED_ON]; 
		_changed_by = json[FIELD_CHANGED_BY]; 
		_changed_on = json[FIELD_CHANGED_ON]; 
		_details = json[FIELD_DETAILS]; 
		_p_mode = json[FIELD_P_MODE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_FAULT_ID] = _fault_id;
		data[FIELD_FAULT_TYPE] = _fault_type;
		data[FIELD_FAILURE_MODE] = _failure_mode;
		data[FIELD_PRIORITY] = _priority;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_PLANT_ID] = _plant_id;
		data[FIELD_LOCATION_ID] = _location_id;
		data[FIELD_ASSET_NO] = _asset_no;
		data[FIELD_STATUS] = _status;
		data[FIELD_INSP_TASK_ID] = _insp_task_id;
		data[FIELD_CILT_TASK_ID] = _cilt_task_id;
		data[FIELD_REPORTED_BY] = _reported_by;
		data[FIELD_REPORTED_ON] = _reported_on;
		data[FIELD_REQ_START] = _req_start;
		data[FIELD_REQ_END] = _req_end;
		data[FIELD_MALF_START] = _malf_start;
		data[FIELD_MALF_END] = _malf_end;
		data[FIELD_BREAKDOWN] = _breakdown;
		data[FIELD_JOB_ID] = _job_id;
		data[FIELD_EXTERNAL_ID] = _external_id;
		data[FIELD_EXT_SOURCE] = _ext_source;
		data[FIELD_CREATED_BY] = _created_by;
		data[FIELD_CREATED_ON] = _created_on;
		data[FIELD_CHANGED_BY] = _changed_by;
		data[FIELD_CHANGED_ON] = _changed_on;
		data[FIELD_DETAILS] = _details;
		data[FIELD_P_MODE] = _p_mode;

		return data;
  }
  
	int? get fault_id => this._fault_id;
	
	set fault_id(int? fault_id) => this._fault_id = fault_id;	

	String? get fault_type => this._fault_type;
	
	set fault_type(String? fault_type) => this._fault_type = fault_type;	

	String? get failure_mode => this._failure_mode;
	
	set failure_mode(String? failure_mode) => this._failure_mode = failure_mode;	

	String? get priority => this._priority;
	
	set priority(String? priority) => this._priority = priority;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get plant_id => this._plant_id;
	
	set plant_id(String? plant_id) => this._plant_id = plant_id;	

	String? get location_id => this._location_id;
	
	set location_id(String? location_id) => this._location_id = location_id;	

	int? get asset_no => this._asset_no;
	
	set asset_no(int? asset_no) => this._asset_no = asset_no;	

	String? get status => this._status;
	
	set status(String? status) => this._status = status;	

	int? get insp_task_id => this._insp_task_id;
	
	set insp_task_id(int? insp_task_id) => this._insp_task_id = insp_task_id;	

	int? get cilt_task_id => this._cilt_task_id;
	
	set cilt_task_id(int? cilt_task_id) => this._cilt_task_id = cilt_task_id;	

	String? get reported_by => this._reported_by;
	
	set reported_by(String? reported_by) => this._reported_by = reported_by;	

	int? get reported_on => this._reported_on;
	
	set reported_on(int? reported_on) => this._reported_on = reported_on;	

	int? get req_start => this._req_start;
	
	set req_start(int? req_start) => this._req_start = req_start;	

	int? get req_end => this._req_end;
	
	set req_end(int? req_end) => this._req_end = req_end;	

	int? get malf_start => this._malf_start;
	
	set malf_start(int? malf_start) => this._malf_start = malf_start;	

	int? get malf_end => this._malf_end;
	
	set malf_end(int? malf_end) => this._malf_end = malf_end;	

	String? get breakdown => this._breakdown;
	
	set breakdown(String? breakdown) => this._breakdown = breakdown;	

	int? get job_id => this._job_id;
	
	set job_id(int? job_id) => this._job_id = job_id;	

	String? get external_id => this._external_id;
	
	set external_id(String? external_id) => this._external_id = external_id;	

	String? get ext_source => this._ext_source;
	
	set ext_source(String? ext_source) => this._ext_source = ext_source;	

	String? get created_by => this._created_by;
	
	set created_by(String? created_by) => this._created_by = created_by;	

	int? get created_on => this._created_on;
	
	set created_on(int? created_on) => this._created_on = created_on;	

	String? get changed_by => this._changed_by;
	
	set changed_by(String? changed_by) => this._changed_by = changed_by;	

	int? get changed_on => this._changed_on;
	
	set changed_on(int? changed_on) => this._changed_on = changed_on;	

	String? get details => this._details;
	
	set details(String? details) => this._details = details;	

	String? get p_mode => this._p_mode;
	
	set p_mode(String? p_mode) => this._p_mode = p_mode;	
	
}