//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "INSP_SCHED".
*/	
class INSP_SCHED_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "INSP_SCHED_HEADER";
	
	// No desc available
	static const String FIELD_SCHED_ID = "SCHED_ID";

	// No desc available
	static const String FIELD_PLAN_ID = "PLAN_ID";

	// No desc available
	static const String FIELD_PRIORITY = "PRIORITY";

	// No desc available
	static const String FIELD_SCHEDULE = "SCHEDULE";

	// No desc available
	static const String FIELD_ASSGN_TYPE = "ASSGN_TYPE";

	// No desc available
	static const String FIELD_ASSGN_VALUE = "ASSGN_VALUE";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    int? _sched_id;
    int? _plan_id;
    String? _priority;
    String? _schedule;
    String? _assgn_type;
    String? _assgn_value;
    String? _inactive;
	
	INSP_SCHED_HEADER({
			required sched_id,
			plan_id,
			priority,
			schedule,
			assgn_type,
			assgn_value,
			inactive}) :
			_sched_id = sched_id,
			_plan_id = plan_id,
			_priority = priority,
			_schedule = schedule,
			_assgn_type = assgn_type,
			_assgn_value = assgn_value,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	INSP_SCHED_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_sched_id = json[FIELD_SCHED_ID]; 
		_plan_id = json[FIELD_PLAN_ID]; 
		_priority = json[FIELD_PRIORITY]; 
		_schedule = json[FIELD_SCHEDULE]; 
		_assgn_type = json[FIELD_ASSGN_TYPE]; 
		_assgn_value = json[FIELD_ASSGN_VALUE]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_SCHED_ID] = _sched_id;
		data[FIELD_PLAN_ID] = _plan_id;
		data[FIELD_PRIORITY] = _priority;
		data[FIELD_SCHEDULE] = _schedule;
		data[FIELD_ASSGN_TYPE] = _assgn_type;
		data[FIELD_ASSGN_VALUE] = _assgn_value;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	int? get sched_id => this._sched_id;
	
	set sched_id(int? sched_id) => this._sched_id = sched_id;	

	int? get plan_id => this._plan_id;
	
	set plan_id(int? plan_id) => this._plan_id = plan_id;	

	String? get priority => this._priority;
	
	set priority(String? priority) => this._priority = priority;	

	String? get schedule => this._schedule;
	
	set schedule(String? schedule) => this._schedule = schedule;	

	String? get assgn_type => this._assgn_type;
	
	set assgn_type(String? assgn_type) => this._assgn_type = assgn_type;	

	String? get assgn_value => this._assgn_value;
	
	set assgn_value(String? assgn_value) => this._assgn_value = assgn_value;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}