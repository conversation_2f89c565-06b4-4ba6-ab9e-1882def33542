//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "ASSET".
*/	
class ASSET_DOCUMENT extends DataStructure {
	
	static const String TABLE_NAME = "ASSET_DOCUMENT";
	
	// Asset_No is like the Unique Id of an Asset
	static const String FIELD_ASSET_NO = "ASSET_NO";

	// Unique Id of an Documnet or file
	static const String FIELD_DOC_ID = "DOC_ID";

	// No desc available
	static const String FIELD_P_MODE = "P_MODE";
	
    int? _asset_no;
    String? _doc_id;
    String? _p_mode;
	
	ASSET_DOCUMENT({
			required asset_no,
			required doc_id,
			p_mode}) :
			_asset_no = asset_no,
			_doc_id = doc_id,
			_p_mode = p_mode {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	ASSET_DOCUMENT.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_asset_no = json[FIELD_ASSET_NO]; 
		_doc_id = json[FIELD_DOC_ID]; 
		_p_mode = json[FIELD_P_MODE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_ASSET_NO] = _asset_no;
		data[FIELD_DOC_ID] = _doc_id;
		data[FIELD_P_MODE] = _p_mode;

		return data;
  }
  
	int? get asset_no => this._asset_no;
	
	set asset_no(int? asset_no) => this._asset_no = asset_no;	

	String? get doc_id => this._doc_id;
	
	set doc_id(String? doc_id) => this._doc_id = doc_id;	

	String? get p_mode => this._p_mode;
	
	set p_mode(String? p_mode) => this._p_mode = p_mode;	
	
}