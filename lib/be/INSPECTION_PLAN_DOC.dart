//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "INSPECTION_PLAN".
*/	
class INSPECTION_PLAN_DOC extends DataStructure {
	
	static const String TABLE_NAME = "INSPECTION_PLAN_DOC";
	
	// No desc available
	static const String FIELD_PLAN_ID = "PLAN_ID";

	// Unique Id of an Documnet or file
	static const String FIELD_DOC_ID = "DOC_ID";
	
    int? _plan_id;
    String? _doc_id;
	
	INSPECTION_PLAN_DOC({
			required plan_id,
			required doc_id}) :
			_plan_id = plan_id,
			_doc_id = doc_id {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	INSPECTION_PLAN_DOC.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_plan_id = json[FIELD_PLAN_ID]; 
		_doc_id = json[FIELD_DOC_ID]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_PLAN_ID] = _plan_id;
		data[FIELD_DOC_ID] = _doc_id;

		return data;
  }
  
	int? get plan_id => this._plan_id;
	
	set plan_id(int? plan_id) => this._plan_id = plan_id;	

	String? get doc_id => this._doc_id;
	
	set doc_id(String? doc_id) => this._doc_id = doc_id;	
	
}