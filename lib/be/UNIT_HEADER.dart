//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "UNIT".
*/	
class UNIT_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "UNIT_HEADER";
	
	// No desc available
	static const String FIELD_UOM = "UOM";

	// No desc available
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// No desc available
	static const String FIELD_DIMENSION = "DIMENSION";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    String? _uom;
    String? _description;
    String? _dimension;
    String? _inactive;
	
	UNIT_HEADER({
			required uom,
			description,
			dimension,
			inactive}) :
			_uom = uom,
			_description = description,
			_dimension = dimension,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	UNIT_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_uom = json[FIELD_UOM]; 
		_description = json[FIELD_DESCRIPTION]; 
		_dimension = json[FIELD_DIMENSION]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_UOM] = _uom;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_DIMENSION] = _dimension;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	String? get uom => this._uom;
	
	set uom(String? uom) => this._uom = uom;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get dimension => this._dimension;
	
	set dimension(String? dimension) => this._dimension = dimension;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}