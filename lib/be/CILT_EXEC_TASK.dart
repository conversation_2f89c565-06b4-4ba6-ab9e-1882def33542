//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "CILT_EXEC".
*/	
class CILT_EXEC_TASK extends DataStructure {
	
	static const String TABLE_NAME = "CILT_EXEC_TASK";
	
	// No desc available
	static const String FIELD_CILT_ID = "CILT_ID";

	// No desc available
	static const String FIELD_CILT_TASK_ID = "CILT_TASK_ID";

	// No desc available
	static const String FIELD_TASK_NO = "TASK_NO";

	// No desc available
	static const String FIELD_SECTION_ID = "SECTION_ID";

	// No desc available
	static const String FIELD_TASK_ID = "TASK_ID";

	// No desc available
	static const String FIELD_STATUS = "STATUS";

	// No desc available
	static const String FIELD_COMPLETED_BY = "COMPLETED_BY";

	// No desc available
	static const String FIELD_COMPLETED_ON = "COMPLETED_ON";

	// No desc available
	static const String FIELD_LATITUDE = "LATITUDE";

	// No desc available
	static const String FIELD_LONGITUDE = "LONGITUDE";

	// No desc available
	static const String FIELD_REASON = "REASON";

	// No desc available
	static const String FIELD_SKIP_COMMENTS = "SKIP_COMMENTS";

	// No desc available
	static const String FIELD_COMMENTS = "COMMENTS";

	// No desc available
	static const String FIELD_P_MODE = "P_MODE";
	
    int? _cilt_id;
    int? _cilt_task_id;
    int? _task_no;
    int? _section_id;
    int? _task_id;
    String? _status;
    String? _completed_by;
    int? _completed_on;
    double? _latitude;
    double? _longitude;
    String? _reason;
    String? _skip_comments;
    String? _comments;
    String? _p_mode;
	
	CILT_EXEC_TASK({
			required cilt_id,
			required cilt_task_id,
			task_no,
			section_id,
			task_id,
			status,
			completed_by,
			completed_on,
			latitude,
			longitude,
			reason,
			skip_comments,
			comments,
			p_mode}) :
			_cilt_id = cilt_id,
			_cilt_task_id = cilt_task_id,
			_task_no = task_no,
			_section_id = section_id,
			_task_id = task_id,
			_status = status,
			_completed_by = completed_by,
			_completed_on = completed_on,
			_latitude = latitude,
			_longitude = longitude,
			_reason = reason,
			_skip_comments = skip_comments,
			_comments = comments,
			_p_mode = p_mode {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	CILT_EXEC_TASK.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_cilt_id = json[FIELD_CILT_ID]; 
		_cilt_task_id = json[FIELD_CILT_TASK_ID]; 
		_task_no = json[FIELD_TASK_NO]; 
		_section_id = json[FIELD_SECTION_ID]; 
		_task_id = json[FIELD_TASK_ID]; 
		_status = json[FIELD_STATUS]; 
		_completed_by = json[FIELD_COMPLETED_BY]; 
		_completed_on = json[FIELD_COMPLETED_ON]; 
		_latitude = json[FIELD_LATITUDE]; 
		_longitude = json[FIELD_LONGITUDE]; 
		_reason = json[FIELD_REASON]; 
		_skip_comments = json[FIELD_SKIP_COMMENTS]; 
		_comments = json[FIELD_COMMENTS]; 
		_p_mode = json[FIELD_P_MODE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_CILT_ID] = _cilt_id;
		data[FIELD_CILT_TASK_ID] = _cilt_task_id;
		data[FIELD_TASK_NO] = _task_no;
		data[FIELD_SECTION_ID] = _section_id;
		data[FIELD_TASK_ID] = _task_id;
		data[FIELD_STATUS] = _status;
		data[FIELD_COMPLETED_BY] = _completed_by;
		data[FIELD_COMPLETED_ON] = _completed_on;
		data[FIELD_LATITUDE] = _latitude;
		data[FIELD_LONGITUDE] = _longitude;
		data[FIELD_REASON] = _reason;
		data[FIELD_SKIP_COMMENTS] = _skip_comments;
		data[FIELD_COMMENTS] = _comments;
		data[FIELD_P_MODE] = _p_mode;

		return data;
  }
  
	int? get cilt_id => this._cilt_id;
	
	set cilt_id(int? cilt_id) => this._cilt_id = cilt_id;	

	int? get cilt_task_id => this._cilt_task_id;
	
	set cilt_task_id(int? cilt_task_id) => this._cilt_task_id = cilt_task_id;	

	int? get task_no => this._task_no;
	
	set task_no(int? task_no) => this._task_no = task_no;	

	int? get section_id => this._section_id;
	
	set section_id(int? section_id) => this._section_id = section_id;	

	int? get task_id => this._task_id;
	
	set task_id(int? task_id) => this._task_id = task_id;	

	String? get status => this._status;
	
	set status(String? status) => this._status = status;	

	String? get completed_by => this._completed_by;
	
	set completed_by(String? completed_by) => this._completed_by = completed_by;	

	int? get completed_on => this._completed_on;
	
	set completed_on(int? completed_on) => this._completed_on = completed_on;	

	double? get latitude => this._latitude;
	
	set latitude(double? latitude) => this._latitude = latitude;	

	double? get longitude => this._longitude;
	
	set longitude(double? longitude) => this._longitude = longitude;	

	String? get reason => this._reason;
	
	set reason(String? reason) => this._reason = reason;	

	String? get skip_comments => this._skip_comments;
	
	set skip_comments(String? skip_comments) => this._skip_comments = skip_comments;	

	String? get comments => this._comments;
	
	set comments(String? comments) => this._comments = comments;	

	String? get p_mode => this._p_mode;
	
	set p_mode(String? p_mode) => this._p_mode = p_mode;	
	
}