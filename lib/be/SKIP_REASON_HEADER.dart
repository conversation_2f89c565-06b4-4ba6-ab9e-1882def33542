//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "SKIP_REASON".
*/	
class SKIP_REASON_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "SKIP_REASON_HEADER";
	
	// No desc available
	static const String FIELD_REASON = "REASON";

	// No desc available
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// 000 - Header, Section, Task
	static const String FIELD_CATEGORY = "CATEGORY";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    String? _reason;
    String? _description;
    int? _category;
    String? _inactive;
	
	SKIP_REASON_HEADER({
			required reason,
			description,
			category,
			inactive}) :
			_reason = reason,
			_description = description,
			_category = category,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	SKIP_REASON_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_reason = json[FIELD_REASON]; 
		_description = json[FIELD_DESCRIPTION]; 
		_category = json[FIELD_CATEGORY]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_REASON] = _reason;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_CATEGORY] = _category;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	String? get reason => this._reason;
	
	set reason(String? reason) => this._reason = reason;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	int? get category => this._category;
	
	set category(int? category) => this._category = category;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}