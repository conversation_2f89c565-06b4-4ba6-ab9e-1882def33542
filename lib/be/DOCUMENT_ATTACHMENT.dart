//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "DOCUMENT".
*/	
class DOCUMENT_ATTACHMENT extends DataStructure {
	
	static const String TABLE_NAME = "DOCUMENT_ATTACHMENT";
	
	// UID
	static const String FIELD_UID = "UID";

	// File Name
	static const String FIELD_FILE_NAME = "FILE_NAME";

	// Mime Type
	static const String FIELD_MIME_TYPE = "MIME_TYPE";

	// Download URL
	static const String FIELD_URL = "URL";

	// External or Internal URL
	static const String FIELD_EXTERNAL_URL = "EXTERNAL_URL";

	// External URL Requires Authentication
	static const String FIELD_URL_REQUIRES_AUTH = "URL_REQUIRES_AUTH";

	// Path to the file on the device
	static const String FIELD_LOCAL_PATH = "LOCAL_PATH";

	// Do not cache
	static const String FIELD_NO_CACHE = "NO_CACHE";

	// Server timestamp
	static const String FIELD_SERVER_TIMESTAMP = "SERVER_TIMESTAMP";

	// Tag 1
	static const String FIELD_TAG1 = "TAG1";

	// Tag 2
	static const String FIELD_TAG2 = "TAG2";

	// Tag 3
	static const String FIELD_TAG3 = "TAG3";

	// Tag 4
	static const String FIELD_TAG4 = "TAG4";

	// Tag 5
	static const String FIELD_TAG5 = "TAG5";

	// Status
	static const String FIELD_ATTACHMENT_STATUS = "ATTACHMENT_STATUS";

	// Auto Download Flag
	static const String FIELD_AUTO_DOWNLOAD = "AUTO_DOWNLOAD";

	// Name of the param
	static const String FIELD_PARAM = "PARAM";

	// Message from User
	static const String FIELD_MESSAGE = "MESSAGE";
	
    String? _uid;
    String? _file_name;
    String? _mime_type;
    String? _url;
    String? _external_url;
    String? _url_requires_auth;
    String? _local_path;
    String? _no_cache;
    int? _server_timestamp;
    String? _tag1;
    String? _tag2;
    String? _tag3;
    String? _tag4;
    String? _tag5;
    String? _attachment_status;
    String? _auto_download;
    String? _param;
    String? _message;
	
	DOCUMENT_ATTACHMENT({
			required uid,
			file_name,
			mime_type,
			url,
			external_url,
			url_requires_auth,
			local_path,
			no_cache,
			server_timestamp,
			tag1,
			tag2,
			tag3,
			tag4,
			tag5,
			attachment_status,
			auto_download,
			param,
			message}) :
			_uid = uid,
			_file_name = file_name,
			_mime_type = mime_type,
			_url = url,
			_external_url = external_url,
			_url_requires_auth = url_requires_auth,
			_local_path = local_path,
			_no_cache = no_cache,
			_server_timestamp = server_timestamp,
			_tag1 = tag1,
			_tag2 = tag2,
			_tag3 = tag3,
			_tag4 = tag4,
			_tag5 = tag5,
			_attachment_status = attachment_status,
			_auto_download = auto_download,
			_param = param,
			_message = message {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	DOCUMENT_ATTACHMENT.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_uid = json[FIELD_UID]; 
		_file_name = json[FIELD_FILE_NAME]; 
		_mime_type = json[FIELD_MIME_TYPE]; 
		_url = json[FIELD_URL]; 
		_external_url = json[FIELD_EXTERNAL_URL]; 
		_url_requires_auth = json[FIELD_URL_REQUIRES_AUTH]; 
		_local_path = json[FIELD_LOCAL_PATH]; 
		_no_cache = json[FIELD_NO_CACHE]; 
		_server_timestamp = json[FIELD_SERVER_TIMESTAMP]; 
		_tag1 = json[FIELD_TAG1]; 
		_tag2 = json[FIELD_TAG2]; 
		_tag3 = json[FIELD_TAG3]; 
		_tag4 = json[FIELD_TAG4]; 
		_tag5 = json[FIELD_TAG5]; 
		_attachment_status = json[FIELD_ATTACHMENT_STATUS]; 
		_auto_download = json[FIELD_AUTO_DOWNLOAD]; 
		_param = json[FIELD_PARAM]; 
		_message = json[FIELD_MESSAGE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_UID] = _uid;
		data[FIELD_FILE_NAME] = _file_name;
		data[FIELD_MIME_TYPE] = _mime_type;
		data[FIELD_URL] = _url;
		data[FIELD_EXTERNAL_URL] = _external_url;
		data[FIELD_URL_REQUIRES_AUTH] = _url_requires_auth;
		data[FIELD_LOCAL_PATH] = _local_path;
		data[FIELD_NO_CACHE] = _no_cache;
		data[FIELD_SERVER_TIMESTAMP] = _server_timestamp;
		data[FIELD_TAG1] = _tag1;
		data[FIELD_TAG2] = _tag2;
		data[FIELD_TAG3] = _tag3;
		data[FIELD_TAG4] = _tag4;
		data[FIELD_TAG5] = _tag5;
		data[FIELD_ATTACHMENT_STATUS] = _attachment_status;
		data[FIELD_AUTO_DOWNLOAD] = _auto_download;
		data[FIELD_PARAM] = _param;
		data[FIELD_MESSAGE] = _message;

		return data;
  }
  
	String? get uid => this._uid;
	
	set uid(String? uid) => this._uid = uid;	

	String? get file_name => this._file_name;
	
	set file_name(String? file_name) => this._file_name = file_name;	

	String? get mime_type => this._mime_type;
	
	set mime_type(String? mime_type) => this._mime_type = mime_type;	

	String? get url => this._url;
	
	set url(String? url) => this._url = url;	

	String? get external_url => this._external_url;
	
	set external_url(String? external_url) => this._external_url = external_url;	

	String? get url_requires_auth => this._url_requires_auth;
	
	set url_requires_auth(String? url_requires_auth) => this._url_requires_auth = url_requires_auth;	

	String? get local_path => this._local_path;
	
	set local_path(String? local_path) => this._local_path = local_path;	

	String? get no_cache => this._no_cache;
	
	set no_cache(String? no_cache) => this._no_cache = no_cache;	

	int? get server_timestamp => this._server_timestamp;
	
	set server_timestamp(int? server_timestamp) => this._server_timestamp = server_timestamp;	

	String? get tag1 => this._tag1;
	
	set tag1(String? tag1) => this._tag1 = tag1;	

	String? get tag2 => this._tag2;
	
	set tag2(String? tag2) => this._tag2 = tag2;	

	String? get tag3 => this._tag3;
	
	set tag3(String? tag3) => this._tag3 = tag3;	

	String? get tag4 => this._tag4;
	
	set tag4(String? tag4) => this._tag4 = tag4;	

	String? get tag5 => this._tag5;
	
	set tag5(String? tag5) => this._tag5 = tag5;	

	String? get attachment_status => this._attachment_status;
	
	set attachment_status(String? attachment_status) => this._attachment_status = attachment_status;	

	String? get auto_download => this._auto_download;
	
	set auto_download(String? auto_download) => this._auto_download = auto_download;	

	String? get param => this._param;
	
	set param(String? param) => this._param = param;	

	String? get message => this._message;
	
	set message(String? message) => this._message = message;	
	
}