//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "CILT_EXEC".
*/	
class CILT_EXEC_DOC extends DataStructure {
	
	static const String TABLE_NAME = "CILT_EXEC_DOC";
	
	// No desc available
	static const String FIELD_CILT_ID = "CILT_ID";

	// Unique Id of an Documnet or file
	static const String FIELD_DOC_ID = "DOC_ID";

	// No desc available
	static const String FIELD_CILT_TASK_ID = "CILT_TASK_ID";

	// No desc available
	static const String FIELD_P_MODE = "P_MODE";
	
    int? _cilt_id;
    String? _doc_id;
    int? _cilt_task_id;
    String? _p_mode;
	
	CILT_EXEC_DOC({
			required cilt_id,
			required doc_id,
			cilt_task_id,
			p_mode}) :
			_cilt_id = cilt_id,
			_doc_id = doc_id,
			_cilt_task_id = cilt_task_id,
			_p_mode = p_mode {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	CILT_EXEC_DOC.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_cilt_id = json[FIELD_CILT_ID]; 
		_doc_id = json[FIELD_DOC_ID]; 
		_cilt_task_id = json[FIELD_CILT_TASK_ID]; 
		_p_mode = json[FIELD_P_MODE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_CILT_ID] = _cilt_id;
		data[FIELD_DOC_ID] = _doc_id;
		data[FIELD_CILT_TASK_ID] = _cilt_task_id;
		data[FIELD_P_MODE] = _p_mode;

		return data;
  }
  
	int? get cilt_id => this._cilt_id;
	
	set cilt_id(int? cilt_id) => this._cilt_id = cilt_id;	

	String? get doc_id => this._doc_id;
	
	set doc_id(String? doc_id) => this._doc_id = doc_id;	

	int? get cilt_task_id => this._cilt_task_id;
	
	set cilt_task_id(int? cilt_task_id) => this._cilt_task_id = cilt_task_id;	

	String? get p_mode => this._p_mode;
	
	set p_mode(String? p_mode) => this._p_mode = p_mode;	
	
}