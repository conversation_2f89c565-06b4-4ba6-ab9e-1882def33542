//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "USER_GROUP".
*/	
class USER_GROUP_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "USER_GROUP_HEADER";
	
	// Unique Id of an Group
	static const String FIELD_GROUP_ID = "GROUP_ID";

	// Description of an Group
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// Associated Plant
	static const String FIELD_PLANT_ID = "PLANT_ID";
	
    String? _group_id;
    String? _description;
    String? _plant_id;
	
	USER_GROUP_HEADER({
			required group_id,
			description,
			plant_id}) :
			_group_id = group_id,
			_description = description,
			_plant_id = plant_id {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	USER_GROUP_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_group_id = json[FIELD_GROUP_ID]; 
		_description = json[FIELD_DESCRIPTION]; 
		_plant_id = json[FIELD_PLANT_ID]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_GROUP_ID] = _group_id;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_PLANT_ID] = _plant_id;

		return data;
  }
  
	String? get group_id => this._group_id;
	
	set group_id(String? group_id) => this._group_id = group_id;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get plant_id => this._plant_id;
	
	set plant_id(String? plant_id) => this._plant_id = plant_id;	
	
}