//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "PRIORITY".
*/	
class PRIORITY_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "PRIORITY_HEADER";
	
	// 1,2,3,4,5
	static const String FIELD_PRIORITY_CODE = "PRIORITY_CODE";

	// 1=Highest,2=High,3=Medium,4=Low,5=Lowest
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// Status of an Priority Code 1=Active/Created or 0=Inactive/Deleted 
	static const String FIELD_INACTIVE = "INACTIVE";
	
    String? _priority_code;
    String? _description;
    String? _inactive;
	
	PRIORITY_HEADER({
			required priority_code,
			description,
			inactive}) :
			_priority_code = priority_code,
			_description = description,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	PRIORITY_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_priority_code = json[FIELD_PRIORITY_CODE]; 
		_description = json[FIELD_DESCRIPTION]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_PRIORITY_CODE] = _priority_code;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	String? get priority_code => this._priority_code;
	
	set priority_code(String? priority_code) => this._priority_code = priority_code;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}