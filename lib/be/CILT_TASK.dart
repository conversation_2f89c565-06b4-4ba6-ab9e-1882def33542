//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "CILT_PLAN".
*/	
class CILT_TASK extends DataStructure {
	
	static const String TABLE_NAME = "CILT_TASK";
	
	// No desc available
	static const String FIELD_PLAN_ID = "PLAN_ID";

	// No desc available
	static const String FIELD_TASK_ID = "TASK_ID";

	// Unique Id of an Section
	static const String FIELD_SECTION_ID = "SECTION_ID";

	// No desc available
	static const String FIELD_SEQ_NO = "SEQ_NO";

	// No desc available
	static const String FIELD_CILT_CODE = "CILT_CODE";

	// No desc available
	static const String FIELD_LOCATION_ID = "LOCATION_ID";

	// No desc available
	static const String FIELD_ASSET_NO = "ASSET_NO";

	// No desc available
	static const String FIELD_SYS_COND = "SYS_COND";

	// No desc available
	static const String FIELD_ACTIVITY = "ACTIVITY";

	// No desc available
	static const String FIELD_MACHINE_PART = "MACHINE_PART";

	// No desc available
	static const String FIELD_STANDARD = "STANDARD";

	// No desc available
	static const String FIELD_METHOD = "METHOD";

	// No desc available
	static const String FIELD_TOOL = "TOOL";

	// How much duration required to execute the task 
	static const String FIELD_DURATION = "DURATION";

	// No desc available
	static const String FIELD_UOM = "UOM";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    int? _plan_id;
    int? _task_id;
    int? _section_id;
    int? _seq_no;
    int? _cilt_code;
    String? _location_id;
    int? _asset_no;
    String? _sys_cond;
    String? _activity;
    String? _machine_part;
    String? _standard;
    String? _method;
    String? _tool;
    double? _duration;
    String? _uom;
    String? _inactive;
	
	CILT_TASK({
			required plan_id,
			required task_id,
			required section_id,
			seq_no,
			cilt_code,
			location_id,
			asset_no,
			sys_cond,
			activity,
			machine_part,
			standard,
			method,
			tool,
			duration,
			uom,
			inactive}) :
			_plan_id = plan_id,
			_task_id = task_id,
			_section_id = section_id,
			_seq_no = seq_no,
			_cilt_code = cilt_code,
			_location_id = location_id,
			_asset_no = asset_no,
			_sys_cond = sys_cond,
			_activity = activity,
			_machine_part = machine_part,
			_standard = standard,
			_method = method,
			_tool = tool,
			_duration = duration,
			_uom = uom,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	CILT_TASK.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_plan_id = json[FIELD_PLAN_ID]; 
		_task_id = json[FIELD_TASK_ID]; 
		_section_id = json[FIELD_SECTION_ID]; 
		_seq_no = json[FIELD_SEQ_NO]; 
		_cilt_code = json[FIELD_CILT_CODE]; 
		_location_id = json[FIELD_LOCATION_ID]; 
		_asset_no = json[FIELD_ASSET_NO]; 
		_sys_cond = json[FIELD_SYS_COND]; 
		_activity = json[FIELD_ACTIVITY]; 
		_machine_part = json[FIELD_MACHINE_PART]; 
		_standard = json[FIELD_STANDARD]; 
		_method = json[FIELD_METHOD]; 
		_tool = json[FIELD_TOOL]; 
		_duration = json[FIELD_DURATION]; 
		_uom = json[FIELD_UOM]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_PLAN_ID] = _plan_id;
		data[FIELD_TASK_ID] = _task_id;
		data[FIELD_SECTION_ID] = _section_id;
		data[FIELD_SEQ_NO] = _seq_no;
		data[FIELD_CILT_CODE] = _cilt_code;
		data[FIELD_LOCATION_ID] = _location_id;
		data[FIELD_ASSET_NO] = _asset_no;
		data[FIELD_SYS_COND] = _sys_cond;
		data[FIELD_ACTIVITY] = _activity;
		data[FIELD_MACHINE_PART] = _machine_part;
		data[FIELD_STANDARD] = _standard;
		data[FIELD_METHOD] = _method;
		data[FIELD_TOOL] = _tool;
		data[FIELD_DURATION] = _duration;
		data[FIELD_UOM] = _uom;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	int? get plan_id => this._plan_id;
	
	set plan_id(int? plan_id) => this._plan_id = plan_id;	

	int? get task_id => this._task_id;
	
	set task_id(int? task_id) => this._task_id = task_id;	

	int? get section_id => this._section_id;
	
	set section_id(int? section_id) => this._section_id = section_id;	

	int? get seq_no => this._seq_no;
	
	set seq_no(int? seq_no) => this._seq_no = seq_no;	

	int? get cilt_code => this._cilt_code;
	
	set cilt_code(int? cilt_code) => this._cilt_code = cilt_code;	

	String? get location_id => this._location_id;
	
	set location_id(String? location_id) => this._location_id = location_id;	

	int? get asset_no => this._asset_no;
	
	set asset_no(int? asset_no) => this._asset_no = asset_no;	

	String? get sys_cond => this._sys_cond;
	
	set sys_cond(String? sys_cond) => this._sys_cond = sys_cond;	

	String? get activity => this._activity;
	
	set activity(String? activity) => this._activity = activity;	

	String? get machine_part => this._machine_part;
	
	set machine_part(String? machine_part) => this._machine_part = machine_part;	

	String? get standard => this._standard;
	
	set standard(String? standard) => this._standard = standard;	

	String? get method => this._method;
	
	set method(String? method) => this._method = method;	

	String? get tool => this._tool;
	
	set tool(String? tool) => this._tool = tool;	

	double? get duration => this._duration;
	
	set duration(double? duration) => this._duration = duration;	

	String? get uom => this._uom;
	
	set uom(String? uom) => this._uom = uom;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}