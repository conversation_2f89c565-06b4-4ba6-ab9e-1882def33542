//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "CILT_EXEC".
*/	
class CILT_EXEC_SEC extends DataStructure {
	
	static const String TABLE_NAME = "CILT_EXEC_SEC";
	
	// No desc available
	static const String FIELD_CILT_ID = "CILT_ID";

	// No desc available
	static const String FIELD_SECTION_ID = "SECTION_ID";

	// No desc available
	static const String FIELD_REASON = "REASON";

	// No desc available
	static const String FIELD_SKIP_COMMENTS = "SKIP_COMMENTS";

	// No desc available
	static const String FIELD_P_MODE = "P_MODE";
	
    int? _cilt_id;
    int? _section_id;
    String? _reason;
    String? _skip_comments;
    String? _p_mode;
	
	CILT_EXEC_SEC({
			required cilt_id,
			required section_id,
			reason,
			skip_comments,
			p_mode}) :
			_cilt_id = cilt_id,
			_section_id = section_id,
			_reason = reason,
			_skip_comments = skip_comments,
			_p_mode = p_mode {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	CILT_EXEC_SEC.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_cilt_id = json[FIELD_CILT_ID]; 
		_section_id = json[FIELD_SECTION_ID]; 
		_reason = json[FIELD_REASON]; 
		_skip_comments = json[FIELD_SKIP_COMMENTS]; 
		_p_mode = json[FIELD_P_MODE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_CILT_ID] = _cilt_id;
		data[FIELD_SECTION_ID] = _section_id;
		data[FIELD_REASON] = _reason;
		data[FIELD_SKIP_COMMENTS] = _skip_comments;
		data[FIELD_P_MODE] = _p_mode;

		return data;
  }
  
	int? get cilt_id => this._cilt_id;
	
	set cilt_id(int? cilt_id) => this._cilt_id = cilt_id;	

	int? get section_id => this._section_id;
	
	set section_id(int? section_id) => this._section_id = section_id;	

	String? get reason => this._reason;
	
	set reason(String? reason) => this._reason = reason;	

	String? get skip_comments => this._skip_comments;
	
	set skip_comments(String? skip_comments) => this._skip_comments = skip_comments;	

	String? get p_mode => this._p_mode;
	
	set p_mode(String? p_mode) => this._p_mode = p_mode;	
	
}