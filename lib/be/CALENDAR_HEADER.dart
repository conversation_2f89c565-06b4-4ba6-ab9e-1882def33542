//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "CALENDAR".
*/	
class CALENDAR_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "CALEND<PERSON>_HEADER";
	
	// No desc available
	static const String FIELD_CALENDAR_ID = "CALENDAR_ID";

	// No desc available
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// No desc available
	static const String FIELD_SUNDAY = "SUNDAY";

	// No desc available
	static const String FIELD_MONDAY = "MONDAY";

	// No desc available
	static const String FIELD_TUESDAY = "TUESDAY";

	// No desc available
	static const String FIELD_WEDNESDAY = "WEDNESDAY";

	// No desc available
	static const String FIELD_THURSDAY = "THURSDAY";

	// No desc available
	static const String FIELD_FRIDAY = "FRIDAY";

	// No desc available
	static const String FIELD_SATURDAY = "SATURDAY";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    String? _calendar_id;
    String? _description;
    String? _sunday;
    String? _monday;
    String? _tuesday;
    String? _wednesday;
    String? _thursday;
    String? _friday;
    String? _saturday;
    String? _inactive;
	
	CALENDAR_HEADER({
			required calendar_id,
			description,
			sunday,
			monday,
			tuesday,
			wednesday,
			thursday,
			friday,
			saturday,
			inactive}) :
			_calendar_id = calendar_id,
			_description = description,
			_sunday = sunday,
			_monday = monday,
			_tuesday = tuesday,
			_wednesday = wednesday,
			_thursday = thursday,
			_friday = friday,
			_saturday = saturday,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	CALENDAR_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_calendar_id = json[FIELD_CALENDAR_ID]; 
		_description = json[FIELD_DESCRIPTION]; 
		_sunday = json[FIELD_SUNDAY]; 
		_monday = json[FIELD_MONDAY]; 
		_tuesday = json[FIELD_TUESDAY]; 
		_wednesday = json[FIELD_WEDNESDAY]; 
		_thursday = json[FIELD_THURSDAY]; 
		_friday = json[FIELD_FRIDAY]; 
		_saturday = json[FIELD_SATURDAY]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_CALENDAR_ID] = _calendar_id;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_SUNDAY] = _sunday;
		data[FIELD_MONDAY] = _monday;
		data[FIELD_TUESDAY] = _tuesday;
		data[FIELD_WEDNESDAY] = _wednesday;
		data[FIELD_THURSDAY] = _thursday;
		data[FIELD_FRIDAY] = _friday;
		data[FIELD_SATURDAY] = _saturday;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	String? get calendar_id => this._calendar_id;
	
	set calendar_id(String? calendar_id) => this._calendar_id = calendar_id;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get sunday => this._sunday;
	
	set sunday(String? sunday) => this._sunday = sunday;	

	String? get monday => this._monday;
	
	set monday(String? monday) => this._monday = monday;	

	String? get tuesday => this._tuesday;
	
	set tuesday(String? tuesday) => this._tuesday = tuesday;	

	String? get wednesday => this._wednesday;
	
	set wednesday(String? wednesday) => this._wednesday = wednesday;	

	String? get thursday => this._thursday;
	
	set thursday(String? thursday) => this._thursday = thursday;	

	String? get friday => this._friday;
	
	set friday(String? friday) => this._friday = friday;	

	String? get saturday => this._saturday;
	
	set saturday(String? saturday) => this._saturday = saturday;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}