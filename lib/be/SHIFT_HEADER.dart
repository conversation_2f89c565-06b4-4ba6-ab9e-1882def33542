//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "SHIFT".
*/	
class SHIFT_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "SHIFT_HEADER";
	
	// No desc available
	static const String FIELD_PLANT_ID = "PLANT_ID";

	// No desc available
	static const String FIELD_SHIFT_CODE = "SHIFT_CODE";

	// No desc available
	static const String FIELD_SHIFT_NAME = "SHIFT_NAME";

	// Start time of an shift
	static const String FIELD_START_TIME = "START_TIME";

	// End time of an shift
	static const String FIELD_END_TIME = "END_TIME";

	// No desc available
	static const String FIELD_CREATED_BY = "CREATED_BY";

	// No desc available
	static const String FIELD_CREATED_ON = "CREATED_ON";

	// No desc available
	static const String FIELD_CHANGED_BY = "CHANGED_BY";

	// No desc available
	static const String FIELD_CHANGED_ON = "CHANGED_ON";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    String? _plant_id;
    String? _shift_code;
    String? _shift_name;
    String? _start_time;
    String? _end_time;
    String? _created_by;
    int? _created_on;
    String? _changed_by;
    int? _changed_on;
    String? _inactive;
	
	SHIFT_HEADER({
			required plant_id,
			required shift_code,
			shift_name,
			start_time,
			end_time,
			created_by,
			created_on,
			changed_by,
			changed_on,
			inactive}) :
			_plant_id = plant_id,
			_shift_code = shift_code,
			_shift_name = shift_name,
			_start_time = start_time,
			_end_time = end_time,
			_created_by = created_by,
			_created_on = created_on,
			_changed_by = changed_by,
			_changed_on = changed_on,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	SHIFT_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_plant_id = json[FIELD_PLANT_ID]; 
		_shift_code = json[FIELD_SHIFT_CODE]; 
		_shift_name = json[FIELD_SHIFT_NAME]; 
		_start_time = json[FIELD_START_TIME]; 
		_end_time = json[FIELD_END_TIME]; 
		_created_by = json[FIELD_CREATED_BY]; 
		_created_on = json[FIELD_CREATED_ON]; 
		_changed_by = json[FIELD_CHANGED_BY]; 
		_changed_on = json[FIELD_CHANGED_ON]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_PLANT_ID] = _plant_id;
		data[FIELD_SHIFT_CODE] = _shift_code;
		data[FIELD_SHIFT_NAME] = _shift_name;
		data[FIELD_START_TIME] = _start_time;
		data[FIELD_END_TIME] = _end_time;
		data[FIELD_CREATED_BY] = _created_by;
		data[FIELD_CREATED_ON] = _created_on;
		data[FIELD_CHANGED_BY] = _changed_by;
		data[FIELD_CHANGED_ON] = _changed_on;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	String? get plant_id => this._plant_id;
	
	set plant_id(String? plant_id) => this._plant_id = plant_id;	

	String? get shift_code => this._shift_code;
	
	set shift_code(String? shift_code) => this._shift_code = shift_code;	

	String? get shift_name => this._shift_name;
	
	set shift_name(String? shift_name) => this._shift_name = shift_name;	

	String? get start_time => this._start_time;
	
	set start_time(String? start_time) => this._start_time = start_time;	

	String? get end_time => this._end_time;
	
	set end_time(String? end_time) => this._end_time = end_time;	

	String? get created_by => this._created_by;
	
	set created_by(String? created_by) => this._created_by = created_by;	

	int? get created_on => this._created_on;
	
	set created_on(int? created_on) => this._created_on = created_on;	

	String? get changed_by => this._changed_by;
	
	set changed_by(String? changed_by) => this._changed_by = changed_by;	

	int? get changed_on => this._changed_on;
	
	set changed_on(int? changed_on) => this._changed_on = changed_on;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}