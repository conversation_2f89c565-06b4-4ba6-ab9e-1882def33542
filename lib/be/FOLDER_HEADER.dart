//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "FOLDER".
*/	
class FOLDER_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "FOLDER_HEADER";
	
	// No desc available
	static const String FIELD_FOLDER_ID = "FOLDER_ID";

	// No desc available
	static const String FIELD_PARENT_ID = "PARENT_ID";

	// No desc available
	static const String FIELD_NAME = "NAME";

	// No desc available
	static const String FIELD_CREATED_BY = "CREATED_BY";

	// No desc available
	static const String FIELD_CREATED_ON = "CREATED_ON";

	// No desc available
	static const String FIELD_CHANGED_BY = "CHANGED_BY";

	// No desc available
	static const String FIELD_CHANGED_ON = "CHANGED_ON";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    int? _folder_id;
    int? _parent_id;
    String? _name;
    String? _created_by;
    int? _created_on;
    String? _changed_by;
    int? _changed_on;
    String? _inactive;
	
	FOLDER_HEADER({
			required folder_id,
			parent_id,
			name,
			created_by,
			created_on,
			changed_by,
			changed_on,
			inactive}) :
			_folder_id = folder_id,
			_parent_id = parent_id,
			_name = name,
			_created_by = created_by,
			_created_on = created_on,
			_changed_by = changed_by,
			_changed_on = changed_on,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	FOLDER_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_folder_id = json[FIELD_FOLDER_ID]; 
		_parent_id = json[FIELD_PARENT_ID]; 
		_name = json[FIELD_NAME]; 
		_created_by = json[FIELD_CREATED_BY]; 
		_created_on = json[FIELD_CREATED_ON]; 
		_changed_by = json[FIELD_CHANGED_BY]; 
		_changed_on = json[FIELD_CHANGED_ON]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_FOLDER_ID] = _folder_id;
		data[FIELD_PARENT_ID] = _parent_id;
		data[FIELD_NAME] = _name;
		data[FIELD_CREATED_BY] = _created_by;
		data[FIELD_CREATED_ON] = _created_on;
		data[FIELD_CHANGED_BY] = _changed_by;
		data[FIELD_CHANGED_ON] = _changed_on;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	int? get folder_id => this._folder_id;
	
	set folder_id(int? folder_id) => this._folder_id = folder_id;	

	int? get parent_id => this._parent_id;
	
	set parent_id(int? parent_id) => this._parent_id = parent_id;	

	String? get name => this._name;
	
	set name(String? name) => this._name = name;	

	String? get created_by => this._created_by;
	
	set created_by(String? created_by) => this._created_by = created_by;	

	int? get created_on => this._created_on;
	
	set created_on(int? created_on) => this._created_on = created_on;	

	String? get changed_by => this._changed_by;
	
	set changed_by(String? changed_by) => this._changed_by = changed_by;	

	int? get changed_on => this._changed_on;
	
	set changed_on(int? changed_on) => this._changed_on = changed_on;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}