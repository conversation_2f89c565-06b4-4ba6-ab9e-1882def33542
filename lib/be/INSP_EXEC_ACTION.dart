//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "INSP_EXEC".
*/	
class INSP_EXEC_ACTION extends DataStructure {
	
	static const String TABLE_NAME = "INSP_EXEC_ACTION";
	
	// No desc available
	static const String FIELD_INSP_ID = "INSP_ID";

	// No desc available
	static const String FIELD_USER_ACTION = "USER_ACTION";

	// No desc available
	static const String FIELD_CONTEXT1 = "CONTEXT1";

	// No desc available
	static const String FIELD_CONTEXT2 = "CONTEXT2";

	// No desc available
	static const String FIELD_CONTEXT3 = "CONTEXT3";
	
    int? _insp_id;
    String? _user_action;
    String? _context1;
    String? _context2;
    String? _context3;
	
	INSP_EXEC_ACTION({
			required insp_id,
			required user_action,
			context1,
			context2,
			context3}) :
			_insp_id = insp_id,
			_user_action = user_action,
			_context1 = context1,
			_context2 = context2,
			_context3 = context3 {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	INSP_EXEC_ACTION.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_insp_id = json[FIELD_INSP_ID]; 
		_user_action = json[FIELD_USER_ACTION]; 
		_context1 = json[FIELD_CONTEXT1]; 
		_context2 = json[FIELD_CONTEXT2]; 
		_context3 = json[FIELD_CONTEXT3]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_INSP_ID] = _insp_id;
		data[FIELD_USER_ACTION] = _user_action;
		data[FIELD_CONTEXT1] = _context1;
		data[FIELD_CONTEXT2] = _context2;
		data[FIELD_CONTEXT3] = _context3;

		return data;
  }
  
	int? get insp_id => this._insp_id;
	
	set insp_id(int? insp_id) => this._insp_id = insp_id;	

	String? get user_action => this._user_action;
	
	set user_action(String? user_action) => this._user_action = user_action;	

	String? get context1 => this._context1;
	
	set context1(String? context1) => this._context1 = context1;	

	String? get context2 => this._context2;
	
	set context2(String? context2) => this._context2 = context2;	

	String? get context3 => this._context3;
	
	set context3(String? context3) => this._context3 = context3;	
	
}