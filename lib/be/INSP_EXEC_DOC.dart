//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "INSP_EXEC".
*/	
class INSP_EXEC_DOC extends DataStructure {
	
	static const String TABLE_NAME = "INSP_EXEC_DOC";
	
	// No desc available
	static const String FIELD_INSP_ID = "INSP_ID";

	// No desc available
	static const String FIELD_DOC_ID = "DOC_ID";

	// Unique Id of an Task 
	static const String FIELD_INSP_TASK_ID = "INSP_TASK_ID";

	// No desc available
	static const String FIELD_P_MODE = "P_MODE";
	
    int? _insp_id;
    String? _doc_id;
    int? _insp_task_id;
    String? _p_mode;
	
	INSP_EXEC_DOC({
			required insp_id,
			required doc_id,
			insp_task_id,
			p_mode}) :
			_insp_id = insp_id,
			_doc_id = doc_id,
			_insp_task_id = insp_task_id,
			_p_mode = p_mode {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	INSP_EXEC_DOC.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_insp_id = json[FIELD_INSP_ID]; 
		_doc_id = json[FIELD_DOC_ID]; 
		_insp_task_id = json[FIELD_INSP_TASK_ID]; 
		_p_mode = json[FIELD_P_MODE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_INSP_ID] = _insp_id;
		data[FIELD_DOC_ID] = _doc_id;
		data[FIELD_INSP_TASK_ID] = _insp_task_id;
		data[FIELD_P_MODE] = _p_mode;

		return data;
  }
  
	int? get insp_id => this._insp_id;
	
	set insp_id(int? insp_id) => this._insp_id = insp_id;	

	String? get doc_id => this._doc_id;
	
	set doc_id(String? doc_id) => this._doc_id = doc_id;	

	int? get insp_task_id => this._insp_task_id;
	
	set insp_task_id(int? insp_task_id) => this._insp_task_id = insp_task_id;	

	String? get p_mode => this._p_mode;
	
	set p_mode(String? p_mode) => this._p_mode = p_mode;	
	
}