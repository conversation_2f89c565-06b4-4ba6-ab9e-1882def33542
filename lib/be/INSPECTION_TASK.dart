//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "INSPECTION_PLAN".
*/	
class INSPECTION_TASK extends DataStructure {
	
	static const String TABLE_NAME = "INSPECTION_TASK";
	
	// No desc available
	static const String FIELD_PLAN_ID = "PLAN_ID";

	// No desc available
	static const String FIELD_TASK_ID = "TASK_ID";

	// Unique Id of an Section
	static const String FIELD_SECTION_ID = "SECTION_ID";

	// No desc available
	static const String FIELD_SEQ_NO = "SEQ_NO";

	// No desc available
	static const String FIELD_TASK_TYPE = "TASK_TYPE";

	// No desc available
	static const String FIELD_TITLE = "TITLE";

	// No desc available
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// No desc available
	static const String FIELD_LOCATION_ID = "LOCATION_ID";

	// No desc available
	static const String FIELD_ASSET_NO = "ASSET_NO";

	// No desc available
	static const String FIELD_SYS_COND = "SYS_COND";

	// No desc available
	static const String FIELD_MANDATORY = "MANDATORY";

	// No desc available
	static const String FIELD_PICKLIST_ID = "PICKLIST_ID";

	// No desc available
	static const String FIELD_LENGTH = "LENGTH";

	// No desc available
	static const String FIELD_ACCURACY = "ACCURACY";

	// No desc available
	static const String FIELD_UOM = "UOM";

	// No desc available
	static const String FIELD_UPPER_LIMIT = "UPPER_LIMIT";

	// No desc available
	static const String FIELD_LOWER_LIMIT = "LOWER_LIMIT";

	// No desc available
	static const String FIELD_STANDARD_VALUE = "STANDARD_VALUE";

	// No desc available
	static const String FIELD_UPPER_LIMIT_MSG = "UPPER_LIMIT_MSG";

	// No desc available
	static const String FIELD_LOWER_LIMIT_MSG = "LOWER_LIMIT_MSG";

	// No desc available
	static const String FIELD_KPI_ID = "KPI_ID";

	// No desc available
	static const String FIELD_DEPENDENT = "DEPENDENT";

	// No desc available
	static const String FIELD_DEP_TASK_ID = "DEP_TASK_ID";

	// No desc available
	static const String FIELD_DEP_COND_CODE = "DEP_COND_CODE";

	// No desc available
	static const String FIELD_DEP_COND_VAL = "DEP_COND_VAL";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    int? _plan_id;
    int? _task_id;
    int? _section_id;
    int? _seq_no;
    String? _task_type;
    String? _title;
    String? _description;
    String? _location_id;
    int? _asset_no;
    String? _sys_cond;
    String? _mandatory;
    String? _picklist_id;
    int? _length;
    int? _accuracy;
    String? _uom;
    double? _upper_limit;
    double? _lower_limit;
    double? _standard_value;
    String? _upper_limit_msg;
    String? _lower_limit_msg;
    int? _kpi_id;
    String? _dependent;
    int? _dep_task_id;
    String? _dep_cond_code;
    String? _dep_cond_val;
    String? _inactive;
	
	INSPECTION_TASK({
			required plan_id,
			required task_id,
			required section_id,
			seq_no,
			task_type,
			title,
			description,
			location_id,
			asset_no,
			sys_cond,
			mandatory,
			picklist_id,
			length,
			accuracy,
			uom,
			upper_limit,
			lower_limit,
			standard_value,
			upper_limit_msg,
			lower_limit_msg,
			kpi_id,
			dependent,
			dep_task_id,
			dep_cond_code,
			dep_cond_val,
			inactive}) :
			_plan_id = plan_id,
			_task_id = task_id,
			_section_id = section_id,
			_seq_no = seq_no,
			_task_type = task_type,
			_title = title,
			_description = description,
			_location_id = location_id,
			_asset_no = asset_no,
			_sys_cond = sys_cond,
			_mandatory = mandatory,
			_picklist_id = picklist_id,
			_length = length,
			_accuracy = accuracy,
			_uom = uom,
			_upper_limit = upper_limit,
			_lower_limit = lower_limit,
			_standard_value = standard_value,
			_upper_limit_msg = upper_limit_msg,
			_lower_limit_msg = lower_limit_msg,
			_kpi_id = kpi_id,
			_dependent = dependent,
			_dep_task_id = dep_task_id,
			_dep_cond_code = dep_cond_code,
			_dep_cond_val = dep_cond_val,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	INSPECTION_TASK.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_plan_id = json[FIELD_PLAN_ID]; 
		_task_id = json[FIELD_TASK_ID]; 
		_section_id = json[FIELD_SECTION_ID]; 
		_seq_no = json[FIELD_SEQ_NO]; 
		_task_type = json[FIELD_TASK_TYPE]; 
		_title = json[FIELD_TITLE]; 
		_description = json[FIELD_DESCRIPTION]; 
		_location_id = json[FIELD_LOCATION_ID]; 
		_asset_no = json[FIELD_ASSET_NO]; 
		_sys_cond = json[FIELD_SYS_COND]; 
		_mandatory = json[FIELD_MANDATORY]; 
		_picklist_id = json[FIELD_PICKLIST_ID]; 
		_length = json[FIELD_LENGTH]; 
		_accuracy = json[FIELD_ACCURACY]; 
		_uom = json[FIELD_UOM]; 
		_upper_limit = json[FIELD_UPPER_LIMIT]; 
		_lower_limit = json[FIELD_LOWER_LIMIT]; 
		_standard_value = json[FIELD_STANDARD_VALUE]; 
		_upper_limit_msg = json[FIELD_UPPER_LIMIT_MSG]; 
		_lower_limit_msg = json[FIELD_LOWER_LIMIT_MSG]; 
		_kpi_id = json[FIELD_KPI_ID]; 
		_dependent = json[FIELD_DEPENDENT]; 
		_dep_task_id = json[FIELD_DEP_TASK_ID]; 
		_dep_cond_code = json[FIELD_DEP_COND_CODE]; 
		_dep_cond_val = json[FIELD_DEP_COND_VAL]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_PLAN_ID] = _plan_id;
		data[FIELD_TASK_ID] = _task_id;
		data[FIELD_SECTION_ID] = _section_id;
		data[FIELD_SEQ_NO] = _seq_no;
		data[FIELD_TASK_TYPE] = _task_type;
		data[FIELD_TITLE] = _title;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_LOCATION_ID] = _location_id;
		data[FIELD_ASSET_NO] = _asset_no;
		data[FIELD_SYS_COND] = _sys_cond;
		data[FIELD_MANDATORY] = _mandatory;
		data[FIELD_PICKLIST_ID] = _picklist_id;
		data[FIELD_LENGTH] = _length;
		data[FIELD_ACCURACY] = _accuracy;
		data[FIELD_UOM] = _uom;
		data[FIELD_UPPER_LIMIT] = _upper_limit;
		data[FIELD_LOWER_LIMIT] = _lower_limit;
		data[FIELD_STANDARD_VALUE] = _standard_value;
		data[FIELD_UPPER_LIMIT_MSG] = _upper_limit_msg;
		data[FIELD_LOWER_LIMIT_MSG] = _lower_limit_msg;
		data[FIELD_KPI_ID] = _kpi_id;
		data[FIELD_DEPENDENT] = _dependent;
		data[FIELD_DEP_TASK_ID] = _dep_task_id;
		data[FIELD_DEP_COND_CODE] = _dep_cond_code;
		data[FIELD_DEP_COND_VAL] = _dep_cond_val;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	int? get plan_id => this._plan_id;
	
	set plan_id(int? plan_id) => this._plan_id = plan_id;	

	int? get task_id => this._task_id;
	
	set task_id(int? task_id) => this._task_id = task_id;	

	int? get section_id => this._section_id;
	
	set section_id(int? section_id) => this._section_id = section_id;	

	int? get seq_no => this._seq_no;
	
	set seq_no(int? seq_no) => this._seq_no = seq_no;	

	String? get task_type => this._task_type;
	
	set task_type(String? task_type) => this._task_type = task_type;	

	String? get title => this._title;
	
	set title(String? title) => this._title = title;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get location_id => this._location_id;
	
	set location_id(String? location_id) => this._location_id = location_id;	

	int? get asset_no => this._asset_no;
	
	set asset_no(int? asset_no) => this._asset_no = asset_no;	

	String? get sys_cond => this._sys_cond;
	
	set sys_cond(String? sys_cond) => this._sys_cond = sys_cond;	

	String? get mandatory => this._mandatory;
	
	set mandatory(String? mandatory) => this._mandatory = mandatory;	

	String? get picklist_id => this._picklist_id;
	
	set picklist_id(String? picklist_id) => this._picklist_id = picklist_id;	

	int? get length => this._length;
	
	set length(int? length) => this._length = length;	

	int? get accuracy => this._accuracy;
	
	set accuracy(int? accuracy) => this._accuracy = accuracy;	

	String? get uom => this._uom;
	
	set uom(String? uom) => this._uom = uom;	

	double? get upper_limit => this._upper_limit;
	
	set upper_limit(double? upper_limit) => this._upper_limit = upper_limit;	

	double? get lower_limit => this._lower_limit;
	
	set lower_limit(double? lower_limit) => this._lower_limit = lower_limit;	

	double? get standard_value => this._standard_value;
	
	set standard_value(double? standard_value) => this._standard_value = standard_value;	

	String? get upper_limit_msg => this._upper_limit_msg;
	
	set upper_limit_msg(String? upper_limit_msg) => this._upper_limit_msg = upper_limit_msg;	

	String? get lower_limit_msg => this._lower_limit_msg;
	
	set lower_limit_msg(String? lower_limit_msg) => this._lower_limit_msg = lower_limit_msg;	

	int? get kpi_id => this._kpi_id;
	
	set kpi_id(int? kpi_id) => this._kpi_id = kpi_id;	

	String? get dependent => this._dependent;
	
	set dependent(String? dependent) => this._dependent = dependent;	

	int? get dep_task_id => this._dep_task_id;
	
	set dep_task_id(int? dep_task_id) => this._dep_task_id = dep_task_id;	

	String? get dep_cond_code => this._dep_cond_code;
	
	set dep_cond_code(String? dep_cond_code) => this._dep_cond_code = dep_cond_code;	

	String? get dep_cond_val => this._dep_cond_val;
	
	set dep_cond_val(String? dep_cond_val) => this._dep_cond_val = dep_cond_val;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}