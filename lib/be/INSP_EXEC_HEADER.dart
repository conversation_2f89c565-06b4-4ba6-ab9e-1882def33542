//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "INSP_EXEC".
*/	
class INSP_EXEC_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "INSP_EXEC_HEADER";
	
	// No desc available
	static const String FIELD_INSP_ID = "INSP_ID";

	// No desc available
	static const String FIELD_PLAN_ID = "PLAN_ID";

	// No desc available
	static const String FIELD_PLAN_TYPE = "PLAN_TYPE";

	// No desc available
	static const String FIELD_SCHED_ID = "SCHED_ID";

	// No desc available
	static const String FIELD_PLANT_ID = "PLANT_ID";

	// No desc available
	static const String FIELD_PLANT_SEC_ID = "PLANT_SEC_ID";

	// No desc available
	static const String FIELD_SHIFT = "SHIFT";

	// No desc available
	static const String FIELD_STATUS = "STATUS";

	// No desc available
	static const String FIELD_PRIORITY = "PRIORITY";

	// No desc available
	static const String FIELD_CREATED_BY = "CREATED_BY";

	// No desc available
	static const String FIELD_CREATED_ON = "CREATED_ON";

	// No desc available
	static const String FIELD_START_ON = "START_ON";

	// No desc available
	static const String FIELD_START_AT = "START_AT";

	// No desc available
	static const String FIELD_END_AT = "END_AT";

	// No desc available
	static const String FIELD_ASSIGNED_BY = "ASSIGNED_BY";

	// No desc available
	static const String FIELD_ASSIGNED_TO = "ASSIGNED_TO";

	// No desc available
	static const String FIELD_ASSIGNED_ON = "ASSIGNED_ON";

	// No desc available
	static const String FIELD_ACCEPTED_ON = "ACCEPTED_ON";

	// No desc available
	static const String FIELD_COMPLETED_BY = "COMPLETED_BY";

	// No desc available
	static const String FIELD_COMPLETED_ON = "COMPLETED_ON";

	// No desc available
	static const String FIELD_REASON = "REASON";

	// No desc available
	static const String FIELD_SKIP_COMMENTS = "SKIP_COMMENTS";

	// No desc available
	static const String FIELD_DELAY_COMMENTS = "DELAY_COMMENTS";

	// No desc available
	static const String FIELD_COMMENTS = "COMMENTS";

	// No desc available
	static const String FIELD_WORK_DURATION = "WORK_DURATION";

	// No desc available
	static const String FIELD_P_MODE = "P_MODE";
	
    int? _insp_id;
    int? _plan_id;
    String? _plan_type;
    int? _sched_id;
    String? _plant_id;
    String? _plant_sec_id;
    String? _shift;
    String? _status;
    String? _priority;
    String? _created_by;
    int? _created_on;
    int? _start_on;
    int? _start_at;
    int? _end_at;
    String? _assigned_by;
    String? _assigned_to;
    int? _assigned_on;
    int? _accepted_on;
    String? _completed_by;
    int? _completed_on;
    String? _reason;
    String? _skip_comments;
    String? _delay_comments;
    String? _comments;
    int? _work_duration;
    String? _p_mode;
	
	INSP_EXEC_HEADER({
			required insp_id,
			plan_id,
			plan_type,
			sched_id,
			plant_id,
			plant_sec_id,
			shift,
			status,
			priority,
			created_by,
			created_on,
			start_on,
			start_at,
			end_at,
			assigned_by,
			assigned_to,
			assigned_on,
			accepted_on,
			completed_by,
			completed_on,
			reason,
			skip_comments,
			delay_comments,
			comments,
			work_duration,
			p_mode}) :
			_insp_id = insp_id,
			_plan_id = plan_id,
			_plan_type = plan_type,
			_sched_id = sched_id,
			_plant_id = plant_id,
			_plant_sec_id = plant_sec_id,
			_shift = shift,
			_status = status,
			_priority = priority,
			_created_by = created_by,
			_created_on = created_on,
			_start_on = start_on,
			_start_at = start_at,
			_end_at = end_at,
			_assigned_by = assigned_by,
			_assigned_to = assigned_to,
			_assigned_on = assigned_on,
			_accepted_on = accepted_on,
			_completed_by = completed_by,
			_completed_on = completed_on,
			_reason = reason,
			_skip_comments = skip_comments,
			_delay_comments = delay_comments,
			_comments = comments,
			_work_duration = work_duration,
			_p_mode = p_mode {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	INSP_EXEC_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_insp_id = json[FIELD_INSP_ID]; 
		_plan_id = json[FIELD_PLAN_ID]; 
		_plan_type = json[FIELD_PLAN_TYPE]; 
		_sched_id = json[FIELD_SCHED_ID]; 
		_plant_id = json[FIELD_PLANT_ID]; 
		_plant_sec_id = json[FIELD_PLANT_SEC_ID]; 
		_shift = json[FIELD_SHIFT]; 
		_status = json[FIELD_STATUS]; 
		_priority = json[FIELD_PRIORITY]; 
		_created_by = json[FIELD_CREATED_BY]; 
		_created_on = json[FIELD_CREATED_ON]; 
		_start_on = json[FIELD_START_ON]; 
		_start_at = json[FIELD_START_AT]; 
		_end_at = json[FIELD_END_AT]; 
		_assigned_by = json[FIELD_ASSIGNED_BY]; 
		_assigned_to = json[FIELD_ASSIGNED_TO]; 
		_assigned_on = json[FIELD_ASSIGNED_ON]; 
		_accepted_on = json[FIELD_ACCEPTED_ON]; 
		_completed_by = json[FIELD_COMPLETED_BY]; 
		_completed_on = json[FIELD_COMPLETED_ON]; 
		_reason = json[FIELD_REASON]; 
		_skip_comments = json[FIELD_SKIP_COMMENTS]; 
		_delay_comments = json[FIELD_DELAY_COMMENTS]; 
		_comments = json[FIELD_COMMENTS]; 
		_work_duration = json[FIELD_WORK_DURATION]; 
		_p_mode = json[FIELD_P_MODE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_INSP_ID] = _insp_id;
		data[FIELD_PLAN_ID] = _plan_id;
		data[FIELD_PLAN_TYPE] = _plan_type;
		data[FIELD_SCHED_ID] = _sched_id;
		data[FIELD_PLANT_ID] = _plant_id;
		data[FIELD_PLANT_SEC_ID] = _plant_sec_id;
		data[FIELD_SHIFT] = _shift;
		data[FIELD_STATUS] = _status;
		data[FIELD_PRIORITY] = _priority;
		data[FIELD_CREATED_BY] = _created_by;
		data[FIELD_CREATED_ON] = _created_on;
		data[FIELD_START_ON] = _start_on;
		data[FIELD_START_AT] = _start_at;
		data[FIELD_END_AT] = _end_at;
		data[FIELD_ASSIGNED_BY] = _assigned_by;
		data[FIELD_ASSIGNED_TO] = _assigned_to;
		data[FIELD_ASSIGNED_ON] = _assigned_on;
		data[FIELD_ACCEPTED_ON] = _accepted_on;
		data[FIELD_COMPLETED_BY] = _completed_by;
		data[FIELD_COMPLETED_ON] = _completed_on;
		data[FIELD_REASON] = _reason;
		data[FIELD_SKIP_COMMENTS] = _skip_comments;
		data[FIELD_DELAY_COMMENTS] = _delay_comments;
		data[FIELD_COMMENTS] = _comments;
		data[FIELD_WORK_DURATION] = _work_duration;
		data[FIELD_P_MODE] = _p_mode;

		return data;
  }
  
	int? get insp_id => this._insp_id;
	
	set insp_id(int? insp_id) => this._insp_id = insp_id;	

	int? get plan_id => this._plan_id;
	
	set plan_id(int? plan_id) => this._plan_id = plan_id;	

	String? get plan_type => this._plan_type;
	
	set plan_type(String? plan_type) => this._plan_type = plan_type;	

	int? get sched_id => this._sched_id;
	
	set sched_id(int? sched_id) => this._sched_id = sched_id;	

	String? get plant_id => this._plant_id;
	
	set plant_id(String? plant_id) => this._plant_id = plant_id;	

	String? get plant_sec_id => this._plant_sec_id;
	
	set plant_sec_id(String? plant_sec_id) => this._plant_sec_id = plant_sec_id;	

	String? get shift => this._shift;
	
	set shift(String? shift) => this._shift = shift;	

	String? get status => this._status;
	
	set status(String? status) => this._status = status;	

	String? get priority => this._priority;
	
	set priority(String? priority) => this._priority = priority;	

	String? get created_by => this._created_by;
	
	set created_by(String? created_by) => this._created_by = created_by;	

	int? get created_on => this._created_on;
	
	set created_on(int? created_on) => this._created_on = created_on;	

	int? get start_on => this._start_on;
	
	set start_on(int? start_on) => this._start_on = start_on;	

	int? get start_at => this._start_at;
	
	set start_at(int? start_at) => this._start_at = start_at;	

	int? get end_at => this._end_at;
	
	set end_at(int? end_at) => this._end_at = end_at;	

	String? get assigned_by => this._assigned_by;
	
	set assigned_by(String? assigned_by) => this._assigned_by = assigned_by;	

	String? get assigned_to => this._assigned_to;
	
	set assigned_to(String? assigned_to) => this._assigned_to = assigned_to;	

	int? get assigned_on => this._assigned_on;
	
	set assigned_on(int? assigned_on) => this._assigned_on = assigned_on;	

	int? get accepted_on => this._accepted_on;
	
	set accepted_on(int? accepted_on) => this._accepted_on = accepted_on;	

	String? get completed_by => this._completed_by;
	
	set completed_by(String? completed_by) => this._completed_by = completed_by;	

	int? get completed_on => this._completed_on;
	
	set completed_on(int? completed_on) => this._completed_on = completed_on;	

	String? get reason => this._reason;
	
	set reason(String? reason) => this._reason = reason;	

	String? get skip_comments => this._skip_comments;
	
	set skip_comments(String? skip_comments) => this._skip_comments = skip_comments;	

	String? get delay_comments => this._delay_comments;
	
	set delay_comments(String? delay_comments) => this._delay_comments = delay_comments;	

	String? get comments => this._comments;
	
	set comments(String? comments) => this._comments = comments;	

	int? get work_duration => this._work_duration;
	
	set work_duration(int? work_duration) => this._work_duration = work_duration;	

	String? get p_mode => this._p_mode;
	
	set p_mode(String? p_mode) => this._p_mode = p_mode;	
	
}