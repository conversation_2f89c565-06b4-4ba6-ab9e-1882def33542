//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "JOB".
*/	
class JOB_ACTION extends DataStructure {
	
	static const String TABLE_NAME = "JOB_ACTION";
	
	// Unique Id of an Job
	static const String FIELD_JOB_ID = "JOB_ID";

	// No desc available
	static const String FIELD_USER_ACTION = "USER_ACTION";
	
    int? _job_id;
    String? _user_action;
	
	JOB_ACTION({
			required job_id,
			user_action}) :
			_job_id = job_id,
			_user_action = user_action {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	JOB_ACTION.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_job_id = json[FIELD_JOB_ID]; 
		_user_action = json[FIELD_USER_ACTION]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_JOB_ID] = _job_id;
		data[FIELD_USER_ACTION] = _user_action;

		return data;
  }
  
	int? get job_id => this._job_id;
	
	set job_id(int? job_id) => this._job_id = job_id;	

	String? get user_action => this._user_action;
	
	set user_action(String? user_action) => this._user_action = user_action;	
	
}