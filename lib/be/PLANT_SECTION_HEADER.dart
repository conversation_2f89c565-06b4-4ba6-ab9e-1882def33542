//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "PLANT_SECTION".
*/	
class PLANT_SECTION_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "PLANT_SECTION_HEADER";
	
	// No desc available
	static const String FIELD_PLANT_ID = "PLANT_ID";

	// No desc available
	static const String FIELD_SECTION_ID = "SECTION_ID";

	// No desc available
	static const String FIELD_DESCRIPTION = "DESCRIPTION";
	
    String? _plant_id;
    String? _section_id;
    String? _description;
	
	PLANT_SECTION_HEADER({
			required plant_id,
			required section_id,
			description}) :
			_plant_id = plant_id,
			_section_id = section_id,
			_description = description {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	PLANT_SECTION_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_plant_id = json[FIELD_PLANT_ID]; 
		_section_id = json[FIELD_SECTION_ID]; 
		_description = json[FIELD_DESCRIPTION]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_PLANT_ID] = _plant_id;
		data[FIELD_SECTION_ID] = _section_id;
		data[FIELD_DESCRIPTION] = _description;

		return data;
  }
  
	String? get plant_id => this._plant_id;
	
	set plant_id(String? plant_id) => this._plant_id = plant_id;	

	String? get section_id => this._section_id;
	
	set section_id(String? section_id) => this._section_id = section_id;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	
	
}