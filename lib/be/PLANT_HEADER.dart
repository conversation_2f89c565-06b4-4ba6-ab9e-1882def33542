//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "PLANT".
*/	
class PLANT_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "PLANT_HEADER";
	
	// No desc available
	static const String FIELD_PLANT_ID = "PLANT_ID";

	// No desc available
	static const String FIELD_PLANT_NAME = "PLANT_NAME";

	// No desc available
	static const String FIELD_ADDRESS = "ADDRESS";

	// No desc available
	static const String FIELD_CITY = "CITY";

	// No desc available
	static const String FIELD_STATE = "STATE";

	// No desc available
	static const String FIELD_PINCODE = "PINCODE";

	// No desc available
	static const String FIELD_COUNTRY = "COUNTRY";

	// No desc available
	static const String FIELD_LATITUDE = "LATITUDE";

	// No desc available
	static const String FIELD_LONGITUDE = "LONGITUDE";

	// No desc available
	static const String FIELD_STRUCTURE = "STRUCTURE";

	// No desc available
	static const String FIELD_CALENDAR_ID = "CALENDAR_ID";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    String? _plant_id;
    String? _plant_name;
    String? _address;
    String? _city;
    String? _state;
    String? _pincode;
    String? _country;
    double? _latitude;
    double? _longitude;
    String? _structure;
    String? _calendar_id;
    String? _inactive;
	
	PLANT_HEADER({
			required plant_id,
			plant_name,
			address,
			city,
			state,
			pincode,
			country,
			latitude,
			longitude,
			structure,
			calendar_id,
			inactive}) :
			_plant_id = plant_id,
			_plant_name = plant_name,
			_address = address,
			_city = city,
			_state = state,
			_pincode = pincode,
			_country = country,
			_latitude = latitude,
			_longitude = longitude,
			_structure = structure,
			_calendar_id = calendar_id,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	PLANT_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_plant_id = json[FIELD_PLANT_ID]; 
		_plant_name = json[FIELD_PLANT_NAME]; 
		_address = json[FIELD_ADDRESS]; 
		_city = json[FIELD_CITY]; 
		_state = json[FIELD_STATE]; 
		_pincode = json[FIELD_PINCODE]; 
		_country = json[FIELD_COUNTRY]; 
		_latitude = json[FIELD_LATITUDE]; 
		_longitude = json[FIELD_LONGITUDE]; 
		_structure = json[FIELD_STRUCTURE]; 
		_calendar_id = json[FIELD_CALENDAR_ID]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_PLANT_ID] = _plant_id;
		data[FIELD_PLANT_NAME] = _plant_name;
		data[FIELD_ADDRESS] = _address;
		data[FIELD_CITY] = _city;
		data[FIELD_STATE] = _state;
		data[FIELD_PINCODE] = _pincode;
		data[FIELD_COUNTRY] = _country;
		data[FIELD_LATITUDE] = _latitude;
		data[FIELD_LONGITUDE] = _longitude;
		data[FIELD_STRUCTURE] = _structure;
		data[FIELD_CALENDAR_ID] = _calendar_id;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	String? get plant_id => this._plant_id;
	
	set plant_id(String? plant_id) => this._plant_id = plant_id;	

	String? get plant_name => this._plant_name;
	
	set plant_name(String? plant_name) => this._plant_name = plant_name;	

	String? get address => this._address;
	
	set address(String? address) => this._address = address;	

	String? get city => this._city;
	
	set city(String? city) => this._city = city;	

	String? get state => this._state;
	
	set state(String? state) => this._state = state;	

	String? get pincode => this._pincode;
	
	set pincode(String? pincode) => this._pincode = pincode;	

	String? get country => this._country;
	
	set country(String? country) => this._country = country;	

	double? get latitude => this._latitude;
	
	set latitude(double? latitude) => this._latitude = latitude;	

	double? get longitude => this._longitude;
	
	set longitude(double? longitude) => this._longitude = longitude;	

	String? get structure => this._structure;
	
	set structure(String? structure) => this._structure = structure;	

	String? get calendar_id => this._calendar_id;
	
	set calendar_id(String? calendar_id) => this._calendar_id = calendar_id;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}