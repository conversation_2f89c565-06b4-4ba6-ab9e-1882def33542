//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "STATE".
*/	
class STATE_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "STATE_HEADER";
	
	// No desc available
	static const String FIELD_COUNTRY_CODE = "COUNTRY_CODE";

	// No desc available
	static const String FIELD_STATE_CODE = "STATE_CODE";

	// No desc available
	static const String FIELD_DESCRIPTION = "DESCRIPTION";
	
    String? _country_code;
    String? _state_code;
    String? _description;
	
	STATE_HEADER({
			required country_code,
			required state_code,
			description}) :
			_country_code = country_code,
			_state_code = state_code,
			_description = description {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	STATE_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_country_code = json[FIELD_COUNTRY_CODE]; 
		_state_code = json[FIELD_STATE_CODE]; 
		_description = json[FIELD_DESCRIPTION]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_COUNTRY_CODE] = _country_code;
		data[FIELD_STATE_CODE] = _state_code;
		data[FIELD_DESCRIPTION] = _description;

		return data;
  }
  
	String? get country_code => this._country_code;
	
	set country_code(String? country_code) => this._country_code = country_code;	

	String? get state_code => this._state_code;
	
	set state_code(String? state_code) => this._state_code = state_code;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	
	
}