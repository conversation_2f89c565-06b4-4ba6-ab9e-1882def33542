//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "JOB".
*/	
class JOB_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "JOB_HEADER";
	
	// Unique Id of an Job
	static const String FIELD_JOB_ID = "JOB_ID";

	// No desc available
	static const String FIELD_JOB_TYPE = "JOB_TYPE";

	// Priority of an Job 1=Highest , 2=High , 3=Medium , 4=Low , 5=Lowest
	static const String FIELD_PRIORITY = "PRIORITY";

	// Description of an Job
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// Associated Fault Id 
	static const String FIELD_FAULT_ID = "FAULT_ID";

	// No desc available
	static const String FIELD_PLANT_ID = "PLANT_ID";

	// Associated Funcational Location Id
	static const String FIELD_LOCATION_ID = "LOCATION_ID";

	// Associated Equipment Number 
	static const String FIELD_ASSET_NO = "ASSET_NO";

	// Status of an Job Open , Inprogress , Closed 
	static const String FIELD_STATUS = "STATUS";

	// Job Created By User Name/ID
	static const String FIELD_CREATED_BY = "CREATED_BY";

	// Job Created Date
	static const String FIELD_CREATED_ON = "CREATED_ON";

	// No desc available
	static const String FIELD_CHANGED_BY = "CHANGED_BY";

	// No desc available
	static const String FIELD_CHANGED_ON = "CHANGED_ON";

	// No desc available
	static const String FIELD_ASSIGNED_BY = "ASSIGNED_BY";

	// Job Assigned to 
	static const String FIELD_ASSIGNED_TO = "ASSIGNED_TO";

	// No desc available
	static const String FIELD_ASSIGNED_ON = "ASSIGNED_ON";

	// No desc available
	static const String FIELD_START_DATE = "START_DATE";

	// No desc available
	static const String FIELD_END_DATE = "END_DATE";

	// No desc available
	static const String FIELD_COMPLETED_ON = "COMPLETED_ON";

	// ERP Reference Number 
	static const String FIELD_EXTERNAL_ID = "EXTERNAL_ID";

	// ERP Source 
	static const String FIELD_EXT_SOURCE = "EXT_SOURCE";

	// Detailed Description 
	static const String FIELD_DETAILS = "DETAILS";

	// No desc available
	static const String FIELD_P_MODE = "P_MODE";
	
    int? _job_id;
    String? _job_type;
    String? _priority;
    String? _description;
    int? _fault_id;
    String? _plant_id;
    String? _location_id;
    int? _asset_no;
    String? _status;
    String? _created_by;
    int? _created_on;
    String? _changed_by;
    int? _changed_on;
    String? _assigned_by;
    String? _assigned_to;
    int? _assigned_on;
    int? _start_date;
    int? _end_date;
    int? _completed_on;
    String? _external_id;
    String? _ext_source;
    String? _details;
    String? _p_mode;
	
	JOB_HEADER({
			required job_id,
			job_type,
			priority,
			description,
			fault_id,
			plant_id,
			location_id,
			asset_no,
			status,
			created_by,
			created_on,
			changed_by,
			changed_on,
			assigned_by,
			assigned_to,
			assigned_on,
			start_date,
			end_date,
			completed_on,
			external_id,
			ext_source,
			details,
			p_mode}) :
			_job_id = job_id,
			_job_type = job_type,
			_priority = priority,
			_description = description,
			_fault_id = fault_id,
			_plant_id = plant_id,
			_location_id = location_id,
			_asset_no = asset_no,
			_status = status,
			_created_by = created_by,
			_created_on = created_on,
			_changed_by = changed_by,
			_changed_on = changed_on,
			_assigned_by = assigned_by,
			_assigned_to = assigned_to,
			_assigned_on = assigned_on,
			_start_date = start_date,
			_end_date = end_date,
			_completed_on = completed_on,
			_external_id = external_id,
			_ext_source = ext_source,
			_details = details,
			_p_mode = p_mode {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	JOB_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_job_id = json[FIELD_JOB_ID]; 
		_job_type = json[FIELD_JOB_TYPE]; 
		_priority = json[FIELD_PRIORITY]; 
		_description = json[FIELD_DESCRIPTION]; 
		_fault_id = json[FIELD_FAULT_ID]; 
		_plant_id = json[FIELD_PLANT_ID]; 
		_location_id = json[FIELD_LOCATION_ID]; 
		_asset_no = json[FIELD_ASSET_NO]; 
		_status = json[FIELD_STATUS]; 
		_created_by = json[FIELD_CREATED_BY]; 
		_created_on = json[FIELD_CREATED_ON]; 
		_changed_by = json[FIELD_CHANGED_BY]; 
		_changed_on = json[FIELD_CHANGED_ON]; 
		_assigned_by = json[FIELD_ASSIGNED_BY]; 
		_assigned_to = json[FIELD_ASSIGNED_TO]; 
		_assigned_on = json[FIELD_ASSIGNED_ON]; 
		_start_date = json[FIELD_START_DATE]; 
		_end_date = json[FIELD_END_DATE]; 
		_completed_on = json[FIELD_COMPLETED_ON]; 
		_external_id = json[FIELD_EXTERNAL_ID]; 
		_ext_source = json[FIELD_EXT_SOURCE]; 
		_details = json[FIELD_DETAILS]; 
		_p_mode = json[FIELD_P_MODE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_JOB_ID] = _job_id;
		data[FIELD_JOB_TYPE] = _job_type;
		data[FIELD_PRIORITY] = _priority;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_FAULT_ID] = _fault_id;
		data[FIELD_PLANT_ID] = _plant_id;
		data[FIELD_LOCATION_ID] = _location_id;
		data[FIELD_ASSET_NO] = _asset_no;
		data[FIELD_STATUS] = _status;
		data[FIELD_CREATED_BY] = _created_by;
		data[FIELD_CREATED_ON] = _created_on;
		data[FIELD_CHANGED_BY] = _changed_by;
		data[FIELD_CHANGED_ON] = _changed_on;
		data[FIELD_ASSIGNED_BY] = _assigned_by;
		data[FIELD_ASSIGNED_TO] = _assigned_to;
		data[FIELD_ASSIGNED_ON] = _assigned_on;
		data[FIELD_START_DATE] = _start_date;
		data[FIELD_END_DATE] = _end_date;
		data[FIELD_COMPLETED_ON] = _completed_on;
		data[FIELD_EXTERNAL_ID] = _external_id;
		data[FIELD_EXT_SOURCE] = _ext_source;
		data[FIELD_DETAILS] = _details;
		data[FIELD_P_MODE] = _p_mode;

		return data;
  }
  
	int? get job_id => this._job_id;
	
	set job_id(int? job_id) => this._job_id = job_id;	

	String? get job_type => this._job_type;
	
	set job_type(String? job_type) => this._job_type = job_type;	

	String? get priority => this._priority;
	
	set priority(String? priority) => this._priority = priority;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	int? get fault_id => this._fault_id;
	
	set fault_id(int? fault_id) => this._fault_id = fault_id;	

	String? get plant_id => this._plant_id;
	
	set plant_id(String? plant_id) => this._plant_id = plant_id;	

	String? get location_id => this._location_id;
	
	set location_id(String? location_id) => this._location_id = location_id;	

	int? get asset_no => this._asset_no;
	
	set asset_no(int? asset_no) => this._asset_no = asset_no;	

	String? get status => this._status;
	
	set status(String? status) => this._status = status;	

	String? get created_by => this._created_by;
	
	set created_by(String? created_by) => this._created_by = created_by;	

	int? get created_on => this._created_on;
	
	set created_on(int? created_on) => this._created_on = created_on;	

	String? get changed_by => this._changed_by;
	
	set changed_by(String? changed_by) => this._changed_by = changed_by;	

	int? get changed_on => this._changed_on;
	
	set changed_on(int? changed_on) => this._changed_on = changed_on;	

	String? get assigned_by => this._assigned_by;
	
	set assigned_by(String? assigned_by) => this._assigned_by = assigned_by;	

	String? get assigned_to => this._assigned_to;
	
	set assigned_to(String? assigned_to) => this._assigned_to = assigned_to;	

	int? get assigned_on => this._assigned_on;
	
	set assigned_on(int? assigned_on) => this._assigned_on = assigned_on;	

	int? get start_date => this._start_date;
	
	set start_date(int? start_date) => this._start_date = start_date;	

	int? get end_date => this._end_date;
	
	set end_date(int? end_date) => this._end_date = end_date;	

	int? get completed_on => this._completed_on;
	
	set completed_on(int? completed_on) => this._completed_on = completed_on;	

	String? get external_id => this._external_id;
	
	set external_id(String? external_id) => this._external_id = external_id;	

	String? get ext_source => this._ext_source;
	
	set ext_source(String? ext_source) => this._ext_source = ext_source;	

	String? get details => this._details;
	
	set details(String? details) => this._details = details;	

	String? get p_mode => this._p_mode;
	
	set p_mode(String? p_mode) => this._p_mode = p_mode;	
	
}