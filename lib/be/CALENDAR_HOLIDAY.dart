//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "CALENDAR".
*/	
class CALENDAR_HOLIDAY extends DataStructure {
	
	static const String TABLE_NAME = "CALENDAR_HOLIDAY";
	
	// No desc available
	static const String FIELD_CALENDAR_ID = "CALENDAR_ID";

	// No desc available
	static const String FIELD_YEAR = "YEAR";

	// Date of an calendar
	static const String FIELD_DATE = "DATE";

	// No desc available
	static const String FIELD_DESCRIPTION = "DESCRIPTION";
	
    String? _calendar_id;
    int? _year;
    String? _date;
    String? _description;
	
	CALENDAR_HOLIDAY({
			required calendar_id,
			required year,
			required date,
			description}) :
			_calendar_id = calendar_id,
			_year = year,
			_date = date,
			_description = description {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	CALENDAR_HOLIDAY.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_calendar_id = json[FIELD_CALENDAR_ID]; 
		_year = json[FIELD_YEAR]; 
		_date = json[FIELD_DATE]; 
		_description = json[FIELD_DESCRIPTION]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_CALENDAR_ID] = _calendar_id;
		data[FIELD_YEAR] = _year;
		data[FIELD_DATE] = _date;
		data[FIELD_DESCRIPTION] = _description;

		return data;
  }
  
	String? get calendar_id => this._calendar_id;
	
	set calendar_id(String? calendar_id) => this._calendar_id = calendar_id;	

	int? get year => this._year;
	
	set year(int? year) => this._year = year;	

	String? get date => this._date;
	
	set date(String? date) => this._date = date;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	
	
}