//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "JOB".
*/	
class JOB_DOCUMENT extends DataStructure {
	
	static const String TABLE_NAME = "JOB_DOCUMENT";
	
	// Unique Id of an Job
	static const String FIELD_JOB_ID = "JOB_ID";

	// Id of an Document
	static const String FIELD_DOC_ID = "DOC_ID";

	// No desc available
	static const String FIELD_P_MODE = "P_MODE";
	
    int? _job_id;
    String? _doc_id;
    String? _p_mode;
	
	JOB_DOCUMENT({
			required job_id,
			required doc_id,
			p_mode}) :
			_job_id = job_id,
			_doc_id = doc_id,
			_p_mode = p_mode {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	JOB_DOCUMENT.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_job_id = json[FIELD_JOB_ID]; 
		_doc_id = json[FIELD_DOC_ID]; 
		_p_mode = json[FIELD_P_MODE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_JOB_ID] = _job_id;
		data[FIELD_DOC_ID] = _doc_id;
		data[FIELD_P_MODE] = _p_mode;

		return data;
  }
  
	int? get job_id => this._job_id;
	
	set job_id(int? job_id) => this._job_id = job_id;	

	String? get doc_id => this._doc_id;
	
	set doc_id(String? doc_id) => this._doc_id = doc_id;	

	String? get p_mode => this._p_mode;
	
	set p_mode(String? p_mode) => this._p_mode = p_mode;	
	
}