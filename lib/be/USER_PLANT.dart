//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "USER".
*/	
class USER_PLANT extends DataStructure {
	
	static const String TABLE_NAME = "USER_PLANT";
	
	// No desc available
	static const String FIELD_USER_ID = "USER_ID";

	// No desc available
	static const String FIELD_PLANT_ID = "PLANT_ID";
	
    String? _user_id;
    String? _plant_id;
	
	USER_PLANT({
			required user_id,
			required plant_id}) :
			_user_id = user_id,
			_plant_id = plant_id {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	USER_PLANT.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_user_id = json[FIELD_USER_ID]; 
		_plant_id = json[FIELD_PLANT_ID]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_USER_ID] = _user_id;
		data[FIELD_PLANT_ID] = _plant_id;

		return data;
  }
  
	String? get user_id => this._user_id;
	
	set user_id(String? user_id) => this._user_id = user_id;	

	String? get plant_id => this._plant_id;
	
	set plant_id(String? plant_id) => this._plant_id = plant_id;	
	
}