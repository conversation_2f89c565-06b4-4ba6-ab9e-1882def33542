//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "INSPECTION_PLAN".
*/	
class INSPECTION_PLAN_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "INSPECTION_PLAN_HEADER";
	
	// No desc available
	static const String FIELD_PLAN_ID = "PLAN_ID";

	// No desc available
	static const String FIELD_PLAN_TYPE = "PLAN_TYPE";

	// No desc available
	static const String FIELD_PLANT_ID = "PLANT_ID";

	// No desc available
	static const String FIELD_PLANT_SEC_ID = "PLANT_SEC_ID";

	// No desc available
	static const String FIELD_TITLE = "TITLE";

	// No desc available
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// L - Location A - Asset
	static const String FIELD_REF_OBJECT = "REF_OBJECT";

	// No desc available
	static const String FIELD_LOCATION_ID = "LOCATION_ID";

	// No desc available
	static const String FIELD_ASSET_NO = "ASSET_NO";

	// No desc available
	static const String FIELD_CREATED_BY = "CREATED_BY";

	// No desc available
	static const String FIELD_CREATED_ON = "CREATED_ON";

	// No desc available
	static const String FIELD_CHANGED_BY = "CHANGED_BY";

	// No desc available
	static const String FIELD_CHANGED_ON = "CHANGED_ON";

	// No desc available
	static const String FIELD_STATUS = "STATUS";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    int? _plan_id;
    String? _plan_type;
    String? _plant_id;
    String? _plant_sec_id;
    String? _title;
    String? _description;
    String? _ref_object;
    String? _location_id;
    int? _asset_no;
    String? _created_by;
    int? _created_on;
    String? _changed_by;
    int? _changed_on;
    String? _status;
    String? _inactive;
	
	INSPECTION_PLAN_HEADER({
			required plan_id,
			plan_type,
			plant_id,
			plant_sec_id,
			title,
			description,
			ref_object,
			location_id,
			asset_no,
			created_by,
			created_on,
			changed_by,
			changed_on,
			status,
			inactive}) :
			_plan_id = plan_id,
			_plan_type = plan_type,
			_plant_id = plant_id,
			_plant_sec_id = plant_sec_id,
			_title = title,
			_description = description,
			_ref_object = ref_object,
			_location_id = location_id,
			_asset_no = asset_no,
			_created_by = created_by,
			_created_on = created_on,
			_changed_by = changed_by,
			_changed_on = changed_on,
			_status = status,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	INSPECTION_PLAN_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_plan_id = json[FIELD_PLAN_ID]; 
		_plan_type = json[FIELD_PLAN_TYPE]; 
		_plant_id = json[FIELD_PLANT_ID]; 
		_plant_sec_id = json[FIELD_PLANT_SEC_ID]; 
		_title = json[FIELD_TITLE]; 
		_description = json[FIELD_DESCRIPTION]; 
		_ref_object = json[FIELD_REF_OBJECT]; 
		_location_id = json[FIELD_LOCATION_ID]; 
		_asset_no = json[FIELD_ASSET_NO]; 
		_created_by = json[FIELD_CREATED_BY]; 
		_created_on = json[FIELD_CREATED_ON]; 
		_changed_by = json[FIELD_CHANGED_BY]; 
		_changed_on = json[FIELD_CHANGED_ON]; 
		_status = json[FIELD_STATUS]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_PLAN_ID] = _plan_id;
		data[FIELD_PLAN_TYPE] = _plan_type;
		data[FIELD_PLANT_ID] = _plant_id;
		data[FIELD_PLANT_SEC_ID] = _plant_sec_id;
		data[FIELD_TITLE] = _title;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_REF_OBJECT] = _ref_object;
		data[FIELD_LOCATION_ID] = _location_id;
		data[FIELD_ASSET_NO] = _asset_no;
		data[FIELD_CREATED_BY] = _created_by;
		data[FIELD_CREATED_ON] = _created_on;
		data[FIELD_CHANGED_BY] = _changed_by;
		data[FIELD_CHANGED_ON] = _changed_on;
		data[FIELD_STATUS] = _status;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	int? get plan_id => this._plan_id;
	
	set plan_id(int? plan_id) => this._plan_id = plan_id;	

	String? get plan_type => this._plan_type;
	
	set plan_type(String? plan_type) => this._plan_type = plan_type;	

	String? get plant_id => this._plant_id;
	
	set plant_id(String? plant_id) => this._plant_id = plant_id;	

	String? get plant_sec_id => this._plant_sec_id;
	
	set plant_sec_id(String? plant_sec_id) => this._plant_sec_id = plant_sec_id;	

	String? get title => this._title;
	
	set title(String? title) => this._title = title;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get ref_object => this._ref_object;
	
	set ref_object(String? ref_object) => this._ref_object = ref_object;	

	String? get location_id => this._location_id;
	
	set location_id(String? location_id) => this._location_id = location_id;	

	int? get asset_no => this._asset_no;
	
	set asset_no(int? asset_no) => this._asset_no = asset_no;	

	String? get created_by => this._created_by;
	
	set created_by(String? created_by) => this._created_by = created_by;	

	int? get created_on => this._created_on;
	
	set created_on(int? created_on) => this._created_on = created_on;	

	String? get changed_by => this._changed_by;
	
	set changed_by(String? changed_by) => this._changed_by = changed_by;	

	int? get changed_on => this._changed_on;
	
	set changed_on(int? changed_on) => this._changed_on = changed_on;	

	String? get status => this._status;
	
	set status(String? status) => this._status = status;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}