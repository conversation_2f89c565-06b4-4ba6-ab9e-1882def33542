//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "PICKLIST".
*/	
class PICKLIST_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "PICKLIST_HEADER";
	
	// No desc available
	static const String FIELD_PICKLIST_ID = "PICKLIST_ID";

	// No desc available
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    String? _picklist_id;
    String? _description;
    String? _inactive;
	
	PICKLIST_HEADER({
			required picklist_id,
			description,
			inactive}) :
			_picklist_id = picklist_id,
			_description = description,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	PICKLIST_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_picklist_id = json[FIELD_PICKLIST_ID]; 
		_description = json[FIELD_DESCRIPTION]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_PICKLIST_ID] = _picklist_id;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	String? get picklist_id => this._picklist_id;
	
	set picklist_id(String? picklist_id) => this._picklist_id = picklist_id;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}