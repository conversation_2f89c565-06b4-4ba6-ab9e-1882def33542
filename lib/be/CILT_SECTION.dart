//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "CILT_PLAN".
*/	
class CILT_SECTION extends DataStructure {
	
	static const String TABLE_NAME = "CILT_SECTION";
	
	// No desc available
	static const String FIELD_SECTION_ID = "SECTION_ID";

	// No desc available
	static const String FIELD_PLAN_ID = "PLAN_ID";

	// No desc available
	static const String FIELD_TITLE = "TITLE";

	// No desc available
	static const String FIELD_SEQ_NO = "SEQ_NO";

	// No desc available
	static const String FIELD_LOCATION_ID = "LOCATION_ID";

	// No desc available
	static const String FIELD_ASSET_NO = "ASSET_NO";

	// No desc available
	static const String FIELD_CILT_CODE = "CILT_CODE";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    int? _section_id;
    int? _plan_id;
    String? _title;
    int? _seq_no;
    String? _location_id;
    int? _asset_no;
    int? _cilt_code;
    String? _inactive;
	
	CILT_SECTION({
			required section_id,
			required plan_id,
			title,
			seq_no,
			location_id,
			asset_no,
			cilt_code,
			inactive}) :
			_section_id = section_id,
			_plan_id = plan_id,
			_title = title,
			_seq_no = seq_no,
			_location_id = location_id,
			_asset_no = asset_no,
			_cilt_code = cilt_code,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	CILT_SECTION.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_section_id = json[FIELD_SECTION_ID]; 
		_plan_id = json[FIELD_PLAN_ID]; 
		_title = json[FIELD_TITLE]; 
		_seq_no = json[FIELD_SEQ_NO]; 
		_location_id = json[FIELD_LOCATION_ID]; 
		_asset_no = json[FIELD_ASSET_NO]; 
		_cilt_code = json[FIELD_CILT_CODE]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_SECTION_ID] = _section_id;
		data[FIELD_PLAN_ID] = _plan_id;
		data[FIELD_TITLE] = _title;
		data[FIELD_SEQ_NO] = _seq_no;
		data[FIELD_LOCATION_ID] = _location_id;
		data[FIELD_ASSET_NO] = _asset_no;
		data[FIELD_CILT_CODE] = _cilt_code;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	int? get section_id => this._section_id;
	
	set section_id(int? section_id) => this._section_id = section_id;	

	int? get plan_id => this._plan_id;
	
	set plan_id(int? plan_id) => this._plan_id = plan_id;	

	String? get title => this._title;
	
	set title(String? title) => this._title = title;	

	int? get seq_no => this._seq_no;
	
	set seq_no(int? seq_no) => this._seq_no = seq_no;	

	String? get location_id => this._location_id;
	
	set location_id(String? location_id) => this._location_id = location_id;	

	int? get asset_no => this._asset_no;
	
	set asset_no(int? asset_no) => this._asset_no = asset_no;	

	int? get cilt_code => this._cilt_code;
	
	set cilt_code(int? cilt_code) => this._cilt_code = cilt_code;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}