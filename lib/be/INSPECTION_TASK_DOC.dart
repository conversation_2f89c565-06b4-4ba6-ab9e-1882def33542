//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "INSPECTION_PLAN".
*/	
class INSPECTION_TASK_DOC extends DataStructure {
	
	static const String TABLE_NAME = "INSPECTION_TASK_DOC";
	
	// No desc available
	static const String FIELD_PLAN_ID = "PLAN_ID";

	// Unique Id of an Task in a inspection plan
	static const String FIELD_TASK_ID = "TASK_ID";

	// Unique Id of an section in an inspection plan
	static const String FIELD_SECTION_ID = "SECTION_ID";

	// Unique Id of an Documnet or file
	static const String FIELD_DOC_ID = "DOC_ID";
	
    int? _plan_id;
    int? _task_id;
    int? _section_id;
    String? _doc_id;
	
	INSPECTION_TASK_DOC({
			required plan_id,
			required task_id,
			required section_id,
			required doc_id}) :
			_plan_id = plan_id,
			_task_id = task_id,
			_section_id = section_id,
			_doc_id = doc_id {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	INSPECTION_TASK_DOC.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_plan_id = json[FIELD_PLAN_ID]; 
		_task_id = json[FIELD_TASK_ID]; 
		_section_id = json[FIELD_SECTION_ID]; 
		_doc_id = json[FIELD_DOC_ID]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_PLAN_ID] = _plan_id;
		data[FIELD_TASK_ID] = _task_id;
		data[FIELD_SECTION_ID] = _section_id;
		data[FIELD_DOC_ID] = _doc_id;

		return data;
  }
  
	int? get plan_id => this._plan_id;
	
	set plan_id(int? plan_id) => this._plan_id = plan_id;	

	int? get task_id => this._task_id;
	
	set task_id(int? task_id) => this._task_id = task_id;	

	int? get section_id => this._section_id;
	
	set section_id(int? section_id) => this._section_id = section_id;	

	String? get doc_id => this._doc_id;
	
	set doc_id(String? doc_id) => this._doc_id = doc_id;	
	
}