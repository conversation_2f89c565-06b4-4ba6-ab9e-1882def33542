//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "INSP_EXEC".
*/	
class INSP_EXEC_TASK extends DataStructure {
	
	static const String TABLE_NAME = "INSP_EXEC_TASK";
	
	// No desc available
	static const String FIELD_INSP_ID = "INSP_ID";

	// No desc available
	static const String FIELD_INSP_TASK_ID = "INSP_TASK_ID";

	// No desc available
	static const String FIELD_TASK_NO = "TASK_NO";

	// No desc available
	static const String FIELD_DEP_INSP_TASK_ID = "DEP_INSP_TASK_ID";

	// No desc available
	static const String FIELD_SECTION_ID = "SECTION_ID";

	// No desc available
	static const String FIELD_TASK_ID = "TASK_ID";

	// No desc available
	static const String FIELD_STATUS = "STATUS";

	// No desc available
	static const String FIELD_COMPLETED_BY = "COMPLETED_BY";

	// No desc available
	static const String FIELD_COMPLETED_ON = "COMPLETED_ON";

	// No desc available
	static const String FIELD_LATITUDE = "LATITUDE";

	// No desc available
	static const String FIELD_LONGITUDE = "LONGITUDE";

	// No desc available
	static const String FIELD_NUM_VALUE = "NUM_VALUE";

	// No desc available
	static const String FIELD_STR_VALUE = "STR_VALUE";

	// No desc available
	static const String FIELD_BLOB_VALUE = "BLOB_VALUE";

	// No desc available
	static const String FIELD_REASON = "REASON";

	// No desc available
	static const String FIELD_SKIP_COMMENTS = "SKIP_COMMENTS";

	// No desc available
	static const String FIELD_COMMENTS = "COMMENTS";

	// No desc available
	static const String FIELD_IS_SKIPPED = "IS_SKIPPED";

	// No desc available
	static const String FIELD_IS_IRRELEVANT = "IS_IRRELEVANT";

	// No desc available
	static const String FIELD_IS_DISCREPANT = "IS_DISCREPANT";

	// No desc available
	static const String FIELD_P_MODE = "P_MODE";
	
    int? _insp_id;
    int? _insp_task_id;
    int? _task_no;
    int? _dep_insp_task_id;
    int? _section_id;
    int? _task_id;
    String? _status;
    String? _completed_by;
    int? _completed_on;
    double? _latitude;
    double? _longitude;
    double? _num_value;
    String? _str_value;
    String? _blob_value;
    String? _reason;
    String? _skip_comments;
    String? _comments;
    String? _is_skipped;
    String? _is_irrelevant;
    String? _is_discrepant;
    String? _p_mode;
	
	INSP_EXEC_TASK({
			required insp_id,
			required insp_task_id,
			required task_no,
			dep_insp_task_id,
			section_id,
			task_id,
			status,
			completed_by,
			completed_on,
			latitude,
			longitude,
			num_value,
			str_value,
			blob_value,
			reason,
			skip_comments,
			comments,
			is_skipped,
			is_irrelevant,
			is_discrepant,
			p_mode}) :
			_insp_id = insp_id,
			_insp_task_id = insp_task_id,
			_task_no = task_no,
			_dep_insp_task_id = dep_insp_task_id,
			_section_id = section_id,
			_task_id = task_id,
			_status = status,
			_completed_by = completed_by,
			_completed_on = completed_on,
			_latitude = latitude,
			_longitude = longitude,
			_num_value = num_value,
			_str_value = str_value,
			_blob_value = blob_value,
			_reason = reason,
			_skip_comments = skip_comments,
			_comments = comments,
			_is_skipped = is_skipped,
			_is_irrelevant = is_irrelevant,
			_is_discrepant = is_discrepant,
			_p_mode = p_mode {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	INSP_EXEC_TASK.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_insp_id = json[FIELD_INSP_ID]; 
		_insp_task_id = json[FIELD_INSP_TASK_ID]; 
		_task_no = json[FIELD_TASK_NO]; 
		_dep_insp_task_id = json[FIELD_DEP_INSP_TASK_ID]; 
		_section_id = json[FIELD_SECTION_ID]; 
		_task_id = json[FIELD_TASK_ID]; 
		_status = json[FIELD_STATUS]; 
		_completed_by = json[FIELD_COMPLETED_BY]; 
		_completed_on = json[FIELD_COMPLETED_ON]; 
		_latitude = json[FIELD_LATITUDE]; 
		_longitude = json[FIELD_LONGITUDE]; 
		_num_value = json[FIELD_NUM_VALUE]; 
		_str_value = json[FIELD_STR_VALUE]; 
		_blob_value = json[FIELD_BLOB_VALUE]; 
		_reason = json[FIELD_REASON]; 
		_skip_comments = json[FIELD_SKIP_COMMENTS]; 
		_comments = json[FIELD_COMMENTS]; 
		_is_skipped = json[FIELD_IS_SKIPPED]; 
		_is_irrelevant = json[FIELD_IS_IRRELEVANT]; 
		_is_discrepant = json[FIELD_IS_DISCREPANT]; 
		_p_mode = json[FIELD_P_MODE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_INSP_ID] = _insp_id;
		data[FIELD_INSP_TASK_ID] = _insp_task_id;
		data[FIELD_TASK_NO] = _task_no;
		data[FIELD_DEP_INSP_TASK_ID] = _dep_insp_task_id;
		data[FIELD_SECTION_ID] = _section_id;
		data[FIELD_TASK_ID] = _task_id;
		data[FIELD_STATUS] = _status;
		data[FIELD_COMPLETED_BY] = _completed_by;
		data[FIELD_COMPLETED_ON] = _completed_on;
		data[FIELD_LATITUDE] = _latitude;
		data[FIELD_LONGITUDE] = _longitude;
		data[FIELD_NUM_VALUE] = _num_value;
		data[FIELD_STR_VALUE] = _str_value;
		data[FIELD_BLOB_VALUE] = _blob_value;
		data[FIELD_REASON] = _reason;
		data[FIELD_SKIP_COMMENTS] = _skip_comments;
		data[FIELD_COMMENTS] = _comments;
		data[FIELD_IS_SKIPPED] = _is_skipped;
		data[FIELD_IS_IRRELEVANT] = _is_irrelevant;
		data[FIELD_IS_DISCREPANT] = _is_discrepant;
		data[FIELD_P_MODE] = _p_mode;

		return data;
  }
  
	int? get insp_id => this._insp_id;
	
	set insp_id(int? insp_id) => this._insp_id = insp_id;	

	int? get insp_task_id => this._insp_task_id;
	
	set insp_task_id(int? insp_task_id) => this._insp_task_id = insp_task_id;	

	int? get task_no => this._task_no;
	
	set task_no(int? task_no) => this._task_no = task_no;	

	int? get dep_insp_task_id => this._dep_insp_task_id;
	
	set dep_insp_task_id(int? dep_insp_task_id) => this._dep_insp_task_id = dep_insp_task_id;	

	int? get section_id => this._section_id;
	
	set section_id(int? section_id) => this._section_id = section_id;	

	int? get task_id => this._task_id;
	
	set task_id(int? task_id) => this._task_id = task_id;	

	String? get status => this._status;
	
	set status(String? status) => this._status = status;	

	String? get completed_by => this._completed_by;
	
	set completed_by(String? completed_by) => this._completed_by = completed_by;	

	int? get completed_on => this._completed_on;
	
	set completed_on(int? completed_on) => this._completed_on = completed_on;	

	double? get latitude => this._latitude;
	
	set latitude(double? latitude) => this._latitude = latitude;	

	double? get longitude => this._longitude;
	
	set longitude(double? longitude) => this._longitude = longitude;	

	double? get num_value => this._num_value;
	
	set num_value(double? num_value) => this._num_value = num_value;	

	String? get str_value => this._str_value;
	
	set str_value(String? str_value) => this._str_value = str_value;	

	String? get blob_value => this._blob_value;
	
	set blob_value(String? blob_value) => this._blob_value = blob_value;	

	String? get reason => this._reason;
	
	set reason(String? reason) => this._reason = reason;	

	String? get skip_comments => this._skip_comments;
	
	set skip_comments(String? skip_comments) => this._skip_comments = skip_comments;	

	String? get comments => this._comments;
	
	set comments(String? comments) => this._comments = comments;	

	String? get is_skipped => this._is_skipped;
	
	set is_skipped(String? is_skipped) => this._is_skipped = is_skipped;	

	String? get is_irrelevant => this._is_irrelevant;
	
	set is_irrelevant(String? is_irrelevant) => this._is_irrelevant = is_irrelevant;	

	String? get is_discrepant => this._is_discrepant;
	
	set is_discrepant(String? is_discrepant) => this._is_discrepant = is_discrepant;	

	String? get p_mode => this._p_mode;
	
	set p_mode(String? p_mode) => this._p_mode = p_mode;	
	
}