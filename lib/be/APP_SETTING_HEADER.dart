//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "APP_SETTING".
*/	
class APP_SETTING_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "APP_SETTING_HEADER";
	
	// No desc available
	static const String FIELD_PROP_NAME = "PROP_NAME";

	// No desc available
	static const String FIELD_PROP_VALUE = "PROP_VALUE";
	
    String? _prop_name;
    String? _prop_value;
	
	APP_SETTING_HEADER({
			required prop_name,
			prop_value}) :
			_prop_name = prop_name,
			_prop_value = prop_value {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	APP_SETTING_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_prop_name = json[FIELD_PROP_NAME]; 
		_prop_value = json[FIELD_PROP_VALUE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_PROP_NAME] = _prop_name;
		data[FIELD_PROP_VALUE] = _prop_value;

		return data;
  }
  
	String? get prop_name => this._prop_name;
	
	set prop_name(String? prop_name) => this._prop_name = prop_name;	

	String? get prop_value => this._prop_value;
	
	set prop_value(String? prop_value) => this._prop_value = prop_value;	
	
}