//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "ROLE".
*/	
class ROLE_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "ROLE_HEADER";
	
	// No desc available
	static const String FIELD_ROLE_ID = "ROLE_ID";

	// No desc available
	static const String FIELD_CONFIG_MGMT = "CONFIG_MGMT";

	// No desc available
	static const String FIELD_USER_MGMT = "USER_MGMT";

	// No desc available
	static const String FIELD_ASSET_MGMT = "ASSET_MGMT";

	// No desc available
	static const String FIELD_PLANNING = "PLANNING";

	// No desc available
	static const String FIELD_SCHEDULING = "SCHEDULING";

	// No desc available
	static const String FIELD_REPORTING = "REPORTING";

	// No desc available
	static const String FIELD_DMS = "DMS";

	// No desc available
	static const String FIELD_CILT = "CILT";

	// No desc available
	static const String FIELD_INSPECTION = "INSPECTION";

	// No desc available
	static const String FIELD_FAULT = "FAULT";

	// No desc available
	static const String FIELD_TASK = "TASK";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    String? _role_id;
    String? _config_mgmt;
    String? _user_mgmt;
    String? _asset_mgmt;
    String? _planning;
    String? _scheduling;
    String? _reporting;
    String? _dms;
    int? _cilt;
    int? _inspection;
    int? _fault;
    int? _task;
    String? _inactive;
	
	ROLE_HEADER({
			required role_id,
			config_mgmt,
			user_mgmt,
			asset_mgmt,
			planning,
			scheduling,
			reporting,
			dms,
			cilt,
			inspection,
			fault,
			task,
			inactive}) :
			_role_id = role_id,
			_config_mgmt = config_mgmt,
			_user_mgmt = user_mgmt,
			_asset_mgmt = asset_mgmt,
			_planning = planning,
			_scheduling = scheduling,
			_reporting = reporting,
			_dms = dms,
			_cilt = cilt,
			_inspection = inspection,
			_fault = fault,
			_task = task,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	ROLE_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_role_id = json[FIELD_ROLE_ID]; 
		_config_mgmt = json[FIELD_CONFIG_MGMT]; 
		_user_mgmt = json[FIELD_USER_MGMT]; 
		_asset_mgmt = json[FIELD_ASSET_MGMT]; 
		_planning = json[FIELD_PLANNING]; 
		_scheduling = json[FIELD_SCHEDULING]; 
		_reporting = json[FIELD_REPORTING]; 
		_dms = json[FIELD_DMS]; 
		_cilt = json[FIELD_CILT]; 
		_inspection = json[FIELD_INSPECTION]; 
		_fault = json[FIELD_FAULT]; 
		_task = json[FIELD_TASK]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_ROLE_ID] = _role_id;
		data[FIELD_CONFIG_MGMT] = _config_mgmt;
		data[FIELD_USER_MGMT] = _user_mgmt;
		data[FIELD_ASSET_MGMT] = _asset_mgmt;
		data[FIELD_PLANNING] = _planning;
		data[FIELD_SCHEDULING] = _scheduling;
		data[FIELD_REPORTING] = _reporting;
		data[FIELD_DMS] = _dms;
		data[FIELD_CILT] = _cilt;
		data[FIELD_INSPECTION] = _inspection;
		data[FIELD_FAULT] = _fault;
		data[FIELD_TASK] = _task;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	String? get role_id => this._role_id;
	
	set role_id(String? role_id) => this._role_id = role_id;	

	String? get config_mgmt => this._config_mgmt;
	
	set config_mgmt(String? config_mgmt) => this._config_mgmt = config_mgmt;	

	String? get user_mgmt => this._user_mgmt;
	
	set user_mgmt(String? user_mgmt) => this._user_mgmt = user_mgmt;	

	String? get asset_mgmt => this._asset_mgmt;
	
	set asset_mgmt(String? asset_mgmt) => this._asset_mgmt = asset_mgmt;	

	String? get planning => this._planning;
	
	set planning(String? planning) => this._planning = planning;	

	String? get scheduling => this._scheduling;
	
	set scheduling(String? scheduling) => this._scheduling = scheduling;	

	String? get reporting => this._reporting;
	
	set reporting(String? reporting) => this._reporting = reporting;	

	String? get dms => this._dms;
	
	set dms(String? dms) => this._dms = dms;	

	int? get cilt => this._cilt;
	
	set cilt(int? cilt) => this._cilt = cilt;	

	int? get inspection => this._inspection;
	
	set inspection(int? inspection) => this._inspection = inspection;	

	int? get fault => this._fault;
	
	set fault(int? fault) => this._fault = fault;	

	int? get task => this._task;
	
	set task(int? task) => this._task = task;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}