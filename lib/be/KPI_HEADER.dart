//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "KPI".
*/	
class KPI_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "KPI_HEADER";
	
	// No desc available
	static const String FIELD_KPI_ID = "KPI_ID";

	// No desc available
	static const String FIELD_PLANT_ID = "PLANT_ID";

	// No desc available
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// No desc available
	static const String FIELD_POSITION = "POSITION";

	// L - Location A - Asset
	static const String FIELD_REF_OBJECT = "REF_OBJECT";

	// No desc available
	static const String FIELD_LOCATION_ID = "LOCATION_ID";

	// No desc available
	static const String FIELD_ASSET_NO = "ASSET_NO";

	// No desc available
	static const String FIELD_EXTERNAL_ID = "EXTERNAL_ID";

	// ' ', SAP
	static const String FIELD_SOURCE = "SOURCE";

	// 1 - Qualitative 2 - Quantitative 3 - Counter
	static const String FIELD_KPI_TYPE = "KPI_TYPE";

	// No desc available
	static const String FIELD_PICKLIST_ID = "PICKLIST_ID";

	// No desc available
	static const String FIELD_LENGTH = "LENGTH";

	// No desc available
	static const String FIELD_ACCURACY = "ACCURACY";

	// No desc available
	static const String FIELD_UOM = "UOM";

	// No desc available
	static const String FIELD_UPPER_LIMIT = "UPPER_LIMIT";

	// No desc available
	static const String FIELD_STANDARD_VALUE = "STANDARD_VALUE";

	// No desc available
	static const String FIELD_LOWER_LIMIT = "LOWER_LIMIT";

	// No desc available
	static const String FIELD_UPPER_LIMIT_MSG = "UPPER_LIMIT_MSG";

	// No desc available
	static const String FIELD_LOWER_LIMIT_MSG = "LOWER_LIMIT_MSG";

	// No desc available
	static const String FIELD_OVERFLOW = "OVERFLOW";

	// No desc available
	static const String FIELD_START_COUNTER = "START_COUNTER";

	// No desc available
	static const String FIELD_CNT_STEP_ALERT = "CNT_STEP_ALERT";

	// No desc available
	static const String FIELD_CREATED_BY = "CREATED_BY";

	// No desc available
	static const String FIELD_CREATED_ON = "CREATED_ON";

	// No desc available
	static const String FIELD_CHANGED_BY = "CHANGED_BY";

	// No desc available
	static const String FIELD_CHANGED_ON = "CHANGED_ON";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    int? _kpi_id;
    String? _plant_id;
    String? _description;
    String? _position;
    String? _ref_object;
    String? _location_id;
    int? _asset_no;
    String? _external_id;
    String? _source;
    String? _kpi_type;
    String? _picklist_id;
    int? _length;
    int? _accuracy;
    String? _uom;
    double? _upper_limit;
    double? _standard_value;
    double? _lower_limit;
    String? _upper_limit_msg;
    String? _lower_limit_msg;
    double? _overflow;
    double? _start_counter;
    double? _cnt_step_alert;
    String? _created_by;
    int? _created_on;
    String? _changed_by;
    int? _changed_on;
    String? _inactive;
	
	KPI_HEADER({
			required kpi_id,
			plant_id,
			description,
			position,
			ref_object,
			location_id,
			asset_no,
			external_id,
			source,
			kpi_type,
			picklist_id,
			length,
			accuracy,
			uom,
			upper_limit,
			standard_value,
			lower_limit,
			upper_limit_msg,
			lower_limit_msg,
			overflow,
			start_counter,
			cnt_step_alert,
			created_by,
			created_on,
			changed_by,
			changed_on,
			inactive}) :
			_kpi_id = kpi_id,
			_plant_id = plant_id,
			_description = description,
			_position = position,
			_ref_object = ref_object,
			_location_id = location_id,
			_asset_no = asset_no,
			_external_id = external_id,
			_source = source,
			_kpi_type = kpi_type,
			_picklist_id = picklist_id,
			_length = length,
			_accuracy = accuracy,
			_uom = uom,
			_upper_limit = upper_limit,
			_standard_value = standard_value,
			_lower_limit = lower_limit,
			_upper_limit_msg = upper_limit_msg,
			_lower_limit_msg = lower_limit_msg,
			_overflow = overflow,
			_start_counter = start_counter,
			_cnt_step_alert = cnt_step_alert,
			_created_by = created_by,
			_created_on = created_on,
			_changed_by = changed_by,
			_changed_on = changed_on,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	KPI_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_kpi_id = json[FIELD_KPI_ID]; 
		_plant_id = json[FIELD_PLANT_ID]; 
		_description = json[FIELD_DESCRIPTION]; 
		_position = json[FIELD_POSITION]; 
		_ref_object = json[FIELD_REF_OBJECT]; 
		_location_id = json[FIELD_LOCATION_ID]; 
		_asset_no = json[FIELD_ASSET_NO]; 
		_external_id = json[FIELD_EXTERNAL_ID]; 
		_source = json[FIELD_SOURCE]; 
		_kpi_type = json[FIELD_KPI_TYPE]; 
		_picklist_id = json[FIELD_PICKLIST_ID]; 
		_length = json[FIELD_LENGTH]; 
		_accuracy = json[FIELD_ACCURACY]; 
		_uom = json[FIELD_UOM]; 
		_upper_limit = json[FIELD_UPPER_LIMIT]; 
		_standard_value = json[FIELD_STANDARD_VALUE]; 
		_lower_limit = json[FIELD_LOWER_LIMIT]; 
		_upper_limit_msg = json[FIELD_UPPER_LIMIT_MSG]; 
		_lower_limit_msg = json[FIELD_LOWER_LIMIT_MSG]; 
		_overflow = json[FIELD_OVERFLOW]; 
		_start_counter = json[FIELD_START_COUNTER]; 
		_cnt_step_alert = json[FIELD_CNT_STEP_ALERT]; 
		_created_by = json[FIELD_CREATED_BY]; 
		_created_on = json[FIELD_CREATED_ON]; 
		_changed_by = json[FIELD_CHANGED_BY]; 
		_changed_on = json[FIELD_CHANGED_ON]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_KPI_ID] = _kpi_id;
		data[FIELD_PLANT_ID] = _plant_id;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_POSITION] = _position;
		data[FIELD_REF_OBJECT] = _ref_object;
		data[FIELD_LOCATION_ID] = _location_id;
		data[FIELD_ASSET_NO] = _asset_no;
		data[FIELD_EXTERNAL_ID] = _external_id;
		data[FIELD_SOURCE] = _source;
		data[FIELD_KPI_TYPE] = _kpi_type;
		data[FIELD_PICKLIST_ID] = _picklist_id;
		data[FIELD_LENGTH] = _length;
		data[FIELD_ACCURACY] = _accuracy;
		data[FIELD_UOM] = _uom;
		data[FIELD_UPPER_LIMIT] = _upper_limit;
		data[FIELD_STANDARD_VALUE] = _standard_value;
		data[FIELD_LOWER_LIMIT] = _lower_limit;
		data[FIELD_UPPER_LIMIT_MSG] = _upper_limit_msg;
		data[FIELD_LOWER_LIMIT_MSG] = _lower_limit_msg;
		data[FIELD_OVERFLOW] = _overflow;
		data[FIELD_START_COUNTER] = _start_counter;
		data[FIELD_CNT_STEP_ALERT] = _cnt_step_alert;
		data[FIELD_CREATED_BY] = _created_by;
		data[FIELD_CREATED_ON] = _created_on;
		data[FIELD_CHANGED_BY] = _changed_by;
		data[FIELD_CHANGED_ON] = _changed_on;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	int? get kpi_id => this._kpi_id;
	
	set kpi_id(int? kpi_id) => this._kpi_id = kpi_id;	

	String? get plant_id => this._plant_id;
	
	set plant_id(String? plant_id) => this._plant_id = plant_id;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get position => this._position;
	
	set position(String? position) => this._position = position;	

	String? get ref_object => this._ref_object;
	
	set ref_object(String? ref_object) => this._ref_object = ref_object;	

	String? get location_id => this._location_id;
	
	set location_id(String? location_id) => this._location_id = location_id;	

	int? get asset_no => this._asset_no;
	
	set asset_no(int? asset_no) => this._asset_no = asset_no;	

	String? get external_id => this._external_id;
	
	set external_id(String? external_id) => this._external_id = external_id;	

	String? get source => this._source;
	
	set source(String? source) => this._source = source;	

	String? get kpi_type => this._kpi_type;
	
	set kpi_type(String? kpi_type) => this._kpi_type = kpi_type;	

	String? get picklist_id => this._picklist_id;
	
	set picklist_id(String? picklist_id) => this._picklist_id = picklist_id;	

	int? get length => this._length;
	
	set length(int? length) => this._length = length;	

	int? get accuracy => this._accuracy;
	
	set accuracy(int? accuracy) => this._accuracy = accuracy;	

	String? get uom => this._uom;
	
	set uom(String? uom) => this._uom = uom;	

	double? get upper_limit => this._upper_limit;
	
	set upper_limit(double? upper_limit) => this._upper_limit = upper_limit;	

	double? get standard_value => this._standard_value;
	
	set standard_value(double? standard_value) => this._standard_value = standard_value;	

	double? get lower_limit => this._lower_limit;
	
	set lower_limit(double? lower_limit) => this._lower_limit = lower_limit;	

	String? get upper_limit_msg => this._upper_limit_msg;
	
	set upper_limit_msg(String? upper_limit_msg) => this._upper_limit_msg = upper_limit_msg;	

	String? get lower_limit_msg => this._lower_limit_msg;
	
	set lower_limit_msg(String? lower_limit_msg) => this._lower_limit_msg = lower_limit_msg;	

	double? get overflow => this._overflow;
	
	set overflow(double? overflow) => this._overflow = overflow;	

	double? get start_counter => this._start_counter;
	
	set start_counter(double? start_counter) => this._start_counter = start_counter;	

	double? get cnt_step_alert => this._cnt_step_alert;
	
	set cnt_step_alert(double? cnt_step_alert) => this._cnt_step_alert = cnt_step_alert;	

	String? get created_by => this._created_by;
	
	set created_by(String? created_by) => this._created_by = created_by;	

	int? get created_on => this._created_on;
	
	set created_on(int? created_on) => this._created_on = created_on;	

	String? get changed_by => this._changed_by;
	
	set changed_by(String? changed_by) => this._changed_by = changed_by;	

	int? get changed_on => this._changed_on;
	
	set changed_on(int? changed_on) => this._changed_on = changed_on;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}