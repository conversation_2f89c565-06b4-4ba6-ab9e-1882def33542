//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "LOCATION".
*/	
class LOCATION_DOCUMENT extends DataStructure {
	
	static const String TABLE_NAME = "LOCATION_DOCUMENT";
	
	// Unique Id of an functional location 
	static const String FIELD_LOCATION_ID = "LOCATION_ID";

	// Unique Id of an Documnet or file
	static const String FIELD_DOC_ID = "DOC_ID";

	// No desc available
	static const String FIELD_P_MODE = "P_MODE";
	
    String? _location_id;
    String? _doc_id;
    String? _p_mode;
	
	LOCATION_DOCUMENT({
			required location_id,
			required doc_id,
			p_mode}) :
			_location_id = location_id,
			_doc_id = doc_id,
			_p_mode = p_mode {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	LOCATION_DOCUMENT.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_location_id = json[FIELD_LOCATION_ID]; 
		_doc_id = json[FIELD_DOC_ID]; 
		_p_mode = json[FIELD_P_MODE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_LOCATION_ID] = _location_id;
		data[FIELD_DOC_ID] = _doc_id;
		data[FIELD_P_MODE] = _p_mode;

		return data;
  }
  
	String? get location_id => this._location_id;
	
	set location_id(String? location_id) => this._location_id = location_id;	

	String? get doc_id => this._doc_id;
	
	set doc_id(String? doc_id) => this._doc_id = doc_id;	

	String? get p_mode => this._p_mode;
	
	set p_mode(String? p_mode) => this._p_mode = p_mode;	
	
}