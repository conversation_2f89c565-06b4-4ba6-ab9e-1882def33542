//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "USER".
*/	
class USER_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "USER_HEADER";
	
	// No desc available
	static const String FIELD_USER_ID = "USER_ID";

	// No desc available
	static const String FIELD_FIRST_NAME = "FIRST_NAME";

	// No desc available
	static const String FIELD_LAST_NAME = "LAST_NAME";

	// No desc available
	static const String FIELD_PHONE = "PHONE";

	// No desc available
	static const String FIELD_EMAIL = "EMAIL";

	// No desc available
	static const String FIELD_ROLE = "ROLE";

	// No desc available
	static const String FIELD_LANGUAGE = "LANGUAGE";

	// No desc available
	static const String FIELD_THUMBNAIL = "THUMBNAIL";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    String? _user_id;
    String? _first_name;
    String? _last_name;
    String? _phone;
    String? _email;
    String? _role;
    String? _language;
    String? _thumbnail;
    String? _inactive;
	
	USER_HEADER({
			required user_id,
			first_name,
			last_name,
			phone,
			email,
			role,
			language,
			thumbnail,
			inactive}) :
			_user_id = user_id,
			_first_name = first_name,
			_last_name = last_name,
			_phone = phone,
			_email = email,
			_role = role,
			_language = language,
			_thumbnail = thumbnail,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	USER_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_user_id = json[FIELD_USER_ID]; 
		_first_name = json[FIELD_FIRST_NAME]; 
		_last_name = json[FIELD_LAST_NAME]; 
		_phone = json[FIELD_PHONE]; 
		_email = json[FIELD_EMAIL]; 
		_role = json[FIELD_ROLE]; 
		_language = json[FIELD_LANGUAGE]; 
		_thumbnail = json[FIELD_THUMBNAIL]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_USER_ID] = _user_id;
		data[FIELD_FIRST_NAME] = _first_name;
		data[FIELD_LAST_NAME] = _last_name;
		data[FIELD_PHONE] = _phone;
		data[FIELD_EMAIL] = _email;
		data[FIELD_ROLE] = _role;
		data[FIELD_LANGUAGE] = _language;
		data[FIELD_THUMBNAIL] = _thumbnail;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	String? get user_id => this._user_id;
	
	set user_id(String? user_id) => this._user_id = user_id;	

	String? get first_name => this._first_name;
	
	set first_name(String? first_name) => this._first_name = first_name;	

	String? get last_name => this._last_name;
	
	set last_name(String? last_name) => this._last_name = last_name;	

	String? get phone => this._phone;
	
	set phone(String? phone) => this._phone = phone;	

	String? get email => this._email;
	
	set email(String? email) => this._email = email;	

	String? get role => this._role;
	
	set role(String? role) => this._role = role;	

	String? get language => this._language;
	
	set language(String? language) => this._language = language;	

	String? get thumbnail => this._thumbnail;
	
	set thumbnail(String? thumbnail) => this._thumbnail = thumbnail;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}