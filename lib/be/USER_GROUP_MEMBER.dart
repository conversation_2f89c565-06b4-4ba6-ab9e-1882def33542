//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "USER_GROUP".
*/	
class USER_GROUP_MEMBER extends DataStructure {
	
	static const String TABLE_NAME = "USER_GROUP_MEMBER";
	
	// Unique Group Id 
	static const String FIELD_GROUP_ID = "GROUP_ID";

	// List of User id in specific Group
	static const String FIELD_USER_ID = "USER_ID";
	
    String? _group_id;
    String? _user_id;
	
	USER_GROUP_MEMBER({
			required group_id,
			required user_id}) :
			_group_id = group_id,
			_user_id = user_id {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	USER_GROUP_MEMBER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_group_id = json[FIELD_GROUP_ID]; 
		_user_id = json[FIELD_USER_ID]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_GROUP_ID] = _group_id;
		data[FIELD_USER_ID] = _user_id;

		return data;
  }
  
	String? get group_id => this._group_id;
	
	set group_id(String? group_id) => this._group_id = group_id;	

	String? get user_id => this._user_id;
	
	set user_id(String? user_id) => this._user_id = user_id;	
	
}