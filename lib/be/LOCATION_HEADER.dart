//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "LOCATION".
*/	
class LOCATION_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "LOCATION_HEADER";
	
	// No desc available
	static const String FIELD_LOCATION_ID = "LOCATION_ID";

	// No desc available
	static const String FIELD_PLANT_ID = "PLANT_ID";

	// No desc available
	static const String FIELD_PLANT_SEC_ID = "PLANT_SEC_ID";

	// No desc available
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// No desc available
	static const String FIELD_EXTERNAL_ID = "EXTERNAL_ID";

	// No desc available
	static const String FIELD_EXT_SOURCE = "EXT_SOURCE";

	// No desc available
	static const String FIELD_CATEGORY = "CATEGORY";

	// No desc available
	static const String FIELD_PARENT_LOC_ID = "PARENT_LOC_ID";

	// No desc available
	static const String FIELD_TECHNICAL_ID = "TECHNICAL_ID";

	// No desc available
	static const String FIELD_MANUFACTURER = "MANUFACTURER";

	// No desc available
	static const String FIELD_ABC_INDICATOR = "ABC_INDICATOR";

	// No desc available
	static const String FIELD_LATITUDE = "LATITUDE";

	// No desc available
	static const String FIELD_LONGITUDE = "LONGITUDE";

	// No desc available
	static const String FIELD_CREATED_BY = "CREATED_BY";

	// No desc available
	static const String FIELD_CREATED_ON = "CREATED_ON";

	// No desc available
	static const String FIELD_CHANGED_BY = "CHANGED_BY";

	// No desc available
	static const String FIELD_CHANGED_ON = "CHANGED_ON";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    String? _location_id;
    String? _plant_id;
    String? _plant_sec_id;
    String? _description;
    String? _external_id;
    String? _ext_source;
    String? _category;
    String? _parent_loc_id;
    String? _technical_id;
    String? _manufacturer;
    String? _abc_indicator;
    double? _latitude;
    double? _longitude;
    String? _created_by;
    int? _created_on;
    String? _changed_by;
    int? _changed_on;
    String? _inactive;
	
	LOCATION_HEADER({
			required location_id,
			plant_id,
			plant_sec_id,
			description,
			external_id,
			ext_source,
			category,
			parent_loc_id,
			technical_id,
			manufacturer,
			abc_indicator,
			latitude,
			longitude,
			created_by,
			created_on,
			changed_by,
			changed_on,
			inactive}) :
			_location_id = location_id,
			_plant_id = plant_id,
			_plant_sec_id = plant_sec_id,
			_description = description,
			_external_id = external_id,
			_ext_source = ext_source,
			_category = category,
			_parent_loc_id = parent_loc_id,
			_technical_id = technical_id,
			_manufacturer = manufacturer,
			_abc_indicator = abc_indicator,
			_latitude = latitude,
			_longitude = longitude,
			_created_by = created_by,
			_created_on = created_on,
			_changed_by = changed_by,
			_changed_on = changed_on,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	LOCATION_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_location_id = json[FIELD_LOCATION_ID]; 
		_plant_id = json[FIELD_PLANT_ID]; 
		_plant_sec_id = json[FIELD_PLANT_SEC_ID]; 
		_description = json[FIELD_DESCRIPTION]; 
		_external_id = json[FIELD_EXTERNAL_ID]; 
		_ext_source = json[FIELD_EXT_SOURCE]; 
		_category = json[FIELD_CATEGORY]; 
		_parent_loc_id = json[FIELD_PARENT_LOC_ID]; 
		_technical_id = json[FIELD_TECHNICAL_ID]; 
		_manufacturer = json[FIELD_MANUFACTURER]; 
		_abc_indicator = json[FIELD_ABC_INDICATOR]; 
		_latitude = json[FIELD_LATITUDE]; 
		_longitude = json[FIELD_LONGITUDE]; 
		_created_by = json[FIELD_CREATED_BY]; 
		_created_on = json[FIELD_CREATED_ON]; 
		_changed_by = json[FIELD_CHANGED_BY]; 
		_changed_on = json[FIELD_CHANGED_ON]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_LOCATION_ID] = _location_id;
		data[FIELD_PLANT_ID] = _plant_id;
		data[FIELD_PLANT_SEC_ID] = _plant_sec_id;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_EXTERNAL_ID] = _external_id;
		data[FIELD_EXT_SOURCE] = _ext_source;
		data[FIELD_CATEGORY] = _category;
		data[FIELD_PARENT_LOC_ID] = _parent_loc_id;
		data[FIELD_TECHNICAL_ID] = _technical_id;
		data[FIELD_MANUFACTURER] = _manufacturer;
		data[FIELD_ABC_INDICATOR] = _abc_indicator;
		data[FIELD_LATITUDE] = _latitude;
		data[FIELD_LONGITUDE] = _longitude;
		data[FIELD_CREATED_BY] = _created_by;
		data[FIELD_CREATED_ON] = _created_on;
		data[FIELD_CHANGED_BY] = _changed_by;
		data[FIELD_CHANGED_ON] = _changed_on;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	String? get location_id => this._location_id;
	
	set location_id(String? location_id) => this._location_id = location_id;	

	String? get plant_id => this._plant_id;
	
	set plant_id(String? plant_id) => this._plant_id = plant_id;	

	String? get plant_sec_id => this._plant_sec_id;
	
	set plant_sec_id(String? plant_sec_id) => this._plant_sec_id = plant_sec_id;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get external_id => this._external_id;
	
	set external_id(String? external_id) => this._external_id = external_id;	

	String? get ext_source => this._ext_source;
	
	set ext_source(String? ext_source) => this._ext_source = ext_source;	

	String? get category => this._category;
	
	set category(String? category) => this._category = category;	

	String? get parent_loc_id => this._parent_loc_id;
	
	set parent_loc_id(String? parent_loc_id) => this._parent_loc_id = parent_loc_id;	

	String? get technical_id => this._technical_id;
	
	set technical_id(String? technical_id) => this._technical_id = technical_id;	

	String? get manufacturer => this._manufacturer;
	
	set manufacturer(String? manufacturer) => this._manufacturer = manufacturer;	

	String? get abc_indicator => this._abc_indicator;
	
	set abc_indicator(String? abc_indicator) => this._abc_indicator = abc_indicator;	

	double? get latitude => this._latitude;
	
	set latitude(double? latitude) => this._latitude = latitude;	

	double? get longitude => this._longitude;
	
	set longitude(double? longitude) => this._longitude = longitude;	

	String? get created_by => this._created_by;
	
	set created_by(String? created_by) => this._created_by = created_by;	

	int? get created_on => this._created_on;
	
	set created_on(int? created_on) => this._created_on = created_on;	

	String? get changed_by => this._changed_by;
	
	set changed_by(String? changed_by) => this._changed_by = changed_by;	

	int? get changed_on => this._changed_on;
	
	set changed_on(int? changed_on) => this._changed_on = changed_on;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}