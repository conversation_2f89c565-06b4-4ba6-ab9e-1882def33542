//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "ABCINDICATOR".
*/	
class ABCINDICATOR_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "ABCINDICATOR_HEADER";
	
	// No desc available
	static const String FIELD_ABC_INDICATOR = "ABC_INDICATOR";

	// No desc available
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    String? _abc_indicator;
    String? _description;
    String? _inactive;
	
	ABCINDICATOR_HEADER({
			required abc_indicator,
			description,
			inactive}) :
			_abc_indicator = abc_indicator,
			_description = description,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	ABCINDICATOR_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_abc_indicator = json[FIELD_ABC_INDICATOR]; 
		_description = json[FIELD_DESCRIPTION]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_ABC_INDICATOR] = _abc_indicator;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	String? get abc_indicator => this._abc_indicator;
	
	set abc_indicator(String? abc_indicator) => this._abc_indicator = abc_indicator;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}