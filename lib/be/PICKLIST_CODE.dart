//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "PICKLIST".
*/	
class PICKLIST_CODE extends DataStructure {
	
	static const String TABLE_NAME = "PICKLIST_CODE";
	
	// No desc available
	static const String FIELD_PICKLIST_ID = "PICKLIST_ID";

	// No desc available
	static const String FIELD_CODE = "CODE";

	// No desc available
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// No desc available
	static const String FIELD_ACCCEPTABLE = "ACCCEPTABLE";

	// No desc available
	static const String FIELD_COLOR = "COLOR";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    String? _picklist_id;
    String? _code;
    String? _description;
    String? _accceptable;
    String? _color;
    String? _inactive;
	
	PICKLIST_CODE({
			required picklist_id,
			required code,
			description,
			accceptable,
			color,
			inactive}) :
			_picklist_id = picklist_id,
			_code = code,
			_description = description,
			_accceptable = accceptable,
			_color = color,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	PICKLIST_CODE.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_picklist_id = json[FIELD_PICKLIST_ID]; 
		_code = json[FIELD_CODE]; 
		_description = json[FIELD_DESCRIPTION]; 
		_accceptable = json[FIELD_ACCCEPTABLE]; 
		_color = json[FIELD_COLOR]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_PICKLIST_ID] = _picklist_id;
		data[FIELD_CODE] = _code;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_ACCCEPTABLE] = _accceptable;
		data[FIELD_COLOR] = _color;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	String? get picklist_id => this._picklist_id;
	
	set picklist_id(String? picklist_id) => this._picklist_id = picklist_id;	

	String? get code => this._code;
	
	set code(String? code) => this._code = code;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get accceptable => this._accceptable;
	
	set accceptable(String? accceptable) => this._accceptable = accceptable;	

	String? get color => this._color;
	
	set color(String? color) => this._color = color;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}