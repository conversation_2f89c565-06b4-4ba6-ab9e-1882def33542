//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "FAULT".
*/	
class FAULT_ACTION extends DataStructure {
	
	static const String TABLE_NAME = "FAULT_ACTION";
	
	// No desc available
	static const String FIELD_FAULT_ID = "FAULT_ID";

	// No desc available
	static const String FIELD_USER_ACTION = "USER_ACTION";
	
    String? _fault_id;
    String? _user_action;
	
	FAULT_ACTION({
			required fault_id,
			user_action}) :
			_fault_id = fault_id,
			_user_action = user_action {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	FAULT_ACTION.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_fault_id = json[FIELD_FAULT_ID]; 
		_user_action = json[FIELD_USER_ACTION]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_FAULT_ID] = _fault_id;
		data[FIELD_USER_ACTION] = _user_action;

		return data;
  }
  
	String? get fault_id => this._fault_id;
	
	set fault_id(String? fault_id) => this._fault_id = fault_id;	

	String? get user_action => this._user_action;
	
	set user_action(String? user_action) => this._user_action = user_action;	
	
}