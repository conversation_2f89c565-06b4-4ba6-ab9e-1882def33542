//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "DOCUMENT".
*/	
class DOCUMENT_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "DOCUMENT_HEADER";
	
	// No desc available
	static const String FIELD_DOC_ID = "DOC_ID";

	// No desc available
	static const String FIELD_FOLDER_ID = "FOLDER_ID";

	// No desc available
	static const String FIELD_TITLE = "TITLE";

	// No desc available
	static const String FIELD_FILE_NAME = "FILE_NAME";

	// No desc available
	static const String FIELD_MIME_TYPE = "MIME_TYPE";

	// No desc available
	static const String FIELD_DOC_TYPE = "DOC_TYPE";

	// No desc available
	static const String FIELD_SOURCE = "SOURCE";

	// No desc available
	static const String FIELD_EXT_DOC_ID = "EXT_DOC_ID";

	// No desc available
	static const String FIELD_THUMBNAIL = "THUMBNAIL";

	// No desc available
	static const String FIELD_CREATED_BY = "CREATED_BY";

	// No desc available
	static const String FIELD_CREATED_ON = "CREATED_ON";

	// No desc available
	static const String FIELD_CHANGED_BY = "CHANGED_BY";

	// No desc available
	static const String FIELD_CHANGED_ON = "CHANGED_ON";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    String? _doc_id;
    int? _folder_id;
    String? _title;
    String? _file_name;
    String? _mime_type;
    String? _doc_type;
    String? _source;
    String? _ext_doc_id;
    String? _thumbnail;
    String? _created_by;
    int? _created_on;
    String? _changed_by;
    int? _changed_on;
    String? _inactive;
	
	DOCUMENT_HEADER({
			required doc_id,
			folder_id,
			title,
			file_name,
			mime_type,
			doc_type,
			source,
			ext_doc_id,
			thumbnail,
			created_by,
			created_on,
			changed_by,
			changed_on,
			inactive}) :
			_doc_id = doc_id,
			_folder_id = folder_id,
			_title = title,
			_file_name = file_name,
			_mime_type = mime_type,
			_doc_type = doc_type,
			_source = source,
			_ext_doc_id = ext_doc_id,
			_thumbnail = thumbnail,
			_created_by = created_by,
			_created_on = created_on,
			_changed_by = changed_by,
			_changed_on = changed_on,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	DOCUMENT_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_doc_id = json[FIELD_DOC_ID]; 
		_folder_id = json[FIELD_FOLDER_ID]; 
		_title = json[FIELD_TITLE]; 
		_file_name = json[FIELD_FILE_NAME]; 
		_mime_type = json[FIELD_MIME_TYPE]; 
		_doc_type = json[FIELD_DOC_TYPE]; 
		_source = json[FIELD_SOURCE]; 
		_ext_doc_id = json[FIELD_EXT_DOC_ID]; 
		_thumbnail = json[FIELD_THUMBNAIL]; 
		_created_by = json[FIELD_CREATED_BY]; 
		_created_on = json[FIELD_CREATED_ON]; 
		_changed_by = json[FIELD_CHANGED_BY]; 
		_changed_on = json[FIELD_CHANGED_ON]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_DOC_ID] = _doc_id;
		data[FIELD_FOLDER_ID] = _folder_id;
		data[FIELD_TITLE] = _title;
		data[FIELD_FILE_NAME] = _file_name;
		data[FIELD_MIME_TYPE] = _mime_type;
		data[FIELD_DOC_TYPE] = _doc_type;
		data[FIELD_SOURCE] = _source;
		data[FIELD_EXT_DOC_ID] = _ext_doc_id;
		data[FIELD_THUMBNAIL] = _thumbnail;
		data[FIELD_CREATED_BY] = _created_by;
		data[FIELD_CREATED_ON] = _created_on;
		data[FIELD_CHANGED_BY] = _changed_by;
		data[FIELD_CHANGED_ON] = _changed_on;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	String? get doc_id => this._doc_id;
	
	set doc_id(String? doc_id) => this._doc_id = doc_id;	

	int? get folder_id => this._folder_id;
	
	set folder_id(int? folder_id) => this._folder_id = folder_id;	

	String? get title => this._title;
	
	set title(String? title) => this._title = title;	

	String? get file_name => this._file_name;
	
	set file_name(String? file_name) => this._file_name = file_name;	

	String? get mime_type => this._mime_type;
	
	set mime_type(String? mime_type) => this._mime_type = mime_type;	

	String? get doc_type => this._doc_type;
	
	set doc_type(String? doc_type) => this._doc_type = doc_type;	

	String? get source => this._source;
	
	set source(String? source) => this._source = source;	

	String? get ext_doc_id => this._ext_doc_id;
	
	set ext_doc_id(String? ext_doc_id) => this._ext_doc_id = ext_doc_id;	

	String? get thumbnail => this._thumbnail;
	
	set thumbnail(String? thumbnail) => this._thumbnail = thumbnail;	

	String? get created_by => this._created_by;
	
	set created_by(String? created_by) => this._created_by = created_by;	

	int? get created_on => this._created_on;
	
	set created_on(int? created_on) => this._created_on = created_on;	

	String? get changed_by => this._changed_by;
	
	set changed_by(String? changed_by) => this._changed_by = changed_by;	

	int? get changed_on => this._changed_on;
	
	set changed_on(int? changed_on) => this._changed_on = changed_on;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}