//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "INSP_PLAN_TYPE".
*/	
class INSP_PLAN_TYPE_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "INSP_PLAN_TYPE_HEADER";
	
	// No desc available
	static const String FIELD_PLAN_TYPE = "PLAN_TYPE";

	// No desc available
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    String? _plan_type;
    String? _description;
    String? _inactive;
	
	INSP_PLAN_TYPE_HEADER({
			required plan_type,
			description,
			inactive}) :
			_plan_type = plan_type,
			_description = description,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	INSP_PLAN_TYPE_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_plan_type = json[FIELD_PLAN_TYPE]; 
		_description = json[FIELD_DESCRIPTION]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_PLAN_TYPE] = _plan_type;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	String? get plan_type => this._plan_type;
	
	set plan_type(String? plan_type) => this._plan_type = plan_type;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}