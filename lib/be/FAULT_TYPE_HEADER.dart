//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "FAULT_TYPE".
*/	
class FAULT_TYPE_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "FAULT_TYPE_HEADER";
	
	// No desc available
	static const String FIELD_FAULT_CODE = "FAULT_CODE";

	// Desxription Of Fault
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    String? _fault_code;
    String? _description;
    String? _inactive;
	
	FAULT_TYPE_HEADER({
			required fault_code,
			description,
			inactive}) :
			_fault_code = fault_code,
			_description = description,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	FAULT_TYPE_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_fault_code = json[FIELD_FAULT_CODE]; 
		_description = json[FIELD_DESCRIPTION]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_FAULT_CODE] = _fault_code;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	String? get fault_code => this._fault_code;
	
	set fault_code(String? fault_code) => this._fault_code = fault_code;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}