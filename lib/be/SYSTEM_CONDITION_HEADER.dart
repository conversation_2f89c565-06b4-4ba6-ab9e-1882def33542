//	Generated using Unvired Modeller - Build Bleeding Edge


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "SYSTEM_CONDITION".
*/	
class SYSTEM_CONDITION_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "SYSTEM_CONDITION_HEADER";
	
	// No desc available
	static const String FIELD_DOMAIN = "DOMAIN";

	// No desc available
	static const String FIELD_SYS_COND = "SYS_COND";

	// No desc available
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// No desc available
	static const String FIELD_COLOR = "COLOR";

	// No desc available
	static const String FIELD_INACTIVE = "INACTIVE";
	
    int? _domain;
    String? _sys_cond;
    String? _description;
    String? _color;
    String? _inactive;
	
	SYSTEM_CONDITION_HEADER({
			required domain,
			required sys_cond,
			description,
			color,
			inactive}) :
			_domain = domain,
			_sys_cond = sys_cond,
			_description = description,
			_color = color,
			_inactive = inactive {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	SYSTEM_CONDITION_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_domain = json[FIELD_DOMAIN]; 
		_sys_cond = json[FIELD_SYS_COND]; 
		_description = json[FIELD_DESCRIPTION]; 
		_color = json[FIELD_COLOR]; 
		_inactive = json[FIELD_INACTIVE]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_DOMAIN] = _domain;
		data[FIELD_SYS_COND] = _sys_cond;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_COLOR] = _color;
		data[FIELD_INACTIVE] = _inactive;

		return data;
  }
  
	int? get domain => this._domain;
	
	set domain(int? domain) => this._domain = domain;	

	String? get sys_cond => this._sys_cond;
	
	set sys_cond(String? sys_cond) => this._sys_cond = sys_cond;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get color => this._color;
	
	set color(String? color) => this._color = color;	

	String? get inactive => this._inactive;
	
	set inactive(String? inactive) => this._inactive = inactive;	
	
}