import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/CILT_EXEC_HEADER.dart';
import 'package:rounds/be/CILT_EXEC_TASK.dart';
import 'package:rounds/be/CILT_TASK.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../be/DOCUMENT_ATTACHMENT.dart';
import '../../be/INSPECTION_TASK.dart';
import '../../be/INSP_EXEC_HEADER.dart';
import '../../be/INSP_EXEC_TASK.dart';
import '../../helpers/db_helper.dart';

final documentHeaderProvider =
    StateNotifierProvider<DocumentHeaderNotifier, List<DOCUMENT_HEADER>>((ref) {
  return DocumentHeaderNotifier();
});

class DocumentHeaderNotifier extends StateNotifier<List<DOCUMENT_HEADER>> {
  DocumentHeaderNotifier() : super([]);

  Future<void> fetchDocumentHeaders() async {
    try {
      List<DOCUMENT_HEADER> documentHeaders =
          await DbHelper.getDocumentHeaders();
      state = documentHeaders;
    } catch (e) {
      Logger.logError(
          'DocumentHeaderNotifier', 'fetchDocumentHeaders', e.toString());
    }
  }

  Future<void> insertDocumentHeaders(DOCUMENT_HEADER documentHeader) async {
    try {
      await AppDatabaseManager().insert(
          DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, documentHeader.toJson()));
    } catch (e) {
      Logger.logError(
          'DocumentHeaderNotifier', 'fetchDocumentHeaders', e.toString());
    }
  }
}

final attachMentUploadProgressProvider = StateNotifierProvider<
    AttachementUploadProgressNotifier, Map<String, double>>(
  (ref) => AttachementUploadProgressNotifier(),
);

class AttachementUploadProgressNotifier
    extends StateNotifier<Map<String, double>> {
  AttachementUploadProgressNotifier() : super({});

  void startUpload(String lid) {
    state = {...state, lid: 0.0};
  }

  void updateProgress(String lid, double progress) {
    state = {...state, lid: progress};
  }

  void finishUpload(String lid) {
    state = {...state, lid: 1.0};
  }
}

final documentAttachmentProvider = StateNotifierProvider<
    DocumentAttachmentNotifier, List<DOCUMENT_ATTACHMENT>>((ref) {
  return DocumentAttachmentNotifier();
});

class DocumentAttachmentNotifier
    extends StateNotifier<List<DOCUMENT_ATTACHMENT>> {
  DocumentAttachmentNotifier() : super([]);

  Future<void> fetchDocumentAttachments() async {
    try {
      List<DOCUMENT_ATTACHMENT> documentAttachments =
          await DbHelper.getDocumentAttachments();
      state = documentAttachments;
    } catch (e) {
      Logger.logError('DocumentAttachmentNotifier', 'fetchDocumentAttachments',
          e.toString());
    }
  }

  Future<void> insertDocumentAttachments(
      DOCUMENT_ATTACHMENT documentAttachment) async {
    try {
      await AppDatabaseManager().insert(DBInputEntity(
          DOCUMENT_ATTACHMENT.TABLE_NAME, documentAttachment.toJson()));
    } catch (e) {
      Logger.logError('DocumentAttachmentNotifier', 'insertDocumentAttachments',
          e.toString());
    }
  }

  Future<void> deleteDocumentAttachments(
      DOCUMENT_ATTACHMENT documentAttachment) async {
    try {
      DOCUMENT_HEADER? header =
          await DbHelper.getDocumentHeaderById(documentAttachment.uid ?? "");

      if (kIsWeb) {
        await DbHelper()
            .deleteAttachmentFromIndexDbForUid(header?.doc_id ?? "");
      }
      await AppDatabaseManager().delete(
          DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, header?.toJson() ?? {}));
      await AppDatabaseManager().delete(DBInputEntity(
          DOCUMENT_ATTACHMENT.TABLE_NAME, documentAttachment.toJson()));

      state.removeWhere((element) => element.fid == documentAttachment.fid);
      state = List.from(state);
    } catch (e) {
      Logger.logError('DocumentAttachmentNotifier', 'deleteDocumentAttachments',
          e.toString());
    }
  }
}

final ciltTaskExecDocumentHeaderProvider = StateNotifierProvider<
    TaskExecDocumentHeaderNotifier, Map<int, List<DOCUMENT_HEADER>>>((ref) {
  return TaskExecDocumentHeaderNotifier();
});

class TaskExecDocumentHeaderNotifier
    extends StateNotifier<Map<int, List<DOCUMENT_HEADER>>> {
  TaskExecDocumentHeaderNotifier() : super({});

  Future<void> fetchTaskDocumentHeaders(CILT_EXEC_TASK execTaskHeader,
      CILT_EXEC_HEADER header, CILT_TASK ciltTask) async {
    try {
      List<DOCUMENT_HEADER> documentHeaders =
          await DbHelper.getCiltTaskDocHeaders(ciltTask);

      List<DOCUMENT_HEADER> documentHeaders2 =
          await DbHelper.getCiltExecDocByCiltidAndTaskId(
              execTaskHeader, header);

      documentHeaders.addAll(documentHeaders2);

      state = {...state, execTaskHeader.cilt_task_id!: documentHeaders};
    } catch (e) {
      Logger.logError('TaskDocumentHeaderMapNotifier',
          'fetchTaskDocumentHeaders', e.toString());
    }
  }

  List<DOCUMENT_HEADER>? getDocumentHeadersForTask(CILT_EXEC_TASK task) {
    return state[task.cilt_task_id];
  }
}

final inspectionTaskExecDocumentHeaderProvider = StateNotifierProvider<
    InspectionTaskExecDocumentHeaderNotifier,
    Map<int, List<DOCUMENT_HEADER>>>((ref) {
  return InspectionTaskExecDocumentHeaderNotifier();
});

class InspectionTaskExecDocumentHeaderNotifier
    extends StateNotifier<Map<int, List<DOCUMENT_HEADER>>> {
  InspectionTaskExecDocumentHeaderNotifier() : super({});

  Future<void> fetchInspectionTaskDocumentHeaders(INSP_EXEC_TASK execTaskHeader,
      INSP_EXEC_HEADER header, INSPECTION_TASK inspTask) async {
    try {
      List<DOCUMENT_HEADER> documentHeaders =
          await DbHelper.getInspectionTaskDocHeaders(inspTask);

      List<DOCUMENT_HEADER> documentHeaders2 =
          await DbHelper.getInspectionExecDocByInspidAndTaskId(
              execTaskHeader, header);

      documentHeaders.addAll(documentHeaders2);

      state = {...state, execTaskHeader.insp_task_id!: documentHeaders};
    } catch (e) {
      Logger.logError('InspectionTaskExecDocumentHeaderNotifier',
          'fetchInspectionTaskDocumentHeaders', e.toString());
    }
  }

  List<DOCUMENT_HEADER>? getDocumentHeadersForInspectionTask(
      INSP_EXEC_TASK task) {
    return state[task.insp_task_id];
  }
}

final ciltTaskDocumentHeaders = StateNotifierProvider<TaskDocumentsNotifier,
    Map<int, List<DOCUMENT_HEADER>>>((ref) {
  return TaskDocumentsNotifier();
});

class TaskDocumentsNotifier
    extends StateNotifier<Map<int, List<DOCUMENT_HEADER>>> {
  TaskDocumentsNotifier() : super({});

  Future<void> fetchTaskDocumentHeaders(
      CILT_EXEC_TASK ciltExecTask, CILT_TASK ciltTask) async {
    try {
      //fetching task level doc from planneer
      List<DOCUMENT_HEADER> documentHeaders =
          await DbHelper.getCiltTaskDocHeaders(ciltTask);
      state = {...state, ciltExecTask.task_id!: documentHeaders};
    } catch (e) {
      Logger.logError('TaskDocumentHeaderMapNotifier',
          'fetchTaskDocumentHeaders', e.toString());
    }
  }

  List<DOCUMENT_HEADER>? getDocumentHeadersForTask(CILT_TASK task) {
    return state[task];
  }
}

final inspectionTaskDocumentHeaders = StateNotifierProvider<
    InspectionTaskDocumentsNotifier, Map<int, List<DOCUMENT_HEADER>>>((ref) {
  return InspectionTaskDocumentsNotifier();
});

class InspectionTaskDocumentsNotifier
    extends StateNotifier<Map<int, List<DOCUMENT_HEADER>>> {
  InspectionTaskDocumentsNotifier() : super({});

  Future<void> fetchInspectionTaskDocumentHeaders(
      INSP_EXEC_TASK inspExecTask, INSPECTION_TASK inspTask) async {
    try {
      List<DOCUMENT_HEADER> documentHeaders =
          await DbHelper.getInspectionTaskDocHeaders(inspTask);
      state = {...state, inspExecTask.task_id!: documentHeaders};
    } catch (e) {
      Logger.logError('InspectionTaskDocumentsNotifier',
          'fetchInspectionTaskDocumentHeaders', e.toString());
    }
  }

  List<DOCUMENT_HEADER>? getDocumentHeadersForTask(CILT_TASK task) {
    return state[task];
  }
}
