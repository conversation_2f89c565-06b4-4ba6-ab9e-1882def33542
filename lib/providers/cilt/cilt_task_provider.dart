import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/CILT_TASK.dart';

final ciltTaskProvider =
    StateNotifierProvider<CiltTaskNotifier, CILT_TASK>((ref) {
  return CiltTaskNotifier();
});

class CiltTaskNotifier extends StateNotifier<CILT_TASK> {
  CiltTaskNotifier() : super(CILT_TASK(plan_id: 0, section_id: 0, task_id: 0));

  void getCiltTask(CILT_TASK task) async {
    try {
      state = task;
    } catch (e) {
      Logger.logError('CiltTaskNotifier', 'getCiltTask', e.toString());
    }
  }
}
