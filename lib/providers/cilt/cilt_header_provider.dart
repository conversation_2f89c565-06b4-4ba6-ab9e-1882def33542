import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/CILT_EXEC_HEADER.dart';
import 'package:rounds/be/CILT_EXEC_TASK.dart';
import 'package:rounds/be/CILT_PLAN_HEADER.dart';
import 'package:rounds/be/CILT_TASK.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/providers/user_provider.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/constants.dart';
import 'package:rounds/widgets/chart_widget.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../be/INSP_EXEC_HEADER.dart';
import '../../be/INSP_EXEC_TASK.dart';
import '../../utils/app_constants.dart';
import '../fault/fault_type_provider.dart';

final ciltHeaderListProvider =
    StateNotifierProvider<CiltHeaderListNotifier, List<CILT_EXEC_HEADER>>(
        (ref) {
  return CiltHeaderListNotifier();
});

class CiltHeaderListNotifier extends StateNotifier<List<CILT_EXEC_HEADER>> {
  CiltHeaderListNotifier() : super([]);

  Future<void> fetchCiltListHeaders(String plantId, List<String> plantSec,
      String shift, WidgetRef ref) async {
    try {
      List<CILT_EXEC_HEADER> ciltHeaders =
          await DbHelper.getCiltExecHeaderList(plantId, plantSec, shift);
      state = ciltHeaders;
      ref.read(filteredCiltProvider.notifier).setFilterData(ciltHeaders);
      // Update the state of filteredCiltProvider without using ref
    } catch (e) {
      Logger.logError(
          'CiltHeaderListNotifier', 'fetchCiltListHeaders', e.toString());
    }
  }

  Future<void> filter(String plantId, List<String> plantSec, String shift,
      String searchString, ref) async {
    var priorityNotifier = ref.read(priorityListProvider.notifier);
    List<CILT_EXEC_HEADER> filteredData = [];
    final regex = RegExp(searchString.toLowerCase());
    try {
      List<CILT_PLAN_HEADER> ciltPlans = await DbHelper.getCiltPlanListHeader();

      List<CILT_PLAN_HEADER> filteredPlans = ciltPlans
          .where((element) => regex.hasMatch(element.title!.toLowerCase()))
          .toList();

      List<CILT_EXEC_HEADER> ciltHeaders =
          await DbHelper.getCiltExecHeaderList(plantId, plantSec, shift);

      filteredData = ciltHeaders.where((header) {
        return filteredPlans.any((plan) => plan.plan_id == header.plan_id);
      }).toList();

      List<USER_HEADER> users = await DbHelper.searchUserByName(searchString);

      var tempfilteredData = ciltHeaders.where((element) {
        final priorityText =
            priorityNotifier.fetchPriorityCode(element.priority!);
        return regex.hasMatch(element.status!.toLowerCase()) ||
            regex.hasMatch(priorityText.toLowerCase()) ||
            users.any((user) => user.user_id == element.assigned_to) ||
            regex.hasMatch(element.assigned_to ?? "");
      }).toList();

      for (var element in tempfilteredData) {
        if (!filteredData.any((header) => header.lid == element.lid)) {
          filteredData.add(element);
        }
      }

      state = filteredData;
    } catch (e) {
      Logger.logError(
          'CiltHeaderListNotifier', 'fetchCiltListHeaders', e.toString());
    }
  }

  Future<void> deleteCiltListHeaders(CILT_EXEC_HEADER header) async {
    try {
      await AppDatabaseManager()
          .delete(DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, header.toJson()));
      state = state.where((item) => item.plan_id != header.plan_id).toList();
    } catch (e) {
      // Handle errors
      Logger.logError(
          'CiltHeaderListNotifier', 'deleteCiltListHeaders', e.toString());
    }
  }

  Future<void> insertCiltListHeaders(CILT_EXEC_HEADER header) async {
    try {
      await AppDatabaseManager()
          .insert(DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, header.toJson()));
    } catch (e) {
      // Handle errors
      Logger.logError(
          'CiltHeaderListNotifier', 'insertCiltListHeaders', e.toString());
    }
  }

  List<RoundsPieChartData> fetchRoundsPieChartData(String selectedDate) {
    List<CILT_EXEC_HEADER> ciltHeader = state
        .where((element) =>
            element.start_on == int.parse(selectedDate.replaceAll('-', '')))
        .toList();

    int completed = 0;
    int accepted = 0;
    int rejected = 0;
    int assigned = 0;
    int unassigned = 0;

    for (var header in ciltHeader) {
      final status = header.status;

      if (status == AppConstants.STATE_COMPLETED) {
        completed++;
      } else if (status == AppConstants.STATE_UNASSIGNED) {
        unassigned++;
      } else if (status == AppConstants.STATE_ASSIGNED) {
        assigned++;
      } else if (status == AppConstants.STATE_ACCEPTED) {
        accepted++;
      } else if (status == AppConstants.STATE_REJECTED) {
        rejected++;
      }
    }

    return [
      RoundsPieChartData(
        title: 'Completed',
        value: completed.toDouble(),
        color: AppColors.completedChartColor,
      ),
      RoundsPieChartData(
        title: 'Assigned',
        value: assigned.toDouble(),
        color: AppColors.assignedChartColor,
      ),
      RoundsPieChartData(
        title: 'Unassigned',
        value: unassigned.toDouble(),
        color: AppColors.unAssignedChartColor,
      ),
      RoundsPieChartData(
        title: 'Accepted',
        value: accepted.toDouble(),
        color: AppColors.acceptedChartColor,
      ),
      RoundsPieChartData(
        title: 'Rejected',
        value: rejected.toDouble(),
        color: AppColors.rejectedChartColor,
      ),
    ];
  }

  void updateCiltExecHeader(CILT_EXEC_HEADER updatedHeader) {
    int index = state.indexWhere((element) => element.lid == updatedHeader.lid);
    if (index != -1) {
      final updatedList = List<CILT_EXEC_HEADER>.from(state);
      updatedList[index] = updatedHeader;
      state = updatedList;
    }
  }
}

final filteredCiltProvider =
    StateNotifierProvider<FilteredCltHeaderNotifier, List<CILT_EXEC_HEADER>>(
        (ref) {
  return FilteredCltHeaderNotifier();
});

class FilteredCltHeaderNotifier extends StateNotifier<List<CILT_EXEC_HEADER>> {
  FilteredCltHeaderNotifier() : super([]);

  void setFilterData(List<CILT_EXEC_HEADER> filteredList) {
    state = List.from(filteredList);
  }

  Future<void> filter(String plantId, List<String> plantSec, String shift,
      String searchString, WidgetRef ref) async {
    var priorityNotifier = ref.read(priorityListProvider.notifier);
    List<CILT_EXEC_HEADER> filteredData = [];

    final regex = RegExp(searchString.toLowerCase());
    try {
      final role = ref.watch(roleProvider);
      var isAssignAllow = UIHelper.isAssign(role!.cilt!);

      List<CILT_PLAN_HEADER> ciltPlans = await DbHelper.getCiltPlanListHeader();

      List<CILT_PLAN_HEADER> filteredPlans = ciltPlans
          .where((element) => regex.hasMatch(element.title!.toLowerCase()))
          .toList();

      List<CILT_EXEC_HEADER> ciltHeaders =
          await DbHelper.getCiltExecHeaderList(plantId, plantSec, shift);

      filteredData = ciltHeaders.where((header) {
        return filteredPlans.any((plan) => plan.plan_id == header.plan_id);
      }).toList();

      List<USER_HEADER> users = await DbHelper.searchUserByName(searchString);

      var tempfilteredData = ciltHeaders.where((element) {
        final priorityText =
            priorityNotifier.fetchPriorityCode(element.priority!);
        return regex.hasMatch(element.status!.toLowerCase()) ||
            regex.hasMatch(priorityText.toLowerCase()) ||
            users.any((user) => user.user_id == element.assigned_to) ||
            regex.hasMatch(element.assigned_to ?? "");
      }).toList();

      for (var element in tempfilteredData) {
        if (!filteredData.any((header) => header.lid == element.lid)) {
          filteredData.add(element);
        }
      }

      if (!isAssignAllow) {
        filteredData = filteredData
            .where((element) =>
                element.assigned_to == ref.read(userProvider)?.user_id)
            .toList();
      }

      state = filteredData;
    } catch (e) {
      Logger.logError(
          'CiltHeaderListNotifier', 'fetchCiltListHeaders', e.toString());
    }
  }

  void updateCiltExecHeader(CILT_EXEC_HEADER updatedHeader) {
    int index = state.indexWhere((element) => element.lid == updatedHeader.lid);
    if (index != -1) {
      final updatedList = List<CILT_EXEC_HEADER>.from(state);
      updatedList[index] = updatedHeader;
      state = updatedList;
    }
  }
}

final ciltHeaderProvider =
    StateNotifierProvider<CiltHeaderNotifier, CILT_EXEC_HEADER>((ref) {
  return CiltHeaderNotifier();
});

class CiltHeaderNotifier extends StateNotifier<CILT_EXEC_HEADER> {
  CiltHeaderNotifier() : super(CILT_EXEC_HEADER(cilt_id: 0));

  Future<void> selectedCiltHeaders(CILT_EXEC_HEADER cilt_exec_header) async {
    try {
      CILT_EXEC_HEADER? ciltHeaders =
          await DbHelper.getCiltExecHeader(cilt_exec_header);
      if (ciltHeaders != null) {
        state = ciltHeaders;
      }
    } catch (e) {
      // Handle errors
      Logger.logError('CiltHeaderNotifier', 'fetchCiltHeaders', e.toString());
    }
  }
}

/*final inspectionHeaderProvider =
    StateNotifierProvider<InspectionHeaderNotifier, INSP_EXEC_HEADER>((ref) {
  return InspectionHeaderNotifier();
});

class InspectionHeaderNotifier extends StateNotifier<INSP_EXEC_HEADER> {
  InspectionHeaderNotifier() : super(INSP_EXEC_HEADER(insp_id: 0));

  Future<void> fetchInspectionHeaders(String planId) async {
    try {
      INSP_EXEC_HEADER? inspectionHeaders =
          await DbHelper.getInspectionExecHeader(planId);
      if (inspectionHeaders != null) {
        state = inspectionHeaders;
      }
    } catch (e) {
      Logger.logError(
          'InspectionHeaderNotifier', 'fetchInspectionHeaders', e.toString());
    }
  }
}*/

final ciltTasksProvider =
    StateNotifierProvider<CiltTasksNotifier, Map<int, List<CILT_TASK>>>((ref) {
  return CiltTasksNotifier();
});

class CiltTasksNotifier extends StateNotifier<Map<int, List<CILT_TASK>>> {
  CiltTasksNotifier() : super({});

  Future<void> getCiltTasks(int planId) async {
    try {
      List<CILT_TASK> ciltTasks =
          await DbHelper.getNumberOfCiltTasks(planId.toString());
      // // tasksMap[planId] = ciltTasks;
      state = {...state, planId: ciltTasks};
    } catch (e) {
      Logger.logError('CiltTasksMapNotifier', 'getCiltTasksMap', e.toString());
    }
  }

  Future<void> fetchTasksForAllHeaders(List<CILT_EXEC_HEADER> headers) async {
    try {
      Map<int, List<CILT_TASK>> tasksMap = {};

      for (CILT_EXEC_HEADER header in headers) {
        List<CILT_TASK> ciltTasks =
            await DbHelper.getNumberOfCiltTasks(header.plan_id.toString());

        tasksMap[header.plan_id!] = ciltTasks;
      }

      state = tasksMap;
    } catch (e) {
      Logger.logError(
          'CiltTasksNotifier', 'fetchTasksForAllHeaders', e.toString());
    }
  }
}

final ciltExecuteProvider =
    StateNotifierProvider<CiltExecuteNotifier, Map<int, List<CILT_EXEC_TASK>>>(
        (ref) {
  return CiltExecuteNotifier();
});

class CiltExecuteNotifier
    extends StateNotifier<Map<int, List<CILT_EXEC_TASK>>> {
  CiltExecuteNotifier() : super({});

  Future<void> getCiltExecute(CILT_EXEC_HEADER headers) async {
    try {
      List<CILT_EXEC_TASK> executeHeaders = [];
      List execute = await AppDatabaseManager()
          .select(DBInputEntity(CILT_EXEC_TASK.TABLE_NAME, {}));
      for (Map<String, dynamic> data in execute) {
        CILT_EXEC_TASK exe = CILT_EXEC_TASK.fromJson(data);
        if (exe.cilt_id == headers.cilt_id &&
            exe.status == AppConstants.STATE_TASK_COMP) {
          executeHeaders.add(exe);
        }
      }
      state = {...state, headers.cilt_id!: executeHeaders};
    } catch (e) {
      Logger.logError('CiltExecuteNotifier', 'getCiltExecute', e.toString());
    }
  }

  Future<void> getAllExecTasksForAllExecHeaders(
      List<CILT_EXEC_HEADER> headers) async {
    try {
      Map<int, List<CILT_EXEC_TASK>> cilt_exec_task_map = {};

      for (CILT_EXEC_HEADER header in headers) {
        List<CILT_EXEC_TASK> executeHeaders = [];
        List execute = await AppDatabaseManager()
            .select(DBInputEntity(CILT_EXEC_TASK.TABLE_NAME, {}));

        for (Map<String, dynamic> data in execute) {
          CILT_EXEC_TASK exe = CILT_EXEC_TASK.fromJson(data);
          if (exe.cilt_id == header.cilt_id &&
              exe.status == AppConstants.STATE_TASK_COMP) {
            executeHeaders.add(exe);
          }
        }
        cilt_exec_task_map[header.cilt_id!] = executeHeaders;
      }

      state = cilt_exec_task_map;
    } catch (e) {
      Logger.logError(
          'CiltTasksNotifier', 'fetchTasksForAllHeaders', e.toString());
    }
  }
}

final ciltExecuteTaskListProvider =
    StateNotifierProvider<CiltExecuteTaskListNotifier, List<CILT_EXEC_TASK>>(
        (ref) {
  return CiltExecuteTaskListNotifier();
});

class CiltExecuteTaskListNotifier extends StateNotifier<List<CILT_EXEC_TASK>> {
  CiltExecuteTaskListNotifier() : super([]);

  Future<void> getCiltExecuteTaskList() async {
    try {
      List<CILT_EXEC_TASK> executeHeaders =
          await DbHelper.getAllCiltExecTaskList();

      state = executeHeaders;
    } catch (e) {
      Logger.logError('CiltExecuteTaskListNotifier', 'getCiltExecuteTaskList',
          e.toString());
    }
  }

  List<CILT_EXEC_TASK> findAllCiltExecOfCilt(
      CILT_EXEC_HEADER cilt_exec_header) {
    return state
        .where((element) => element.cilt_id == cilt_exec_header.cilt_id)
        .toList();
  }

  CILT_EXEC_TASK? getciltExecTaskHeaderByTask(
      CILT_TASK task, CILT_EXEC_HEADER ciltHeader) {
    final execTasksToDisplayData = state
        .where((element) =>
            element.task_id == task.task_id &&
            element.section_id == task.section_id &&
            element.cilt_id == ciltHeader.cilt_id)
        .toList();
    return !execTasksToDisplayData.isEmpty ? execTasksToDisplayData[0] : null;
  }

  bool isSectionCompleted(
      {required List<CILT_TASK> ciltTask,
      required int section_id,
      required CILT_EXEC_HEADER ciltHeader}) {
    var tasks =
        ciltTask.where((element) => element.section_id == section_id).toList();

    var execTasksInSection = state
        .where((execTask) => tasks.any((task) =>
            task.task_id == execTask.task_id &&
            task.section_id == execTask.section_id &&
            execTask.cilt_id == ciltHeader.cilt_id))
        .toList();

    if (ciltTask.isEmpty || execTasksInSection.isEmpty) {
      return false;
    }

    return !execTasksInSection.every((task) =>
        task.status?.toLowerCase() ==
        AppConstants.STATE_TASK_COMP.toLowerCase());
  }

  void updateCiltExexTask(CILT_EXEC_TASK updatedExecTask) {
    int index =
        state.indexWhere((element) => element.lid == updatedExecTask.lid);
    final updatedList = List<CILT_EXEC_TASK>.from(state);
    if (index != -1) {
      updatedList[index] = updatedExecTask;
      state = updatedList;
    }
  }
}

/*final inspExecuteTaskListProvider =
    StateNotifierProvider<InspExecuteTaskListNotifier, List<INSP_EXEC_TASK>>(
        (ref) {
  return InspExecuteTaskListNotifier();
});

class InspExecuteTaskListNotifier extends StateNotifier<List<INSP_EXEC_TASK>> {
  InspExecuteTaskListNotifier() : super([]);

  Future<void> getInspExecuteTaskList() async {
    try {
      List<INSP_EXEC_TASK> executeHeaders =
          await DbHelper.getAllInspExecTaskList();
      state = executeHeaders;
    } catch (e) {
      Logger.logError('InspExecuteTaskListNotifier', 'getInspExecuteTaskList',
          e.toString());
    }
  }
}*/

final getCiltExecuteProvider =
    StateNotifierProvider<GetCiltExecuteNotifier, CILT_EXEC_TASK>((ref) {
  return GetCiltExecuteNotifier();
});

class GetCiltExecuteNotifier extends StateNotifier<CILT_EXEC_TASK> {
  GetCiltExecuteNotifier()
      : super(CILT_EXEC_TASK(
            cilt_id: 0, cilt_task_id: 0, task_no: 0, section_id: 0));

  Future<void> getCiltExecuteList(CILT_TASK task) async {
    try {
      String query =
          '${CILT_EXEC_HEADER.FIELD_PLAN_ID}= ${task.plan_id.toString()}';
      List header = await AppDatabaseManager().select(
          DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, {})
            ..setWhereClause(query));
      if (header.isNotEmpty) {
        for (Map<String, dynamic> data in header) {
          CILT_EXEC_HEADER ciltheader = CILT_EXEC_HEADER.fromJson(data);
          String query1 =
              '${CILT_EXEC_HEADER.FIELD_CILT_ID}= ${ciltheader.cilt_id.toString()}';
          List execute = await AppDatabaseManager().select(
              DBInputEntity(CILT_EXEC_TASK.TABLE_NAME, {})
                ..setWhereClause(query1));
          if (execute.isNotEmpty) {
            for (Map<String, dynamic> exec in execute) {
              CILT_EXEC_TASK execHeader = CILT_EXEC_TASK.fromJson(exec);
              if (execHeader.task_id.toString() == task.task_id.toString()) {
                state = execHeader;
                break;
              }
            }
          }
        }
      }
    } catch (e) {
      Logger.logError(
          'GetCiltExecuteNotifier', 'getCiltExecuteList', e.toString());
    }
  }
}
