import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/CILT_EXEC_HEADER.dart';
import 'package:rounds/be/CILT_PLAN_HEADER.dart';
import 'package:rounds/be/CILT_SECTION.dart';
import 'package:rounds/be/CILT_TASK.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:rounds/be/INSPECTION_PLAN_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';
import 'package:rounds/providers/inspection/inspection_header_provider.dart';
import 'package:rounds/utils/app_constants.dart';

import '../../be/CILT_EXEC_SEC.dart';
import '../../be/CILT_EXEC_TASK.dart';

final ciltPlanListHeaderProvider =
    StateNotifierProvider<CiltPlanListHeaderNotifier, List<CILT_PLAN_HEADER>>(
        (ref) {
  return CiltPlanListHeaderNotifier();
});

final ciltPlanSectionListHeaderProvider = StateNotifierProvider<
    CiltPlanSectionListHeaderNotifier, List<CILT_SECTION>>((ref) {
  return CiltPlanSectionListHeaderNotifier();
});

final ciltPlanTaskListHeaderProvider = StateNotifierProvider.family<
    CiltPlanTaskListHeaderNotifier, CiltPlanTaskListState, CILT_SECTION>(
  (ref, section) =>
      CiltPlanTaskListHeaderNotifier()..fetchCiltPlanTaskListHeaders(section),
);

final ciltPlanHeaderProvider =
    StateNotifierProvider<CiltPlanHeaderNotifier, CILT_PLAN_HEADER>((ref) {
  return CiltPlanHeaderNotifier();
});

class CiltPlanListHeaderNotifier extends StateNotifier<List<CILT_PLAN_HEADER>> {
  CiltPlanListHeaderNotifier() : super([]);

  Future<void> fetchCiltPlanListHeaders() async {
    try {
      List<CILT_PLAN_HEADER> ciltPlanHeaders =
          await DbHelper.getCiltPlanListHeader();
      state = ciltPlanHeaders;
    } catch (e) {
      Logger.logError('CiltPlanListHeaderNotifier', 'fetchCiltPlanListHeaders',
          e.toString());
    }
  }
}

final ciltPlanAndTaskDocProvider = StateNotifierProvider<
    CiltPlanAndTaskDocNotifier, Map<int, List<DOCUMENT_HEADER>>>((ref) {
  return CiltPlanAndTaskDocNotifier();
});

class CiltPlanAndTaskDocNotifier
    extends StateNotifier<Map<int, List<DOCUMENT_HEADER>>> {
  CiltPlanAndTaskDocNotifier() : super({});

  Future<void> fetchCiltPlanAndTaskDoc(CILT_PLAN_HEADER ciltPlan) async {
    List<DOCUMENT_HEADER> planDocs = await DbHelper.getPlanDocHeader(ciltPlan);
    state = {...state, ciltPlan.plan_id!: planDocs};
  }

  List<DOCUMENT_HEADER> getCiltPlanDocs(int planid) {
    return state[planid] ?? [];
  }
}

class CiltPlanSectionListHeaderNotifier
    extends StateNotifier<List<CILT_SECTION>> {
  // Maintain a master list to keep the original data
  List<CILT_SECTION> _masterList = [];

  CiltPlanSectionListHeaderNotifier() : super([]);

  Future<void> fetchCiltPlanSectionListHeaders(String planId) async {
    try {
      List<CILT_SECTION> ciltPlanSectionHeaders =
          await DbHelper.getCiltSectionPlanListHeader(planId);
      _masterList =
          List<CILT_SECTION>.from(ciltPlanSectionHeaders); // Save original
      state = ciltPlanSectionHeaders;
    } catch (e) {
      Logger.logError('CiltPlanSectionListHeaderNotifier',
          'fetchCiltPlanSectionListHeaders', e.toString());
    }
  }

  List<CILT_SECTION> filter(List<CILT_TASK> ciltTasks) {
    // Always filter from the master list
    state = _masterList.where((section) {
      return ciltTasks.any((task) => task.section_id == section.section_id);
    }).toList();
    return state;
  }

  // Optionally, add a method to reset to the master/original list
  void reset() {
    state = List<CILT_SECTION>.from(_masterList);
  }
}

class CiltPlanTaskListHeaderNotifier
    extends StateNotifier<CiltPlanTaskListState> {
  CiltPlanTaskListHeaderNotifier() : super(CiltPlanTaskListState());

  Future<void> fetchCiltPlanTaskListHeaders(CILT_SECTION section) async {
    try {
      state = CiltPlanTaskListState(isLoading: true);
      List<CILT_TASK> ciltPlanTaskHeaders =
          await DbHelper.getCiltTaskPlanListHeader(
              section.plan_id!.toString(), section.section_id!.toString());
      state = CiltPlanTaskListState(data: ciltPlanTaskHeaders);
    } catch (e) {
      Logger.logError('CiltPlanTaskListHeaderNotifier',
          'fetchCiltPlanTaskListHeaders', e.toString());
    }
  }

  void updateSearchQuery(String searchQuery) {
    state = state.copyWith(searchQuery: searchQuery);
    filterOrderList(
      searchString: searchQuery,
      ciltPlanTaskHeaders: state.data!,
    );
  }

  void filterOrderList({
    required String searchString,
    required List<CILT_TASK> ciltPlanTaskHeaders,
  }) {
    if (searchString.isNotEmpty) {
      if (searchString.contains('*')) {
        final searchPattern = searchString.replaceAll('*', '.*').toLowerCase();
        final regex = RegExp(searchPattern);
        state = CiltPlanTaskListState(
          data: ciltPlanTaskHeaders
              .where((element) =>
                  regex.hasMatch(element.activity!.toLowerCase()) ||
                  regex.hasMatch(element.machine_part!.toLowerCase()) ||
                  regex.hasMatch(element.standard!.toLowerCase()) ||
                  regex.hasMatch(element.method!.toLowerCase()) ||
                  regex.hasMatch(element.tool!.toLowerCase()) ||
                  regex.hasMatch(element.uom!.toLowerCase()) ||
                  regex.hasMatch(element.inactive!.toLowerCase()))
              .toList(),
        );
      } else {
        final searchKeywords = searchString.toLowerCase().split(' ');
        state = CiltPlanTaskListState(
          data: ciltPlanTaskHeaders.where((element) {
            return searchKeywords.every((keyword) =>
                element.activity!.toLowerCase().contains(keyword) ||
                element.machine_part!.toLowerCase().contains(keyword) ||
                element.standard!.toLowerCase().contains(keyword) ||
                element.method!.toLowerCase().contains(keyword) ||
                element.tool!.toLowerCase().contains(keyword) ||
                element.uom!.toLowerCase().contains(keyword) ||
                element.inactive!.toLowerCase().contains(keyword));
          }).toList(),
        );
      }
    } else {
      state = CiltPlanTaskListState(data: ciltPlanTaskHeaders);
    }
  }
}

class CiltPlanTaskListState {
  final bool isLoading;
  final List<CILT_TASK>? data;
  final String? error;
  final String searchQuery;

  CiltPlanTaskListState({
    this.isLoading = false,
    this.searchQuery = '',
    this.data,
    this.error,
  });
  CiltPlanTaskListState copyWith({
    bool? isLoading,
    List<CILT_TASK>? data,
    String? searchQuery,
  }) {
    return CiltPlanTaskListState(
      isLoading: isLoading ?? this.isLoading,
      data: data ?? this.data,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

class CILTToggleStateNotifier extends StateNotifier<bool> {
  CILTToggleStateNotifier() : super(false);

  void toggle() {
    state = !state;
  }

  void setToggleState(bool newState) {
    state = newState;
  }
}

final ciltToggleStateProvider =
    StateNotifierProvider<CILTToggleStateNotifier, bool>((ref) {
  return CILTToggleStateNotifier();
});

final ciltTaskNotifier =
    StateNotifierProvider<CiltTaskListNotifier, List<CILT_TASK>>((ref) {
  return CiltTaskListNotifier();
});

class CiltTaskListNotifier extends StateNotifier<List<CILT_TASK>> {
  List<CILT_TASK> searchedTaskList = [];
  bool isSearch = false;
  CiltTaskListNotifier() : super([]);

  filter(String planId, String value, WidgetRef ref) async {
    RegExp data = RegExp(value.toLowerCase());
    isSearch = value.isNotEmpty ? true : false;
    try {
      List<CILT_TASK> ciltPlanTaskHeaders =
          await DbHelper.getCiltTaskPlanListHeaderByPlanId(planId);

      List<CILT_TASK> taskList = ciltPlanTaskHeaders
          .where((element) =>
              data.hasMatch(element.activity?.toLowerCase() ?? "") ||
              data.hasMatch(element.machine_part?.toLowerCase() ?? "") ||
              data.hasMatch(element.method?.toLowerCase() ?? "") ||
              data.hasMatch(element.standard?.toLowerCase() ?? "") ||
              data.hasMatch(element.tool?.toLowerCase() ?? ""))
          .toList();
      ref.read(ciltPlanSectionListHeaderProvider.notifier).filter(taskList);
      if (value.isEmpty) {
        state = ciltPlanTaskHeaders;
        ref
            .read(ciltPlanSectionListHeaderProvider.notifier)
            .fetchCiltPlanSectionListHeaders(planId);
      } else {
        state = taskList;
      }
    } catch (e) {
      Logger.logError('CiltTaskListNotifier', 'filter', e.toString());
    }
  }

  void updateTask(CILT_TASK updatedTask) {
    int index = state.indexWhere((task) => task.lid == updatedTask.lid);
    if (index != -1) {
      state[index] = updatedTask;
      final updatedList = List<CILT_TASK>.from(state);
      state = updatedList;
    }
  }

  Future<void> reset(String planId, WidgetRef ref) async {
    List<CILT_TASK> ciltTask =
        await DbHelper.getCiltTaskPlanListHeaderByPlanId(planId);
    ref
        .read(ciltPlanSectionListHeaderProvider.notifier)
        .fetchCiltPlanSectionListHeaders(planId);
    state = ciltTask;
  }

  Future<void> fetchIncompleteTasks(
      String planId, CILT_EXEC_HEADER header, WidgetRef ref) async {
    try {
      List<CILT_TASK> inCompletedTasks =
          await DbHelper.getIncompleteCiltTasks(header);
      ref
          .read(ciltPlanSectionListHeaderProvider.notifier)
          .filter(inCompletedTasks);
      state = inCompletedTasks;
    } catch (e) {
      Logger.logError(
          'CiltTaskListNotifier', 'fetchIncompleteTasks', e.toString());
    }
  }
}

class CiltPlanHeaderNotifier extends StateNotifier<CILT_PLAN_HEADER> {
  CiltPlanHeaderNotifier() : super(CILT_PLAN_HEADER(plan_id: 0));

  Future<void> selectedCiltPlanHeaders(CILT_PLAN_HEADER? ciltPlanHeader) async {
    try {
      if (ciltPlanHeader != null) {
        state = ciltPlanHeader;
      }
    } catch (e) {
      Logger.logError(
          'CiltPlanHeaderNotifier', 'selectedCiltPlanHeaders', e.toString());
    }
  }
}

final ciltCompletedProvider =
    StateNotifierProvider<CiltPlanCompletedNotifier, bool>((ref) {
  return CiltPlanCompletedNotifier();
});

class CiltPlanCompletedNotifier extends StateNotifier<bool> {
  CiltPlanCompletedNotifier() : super(false);

  Future<void> ciltCompleted(CILT_EXEC_HEADER cilt_exec_header) async {
    try {
      CILT_EXEC_HEADER? header =
          await DbHelper.getCiltExecHeader(cilt_exec_header);
      if (header != null) {
        List<CILT_EXEC_TASK> ciltExecHeaders =
            await DbHelper.getCiltExecHeaderListByCiltIdAndStatus(
                header.cilt_id.toString(),
                status: AppConstants.STATE_TASK_COMP);
        if (ciltExecHeaders.isNotEmpty) {
          state = false;
        } else {
          state = true;
        }
      }
    } catch (e) {
      Logger.logError(
          'CiltPlanCompletedNotifier', 'ciltCompleted', e.toString());
    }
  }
}

final ciltExecSectionListHeaderProvider = StateNotifierProvider<
    CiltExecSectionListHeaderNotifier, List<CILT_EXEC_SEC>>((ref) {
  return CiltExecSectionListHeaderNotifier();
});

class CiltExecSectionListHeaderNotifier
    extends StateNotifier<List<CILT_EXEC_SEC>> {
  CiltExecSectionListHeaderNotifier() : super([]);

  Future<void> fetchCiltExecSectionListHeaders(String cilt_id) async {
    try {
      List<CILT_EXEC_SEC> ciltExecSectionHeaders =
          await DbHelper.getCiltExecSecListByCiltId(cilt_id);
      state = ciltExecSectionHeaders;
    } catch (e) {
      Logger.logError('CiltPlanSectionListHeaderNotifier',
          'fetchCiltPlanSectionListHeaders', e.toString());
    }
  }
}
