import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/helpers/db_helper.dart';

final ciltPlanHeaderTitleProvider =
    StateNotifierProvider<CiltPlanHeaderTitleNotifier, List<String>>((ref) {
  return CiltPlanHeaderTitleNotifier();
});

class CiltPlanHeaderTitleNotifier extends StateNotifier<List<String>> {
  CiltPlanHeaderTitleNotifier() : super([]);

  Future<void> getCILTPlanHeaderTitles(List<String> id) async {
    try {
      List<String> dataa = [];
      for (var data in id) {
        String? titles = await DbHelper.gettingTitle(data);
        if (titles != null) {
          dataa.add(titles);
        }
      }
      state = dataa;
    } catch (e) {
 Logger.logError('CiltPlanHeaderTitleNotifier', 'getCILTPlanHeaderTitles', e.toString());
    }
  }
}
