import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/CILT_EXEC_TASK.dart';
import 'package:rounds/be/DOCUMENT_ATTACHMENT.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:rounds/be/FAULT_ACTION.dart';
import 'package:rounds/be/FAULT_DOCUMENT.dart';
import 'package:rounds/be/FAULT_HEADER.dart';
import 'package:rounds/be/PLANT_HEADER.dart';
import 'package:rounds/be/PLANT_SECTION_HEADER.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/be/USER_PLANT.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/constants.dart';
import 'package:rounds/utils/utils.dart';
import 'package:rounds/widgets/chart_widget.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:unvired_settings/utils/app_styles.dart';

import '../../be/ABCINDICATOR_HEADER.dart';
import '../../be/ASSET_CATEGORY_HEADER.dart';
import '../../be/ASSET_HEADER.dart';
import '../../be/CILT_EXEC_HEADER.dart';
import '../../be/INSP_EXEC_HEADER.dart';
import '../../be/INSP_EXEC_TASK.dart';
import '../../be/LOCATION_CATEGORY_HEADER.dart';
import '../../be/SHIFT_HEADER.dart';
import '../../helpers/db_helper.dart';

final faultHeaderListByPlanIdProvider =
    StateNotifierProvider<FaultHeaderListByPlanIdNotifier, List<FAULT_HEADER>>(
        (ref) {
  return FaultHeaderListByPlanIdNotifier();
});

class FaultHeaderListByPlanIdNotifier
    extends StateNotifier<List<FAULT_HEADER>> {
  FaultHeaderListByPlanIdNotifier() : super([]);

  Future<void> fetchFaultHeaderListByPlanId(
      {required String plantId, String? faultID}) async {
    try {
      List<FAULT_HEADER> faultTypeHeaders =
          await DbHelper.getFaultHeaderListByPlanId(plantId, faultID);
      state = faultTypeHeaders;
    } catch (e) {
      Logger.logError('FaultTypeHeaderListNotifier', 'fetchFaultTypeHeaderList',
          e.toString());
    }
  }
}

final faultHeaderListProvider =
    StateNotifierProvider<FaultHeaderListNotifier, List<FAULT_HEADER>>((ref) {
  return FaultHeaderListNotifier();
});

class FaultHeaderListNotifier extends StateNotifier<List<FAULT_HEADER>> {
  FaultHeaderListNotifier() : super([]);

  Future<void> fetchFaultHeaderList(String plant,
      {bool initial = false}) async {
    try {
      List<FAULT_HEADER> faultTypeHeaders =
          await DbHelper.getFaultHeaderList(plant, initial: initial);
      faultTypeHeaders.sort((a, b) {
        DateTime dateA = convertToDateTime(a.reported_on.toString());
        DateTime dateB = convertToDateTime(b.reported_on.toString());
        return dateA.compareTo(dateB); // Ascending order (oldest to newest)
      });
      state = faultTypeHeaders;
    } catch (e) {
      Logger.logError('FaultTypeHeaderListNotifier', 'fetchFaultTypeHeaderList',
          e.toString());
    }
  }

  List<RoundsPieChartData> fetchRoundsPieChartData() {
    try {
      List<FAULT_HEADER> faultTypeHeaders = state;

      int completed = 0;
      int assigned = 0;
      int open = 0;

      for (var header in faultTypeHeaders) {
        final status = header.status;

        if (status == Constants.FAULT_STATE_NOCO) {
          completed++;
        } else if (status == "ASSIGNED") {
          assigned++;
        } else if (status == "CREATED") {
          open++;
        } else {
          open++;
        }
      }

      return [
        RoundsPieChartData(
          title: 'Completed',
          value: completed.toDouble(),
          color: AppColors.completedChartColor,
        ),
        RoundsPieChartData(
          title: 'Assigned',
          value: assigned.toDouble(),
          color: AppColors.assignedChartColor,
        ),
        RoundsPieChartData(
          title: 'Open',
          value: open.toDouble(),
          color: AppColors.openChartColor,
        ),
      ];
    } catch (e) {
      Logger.logError(
          'FaultHeaderListNotifier', 'fetchRoundsPieChartData', e.toString());
      return [];
    }
  }
}

final insertFaultHeaderProvider =
    StateNotifierProvider<InsertFaultHeaderNotifier, FAULT_HEADER>((ref) {
  return InsertFaultHeaderNotifier();
});

class InsertFaultHeaderNotifier extends StateNotifier<FAULT_HEADER> {
  InsertFaultHeaderNotifier() : super(FAULT_HEADER(fault_id: 0));

  Future<void> insertFaultHeader(FAULT_HEADER header) async {
    try {
      await AppDatabaseManager()
          .insert(DBInputEntity(FAULT_HEADER.TABLE_NAME, header.toJson()));
      state = header;
    } catch (e) {
      Logger.logError('FaultHeaderNotifier', 'getFaultHeader', e.toString());
    }
  }
}

final faultHeaderProvider =
    StateNotifierProvider<FaultHeaderNotifier, FAULT_HEADER>((ref) {
  return FaultHeaderNotifier();
});

class FaultHeaderNotifier extends StateNotifier<FAULT_HEADER> {
  FaultHeaderNotifier() : super(FAULT_HEADER(fault_id: 0));

  Future<void> getFaultHeader(
      {String? taskNo,
      String? faultId,
      FAULT_HEADER? data,
      bool isInsp = false}) async {
    try {
      FAULT_HEADER? faultHeader;
      if (taskNo != null) {
        if (isInsp) {
          faultHeader = await DbHelper.getFaultHeaderByTaskNoForInsp(taskNo);
        } else {
          faultHeader = await DbHelper.getFaultHeaderByTaskNo(taskNo);
        }
        if (faultHeader != null) {
          state = faultHeader;
        } else {
          state = FAULT_HEADER(fault_id: 0);
        }
      } else if (faultId != null) {
        faultHeader = await DbHelper.getFaultHeaderByFaultId(faultId);
        if (faultHeader != null) {
          state = faultHeader;
        } else {
          state = FAULT_HEADER(fault_id: 0);
        }
      } else if (data != null) {
        state = data;
      } else {
        state = FAULT_HEADER(fault_id: 0);
      }
    } catch (e) {
      Logger.logError('FaultHeaderNotifier', 'getFaultHeader', e.toString());
    }
  }

  void clearFault(){
    try {
      FAULT_HEADER faultHeader = FAULT_HEADER(fault_id: 0);
      state = faultHeader;
    } catch (e) {
      Logger.logError('FaultHeaderNotifier', 'getFaultHeader', e.toString());
    }
  }

  void resetFault(FAULT_HEADER faultHeader) {
    state = FAULT_HEADER.fromJson(faultHeader.toJson());
  }
}

final getCiltHeaderByFaultHeaderProvider =
    StateNotifierProvider<CiltHeaderByFaultHeaderNotifier, CILT_EXEC_HEADER>(
        (ref) {
  return CiltHeaderByFaultHeaderNotifier();
});

class CiltHeaderByFaultHeaderNotifier extends StateNotifier<CILT_EXEC_HEADER> {
  CiltHeaderByFaultHeaderNotifier() : super(CILT_EXEC_HEADER(cilt_id: 0));

  Future<void> getCiltPlanHeaderByFaultHeader(FAULT_HEADER header) async {
    try {
      List<CILT_EXEC_TASK> task = await DbHelper.getAllCiltExecTaskList();

      for (var data in task) {
        if (data.task_id == header.cilt_task_id) {
          CILT_EXEC_HEADER? cilt =
              await DbHelper.getCiltExeHeaderByCiltId(data.cilt_id.toString());
          if (cilt != null) {
            state = cilt;
          }
        }
      }
    } catch (e) {
      Logger.logError('CiltHeaderByFaultHeaderNotifier',
          'getCiltPlanHeaderByFaultHeader', e.toString());
    }
  }
}

final getInspectionHeaderByFaultHeaderProvider = StateNotifierProvider<
    InspectionHeaderByFaultHeaderNotifier, INSP_EXEC_HEADER>((ref) {
  return InspectionHeaderByFaultHeaderNotifier();
});

class InspectionHeaderByFaultHeaderNotifier
    extends StateNotifier<INSP_EXEC_HEADER> {
  InspectionHeaderByFaultHeaderNotifier() : super(INSP_EXEC_HEADER(insp_id: 0));

  Future<void> getInspectionPlanHeaderByFaultHeader(FAULT_HEADER header) async {
    try {
      List<INSP_EXEC_TASK> task = await DbHelper.getAllInspExecTaskList();

      for (var data in task) {
        if (data.task_id == header.cilt_task_id) {
          INSP_EXEC_HEADER? insp =
              await DbHelper.getInspectionExeHeaderByInspId(
                  data.insp_id.toString());
          if (insp != null) {
            state = insp;
          }
        }
      }
    } catch (e) {
      Logger.logError('InspectionHeaderByFaultHeaderNotifier',
          'getInspectionPlanHeaderByFaultHeader', e.toString());
    }
  }
}

final getFaultActionProvider =
    StateNotifierProvider<FaultActionNotifier, FAULT_ACTION>((ref) {
  return FaultActionNotifier();
});

class FaultActionNotifier extends StateNotifier<FAULT_ACTION> {
  FaultActionNotifier() : super(FAULT_ACTION(fault_id: ''));

  Future<void> getFaultAction(String faultId) async {
    try {
      FAULT_ACTION? action = await DbHelper.getFaultActionByFaultId(faultId);
      if (action != null) {
        state = action;
      }
    } catch (e) {
      Logger.logError(
          'FaultDocumentNotifier', 'getFaultDocuments', e.toString());
    }
  }
}

final getFaultDocumentProvider =
    StateNotifierProvider<FaultDocumentNotifier, List<DOCUMENT_HEADER>>((ref) {
  return FaultDocumentNotifier();
});

class FaultDocumentNotifier extends StateNotifier<List<DOCUMENT_HEADER>> {
  FaultDocumentNotifier() : super([]);

  Future<void> getFaultDocuments(String faultId) async {
    try {
      List<DOCUMENT_HEADER> headers = [];
      List<FAULT_DOCUMENT> documents =
          await DbHelper.getFaultDocumentByFaultId(faultId);
      if (documents.isNotEmpty) {
        for (FAULT_DOCUMENT doc in documents) {
          DOCUMENT_HEADER? header =
              await DbHelper.getDocumentHeadersByDocsId(doc.doc_id.toString());
          if (header != null) {
            headers.add(header);
          } 
          // else {
          //   headers = [];
          // }
        }
        state = headers;
      } else {
        state = [];
      }
    } catch (e) {
      Logger.logError(
          'FaultDocumentNotifier', 'getFaultDocuments', e.toString());
    }
  }
}

final getFaultDocumentHeaderProvider =
    StateNotifierProvider<FaultDocumentHeaderNotifier, List<DOCUMENT_HEADER>>(
        (ref) {
  return FaultDocumentHeaderNotifier();
});

class FaultDocumentHeaderNotifier extends StateNotifier<List<DOCUMENT_HEADER>> {
  FaultDocumentHeaderNotifier() : super([]);

  Future<void> getFaultDocumentsHeaders(String faultId) async {
    try {
      List<DOCUMENT_HEADER> headers = [];
      List<FAULT_DOCUMENT> documents =
          await DbHelper.getFaultDocumentByFaultId(faultId);
      if (documents.isNotEmpty) {
        for (FAULT_DOCUMENT doc in documents) {
          DOCUMENT_HEADER? attachment =
              await DbHelper.getDocumentHeadersByDocsId(doc.doc_id.toString());
          if (attachment != null) {
            headers.add(attachment);
          }
        }
        state = headers;
      } else {
        state = [];
      }
    } catch (e) {
      Logger.logError(
          'FaultDocumentNotifier', 'getFaultDocuments', e.toString());
    }
  }
}

final getCurrentAddedDocumentProvider = StateNotifierProvider<
    CurrentAddedDocumentNotifier, List<DOCUMENT_ATTACHMENT>>((ref) {
  return CurrentAddedDocumentNotifier();
});
List<DOCUMENT_ATTACHMENT> docAttachments = [];

class CurrentAddedDocumentNotifier
    extends StateNotifier<List<DOCUMENT_ATTACHMENT>> {
  CurrentAddedDocumentNotifier() : super([]);

  void getCurrentAddedDocuments(DOCUMENT_ATTACHMENT? attachment) {
    try {
      if (attachment != null) {
        docAttachments.add(attachment);
        state = docAttachments;
      }
    } catch (e) {
      Logger.logError('CurrentAddedDocumentNotifier',
          'getCurrentAddedDocuments', e.toString());
    }
  }
}

final plantListProvider =
    StateNotifierProvider<PlantListNotifier, List<PLANT_HEADER>>((ref) {
  return PlantListNotifier();
});

class PlantListNotifier extends StateNotifier<List<PLANT_HEADER>> {
  PlantListNotifier() : super([]);

  Future<List<PLANT_HEADER>> fetchPlantsList() async {
    try {
      List<PLANT_HEADER> plantsData = [];
      USER_HEADER? userHeader = await DbHelper.getUser();
      if (userHeader != null) {
        List<USER_PLANT> plants =
            await DbHelper.getPlantLists(userHeader.user_id);
        if (plants.isNotEmpty) {
          for (var data in plants) {
            PLANT_HEADER? plantsDataa =
                await DbHelper.getUserPlant(data.plant_id);
            if (plantsDataa != null) {
              plantsData.add(plantsDataa);
            }
          }
        }
      }

      state = plantsData;
      return plantsData;
    } catch (e) {
      Logger.logError('PlantListNotifier', 'fetchPlantsList', e.toString());
      rethrow;
    }
  }
}

final shiftListProvider =
    StateNotifierProvider<ShiftListNotifier, List<SHIFT_HEADER>>((ref) {
  return ShiftListNotifier();
});

class ShiftListNotifier extends StateNotifier<List<SHIFT_HEADER>> {
  ShiftListNotifier() : super([]);

  Future<void> fetchShiftList(String plantId) async {
    try {
      List<SHIFT_HEADER> shifts = await DbHelper.getUserShiftList(plantId);
      state = shifts;
    } catch (e) {
      Logger.logError('ShiftListNotifier', 'fetchShiftList', e.toString());
    }
  }

  void clearShiftList() async {
    state = [];
  }
}

final plantProvider = StateNotifierProvider<PlantNotifier, String>((ref) {
  return PlantNotifier();
});

class PlantNotifier extends StateNotifier<String> {
  PlantNotifier() : super('');

  void setPlant(String data) async {
    try {
      state = data;
    } catch (e) {
      Logger.logError('PlantNotifier', 'getPlant', e.toString());
    }
  }

  void clearPlant() {
    state = '';
  }
}

final plantSectionListProvider =
    StateNotifierProvider<PlantSectionListNotifier, List<PLANT_SECTION_HEADER>>(
        (ref) {
  return PlantSectionListNotifier();
});

class PlantSectionListNotifier
    extends StateNotifier<List<PLANT_SECTION_HEADER>> {
  PlantSectionListNotifier() : super([]);

  Future<void> fetchPlantsSectionList(String plant) async {
    try {
      List<PLANT_SECTION_HEADER> plantsSection =
          await DbHelper.getUserPlantsSectionList(plant);

      state = plantsSection;
    } catch (e) {
      Logger.logError(
          'PlantSectionListNotifier', 'fetchPlantsSectionList', e.toString());
    }
  }

  void clearPlantSectionList() {
    state = [];
  }
}

final plantSectionProvider =
    StateNotifierProvider<PlantSectionNotifier, List<String>>((ref) {
  return PlantSectionNotifier();
});

class PlantSectionNotifier extends StateNotifier<List<String>> {
  PlantSectionNotifier() : super([]);

  void setPlantSection(List<String> data) async {
    try {
      state = data;
    } catch (e) {
      Logger.logError('PlantSectionNotifier', 'getPlantSection', e.toString());
    }
  }

  void clearPlantSection() {
    state = [];
  }
}

final shiftProvider = StateNotifierProvider<ShiftNotifier, String>((ref) {
  return ShiftNotifier();
});

class ShiftNotifier extends StateNotifier<String> {
  ShiftNotifier() : super('');

  void setShift(String data) async {
    try {
      state = data;
    } catch (e) {
      Logger.logError('ShiftNotifier', 'getShift', e.toString());
    }
  }

  void clearShift() {
    state = '';
  }
}

final locationCategoryListProvider = StateNotifierProvider<
    LocationCategoryListNotifier, List<LOCATION_CATEGORY_HEADER>>((ref) {
  return LocationCategoryListNotifier();
});

class LocationCategoryListNotifier
    extends StateNotifier<List<LOCATION_CATEGORY_HEADER>> {
  LocationCategoryListNotifier() : super([]);

  Future<void> fetchLocationCategoryList() async {
    try {
      List<LOCATION_CATEGORY_HEADER> assets =
          await DbHelper.getLocationCategoryList();

      state = assets;
    } catch (e) {
      Logger.logError('LocationCategoryListNotifier',
          'fetchLocationCategoryList', e.toString());
    }
  }

  void clearAssetCategoryList() {
    state = [];
  }
}

final locationListProvider =
    StateNotifierProvider<LocationListNotifier, List<String>>((ref) {
  return LocationListNotifier();
});

class LocationListNotifier extends StateNotifier<List<String>> {
  LocationListNotifier() : super([]);

  Future<void> fetchLocationList(String plantId) async {
    try {
      List<String> locations = await DbHelper.getLocationList(plantId);

      state = locations;
    } catch (e) {
      Logger.logError(
          'LocationListNotifier', 'fetchLocationList', e.toString());
    }
  }
}

final locationProvider = StateNotifierProvider<LocationNotifier, String>((ref) {
  return LocationNotifier();
});

class LocationNotifier extends StateNotifier<String> {
  LocationNotifier() : super('');

  void getLocation(String data) async {
    try {
      state = data;
    } catch (e) {
      Logger.logError('LocationNotifier', 'getLocation', e.toString());
    }
  }

  void clearLocation() {
    state = '';
  }
}

final assetProvider = StateNotifierProvider<AssetNotifier, String>((ref) {
  return AssetNotifier();
});

class AssetNotifier extends StateNotifier<String> {
  AssetNotifier() : super('');

  void getAsset(String data) async {
    try {
      state = data;
    } catch (e) {
      Logger.logError('AssetNotifier', 'getAsset', e.toString());
    }
  }

  void clearAsset() {
    state = '';
  }
}

final assetListProvider =
    StateNotifierProvider<AssetListNotifier, List<String>>((ref) {
  return AssetListNotifier();
});

class AssetListNotifier extends StateNotifier<List<String>> {
  AssetListNotifier() : super([]);

  Future<void> fetchAssetList(String plantId) async {
    try {
      List<String> assets = await DbHelper.getAssetList(plantId);

      state = assets;
    } catch (e) {
      Logger.logError('AssetListNotifier', 'fetchAssetList', e.toString());
    }
  }
}

final assetLocListProvider =
    StateNotifierProvider<AssetLocListNotifier, List<ASSET_HEADER>>((ref) {
  return AssetLocListNotifier();
});

class AssetLocListNotifier extends StateNotifier<List<ASSET_HEADER>> {
  AssetLocListNotifier() : super([]);

  Future<void> fetchAssetLocList(String loc) async {
    try {
      List<ASSET_HEADER> assets = await DbHelper.getAssetLocList(loc);

      state = assets;
    } catch (e) {
      Logger.logError('AssetListNotifier', 'fetchAssetList', e.toString());
    }
  }

  void clearAssetLocList() {
    state = [];
  }
}

final assetCategoryListProvider = StateNotifierProvider<
    AssetCategoryListNotifier, List<ASSET_CATEGORY_HEADER>>((ref) {
  return AssetCategoryListNotifier();
});

class AssetCategoryListNotifier
    extends StateNotifier<List<ASSET_CATEGORY_HEADER>> {
  AssetCategoryListNotifier() : super([]);

  Future<void> fetchAssetCategoryList() async {
    try {
      List<ASSET_CATEGORY_HEADER> assets =
          await DbHelper.getAssetCategoryList();

      state = assets;
    } catch (e) {
      Logger.logError(
          'AssetCategoryListNotifier', 'fetchAssetCategoryList', e.toString());
    }
  }

  void clearAssetCategoryList() {
    state = [];
  }
}

final abcCategoryListProvider =
    StateNotifierProvider<ABCCategoryListNotifier, List<ABCINDICATOR_HEADER>>(
        (ref) {
  return ABCCategoryListNotifier();
});

class ABCCategoryListNotifier extends StateNotifier<List<ABCINDICATOR_HEADER>> {
  ABCCategoryListNotifier() : super([]);

  Future<void> fetchABCCategoryList() async {
    try {
      List<ABCINDICATOR_HEADER> assets = await DbHelper.getABCCategoryList();

      state = assets;
    } catch (e) {
      Logger.logError(
          'ABCCategoryListNotifier', 'fetchABCCategoryList', e.toString());
    }
  }

  void clearAssetCategoryList() {
    state = [];
  }
}

final getDocumentAttachmentProvider =
    StateNotifierProvider<DocumentAttachmentNotifier, DOCUMENT_ATTACHMENT>(
        (ref) {
  return DocumentAttachmentNotifier();
});

class DocumentAttachmentNotifier extends StateNotifier<DOCUMENT_ATTACHMENT> {
  DocumentAttachmentNotifier() : super(DOCUMENT_ATTACHMENT(uid: ''));

  void getDocumentAttachment(DOCUMENT_ATTACHMENT attachment) {
    try {
      state = attachment;
    } catch (e) {
      Logger.logError(
          'DocumentAttachmentNotifier', 'getDocumentAttachment', e.toString());
    }
  }
}

final plantValidationsProvider =
    StateNotifierProvider<PlantValidationNotifier, bool>((ref) {
  return PlantValidationNotifier();
});

class PlantValidationNotifier extends StateNotifier<bool> {
  PlantValidationNotifier() : super(false);

  void getPlantValidation(bool data) async {
    try {
      state = data;
    } catch (e) {
      Logger.logError(
          'PlantValidationNotifier', 'getPlantValidation', e.toString());
    }
  }

  void clearPlantValidation() {
    state = false;
  }
}

final plantSectionValidationsProvider =
    StateNotifierProvider<PlantSectionValidationNotifier, bool>((ref) {
  return PlantSectionValidationNotifier();
});

class PlantSectionValidationNotifier extends StateNotifier<bool> {
  PlantSectionValidationNotifier() : super(false);

  void getPlantSectionValidation(bool data) async {
    try {
      state = data;
    } catch (e) {
      Logger.logError('PlantSectionValidationNotifier',
          'getPlantSectionValidation', e.toString());
    }
  }

  void clearPlantSectionValidation() {
    state = false;
  }
}

final shiftValidationsProvider =
    StateNotifierProvider<ShiftValidationNotifier, bool>((ref) {
  return ShiftValidationNotifier();
});

class ShiftValidationNotifier extends StateNotifier<bool> {
  ShiftValidationNotifier() : super(false);

  void getShiftValidation(bool data) async {
    try {
      state = data;
    } catch (e) {
      Logger.logError(
          'ShiftValidationNotifier', 'getShiftValidation', e.toString());
    }
  }

  void clearShiftValidation() {
    state = false;
  }
}
