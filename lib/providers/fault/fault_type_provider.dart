import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/FAULT_TYPE_HEADER.dart';
import 'package:rounds/be/PRIORITY_HEADER.dart';
import 'package:rounds/be/SKIP_REASON_HEADER.dart';

import '../../be/FAILURE_MODE_HEADER.dart';
import '../../helpers/db_helper.dart';

final faultTypeListProvider =
    StateNotifierProvider<FaultTypeListNotifier, List<FAULT_TYPE_HEADER>>(
        (ref) {
  return FaultTypeListNotifier();
});

class FaultTypeListNotifier extends StateNotifier<List<FAULT_TYPE_HEADER>> {
  FaultTypeListNotifier() : super([]);

  Future<void> fetchFaultTypeList() async {
    try {
      List<FAULT_TYPE_HEADER> faultTypes = await DbHelper.getFaultTypeList();

      state = faultTypes;
    } catch (e) {
      Logger.logError(
          'FaultTypeListNotifier', 'fetchFaultTypeList', e.toString());
    }
  }

  String fetchFaultTypeCode(String code) {
    try {
      FAULT_TYPE_HEADER faultType = state.firstWhere(
          (element) => element.fault_code.toString() == code,
          orElse: () => FAULT_TYPE_HEADER(fault_code: "", description: ""));
      return faultType.description ?? "";
    } catch (e) {
      Logger.logError(
          'FaultTypeListNotifier', 'fetchFaultTypeCode', e.toString());
      return "";
    }
  }
}

final faultTypeProvider =
    StateNotifierProvider<FaultTypeNotifier, FAULT_TYPE_HEADER>((ref) {
  return FaultTypeNotifier();
});

class FaultTypeNotifier extends StateNotifier<FAULT_TYPE_HEADER> {
  FaultTypeNotifier() : super(FAULT_TYPE_HEADER(fault_code: ''));

  Future<void> getFaultType(String data) async {
    try {
      FAULT_TYPE_HEADER? faultTypes = await DbHelper.getFaultType(data);
      if (faultTypes != null) {
        state = faultTypes;
      }
    } catch (e) {
      Logger.logError('FaultTypeNotifier', 'getFaultType', e.toString());
    }
  }

  void clearFaultType() {
    state = FAULT_TYPE_HEADER(fault_code: '');
  }
}

final faultModeHeaderListProvider =
    StateNotifierProvider<FaultModeListNotifier, List<FAILURE_MODE_HEADER>>(
        (ref) {
  return FaultModeListNotifier();
});

class FaultModeListNotifier extends StateNotifier<List<FAILURE_MODE_HEADER>> {
  FaultModeListNotifier() : super([]);

  Future<void> fetchFaultModeList() async {
    try {
      List<FAILURE_MODE_HEADER> faultModes = await DbHelper.getFaultModeList();
      state = faultModes;
    } catch (e) {
      Logger.logError(
          'FaultTypeListNotifier', 'fetchFaultModeList', e.toString());
    }
  }

  String fetchFaultModeByCode(String code) {
    FAILURE_MODE_HEADER? faultMode = state.firstWhere(
        (element) => element.failure_code.toString() == code,
        orElse: () => FAILURE_MODE_HEADER(failure_code: '', description: ''));

    return faultMode.description ?? "";
  }
}

final faultModeHeaderProvider =
    StateNotifierProvider<FaultModeNotifier, FAILURE_MODE_HEADER>((ref) {
  return FaultModeNotifier();
});

class FaultModeNotifier extends StateNotifier<FAILURE_MODE_HEADER> {
  FaultModeNotifier() : super(FAILURE_MODE_HEADER(failure_code: ''));

  Future<void> getFaultMode(String data) async {
    try {
      FAILURE_MODE_HEADER? mode = await DbHelper.getFaultMode(data);
      if (mode != null) {
        state = mode;
      }
    } catch (e) {
      Logger.logError('FaultModeNotifier', 'getFaultMode', e.toString());
    }
  }

  void clearFaultMode() {
    state = FAILURE_MODE_HEADER(failure_code: '');
  }
}

final priorityListProvider =
    StateNotifierProvider<PriorityListNotifier, List<PRIORITY_HEADER>>((ref) {
  return PriorityListNotifier();
});

class PriorityListNotifier extends StateNotifier<List<PRIORITY_HEADER>> {
  PriorityListNotifier() : super([]);

  Future<void> fetchPriorityList() async {
    try {
      List<PRIORITY_HEADER> priority = await DbHelper.getPriorityList();

      state = priority;
    } catch (e) {
      Logger.logError(
          'FaultTypeListNotifier', 'fetchFaultModeList', e.toString());
    }
  }

  String fetchPriorityCode(String code) {
    try {
      PRIORITY_HEADER priority = state.firstWhere(
          (element) => element.priority_code.toString() == code,
          orElse: () => PRIORITY_HEADER(priority_code: "", description: ""));
      return priority.description ?? "";
    } catch (e) {
      Logger.logError(
          'PriorityListNotifier', 'fetchPriorityCode', e.toString());
      return "";
    }
  }
}

final priorityProvider =
    StateNotifierProvider<PriorityNotifier, PRIORITY_HEADER>((ref) {
  return PriorityNotifier();
});

class PriorityNotifier extends StateNotifier<PRIORITY_HEADER> {
  PriorityNotifier() : super(PRIORITY_HEADER(priority_code: ''));

  Future<void> getPriority(String data) async {
    try {
      PRIORITY_HEADER? priority = await DbHelper.getPriority(data);
      if (priority != null) {
        state = priority;
      }
    } catch (e) {
      Logger.logError('PriorityNotifier', 'getPriority', e.toString());
    }
  }

  void clearPriority() {
    state = PRIORITY_HEADER(priority_code: '');
  }
}

final skipReasonListProvider =
    StateNotifierProvider<SkipReasonListNotifier, List<SKIP_REASON_HEADER>>(
        (ref) {
  return SkipReasonListNotifier();
});

class SkipReasonListNotifier extends StateNotifier<List<SKIP_REASON_HEADER>> {
  SkipReasonListNotifier() : super([]);

  Future<void> fetchSkipReasonList() async {
    try {
      List<SKIP_REASON_HEADER> reason = await DbHelper.getSkipReasonList();

      state = reason;
    } catch (e) {
      Logger.logError(
          'FaultTypeListNotifier', 'fetchSkipReasonList', e.toString());
    }
  }
}

final skipReasonProvider =
    StateNotifierProvider<SkipReasonNotifier, SKIP_REASON_HEADER>((ref) {
  return SkipReasonNotifier();
});

class SkipReasonNotifier extends StateNotifier<SKIP_REASON_HEADER> {
  SkipReasonNotifier() : super(SKIP_REASON_HEADER(reason: ''));

  Future<void> getSkipReason(String data) async {
    try {
      SKIP_REASON_HEADER? priority = await DbHelper.getSkipReason(data);
      if (priority != null) {
        state = priority;
      }
    } catch (e) {
      Logger.logError('SkipReasonNotifier', 'getSkipReason', e.toString());
    }
  }

  void clearSkipReason() {
    state = SKIP_REASON_HEADER(reason: '');
  }
}

final faultDescriptionProvider =
    StateNotifierProvider<FaultDescriptionNotifier, String>((ref) {
  return FaultDescriptionNotifier();
});

class FaultDescriptionNotifier extends StateNotifier<String> {
  FaultDescriptionNotifier() : super('');

  void getFaultDescription(String data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError(
          'FaultDescriptionNotifier', 'getFaultDescription', e.toString());
    }
  }

  void resetFaultDescription() {
    state = '';
  }

  void clearFaultDescription() {
    state = '';
  }
}

final faultReportedByProvider =
    StateNotifierProvider<FaultReportedByNotifier, String>((ref) {
  return FaultReportedByNotifier();
});

class FaultReportedByNotifier extends StateNotifier<String> {
  FaultReportedByNotifier() : super('');

  void getFaultReportedBy(String data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError(
          'FaultReportedByNotifier', 'getFaultReportedBy', e.toString());
    }
  }

  void clearFaultReportedBy() {
    state = '';
  }
}

final faultLongTextProvider =
    StateNotifierProvider<FaultLongTextNotifier, String>((ref) {
  return FaultLongTextNotifier();
});

class FaultLongTextNotifier extends StateNotifier<String> {
  FaultLongTextNotifier() : super('');

  void getFaultLongText(String data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError(
          'FaultLongTextNotifier', 'getFaultLongText', e.toString());
    }
  }

  void resetFaultLongText() {
    state = '';
  }

  void clearFaultLongText() {
    state = '';
  }
}

final faultNoticedOnProvider =
    StateNotifierProvider<FaultNoticedOnNotifier, int>((ref) {
  return FaultNoticedOnNotifier();
});

class FaultNoticedOnNotifier extends StateNotifier<int> {
  FaultNoticedOnNotifier() : super(0);

  void getFaultNoticedOn(int data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError(
          'FaultNoticedOnNotifier', 'getFaultNoticedOn', e.toString());
    }
  }

  void clearFaultNoticedOn() {
    state = 0;
  }
}

final faultDueOnProvider =
    StateNotifierProvider<FaultDueOnNotifier, int>((ref) {
  return FaultDueOnNotifier();
});

class FaultDueOnNotifier extends StateNotifier<int> {
  FaultDueOnNotifier() : super(0);

  void getDueOn(int data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError('FaultDueOnNotifier', 'geDueOn', e.toString());
    }
  }

  void clearDueOn() {
    state = 0;
  }
}

final faultStartOnProvider =
    StateNotifierProvider<FaultStartOnNotifier, int>((ref) {
  return FaultStartOnNotifier();
});

class FaultStartOnNotifier extends StateNotifier<int> {
  FaultStartOnNotifier() : super(0);

  void getFaultStartOn(int data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError('FaultStartOnNotifier', 'getFaultStartOn', e.toString());
    }
  }

  void clearStartOn() {
    state = 0;
  }
}

final faultEndOnProvider =
    StateNotifierProvider<FaultEndOnNotifier, int>((ref) {
  return FaultEndOnNotifier();
});

class FaultEndOnNotifier extends StateNotifier<int> {
  FaultEndOnNotifier() : super(0);

  void getEndOn(int data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError('FaultEndOnNotifier', 'getEndOn', e.toString());
    }
  }
}
