import 'package:flutter_riverpod/flutter_riverpod.dart';

/*
final uploadProgressProvider =
    StateNotifierProvider<UploadProgressNotifier, Map<int, double>>(
  (ref) => UploadProgressNotifier(),
);

class UploadProgressNotifier extends StateNotifier<Map<int, double>> {
  UploadProgressNotifier() : super({});

  void startUpload(int index) {
    state = {...state, index: 0.0};
  }

  void updateProgress(int index, double progress) {
    state = {...state, index: progress};
  }

  void finishUpload(int index) {
    state = {...state, index: 1.0};
  }
}
*/



final uploadProgressProvider =
StateNotifierProvider<UploadProgressNotifier, Map<String, double>>(
      (ref) => UploadProgressNotifier(),
);

class UploadProgressNotifier extends StateNotifier<Map<String, double>> {
  UploadProgressNotifier() : super({});

  void startUpload(String index) {
    state = {...state, index: 0.0};
  }

  void updateProgress(String index, double progress) {
    state = {...state, index: progress};
  }

  void finishUpload(String index) {
    state = {...state, index: 1.0};
  }
}


/*
final attachMentUploadProgressProvider = StateNotifierProvider<
    AttachementUploadProgressNotifier, Map<String, double>>(
      (ref) => AttachementUploadProgressNotifier(),
);

class AttachementUploadProgressNotifier
    extends StateNotifier<Map<String, double>> {
  AttachementUploadProgressNotifier() : super({});

  void startUpload(String lid) {
    state = {...state, lid: 0.0};
  }

  void updateProgress(String lid, double progress) {
    state = {...state, lid: progress};
  }

  void finishUpload(String lid) {
    state = {...state, lid: 1.0};
  }
}
*/


final jobUploadProgressProvider =
    StateNotifierProvider<JobUploadProgressNotifier, Map<String, double>>(
  (ref) => JobUploadProgressNotifier(),
);

class JobUploadProgressNotifier extends StateNotifier<Map<String, double>> {
  JobUploadProgressNotifier() : super({});

  void startUpload(String index) {
    state = {...state, index: 0.0};
  }

  void updateProgress(String index, double progress) {
    state = {...state, index: progress};
  }

  void finishUpload(String index) {
    state = {...state, index: 1.0};
  }
}
