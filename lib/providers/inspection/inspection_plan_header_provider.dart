import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/INSPECTION_TASK.dart';
import 'package:rounds/be/INSP_EXEC_SEC.dart';
import 'package:rounds/be/INSP_EXEC_TASK.dart';
import 'package:rounds/be/KPI_HEADER.dart';
import 'package:rounds/providers/fault/fault_header_provider.dart';
import 'package:rounds/providers/inspection/inspection_header_provider.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import '../../be/DOCUMENT_HEADER.dart';
import '../../be/INSPECTION_PLAN_HEADER.dart';
import '../../be/INSPECTION_SECTION.dart';
import '../../be/INSP_EXEC_HEADER.dart';
import '../../helpers/db_helper.dart';
import '../../utils/app_constants.dart';

final inspectionPlanSectionListHeaderProvider = StateNotifierProvider<
    InspectionPlanSectionListHeaderNotifier, List<INSPECTION_SECTION>>((ref) {
  return InspectionPlanSectionListHeaderNotifier();
});

class InspectionPlanSectionListHeaderNotifier
    extends StateNotifier<List<INSPECTION_SECTION>> {
  List<INSPECTION_SECTION> _masterList = [];

  InspectionPlanSectionListHeaderNotifier() : super([]);

  Future<void> fetchInspectionPlanSectionListHeaders(String planId) async {
    try {
      List<INSPECTION_SECTION> inspPlanSectionHeaders =
          await DbHelper.getInspectionSectionPlanListHeader(planId);
      _masterList = List<INSPECTION_SECTION>.from(inspPlanSectionHeaders);
      state = inspPlanSectionHeaders;
    } catch (e) {
      Logger.logError('InspectionPlanSectionListHeaderNotifier',
          'fetchInspectionPlanSectionListHeaders', e.toString());
    }
  }

  List<INSPECTION_SECTION> filter(List<INSPECTION_TASK> inspTasks) {
    state = _masterList.where((section) {
      return inspTasks.any((task) => task.section_id == section.section_id);
    }).toList();
    return state;
  }

  void reset() {
    state = List<INSPECTION_SECTION>.from(_masterList);
  }
}

final inspectionPlanTaskListHeaderProvider = StateNotifierProvider<
    InspectionPlanTaskListHeaderNotifier, InspectionPlanTaskListState>(
  (ref) => InspectionPlanTaskListHeaderNotifier(),
);

class InspectionPlanTaskListHeaderNotifier
    extends StateNotifier<InspectionPlanTaskListState> {
  InspectionPlanTaskListHeaderNotifier() : super(InspectionPlanTaskListState());

  Future<void> fetchInspectionPlanTaskListHeaders({
    required INSPECTION_SECTION section,
    required String plantId,
    required INSP_EXEC_HEADER header,
  }) async {
    try {
      state = InspectionPlanTaskListState(isLoading: true);
      List<INSPECTION_TASK> inspPlanTaskHeaders =
          await DbHelper.getInspectionTaskPlanListHeaderWithoutSection(
              section.plan_id!.toString());
      state = state.copyWith(data: inspPlanTaskHeaders, isLoading: false);
      // create a function that will go through all the task check what the value is slected if any value is selected call fetchInspDependentTasks an the end it will return list of tasks
      await fetchInspDependentTasks(plantId, header);
    } catch (e) {
      Logger.logError('InspectionPlanTaskListHeaderNotifier',
          'fetchInspectionPlanTaskListHeaders', e.toString());
    }
  }

  Future<void> fetchInspDependentTasks(
    String plantId,
    INSP_EXEC_HEADER header,
  ) async {
    try {
      List<INSPECTION_TASK> updatedList = [];
      final currentList = state.data ?? [];
      final existingTaskIds =
          currentList.map((t) => t.task_id.toString()).toSet();
      String query1 = '${INSP_EXEC_HEADER.FIELD_INSP_ID}= "${header.insp_id}"';
      List<INSP_EXEC_TASK> executionTasks = await AppDatabaseManager()
          .select(DBInputEntity(INSP_EXEC_TASK.TABLE_NAME, {})
            ..setWhereClause(query1))
          .then(
              (value) => value.map((e) => INSP_EXEC_TASK.fromJson(e)).toList());
      for (var i = 0; i < currentList.length; i++) {
        var inspectionTask = currentList[i];
        INSP_EXEC_TASK? executionTask = executionTasks.firstWhereOrNull(
            (element) =>
                element.task_id == inspectionTask.task_id &&
                element.section_id == inspectionTask.section_id);
        String? selectedValue;
        if (inspectionTask.task_type == "b") {
          selectedValue = executionTask?.str_value;
        } else if (inspectionTask.task_type == "e") {
          selectedValue = executionTask?.num_value?.toString();
        } else if (inspectionTask.task_type == "m") {
          selectedValue = executionTask?.num_value?.toString();
        } else if (inspectionTask.task_type == "o") {
          List<KPI_HEADER> headers = await DbHelper.getKPIHeaderList(plantId);
          KPI_HEADER? kpiHeader = headers.firstWhereOrNull((element) =>
              element.plant_id == plantId &&
              element.kpi_id.toString() == inspectionTask.kpi_id.toString());
          if (kpiHeader != null &&
              kpiHeader.kpi_id != null &&
              kpiHeader.kpi_id != 0) {
            if (kpiHeader.kpi_type == '2' ||
                kpiHeader.kpi_type == '3' ||
                kpiHeader.kpi_type == '4') {
              selectedValue = executionTask?.num_value?.toString();
            } else {
              selectedValue = executionTask?.str_value;
            }
          }
        } else if (inspectionTask.task_type.toString() == 'n') {
          selectedValue = executionTask?.str_value;
        } else if (inspectionTask.task_type.toString() == 'h') {
          selectedValue = executionTask?.num_value?.toString();
        } else if (inspectionTask.task_type.toString() == 'i') {
          selectedValue = executionTask?.str_value;
        } else if (inspectionTask.task_type.toString() == 'c') {
          selectedValue = executionTask?.str_value;
        } else if (inspectionTask.task_type.toString() == 'k') {
          selectedValue = executionTask?.str_value;
        } else if (inspectionTask.task_type.toString() == 'g') {
          selectedValue = executionTask?.blob_value;
        } else if (inspectionTask.task_type.toString() == 'f') {
          selectedValue = executionTask?.str_value;
        } else if (inspectionTask.task_type.toString() == 'l') {
          selectedValue = executionTask?.num_value?.toString();
        } else if (inspectionTask.task_type.toString() == 'j') {
          selectedValue = executionTask?.num_value?.toString();
        } else if (inspectionTask.task_type.toString() == 'd') {
          selectedValue = executionTask?.num_value?.toString();
        }
        /*    else if (inspectionTask.task_type.toString() == 'm') {
          selectedValue = executionTask?.num_value?.toString();
        }*/

// Get all valid dependents recursively
        final List<INSPECTION_TASK> tasksToInsert =
            await DbHelper().getAllValidNestedDependentsA(
          plantId,
          inspectionTask,
          selectedValue,
          executionTasks,
          existingTaskIds,
        );
// Clean existing ones that should now be removed
        final tasksToRemoveIds = <String>{};
        final dependents =
            await DbHelper.getInspectionTaskPlanListHeaderDependent(
          inspectionTask.plan_id.toString(),
          inspectionTask,
        );
        for (final dependent in dependents) {
          final shouldAdd = shouldShowTask(
            //    type: dependent.task_type.toString(),
            actual: selectedValue,
            expected: dependent.dep_cond_val,
            operatorCode: dependent.dep_cond_code,
          );

          final exists = existingTaskIds.contains(dependent.task_id.toString());
          if (!shouldAdd && exists) {
            tasksToRemoveIds.add(dependent.task_id.toString());
            final nestedToRemove =
                await _getAllDependentTaskIdsRecursively(dependent);
            tasksToRemoveIds.addAll(nestedToRemove);
          }
        }
        updatedList = (updatedList.isEmpty ? currentList : updatedList)
            .where((t) => !tasksToRemoveIds.contains(t.task_id.toString()))
            .toList();
        final parentIndexInUpdated =
            updatedList.indexWhere((t) => t.task_id == inspectionTask.task_id);

        if (tasksToInsert.isNotEmpty && parentIndexInUpdated != -1) {
          updatedList.insertAll(parentIndexInUpdated + 1, tasksToInsert);
        }
      }

      state = InspectionPlanTaskListState(data: List.from(updatedList));
    } catch (e) {
      Logger.logError(
        'InspectionPlanTaskListHeaderNotifier',
        'fetchInspDependentTasks',
        e.toString(),
      );
    }
  }
}

final inspectionExecSectionListHeaderProvider = StateNotifierProvider<
    InspectionExecSectionListHeaderNotifier, List<INSP_EXEC_SEC>>((ref) {
  return InspectionExecSectionListHeaderNotifier();
});

class InspectionExecSectionListHeaderNotifier
    extends StateNotifier<List<INSP_EXEC_SEC>> {
  InspectionExecSectionListHeaderNotifier() : super([]);

  Future<void> fetchInspectionExecSectionListHeaders(int insp_id) async {
    try {
      List<INSP_EXEC_SEC> inspExecSectionHeaders =
          await DbHelper.getInspectionExecSecListByInspId(insp_id);
      state = inspExecSectionHeaders;
    } catch (e) {
      Logger.logError('InspectionExecSectionListHeaderNotifier',
          'fetchInspectionExecSectionListHeaders', e.toString());
    }
  }
}

final inspectionPlanHeaderProvider =
    StateNotifierProvider<InspectionPlanHeaderNotifier, INSPECTION_PLAN_HEADER>(
        (ref) {
  return InspectionPlanHeaderNotifier();
});

class InspectionPlanHeaderNotifier
    extends StateNotifier<INSPECTION_PLAN_HEADER> {
  InspectionPlanHeaderNotifier() : super(INSPECTION_PLAN_HEADER(plan_id: 0));

  Future<void> selectedInspPlanHeaders(
      INSPECTION_PLAN_HEADER? inspPlanHeader) async {
    try {
      if (inspPlanHeader != null) {
        state = inspPlanHeader;
      }
    } catch (e) {
      Logger.logError('InspectionPlanHeaderNotifier', 'selectedInspPlanHeaders',
          e.toString());
    }
  }
}

final inspectionToggleStateProvider =
    StateNotifierProvider<INSPToggleStateNotifier, bool>((ref) {
  return INSPToggleStateNotifier();
});

class INSPToggleStateNotifier extends StateNotifier<bool> {
  INSPToggleStateNotifier() : super(false);

  void toggle() {
    state = !state;
  }

  void setToggleState(bool newState) {
    state = newState;
  }
}

final inspectionTaskNotifier =
    StateNotifierProvider<InspectionTaskListNotifier, List<INSPECTION_TASK>>(
        (ref) {
  return InspectionTaskListNotifier();
});

class InspectionTaskListNotifier extends StateNotifier<List<INSPECTION_TASK>> {
  List<INSPECTION_TASK> searchedTaskList = [];
  bool isSearch = false;
  InspectionTaskListNotifier() : super([]);

  filter(String planId, String value, WidgetRef ref) async {
    RegExp data = RegExp(value.toLowerCase());
    isSearch = value.isNotEmpty ? true : false;
    try {
      List<INSPECTION_TASK> inspPlanTaskHeaders = await fetchInspDependentTasks(
          ref.read(plantProvider), ref.read(inspectionHeaderProvider));

      List<INSPECTION_TASK> taskList = inspPlanTaskHeaders
          .where((element) =>
              data.hasMatch(element.title!.toLowerCase()) ||
              data.hasMatch(element.description != null
                  ? element.description!.toLowerCase()
                  : '') ||
              data.hasMatch(element.asset_no != null
                  ? (element.asset_no!.toString())
                  : '') ||
              data.hasMatch(element.location_id != null
                  ? element.location_id!.toString()
                  : '') ||
              data.hasMatch(
                  element.task_id != null ? element.task_id!.toString() : ''))
          .toList();

      ref
          .read(inspectionPlanSectionListHeaderProvider.notifier)
          .filter(taskList);

      if (value.isEmpty) {
        state = taskList;
        await ref
            .read(inspectionPlanSectionListHeaderProvider.notifier)
            .fetchInspectionPlanSectionListHeaders(planId);
        ref
            .read(inspectionPlanSectionListHeaderProvider.notifier)
            .filter(taskList);
      } else {
        state = taskList;
        ref
            .read(inspectionPlanSectionListHeaderProvider.notifier)
            .filter(taskList);
      }
    } catch (e) {
      Logger.logError('InspectionTaskListNotifier', 'filter', e.toString());
    }
  }

  void updateTask(INSPECTION_TASK updatedTask) {
    int index = state.indexWhere((task) => task.lid == updatedTask.lid);
    if (index != -1) {
      state[index] = updatedTask;
      final updatedList = List<INSPECTION_TASK>.from(state);
      state = updatedList;
    }
  }

  Future<void> reset(String planId, WidgetRef ref) async {
    List<INSPECTION_TASK> inspTask =
        await DbHelper.getInspectionTaskPlanListHeaderByPlanId(planId);
    await ref
        .read(inspectionPlanSectionListHeaderProvider.notifier)
        .fetchInspectionPlanSectionListHeaders(planId);
    state = inspTask;
  }

  Future<void> fetchIncompleteTasks(
      String planId, INSP_EXEC_HEADER header, WidgetRef ref) async {
    try {
      List<INSPECTION_TASK> filteredTasks = [];

      final allSections =
          await DbHelper.getInspectionSectionPlanListHeader(planId.toString());

      for (INSPECTION_SECTION section in allSections) {
        List<INSPECTION_TASK> tasks = await getIncompleteVisibleTasksForSection(
            section: section, plantId: ref.read(plantProvider), header: header);
        filteredTasks.addAll(tasks);
      }

      ref
          .read(inspectionPlanSectionListHeaderProvider.notifier)
          .filter(filteredTasks);

      state = filteredTasks;
    } catch (e) {
      Logger.logError(
          'InspectionTaskListNotifier', 'fetchIncompleteTasks', e.toString());
    }
  }

  Future<List<INSPECTION_TASK>> getIncompleteVisibleTasksForSection({
    required INSPECTION_SECTION section,
    required String plantId,
    required INSP_EXEC_HEADER header,
  }) async {
    try {
      final List<INSPECTION_TASK> result = [];

      // Fetch top-level tasks
      final allTasks = await DbHelper.getInspectionTaskPlanListHeader(
        section.plan_id!.toString(),
        section.section_id!.toString(),
      );

      // Fetch execution tasks
      final String query =
          '${INSP_EXEC_HEADER.FIELD_INSP_ID}= "${header.insp_id}"';

      final executionTasks = await AppDatabaseManager()
          .select(DBInputEntity(INSP_EXEC_TASK.TABLE_NAME, {})
            ..setWhereClause(query))
          .then(
              (value) => value.map((e) => INSP_EXEC_TASK.fromJson(e)).toList());

      for (final parentTask in allTasks) {
        final execTask = executionTasks.firstWhereOrNull(
          (e) =>
              e.task_id == parentTask.task_id &&
              e.section_id == parentTask.section_id,
        );

        final isParentComplete = _checkTaskCompletion(execTask);

        // If parent is complete, we might still need to show children
        final allDependents = await _getVisibleDependentsRecursively(
          plantId: plantId,
          parentTask: parentTask,
          execTasks: executionTasks,
        );

        final allChildrenComplete = allDependents.isNotEmpty &&
            allDependents.every((dep) {
              final depExec = executionTasks.firstWhereOrNull(
                (e) =>
                    e.task_id == dep.task_id && e.section_id == dep.section_id,
              );
              return _checkTaskCompletion(depExec);
            });

        if (allChildrenComplete) {
          // Skip parent + children
          continue;
        }

        if (!isParentComplete ||
            (allDependents.isNotEmpty && !allChildrenComplete)) {
          result.add(parentTask);
        }

        // Add only incomplete children
        // for (final dep in allDependents) {
        //   // final depExec = executionTasks.firstWhereOrNull(
        //   //   (e) => e.task_id == dep.task_id && e.section_id == dep.section_id,
        //   // );
        //   // if (!_checkTaskCompletion(depExec)) {
        //   //   result.add(dep);
        //   // }
        // }

        if (!allChildrenComplete) {
          result.addAll(allDependents);
        }
      }

      return result;
    } catch (e) {
      Logger.logError(
          'TaskFilter', 'getIncompleteVisibleTasksForSection', e.toString());
      return [];
    }
  }

  Future<List<INSPECTION_TASK>> _getVisibleDependentsRecursively({
    required String plantId,
    required INSPECTION_TASK parentTask,
    required List<INSP_EXEC_TASK> execTasks,
    Set<String>? visited,
  }) async {
    visited ??= {};
    final List<INSPECTION_TASK> result = [];

    final execTask = execTasks.firstWhereOrNull(
      (e) =>
          e.task_id == parentTask.task_id &&
          e.section_id == parentTask.section_id,
    );

    // Get selected value
    String? selectedValue;
    if (parentTask.task_type == 'b') {
      selectedValue = execTask?.str_value;
    } else if (parentTask.task_type == 'e') {
      selectedValue = execTask?.num_value?.toString();
    } else if (parentTask.task_type == 'o') {
      final headers = await DbHelper.getKPIHeaderList(plantId);
      final kpiHeader = headers.firstWhereOrNull(
        (element) =>
            element.plant_id == plantId &&
            element.kpi_id.toString() == parentTask.kpi_id.toString(),
      );
      if (kpiHeader != null && ['2', '3', '4'].contains(kpiHeader.kpi_type)) {
        selectedValue = execTask?.num_value?.toString();
      } else {
        selectedValue = execTask?.str_value;
      }
    }

    // Fetch dependents
    final dependents = await DbHelper.getInspectionTaskPlanListHeaderDependent(
      parentTask.plan_id.toString(),
      parentTask,
    );

    for (final dep in dependents) {
      if (visited.contains(dep.task_id.toString())) continue;
      visited.add(dep.task_id.toString());

      final shouldAdd = shouldShowTask(
        //  type: dep.task_type.toString(),
        actual: selectedValue,
        expected: dep.dep_cond_val,
        operatorCode: dep.dep_cond_code,
      );

      if (shouldAdd) {
        result.add(dep);
        final childDependents = await _getVisibleDependentsRecursively(
          plantId: plantId,
          parentTask: dep,
          execTasks: execTasks,
          visited: visited,
        );
        result.addAll(childDependents);
      }
    }

    return result;
  }

// Helper method to check if a task is completed
  bool _checkTaskCompletion(INSP_EXEC_TASK? execTask) {
    if (execTask == null) return false;

    // Check if task status equals completion constant
    return execTask.status == AppConstants.STATE_TASK_COMP;
  }

  Future<List<INSPECTION_TASK>> fetchInspDependentTasks(
    String plantId,
    INSP_EXEC_HEADER header,
  ) async {
    try {
      state = await DbHelper.getInspectionTaskPlanListHeaderByPlanId(
          header.plan_id.toString(),
          onlyParent: true);

      List<INSPECTION_TASK> updatedList = [];
      final currentList = state ?? [];
      final existingTaskIds =
          currentList.map((t) => t.task_id.toString()).toSet();
      String query1 = '${INSP_EXEC_HEADER.FIELD_INSP_ID}= "${header.insp_id}"';
      List<INSP_EXEC_TASK> executionTasks = await AppDatabaseManager()
          .select(DBInputEntity(INSP_EXEC_TASK.TABLE_NAME, {})
            ..setWhereClause(query1))
          .then(
              (value) => value.map((e) => INSP_EXEC_TASK.fromJson(e)).toList());
      for (var i = 0; i < currentList.length; i++) {
        var inspectionTask = currentList[i];
        INSP_EXEC_TASK? executionTask = executionTasks.firstWhereOrNull(
            (element) =>
                element.task_id == inspectionTask.task_id &&
                element.section_id == inspectionTask.section_id);
        String? selectedValue;
        if (inspectionTask.task_type == "b") {
          selectedValue = executionTask?.str_value;
        } else if (inspectionTask.task_type == "e") {
          selectedValue = executionTask?.num_value?.toString();
        } else if (inspectionTask.task_type == "m") {
          selectedValue = executionTask?.num_value?.toString();
        } else if (inspectionTask.task_type == "o") {
          List<KPI_HEADER> headers = await DbHelper.getKPIHeaderList(plantId);
          KPI_HEADER? kpiHeader = headers.firstWhereOrNull((element) =>
              element.plant_id == plantId &&
              element.kpi_id.toString() == inspectionTask.kpi_id.toString());
          if (kpiHeader != null &&
              kpiHeader.kpi_id != null &&
              kpiHeader.kpi_id != 0) {
            if (kpiHeader.kpi_type == '2' ||
                kpiHeader.kpi_type == '3' ||
                kpiHeader.kpi_type == '4') {
              selectedValue = executionTask?.num_value?.toString();
            } else {
              selectedValue = executionTask?.str_value;
            }
          }
        }
// Get all valid dependents recursively
        final List<INSPECTION_TASK> tasksToInsert =
            await _getAllValidNestedDependentsA(
          plantId,
          inspectionTask,
          selectedValue,
          executionTasks,
          existingTaskIds,
        );
// Clean existing ones that should now be removed
        final tasksToRemoveIds = <String>{};
        final dependents =
            await DbHelper.getInspectionTaskPlanListHeaderDependent(
          inspectionTask.plan_id.toString(),
          inspectionTask,
        );
        for (final dependent in dependents) {
          final shouldAdd = shouldShowTask(
            //  type: dependent.task_type.toString(),
            actual: selectedValue,
            expected: dependent.dep_cond_val,
            operatorCode: dependent.dep_cond_code,
          );
          final exists = existingTaskIds.contains(dependent.task_id.toString());
          if (!shouldAdd && exists) {
            tasksToRemoveIds.add(dependent.task_id.toString());
            final nestedToRemove =
                await _getAllDependentTaskIdsRecursively(dependent);
            tasksToRemoveIds.addAll(nestedToRemove);
          }
        }
        updatedList = (updatedList.isEmpty ? currentList : updatedList)
            .where((t) => !tasksToRemoveIds.contains(t.task_id.toString()))
            .toList();
        final parentIndexInUpdated =
            updatedList.indexWhere((t) => t.task_id == inspectionTask.task_id);
        if (tasksToInsert.isNotEmpty && parentIndexInUpdated != -1) {
          updatedList.insertAll(parentIndexInUpdated + 1, tasksToInsert);
        }
      }
      return updatedList;
    } catch (e) {
      Logger.logError(
        'InspectionPlanTaskListHeaderNotifier',
        'fetchInspDependentTasks',
        e.toString(),
      );
      return [];
    }
  }

  Future<List<INSPECTION_TASK>> _getAllValidNestedDependentsA(
    String plantId,
    INSPECTION_TASK parentTask,
    String? selectedValue,
    List<INSP_EXEC_TASK> executionTasks,
    Set<String> existingTaskIds,
  ) async {
    List<INSPECTION_TASK> result = [];
    final dependents = await DbHelper.getInspectionTaskPlanListHeaderDependent(
      parentTask.plan_id.toString(),
      parentTask,
    );
    for (final dependent in dependents) {
      final shouldAdd = shouldShowTask(
        //  type: dependent.task_type.toString(),
        actual: selectedValue,
        expected: dependent.dep_cond_val,
        operatorCode: dependent.dep_cond_code,
      );
      if (shouldAdd &&
          !existingTaskIds.contains(dependent.task_id.toString())) {
        result.add(dependent);
        existingTaskIds.add(dependent.task_id.toString());
        final childExecTask = executionTasks.firstWhereOrNull((e) =>
            e.task_id == dependent.task_id &&
            e.section_id == dependent.section_id);
        String? childSelectedValue;
        if (dependent.task_type == "b") {
          childSelectedValue = childExecTask?.str_value;
        } else if (dependent.task_type == "e") {
          childSelectedValue = childExecTask?.num_value?.toString();
        } else if (dependent.task_type == "o") {
          final headers = await DbHelper.getKPIHeaderList(plantId);
          final kpiHeader = headers.firstWhereOrNull(
            (element) =>
                element.plant_id == plantId &&
                element.kpi_id.toString() == dependent.kpi_id.toString(),
          );
          if (kpiHeader != null &&
              (kpiHeader.kpi_type == '2' ||
                  kpiHeader.kpi_type == '3' ||
                  kpiHeader.kpi_type == '4')) {
            childSelectedValue = childExecTask?.num_value?.toString();
          } else {
            childSelectedValue = childExecTask?.str_value;
          }
        }
        final nested = await _getAllValidNestedDependentsA(
          plantId,
          dependent,
          childSelectedValue,
          executionTasks,
          existingTaskIds,
        );
        result.addAll(nested);
      }
    }
    return result;
  }
}

final inspectionPlanListHeaderProvider = StateNotifierProvider<
    InspectionPlanListHeaderNotifier, List<INSPECTION_PLAN_HEADER>>((ref) {
  return InspectionPlanListHeaderNotifier();
});

class InspectionPlanListHeaderNotifier
    extends StateNotifier<List<INSPECTION_PLAN_HEADER>> {
  InspectionPlanListHeaderNotifier() : super([]);

  Future<void> fetchInspectionPlanListHeaders() async {
    try {
      final inspectionPlanHeaders =
          await DbHelper.getInspectionPlanListHeader();
      state = inspectionPlanHeaders;
    } catch (e) {
      Logger.logError('InspectionPlanListHeaderNotifier',
          'fetchInspectionPlanListHeaders', e.toString());
    }
  }
}

final inspectionExecTaskNotifier =
    StateNotifierProvider<InspectionExecTaskListNotifier, INSP_EXEC_TASK>(
        (ref) {
  return InspectionExecTaskListNotifier();
});

class InspectionExecTaskListNotifier extends StateNotifier<INSP_EXEC_TASK> {
  InspectionExecTaskListNotifier()
      : super(INSP_EXEC_TASK(insp_id: 0, insp_task_id: 0, task_no: 0));

  Future<void> getInspectionExecTaskHeaderByTask(INSPECTION_TASK task) async {
    INSP_EXEC_TASK? insp_exec_task = await DbHelper.getInspectionExecTaskByTask(
        task.section_id.toString(), task.task_id.toString());
    if (insp_exec_task != null) {
      state = insp_exec_task;
    } else {
      state = INSP_EXEC_TASK(insp_id: 0, insp_task_id: 0, task_no: 0);
    }
  }
}

final inspectionPlanAndTaskDocProvider = StateNotifierProvider<
    InspPlanAndTaskDocNotifier, Map<int, List<DOCUMENT_HEADER>>>((ref) {
  return InspPlanAndTaskDocNotifier();
});

class InspPlanAndTaskDocNotifier
    extends StateNotifier<Map<int, List<DOCUMENT_HEADER>>> {
  InspPlanAndTaskDocNotifier() : super({});

  Future<void> fetchInspPlanAndTaskDoc(INSPECTION_PLAN_HEADER inspPlan) async {
    List<DOCUMENT_HEADER> planDocs =
        await DbHelper.getPlanDocInspectionHeader(inspPlan);
    state = {...state, inspPlan.plan_id!: planDocs};
  }

  List<DOCUMENT_HEADER> getInspPlanDocs(int planid) {
    return state[planid] ?? [];
  }
}

final inspCompletedProvider =
    StateNotifierProvider<InspPlanCompletedNotifier, bool>((ref) {
  return InspPlanCompletedNotifier();
});

class InspPlanCompletedNotifier extends StateNotifier<bool> {
  InspPlanCompletedNotifier() : super(false);

  Future<void> inspCompleted(INSP_EXEC_HEADER insp_exec_header) async {
    try {
      INSP_EXEC_HEADER? header =
          await DbHelper.getInspectionExecHeader(insp_exec_header);
      if (header != null) {
        List<INSP_EXEC_TASK> inspExecHeaders =
            await DbHelper.getInspExecHeaderListByInspIdAndStatus(
                header.insp_id.toString(),
                status: AppConstants.STATE_TASK_COMP);
        if (inspExecHeaders.isNotEmpty) {
          state = false;
        } else {
          state = true;
        }
      }
    } catch (e) {
      Logger.logError(
          'InspPlanCompletedNotifier', 'inspCompleted', e.toString());
    }
  }
}

bool shouldShowTask({
  // required String type,
  required String? actual,
  required String? expected,
  required String? operatorCode,
}) {
  if (actual == null || expected == null || operatorCode == null) {
    return false;
  }

  final actualVal = actual;
  final expectedVal = expected;
  final op = operatorCode;

  late List<String> expectedValues;
  try {
    final parsed = jsonDecode(expected);
    if (parsed is List) {
      expectedValues = parsed.map((e) => e.toString()).toList();
    } else {
      expectedValues = [parsed.toString()];
    }
  } catch (_) {
    expectedValues = [expected];
  }

  switch (op) {
    case 'if':
      return expectedValues.contains(actualVal);
    case 'di':
      return !expectedValues.contains(actualVal);
    case 'e':
      return actualVal == expectedVal;

    case 'ne':
      return actualVal != expectedVal;

    case 'gt':
      return double.tryParse(actualVal) != null &&
          double.tryParse(expectedVal) != null &&
          double.parse(actualVal) > double.parse(expectedVal);

    case 'ng':
      return double.tryParse(actualVal) != null &&
          double.tryParse(expectedVal) != null &&
          double.parse(actualVal) <= double.parse(expectedVal);

    case 'lt':
      return double.tryParse(actualVal) != null &&
          double.tryParse(expectedVal) != null &&
          double.parse(actualVal) < double.parse(expectedVal);

    case 'nl':
      return double.tryParse(actualVal) != null &&
          double.tryParse(expectedVal) != null &&
          double.parse(actualVal) >= double.parse(expectedVal);

    case 'bt':
      final parts = expectedVal.split('-');
      if (parts.length == 2) {
        final min = double.tryParse(parts[0]);
        final max = double.tryParse(parts[1]);
        final actualNum = double.tryParse(actualVal);
        if (min != null && max != null && actualNum != null) {
          return actualNum >= min && actualNum <= max;
        }
      }
      return false;

    case 'nb':
      final parts = expectedVal.split('-');
      if (parts.length == 2) {
        final min = double.tryParse(parts[0]);
        final max = double.tryParse(parts[1]);
        final actualNum = double.tryParse(actualVal);
        if (min != null && max != null && actualNum != null) {
          return actualNum < min || actualNum > max;
        }
      }
      return false;

    default:
      // fallback to equals
      // if (type != 'e' || type != 'o' || type != 'b') {
      //   return true;
      // } else {
      return actualVal == expectedVal;
    // }
  }
}

Future<List<INSPECTION_TASK>> _getAllValidNestedDependents(
  INSPECTION_TASK parent,
  String selectedValue,
) async {
  final all = <INSPECTION_TASK>[];

  final children = await DbHelper.getInspectionTaskPlanListHeaderDependent(
    parent.plan_id!.toString(),
    parent,
  );

  for (final child in children) {
    final shouldAdd = shouldShowTask(
      //  type: child.task_type.toString(),
      actual: selectedValue,
      expected: child.dep_cond_val,
      operatorCode: child.dep_cond_code,
    );

    if (shouldAdd) {
      all.add(child);
      final grandChildren =
          await _getAllValidNestedDependents(child, selectedValue);
      all.addAll(grandChildren);
    }
  }

  return all;
}

Future<List<String>> _getAllDependentTaskIdsRecursively(
  INSPECTION_TASK parent,
) async {
  final allIds = <String>[];

  final children = await DbHelper.getInspectionTaskPlanListHeaderDependent(
    parent.plan_id!.toString(),
    parent,
  );

  for (final child in children) {
    allIds.add(child.task_id!.toString());
    final nested = await _getAllDependentTaskIdsRecursively(child);
    allIds.addAll(nested);
  }

  return allIds;
}

class InspectionPlanTaskListState {
  final bool isLoading;
  final List<INSPECTION_TASK>? data;
  final String? error;

  InspectionPlanTaskListState({
    this.isLoading = false,
    this.data,
    this.error,
  });

  InspectionPlanTaskListState copyWith({
    bool? isLoading,
    List<INSPECTION_TASK>? data,
    String? error,
  }) {
    return InspectionPlanTaskListState(
      isLoading: isLoading ?? this.isLoading,
      data: List.from(data ?? []),
      error: error ?? this.error,
    );
  }
}
