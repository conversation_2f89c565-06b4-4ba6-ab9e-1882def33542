import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/INSP_EXEC_TASK.dart';

import '../../be/INSPECTION_TASK.dart';

final inspectionTaskProvider =
    StateNotifierProvider<InspectionTaskNotifier, INSPECTION_TASK>((ref) {
  return InspectionTaskNotifier();
});

class InspectionTaskNotifier extends StateNotifier<INSPECTION_TASK> {
  InspectionTaskNotifier()
      : super(INSPECTION_TASK(plan_id: 0, section_id: 0, task_id: 0));

  void getInspectionTask(INSPECTION_TASK task) {
    try {
      state = task;
    } catch (e) {
      Logger.logError(
          'InspectionTaskNotifier', 'getInspectionTask', e.toString());
    }
  }
}

final inspectionExecTaskProvider =
    StateNotifierProvider<InspectionTaskExecNotifier, INSP_EXEC_TASK>((ref) {
  return InspectionTaskExecNotifier();
});

class InspectionTaskExecNotifier extends StateNotifier<INSP_EXEC_TASK> {
  InspectionTaskExecNotifier()
      : super(INSP_EXEC_TASK(insp_id: 0, insp_task_id: 0, task_no: 0));

  void getInspectionExecTask(INSP_EXEC_TASK task) {
    try {
      state = task;
    } catch (e) {
      Logger.logError(
          'InspectionTaskExecNotifier', 'getInspectionExecTask', e.toString());
    }
  }
}
