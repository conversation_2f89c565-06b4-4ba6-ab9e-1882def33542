import 'dart:convert';
import 'dart:typed_data';

import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/CILT_EXEC_HEADER.dart';
import 'package:rounds/be/KPI_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../be/INSPECTION_PLAN_HEADER.dart';
import '../../be/INSPECTION_SECTION.dart';
import '../../be/INSPECTION_TASK.dart';
import '../../be/INSP_EXEC_HEADER.dart';
import '../../be/INSP_EXEC_TASK.dart';
import '../../be/SKIP_REASON_HEADER.dart';
import '../../be/USER_HEADER.dart';
import '../../helpers/ui_helper.dart';
import '../../pages/inspection/inspection_task_list_screen.dart';
import '../../utils/app_colors.dart';
import '../../utils/app_constants.dart';
import '../../widgets/chart_widget.dart';
import '../fault/fault_type_provider.dart';
import '../user_provider.dart';

final inspectionHeaderListProvider =
    StateNotifierProvider<InspectionHeaderListNotifier, List<INSP_EXEC_HEADER>>(
        (ref) {
  return InspectionHeaderListNotifier();
});

class InspectionHeaderListNotifier
    extends StateNotifier<List<INSP_EXEC_HEADER>> {
  InspectionHeaderListNotifier() : super([]);

  Future<void> fetchInspectionListHeaders(String plantId, List<String> plantSec,
      String shift, WidgetRef ref) async {
    try {
      List<INSP_EXEC_HEADER> inspectionHeaders =
          await DbHelper.getInspectionExecHeaderList(plantId, plantSec, shift);
      state = inspectionHeaders;
      ref
          .read(filterInspectionHeaderListProvider.notifier)
          .setFilterData(inspectionHeaders);
    } catch (e) {
      // Handle errors
      Logger.logError('InspectionHeaderListNotifier',
          'fetchInspectionListHeaders', e.toString());
    }
  }

  Future<void> filter(String plantId, List<String> plantSec, String shift,
      String searchString, ref) async {
    var priorityNotifier = ref.read(priorityListProvider.notifier);
    List<INSP_EXEC_HEADER> filteredData = [];
    final regex = RegExp(searchString.toLowerCase());
    try {
      List<INSPECTION_PLAN_HEADER> ciltPlans =
          await DbHelper.getInspectionPlanListHeader();

      List<INSPECTION_PLAN_HEADER> filteredPlans = ciltPlans
          .where((element) => regex.hasMatch(element.title!.toLowerCase()))
          .toList();

      List<INSP_EXEC_HEADER> ciltHeaders =
          await DbHelper.getInspectionExecHeaderList(plantId, plantSec, shift);

      filteredData = ciltHeaders.where((header) {
        return filteredPlans.any((plan) => plan.plan_id == header.plan_id);
      }).toList();

      List<USER_HEADER> users = await DbHelper.searchUserByName(searchString);

      var tempfilteredData = ciltHeaders.where((element) {
        final priorityText =
            priorityNotifier.fetchPriorityCode(element.priority!);
        return regex.hasMatch(element.status!.toLowerCase()) ||
            regex.hasMatch(priorityText.toLowerCase()) ||
            users.any((user) => user.user_id == element.assigned_to) ||
            regex.hasMatch(element.assigned_to ?? "");
      }).toList();

      for (var element in tempfilteredData) {
        if (!filteredData.any((header) => header.lid == element.lid)) {
          filteredData.add(element);
        }
      }

      state = filteredData;
    } catch (e) {
      Logger.logError('InspectionHeaderListNotifier', 'filter', e.toString());
    }
  }

  Future<void> deleteInspListHeaders(CILT_EXEC_HEADER header) async {
    try {
      await AppDatabaseManager()
          .delete(DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, header.toJson()));
      state = state.where((item) => item.plan_id != header.plan_id).toList();
    } catch (e) {
      // Handle errors
      Logger.logError('InspectionHeaderListNotifier', 'deleteInspListHeaders',
          e.toString());
    }
  }

  Future<void> insertInspListHeaders(CILT_EXEC_HEADER header) async {
    try {
      await AppDatabaseManager()
          .insert(DBInputEntity(CILT_EXEC_HEADER.TABLE_NAME, header.toJson()));
    } catch (e) {
      // Handle errors
      Logger.logError('InspectionHeaderListNotifier', 'insertInspListHeaders',
          e.toString());
    }
  }

  List<RoundsPieChartData> fetchRoundsPieChartData(String selectedDate) {
    List<INSP_EXEC_HEADER> inspHeader = state
        .where((element) =>
            element.start_on == int.parse(selectedDate.replaceAll('-', '')))
        .toList();

    int completed = 0;
    int accepted = 0;
    int rejected = 0;
    int assigned = 0;
    int unassigned = 0;

    for (var header in inspHeader) {
      final status = header.status;

      if (status == AppConstants.STATE_COMPLETED) {
        completed++;
      } else if (status == AppConstants.STATE_UNASSIGNED) {
        unassigned++;
      } else if (status == AppConstants.STATE_ASSIGNED) {
        assigned++;
      } else if (status == AppConstants.STATE_ACCEPTED) {
        accepted++;
      } else if (status == AppConstants.STATE_REJECTED) {
        rejected++;
      }
    }

    return [
      RoundsPieChartData(
        title: 'Completed',
        value: completed.toDouble(),
        color: AppColors.completedChartColor,
      ),
      RoundsPieChartData(
        title: 'Assigned',
        value: assigned.toDouble(),
        color: AppColors.assignedChartColor,
      ),
      RoundsPieChartData(
        title: 'Unassigned',
        value: unassigned.toDouble(),
        color: AppColors.unAssignedChartColor,
      ),
      RoundsPieChartData(
        title: 'Accepted',
        value: accepted.toDouble(),
        color: AppColors.acceptedChartColor,
      ),
      RoundsPieChartData(
        title: 'Rejected',
        value: rejected.toDouble(),
        color: AppColors.rejectedChartColor,
      ),
    ];
  }

  void updateInspExecHeader(INSP_EXEC_HEADER updatedHeader) {
    int index = state.indexWhere((element) => element.lid == updatedHeader.lid);
    if (index != -1) {
      final updatedList = List<INSP_EXEC_HEADER>.from(state);
      updatedList[index] = updatedHeader;
      state = updatedList;
    }
  }
}

final inspectionHeaderProvider =
    StateNotifierProvider<InspectionHeaderNotifier, INSP_EXEC_HEADER>((ref) {
  return InspectionHeaderNotifier();
});

class InspectionHeaderNotifier extends StateNotifier<INSP_EXEC_HEADER> {
  InspectionHeaderNotifier() : super(INSP_EXEC_HEADER(insp_id: 0));

  Future<void> fetchInspectionHeaders(INSP_EXEC_HEADER insp_exec_header) async {
    try {
      INSP_EXEC_HEADER? inspectionHeaders =
          await DbHelper.getInspectionExecHeader(insp_exec_header);
      if (inspectionHeaders != null) {
        state = inspectionHeaders;
      }
    } catch (e) {
      Logger.logError(
          'InspectionHeaderNotifier', 'fetchInspectionHeaders', e.toString());
    }
  }
}

final filterInspectionHeaderListProvider = StateNotifierProvider<
    FilterInspectionHeaderListNotifier, List<INSP_EXEC_HEADER>>((ref) {
  return FilterInspectionHeaderListNotifier();
});

class FilterInspectionHeaderListNotifier
    extends StateNotifier<List<INSP_EXEC_HEADER>> {
  FilterInspectionHeaderListNotifier() : super([]);

  void setFilterData(List<INSP_EXEC_HEADER> filterdata) {
    state = List.from(filterdata);
  }

  Future<void> filter(String plantId, List<String> plantSec, String shift,
      String searchString, ref) async {
    var priorityNotifier = ref.read(priorityListProvider.notifier);
    List<INSP_EXEC_HEADER> filteredData = [];
    final regex = RegExp(searchString.toLowerCase());

    try {
      /*     final role = ref.watch(roleProvider);
      var isAssignAllow = UIHelper.isAssign(role!);*/
      List<INSPECTION_PLAN_HEADER> inspPlans =
          await DbHelper.getInspectionPlanListHeader();

      List<INSPECTION_PLAN_HEADER> filteredPlans = inspPlans
          .where((element) => regex.hasMatch(element.title!.toLowerCase()))
          .toList();

      List<INSP_EXEC_HEADER> inspHeaders =
          await DbHelper.getInspectionExecHeaderList(plantId, plantSec, shift);

      filteredData = inspHeaders.where((header) {
        return filteredPlans.any((plan) => plan.plan_id == header.plan_id);
      }).toList();

      List<USER_HEADER> users = await DbHelper.searchUserByName(searchString);

      var tempfilteredData = inspHeaders.where((element) {
        final priorityText =
            priorityNotifier.fetchPriorityCode(element.priority!);
        return regex.hasMatch(element.status!.toLowerCase()) ||
            regex.hasMatch(element.insp_id.toString()) ||
            regex.hasMatch(priorityText.toLowerCase()) ||
            users.any((user) => user.user_id == element.assigned_to) ||
            regex.hasMatch(element.assigned_to ?? "");
      }).toList();

      for (var element in tempfilteredData) {
        if (!filteredData.any((header) => header.lid == element.lid)) {
          filteredData.add(element);
        }
      }

      /* if (!isAssignAllow) {
        filteredData = filteredData
            .where((element) =>
                element.assigned_to == ref.read(userProvider)?.user_id)
            .toList();
      }*/

      state = filteredData;
    } catch (e) {
      Logger.logError(
          'FilterInspectionHeaderListNotifier', 'filter', e.toString());
    }
  }

  void updateInspExecHeader(INSP_EXEC_HEADER updatedHeader) {
    int index = state.indexWhere((element) => element.lid == updatedHeader.lid);
    if (index != -1) {
      final updatedList = List<INSP_EXEC_HEADER>.from(state);
      updatedList[index] = updatedHeader;
      state = updatedList;
    }
  }
}

final inspExecuteTaskListProvider =
    StateNotifierProvider<InspExecuteTaskListNotifier, List<INSP_EXEC_TASK>>(
        (ref) {
  return InspExecuteTaskListNotifier();
});

class InspExecuteTaskListNotifier extends StateNotifier<List<INSP_EXEC_TASK>> {
  InspExecuteTaskListNotifier() : super([]);

  Future<void> getInspExecuteTaskList() async {
    try {
      List<INSP_EXEC_TASK> executeHeaders =
          await DbHelper.getAllInspExecTaskList();
      state = executeHeaders;
    } catch (e) {
      Logger.logError('InspExecuteTaskListNotifier', 'getInspExecuteTaskList',
          e.toString());
    }
  }

  List<INSP_EXEC_TASK> findAllInspExecOfInsp(
      INSP_EXEC_HEADER insp_exec_header) {
    return state
        .where((element) => element.insp_id == insp_exec_header.insp_id)
        .toList();
  }

  INSP_EXEC_TASK? getInspectionExecTaskHeaderByTask(
      INSPECTION_TASK task, INSP_EXEC_HEADER inspHeader) {
    final execTasksToDisplayData = state
        .where((element) =>
            element.task_id == task.task_id &&
            element.section_id == task.section_id &&
            element.insp_id == inspHeader.insp_id)
        .toList();
    return !execTasksToDisplayData.isEmpty ? execTasksToDisplayData[0] : null;
  }

  bool isSectionCompleted(
      {required List<INSPECTION_TASK> inspTask,
      required int section_id,
      required INSP_EXEC_HEADER inspHeader}) {
    var tasks =
        inspTask.where((element) => element.section_id == section_id).toList();

    var execTasksInSection = state
        .where((execTask) => tasks.any((task) =>
            task.task_id == execTask.task_id &&
            task.section_id == execTask.section_id &&
            execTask.insp_id == inspHeader.insp_id))
        .toList();

    if (inspTask.isEmpty || execTasksInSection.isEmpty) {
      return false;
    }

    return !execTasksInSection.every((task) =>
        task.status?.toLowerCase() ==
        AppConstants.STATE_TASK_COMP.toLowerCase());
  }

  void updateInspExexTask(INSP_EXEC_TASK updatedExecTask) {
    int index =
        state.indexWhere((element) => element.lid == updatedExecTask.lid);
    final updatedList = List<INSP_EXEC_TASK>.from(state);
    if (index != -1) {
      updatedList[index] = updatedExecTask;
      state = updatedList;
    }
  }
}

final skipReasonListInspProvider =
    StateNotifierProvider<SkipReasonListInspNotifier, List<SKIP_REASON_HEADER>>(
        (ref) {
  return SkipReasonListInspNotifier();
});

class SkipReasonListInspNotifier
    extends StateNotifier<List<SKIP_REASON_HEADER>> {
  SkipReasonListInspNotifier() : super([]);

  Future<void> fetchInspSkipReasonList() async {
    try {
      List<SKIP_REASON_HEADER> reason = await DbHelper.getSkipReasonList();

      state = reason;
    } catch (e) {
      Logger.logError('SkipReasonListInspNotifier', 'fetchInspSkipReasonList',
          e.toString());
    }
  }
}

final skipReasonInspProvider =
    StateNotifierProvider<SkipReasonInspNotifier, SKIP_REASON_HEADER>((ref) {
  return SkipReasonInspNotifier();
});

class SkipReasonInspNotifier extends StateNotifier<SKIP_REASON_HEADER> {
  SkipReasonInspNotifier() : super(SKIP_REASON_HEADER(reason: ''));

  Future<void> getInspSkipReason(String data) async {
    try {
      SKIP_REASON_HEADER? priority = await DbHelper.getSkipReason(data);
      if (priority != null) {
        state = priority;
      }
    } catch (e) {
      Logger.logError(
          'SkipReasonInspNotifier', 'getInspSkipReason', e.toString());
    }
  }

  void clearInspSkipReason() {
    state = SKIP_REASON_HEADER(reason: '');
  }
}

final inspTasksProvider =
    StateNotifierProvider<InspTasksNotifier, Map<int, List<INSPECTION_TASK>>>(
  (ref) => InspTasksNotifier(ref),
);

class InspTasksNotifier extends StateNotifier<Map<int, List<INSPECTION_TASK>>> {
  final Ref ref;
  InspTasksNotifier(this.ref) : super({});
  Future<void> getInspTasks(
    int planId, {
    required INSPECTION_SECTION section,
    required String plantId,
    required INSP_EXEC_HEADER header,
  }) async {
    try {
      List<INSPECTION_TASK> parentTasks =
          await DbHelper.getInspectionTaskPlanListHeaderWithoutSection(
              planId.toString());

      List<INSPECTION_TASK> dependentTasks =
          await fetchInspDependentTasks(parentTasks, plantId, header);

      parentTasks.addAll(dependentTasks);

      state = {...state, planId: dependentTasks};
    } catch (e) {
      Logger.logError('InspTasksNotifier', 'getInspTasks', e.toString());
    }
  }

  Future<void> fetchTasksForAllHeaders(
      List<INSP_EXEC_HEADER> headers, String plantId) async {
    try {
      Map<int, List<INSPECTION_TASK>> tasksMap = {};

      for (INSP_EXEC_HEADER header in headers) {
        List<INSPECTION_TASK> inspParentTasks =
            await DbHelper.getInspectionTaskPlanListHeaderWithoutSection(
                header.plan_id.toString());
        List<INSPECTION_TASK> dependentTasks = [];

        dependentTasks =
            await fetchInspDependentTasks(inspParentTasks, plantId, header);
        tasksMap[header.plan_id!] = dependentTasks;
      }

      state = tasksMap;
    } catch (e) {
      Logger.logError(
          'InspTasksNotifier', 'fetchTasksForAllHeaders', e.toString());
    }
  }

  Future<List<INSPECTION_TASK>> fetchInspDependentTasks(
    List<INSPECTION_TASK> parentTasks,
    String plantId,
    INSP_EXEC_HEADER header,
  ) async {
    try {
      List<INSPECTION_TASK> updatedList = [];
      final currentList = parentTasks ?? [];
      final existingTaskIds =
          currentList.map((t) => t.task_id.toString()).toSet();
      String query1 = '${INSP_EXEC_HEADER.FIELD_INSP_ID}= "${header.insp_id}"';
      List<INSP_EXEC_TASK> executionTasks = await AppDatabaseManager()
          .select(DBInputEntity(INSP_EXEC_TASK.TABLE_NAME, {})
            ..setWhereClause(query1))
          .then(
              (value) => value.map((e) => INSP_EXEC_TASK.fromJson(e)).toList());
      for (var i = 0; i < currentList.length; i++) {
        var inspectionTask = currentList[i];
        INSP_EXEC_TASK? executionTask = executionTasks.firstWhereOrNull(
            (element) =>
                element.task_id == inspectionTask.task_id &&
                element.section_id == inspectionTask.section_id);
        String? selectedValue;
        if (inspectionTask.task_type == "b") {
          selectedValue = executionTask?.str_value;
        } else if (inspectionTask.task_type == "e") {
          selectedValue = executionTask?.num_value?.toString();
        } else if (inspectionTask.task_type == "m") {
          selectedValue = executionTask?.num_value?.toString();
        } else if (inspectionTask.task_type == "o") {
          List<KPI_HEADER> headers = await DbHelper.getKPIHeaderList(plantId);
          KPI_HEADER? kpiHeader = headers.firstWhereOrNull((element) =>
              element.plant_id == plantId &&
              element.kpi_id.toString() == inspectionTask.kpi_id.toString());
          if (kpiHeader != null &&
              kpiHeader.kpi_id != null &&
              kpiHeader.kpi_id != 0) {
            if (kpiHeader.kpi_type == '2' ||
                kpiHeader.kpi_type == '3' ||
                kpiHeader.kpi_type == '4') {
              selectedValue = executionTask?.num_value?.toString();
            } else {
              selectedValue = executionTask?.str_value;
            }
          }
        }
// Get all valid dependents recursively
        final List<INSPECTION_TASK> tasksToInsert =
            await DbHelper().getAllValidNestedDependentsA(
          plantId,
          inspectionTask,
          selectedValue,
          executionTasks,
          existingTaskIds,
        );
// Clean existing ones that should now be removed
        final tasksToRemoveIds = <String>{};
        final dependents =
            await DbHelper.getInspectionTaskPlanListHeaderDependent(
          inspectionTask.plan_id.toString(),
          inspectionTask,
        );
        for (final dependent in dependents) {
          final shouldAdd = shouldShowTask(
            //  type: dependent.task_type?.toString() ?? "",
            actual: selectedValue,
            expected: dependent.dep_cond_val,
            operatorCode: dependent.dep_cond_code,
          );
          final exists = existingTaskIds.contains(dependent.task_id.toString());
          if (!shouldAdd && exists) {
            tasksToRemoveIds.add(dependent.task_id.toString());
            final nestedToRemove =
                await _getAllDependentTaskIdsRecursively(dependent);
            tasksToRemoveIds.addAll(nestedToRemove);
          }
        }
        updatedList = (updatedList.isEmpty ? currentList : updatedList)
            .where((t) => !tasksToRemoveIds.contains(t.task_id.toString()))
            .toList();
        final parentIndexInUpdated =
            updatedList.indexWhere((t) => t.task_id == inspectionTask.task_id);
        if (tasksToInsert.isNotEmpty && parentIndexInUpdated != -1) {
          updatedList.insertAll(parentIndexInUpdated + 1, tasksToInsert);
        }
      }
      return updatedList;
    } catch (e) {
      Logger.logError(
        'InspectionPlanTaskListHeaderNotifier',
        'fetchInspDependentTasks',
        e.toString(),
      );
      return [];
    }
  }

  Future<List<String>> _getAllDependentTaskIdsRecursively(
    INSPECTION_TASK parent,
  ) async {
    final allIds = <String>[];

    final children = await DbHelper.getInspectionTaskPlanListHeaderDependent(
      parent.plan_id!.toString(),
      parent,
    );

    for (final child in children) {
      allIds.add(child.task_id!.toString());
      final nested = await _getAllDependentTaskIdsRecursively(child);
      allIds.addAll(nested);
    }

    return allIds;
  }
}

bool shouldShowTask({
  //required String type,
  required String? actual,
  required String? expected,
  required String? operatorCode,
}) {
  if (actual == null || expected == null || operatorCode == null) {
    return false;
  }

  final actualVal = actual;
  final expectedVal = expected;
  final op = operatorCode;

  late List<String> expectedValues;
  try {
    final parsed = jsonDecode(expected);
    if (parsed is List) {
      expectedValues = parsed.map((e) => e.toString()).toList();
    } else {
      expectedValues = [parsed.toString()];
    }
  } catch (_) {
    expectedValues = [expected];
  }

  switch (op) {
    case 'if':
      return expectedValues.contains(actualVal);
    case 'di':
      return !expectedValues.contains(actualVal);
    case 'e':
      return actualVal == expectedVal;

    case 'ne':
      return actualVal != expectedVal;

    case 'gt':
      return double.tryParse(actualVal) != null &&
          double.tryParse(expectedVal) != null &&
          double.parse(actualVal) > double.parse(expectedVal);

    case 'ng':
      return double.tryParse(actualVal) != null &&
          double.tryParse(expectedVal) != null &&
          double.parse(actualVal) <= double.parse(expectedVal);

    case 'lt':
      return double.tryParse(actualVal) != null &&
          double.tryParse(expectedVal) != null &&
          double.parse(actualVal) < double.parse(expectedVal);

    case 'nl':
      return double.tryParse(actualVal) != null &&
          double.tryParse(expectedVal) != null &&
          double.parse(actualVal) >= double.parse(expectedVal);

    case 'bt':
      final parts = expectedVal.split('-');
      if (parts.length == 2) {
        final min = double.tryParse(parts[0]);
        final max = double.tryParse(parts[1]);
        final actualNum = double.tryParse(actualVal);
        if (min != null && max != null && actualNum != null) {
          return actualNum >= min && actualNum <= max;
        }
      }
      return false;

    case 'nb':
      final parts = expectedVal.split('-');
      if (parts.length == 2) {
        final min = double.tryParse(parts[0]);
        final max = double.tryParse(parts[1]);
        final actualNum = double.tryParse(actualVal);
        if (min != null && max != null && actualNum != null) {
          return actualNum < min || actualNum > max;
        }
      }
      return false;

    default:
      return actualVal == expectedVal;
  }
}

final inspExecuteProvider =
    StateNotifierProvider<InspExecuteNotifier, Map<int, List<INSP_EXEC_TASK>>>(
        (ref) {
  return InspExecuteNotifier();
});

class InspExecuteNotifier
    extends StateNotifier<Map<int, List<INSP_EXEC_TASK>>> {
  InspExecuteNotifier() : super({});

  Future<void> getInspExecute(INSP_EXEC_HEADER headers) async {
    try {
      List<INSP_EXEC_TASK> executeHeaders = [];
      List execute = await AppDatabaseManager()
          .select(DBInputEntity(INSP_EXEC_TASK.TABLE_NAME, {}));
      for (Map<String, dynamic> data in execute) {
        if (data[INSP_EXEC_TASK.FIELD_BLOB_VALUE] is Uint8List) {
          data[INSP_EXEC_TASK.FIELD_BLOB_VALUE] =
              base64Encode(data[INSP_EXEC_TASK.FIELD_BLOB_VALUE]);
        }
        INSP_EXEC_TASK exe = INSP_EXEC_TASK.fromJson(data);
        if (exe.insp_id == headers.insp_id &&
            exe.status == AppConstants.STATE_TASK_COMP &&
            (exe.is_irrelevant != 'true' || exe.is_discrepant == 'true')) {
          executeHeaders.add(exe);
        }
      }
      state = {...state, headers.insp_id!: executeHeaders};
    } catch (e) {
      Logger.logError('InspExecuteNotifier', 'getInspExecute', e.toString());
    }
  }

  Future<void> getAllExecTasksForAllExecHeaders(
      List<INSP_EXEC_HEADER> headers) async {
    try {
      Map<int, List<INSP_EXEC_TASK>> insp_exec_task_map = {};

      for (INSP_EXEC_HEADER header in headers) {
        List<INSP_EXEC_TASK> executeHeaders = [];
        List execute = await AppDatabaseManager()
            .select(DBInputEntity(INSP_EXEC_TASK.TABLE_NAME, {}));

        for (Map<String, dynamic> data in execute) {
          if (data[INSP_EXEC_TASK.FIELD_BLOB_VALUE] is Uint8List) {
            data[INSP_EXEC_TASK.FIELD_BLOB_VALUE] =
                base64Encode(data[INSP_EXEC_TASK.FIELD_BLOB_VALUE]);
          }
          INSP_EXEC_TASK exe = INSP_EXEC_TASK.fromJson(data);
          if (exe.insp_id == header.insp_id &&
              (exe.status == AppConstants.STATE_TASK_COMP &&
                  (exe.is_irrelevant != 'true' ||
                      exe.is_discrepant == 'true'))) {
            executeHeaders.add(exe);
          }
        }
        insp_exec_task_map[header.insp_id!] = executeHeaders;
      }

      state = insp_exec_task_map;
    } catch (e) {
      Logger.logError('InspExecuteNotifier', 'getAllExecTasksForAllExecHeaders',
          e.toString());
    }
  }
}

final numericInspectionHeaderProvider = StateNotifierProvider<
    NumericInspectionHeaderNotifier, Map<String, INSP_EXEC_TASK>>((ref) {
  return NumericInspectionHeaderNotifier();
});

class NumericInspectionHeaderNotifier
    extends StateNotifier<Map<String, INSP_EXEC_TASK>> {
  NumericInspectionHeaderNotifier() : super({});

  void setNumericValue(INSP_EXEC_TASK task) {
    final key = _generateKey(task);
    state = {
      ...state,
      key: task,
    };
  }

  INSP_EXEC_TASK? getNumericValue(INSP_EXEC_TASK task) {
    final key = _generateKey(task);
    return state[key];
  }

  void clearNumericValue(INSP_EXEC_TASK task) {
    final key = _generateKey(task);
    final updated = {...state}..remove(key);
    state = updated;
  }

  void clearAllNumericValue() {
    state = {};
  }

  String _generateKey(INSP_EXEC_TASK task) {
    return '${task.insp_id}_${task.insp_task_id}_${task.section_id}_${task.task_id}_${task.dep_insp_task_id}_${task.task_no}';
  }
}

/*final numericInspectionHeaderProvider =
    StateNotifierProvider<NumericInspectionHeaderNotifier, INSP_EXEC_TASK>(
        (ref) {
  return NumericInspectionHeaderNotifier();
});

class NumericInspectionHeaderNotifier extends StateNotifier<INSP_EXEC_TASK> {
  NumericInspectionHeaderNotifier()
      : super(INSP_EXEC_TASK(insp_id: null, insp_task_id: null, task_no: null));

  void getNumericValue(INSP_EXEC_TASK inspectionHeader) {
    try {
      state = inspectionHeader;
    } catch (e) {
      Logger.logError(
          'NumericInspectionHeaderNotifier', 'getNumericValue', e.toString());
    }
  }

  void clearNumericValue() {
    try {
      state = INSP_EXEC_TASK(insp_id: null, insp_task_id: null, task_no: null);
    } catch (e) {
      Logger.logError(
          'NumericInspectionHeaderNotifier', 'getNumericValue', e.toString());
    }
  }
}*/

/*final inspHeaderProvider =
    StateNotifierProvider<InspHeaderNotifier, INSP_EXEC_HEADER>((ref) {
  return InspHeaderNotifier();
});

class InspHeaderNotifier extends StateNotifier<INSP_EXEC_HEADER> {
  InspHeaderNotifier() : super(INSP_EXEC_HEADER(insp_id: 0));

  Future<void> selectedInspHeaders(INSP_EXEC_HEADER insp_exec_header) async {
    try {
      INSP_EXEC_HEADER? inspHeaders =
          await DbHelper.getInspectionExecHeader(insp_exec_header);
      if (inspHeaders != null) {
        state = inspHeaders;
      }
    } catch (e) {
      // Handle errors
      Logger.logError(
          'InspHeaderNotifier', 'selectedInspHeaders', e.toString());
    }
  }
}*/
