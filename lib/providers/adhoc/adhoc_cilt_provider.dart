import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/CILT_PLAN_HEADER.dart';
import 'package:rounds/be/INSPECTION_PLAN_HEADER.dart';
import 'package:rounds/be/PRIORITY_HEADER.dart';
import 'package:rounds/be/SHIFT_HEADER.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AdhocCiltState {
  final bool isToggled;
  final List<INSPECTION_PLAN_HEADER> inspectionPlans;
  final List<CILT_PLAN_HEADER> ciltPlans;
  final List<INSPECTION_PLAN_HEADER> allInspectionPlans;
  final List<CILT_PLAN_HEADER> allCiltPlans;
  final dynamic selectedPlan;
  final DateTime? startTime;
  final DateTime? endTime;
  final USER_HEADER? user_header;
  final SHIFT_HEADER? shift_header;
  final PRIORITY_HEADER? priority;

  AdhocCiltState(
      {required this.isToggled,
      required this.inspectionPlans,
      required this.ciltPlans,
      this.allInspectionPlans = const [],
      this.allCiltPlans = const [],
      this.selectedPlan,
      this.startTime,
      this.endTime,
      this.shift_header,
      this.user_header,
      this.priority});

  AdhocCiltState copyWith(
      {bool? isToggled,
      List<INSPECTION_PLAN_HEADER>? inspectionPlans,
      List<CILT_PLAN_HEADER>? ciltPlans,
      List<INSPECTION_PLAN_HEADER>? allInspectionPlans,
      List<CILT_PLAN_HEADER>? allCiltPlans,
      dynamic selectedPlan,
      DateTime? startTime,
      DateTime? endTime,
      SHIFT_HEADER? shift_header,
      USER_HEADER? user_header,
      PRIORITY_HEADER? priority}) {
    return AdhocCiltState(
        isToggled: isToggled ?? this.isToggled,
        inspectionPlans: inspectionPlans ?? this.inspectionPlans,
        ciltPlans: ciltPlans ?? this.ciltPlans,
        allInspectionPlans: allInspectionPlans ?? this.allInspectionPlans,
        allCiltPlans: allCiltPlans ?? this.allCiltPlans,
        selectedPlan: selectedPlan,
        startTime: startTime ?? this.startTime,
        endTime: endTime ?? this.endTime,
        user_header: user_header ?? this.user_header,
        shift_header: shift_header ?? this.shift_header,
        priority: priority ?? this.priority);
  }
}

class AdhocCiltNotifier extends StateNotifier<AdhocCiltState> {
  AdhocCiltNotifier()
      : super(AdhocCiltState(
            isToggled: false, inspectionPlans: [], ciltPlans: []));

  Future<void> fetchCiltPlanListHeaders() async {
    try {
      List<CILT_PLAN_HEADER> ciltPlanHeaders =
          await DbHelper.getCiltPlanListHeader();

      var preferences = await SharedPreferences.getInstance();

      List<String> seclectedSection =
          preferences.getStringList("PlantSection") ?? [];

      ciltPlanHeaders = ciltPlanHeaders
          .where((cilt_pan) => seclectedSection.contains(cilt_pan.plant_sec_id))
          .toList();

      state = state.copyWith(
          ciltPlans: ciltPlanHeaders, allCiltPlans: ciltPlanHeaders);
    } catch (e) {
      Logger.logError(
          'AdhocCiltNotifier', 'fetchCiltPlanListHeaders', e.toString());
    }
  }

  Future<void> fetchInspectionPlanListHeaders() async {
    try {
      List<INSPECTION_PLAN_HEADER> inspectionPlanHeaders =
          await DbHelper.getInspectionPlanListHeader();

      var preferences = await SharedPreferences.getInstance();

      List<String> seclectedSection =
          preferences.getStringList("PlantSection") ?? [];

      inspectionPlanHeaders = inspectionPlanHeaders
          .where((inspection) =>
              seclectedSection.contains(inspection.plant_sec_id))
          .toList();

      state = state.copyWith(
          inspectionPlans: inspectionPlanHeaders,
          allInspectionPlans: inspectionPlanHeaders);
    } catch (e) {
      Logger.logError('InspectionPlanListHeaderNotifier',
          'fetchInspectionPlanListHeaders', e.toString());
    }
  }

  Future<void> fetchCiltAndInspectionPlans() async {
    // resetState();
    await Future.wait([
      fetchInspectionPlanListHeaders(),
      fetchCiltPlanListHeaders(),
      fetchShiftOfUser(),
      fetchCurentUser()
    ]);
    updateToggleState(false);
  }

  void searchPlans(
      {required String searchQuery,
      bool isCreateInspection = true,
      bool isCreateCilt = true}) {
    if (searchQuery.isEmpty) {
      resetPlans();
    } else {
      searchPlansByTitle(
          searchQuery: searchQuery,
          isCreateInspection: isCreateInspection,
          isCreateCilt: isCreateCilt);
    }
  }

  void searchPlansByTitle(
      {required String searchQuery,
      bool isCreateInspection = true,
      bool isCreateCilt = true}) {
    if (isCreateInspection && isCreateCilt) {
      if (state.isToggled) {
        var plans = state.allCiltPlans
            .where((plan) => (plan.title ?? "")
                .toLowerCase()
                .contains(searchQuery.toLowerCase()))
            .toList();
        state = state.copyWith(ciltPlans: plans);
      } else {
        var plans = state.allInspectionPlans
            .where((plan) => (plan.title ?? "")
                .toLowerCase()
                .contains(searchQuery.toLowerCase()))
            .toList();
        state = state.copyWith(inspectionPlans: plans);
      }
    } else if (isCreateInspection) {
      var plans = state.allInspectionPlans
          .where((plan) => (plan.title ?? "")
              .toLowerCase()
              .contains(searchQuery.toLowerCase()))
          .toList();
      state = state.copyWith(inspectionPlans: plans);
    } else {
      var plans = state.allCiltPlans
          .where((plan) => (plan.title ?? "")
              .toLowerCase()
              .contains(searchQuery.toLowerCase()))
          .toList();
      state = state.copyWith(ciltPlans: plans);
    }
  }

  void resetPlans() async {
    await Future.wait([
      fetchInspectionPlanListHeaders(),
      fetchCiltPlanListHeaders(),
    ]);
  }

  void selectPlan(dynamic plan) {
    state = state.copyWith(selectedPlan: plan);
  }

  bool isPlanSelectedOrNot(dynamic plan) {
    if (state.selectedPlan != null && state.selectedPlan.lid == plan.lid) {
      return true;
    } else {
      return false;
    }
  }

  void updateToggleState(bool newState) {
    state = state.copyWith(
      isToggled: newState,
    );
  }

  Future<void> fetchShiftOfUser() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    String? shift = prefs.getString('Shift');
    String? plant = prefs.getString('Plant');
    SHIFT_HEADER? shiftHeader =
        await DbHelper.getShiftHeader(shift ?? "", plant ?? "");
    state = state.copyWith(shift_header: shiftHeader);
    if (shiftHeader != null && shiftHeader.end_time != null) {
      final now = DateTime.now();
      final endTimeParts = shiftHeader.end_time!.split(':');
      final endTime = DateTime(
        now.year,
        now.month,
        now.day,
        int.parse(endTimeParts[0]),
        int.parse(endTimeParts[1]),
        int.parse(endTimeParts[2]),
      );
      state = state.copyWith(endTime: endTime);
    }
    if (shiftHeader != null && shiftHeader.start_time != null) {
      final now = DateTime.now();
      final startTimeParts = shiftHeader.start_time!.split(':');
      final startTime = DateTime(
        now.year,
        now.month,
        now.day,
        int.parse(startTimeParts[0]),
        int.parse(startTimeParts[1]),
        int.parse(startTimeParts[2]),
      );
      state = state.copyWith(startTime: startTime);
    }
  }

  Future<void> fetchCurentUser() async {
    final data = await DbHelper.getUser();
    state = state.copyWith(user_header: data);
  }

  void updateStartTime(TimeOfDay time) {
    final now = DateTime.now();
    final startTime =
        DateTime(now.year, now.month, now.day, time.hour, time.minute);
    state =
        state.copyWith(startTime: startTime, selectedPlan: state.selectedPlan);
  }

  void updateEndTime(TimeOfDay time) {
    final now = DateTime.now();
    final endTime =
        DateTime(now.year, now.month, now.day, time.hour, time.minute);
    state = state.copyWith(endTime: endTime, selectedPlan: state.selectedPlan);
  }

  void assignUser(USER_HEADER user_header) {
    state = state.copyWith(
        user_header: user_header, selectedPlan: state.selectedPlan);
  }

  void setPriority(PRIORITY_HEADER priority) {
    state =
        state.copyWith(priority: priority, selectedPlan: state.selectedPlan);
  }

  void resetState() {
    state =
        AdhocCiltState(isToggled: false, inspectionPlans: [], ciltPlans: []);
  }
}

final adhocCiltProvider =
    StateNotifierProvider<AdhocCiltNotifier, AdhocCiltState>((ref) {
  return AdhocCiltNotifier();
});
