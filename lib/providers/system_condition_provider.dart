import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/SYSTEM_CONDITION_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';

final systemConditionProvider = StateNotifierProvider<SystemConditionNotifier,
    List<SYSTEM_CONDITION_HEADER>>((ref) {
  return SystemConditionNotifier();
});

class SystemConditionNotifier
    extends StateNotifier<List<SYSTEM_CONDITION_HEADER>> {
  SystemConditionNotifier() : super([]);

  Future<void> getSystemConditions() async {
    try {
      final data = await DbHelper.getSystemConditions();
      state = data;
    } catch (e) {
      Logger.logError(
          'SystemConditionNotifier', 'getSystemConditions', e.toString());
    }
  }

  SYSTEM_CONDITION_HEADER getSystemConditionHeader(String code) {
    try {
      return state.firstWhere(
        (element) => element.sys_cond == code,
        orElse: () => SYSTEM_CONDITION_HEADER(domain: 1, sys_cond: ""),
      );
    } catch (e, stackTrace) {
      Logger.logError(
        'SystemConditionNotifier',
        'getSystemConditionHeader',
        '$e\n$stackTrace',
      );
      return SYSTEM_CONDITION_HEADER(domain: 1, sys_cond: "");
    }
  }
}

final systemConditionInspProvider = StateNotifierProvider<
    SystemConditionInspNotifier, List<SYSTEM_CONDITION_HEADER>>((ref) {
  return SystemConditionInspNotifier();
});

class SystemConditionInspNotifier
    extends StateNotifier<List<SYSTEM_CONDITION_HEADER>> {
  SystemConditionInspNotifier() : super([]);

  Future<void> getSystemConditionsInsp() async {
    try {
      final data = await DbHelper.getSystemConditions();
      state = data;
    } catch (e) {
      Logger.logError(
          'systemConditionInspProvider', 'getSystemConditionsInsp', e.toString());
    }
  }

  SYSTEM_CONDITION_HEADER getSystemConditionHeader(String code) {
    try {
      return state.firstWhere(
        (element) => element.sys_cond == code,
        orElse: () => SYSTEM_CONDITION_HEADER(domain: 1, sys_cond: ""),
      );
    } catch (e, stackTrace) {
      Logger.logError(
        'SystemConditionNotifier',
        'getSystemConditionHeader',
        '$e\n$stackTrace',
      );
      return SYSTEM_CONDITION_HEADER(domain: 1, sys_cond: "");
    }
  }
}
