import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/utils/utils.dart';

import '../../be/JOB_HEADER.dart';
import '../../helpers/db_helper.dart';
import '../../utils/app_constants.dart';

final filteredJobCreationHeaderListProvider = StateNotifierProvider<
    FilteredJobCreationHeaderListNotifier, List<JOB_HEADER>>((ref) {
  return FilteredJobCreationHeaderListNotifier();
});

class FilteredJobCreationHeaderListNotifier
    extends StateNotifier<List<JOB_HEADER>> {
  FilteredJobCreationHeaderListNotifier() : super([]);

  Future<void> filteredJobHeaderList(
      {List<JOB_HEADER>? jobList,
      required String type,
      String? search,
      List<String>? typeList,
      List<String>? priorityList,
      List<String>? statusList,
      required String plantId,
      required List<String> plantSec}) async {
    try {
      if (jobList != null && type == 'Initial') {
        state = jobList;
      } else if ((typeList != null && typeList.isNotEmpty) ||
          (priorityList != null && priorityList.isNotEmpty) ||
          (statusList != null && statusList.isNotEmpty)) {
        List<JOB_HEADER> jobTypeHeaders =
            await DbHelper.getFilteredAndSearchedJobHeaders(
                plantId: plantId,
                plantSec: plantSec,
                typeList: typeList ?? [],
                priorityList: priorityList ?? [],
                statusList: statusList ?? [],
                searchText: search ?? "");

        jobTypeHeaders.sort((a, b) {
          DateTime dateA = convertToDateTime(a.start_date.toString());
          DateTime dateB = convertToDateTime(b.start_date.toString());
          return dateA.compareTo(dateB); // Ascending order (oldest to newest)
        });

        state = jobTypeHeaders;
      } else if (search != null && type == AppConstants.search) {
        List<JOB_HEADER> jobTypeHeaders =
            await DbHelper.getFilteredAndSearchedJobHeaders(
                plantId: plantId,
                plantSec: plantSec,
                typeList: typeList ?? [],
                priorityList: priorityList ?? [],
                statusList: statusList ?? [],
                searchText: search);

        jobTypeHeaders.sort((a, b) {
          DateTime dateA = convertToDateTime(a.start_date.toString());
          DateTime dateB = convertToDateTime(b.start_date.toString());
          return dateA.compareTo(dateB); // Ascending order (oldest to newest)
        });
        state = jobTypeHeaders;
      }
    } catch (e) {
      Logger.logError('FilteredJobHeaderListNotifier', 'filteredJobHeaderList',
          e.toString());
    }
  }
}
