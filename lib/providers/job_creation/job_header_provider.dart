import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/DOCUMENT_ATTACHMENT.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:rounds/be/JOBTYPE_HEADER.dart';
import 'package:rounds/be/JOB_HEADER.dart';
import 'package:rounds/be/PICKLIST_CODE.dart';
import 'package:rounds/be/PICKLIST_HEADER.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/utils/constants.dart';
import 'package:rounds/utils/utils.dart';
import 'package:rounds/widgets/chart_widget.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import '../../be/JOB_ACTION.dart';
import '../../be/JOB_DOCUMENT.dart';
import '../../be/PRIORITY_HEADER.dart';
import '../../helpers/db_helper.dart';

final jobHeaderListProvider =
    StateNotifierProvider<JobHeaderListNotifier, List<JOB_HEADER>>((ref) {
  return JobHeaderListNotifier();
});

class JobHeaderListNotifier extends StateNotifier<List<JOB_HEADER>> {
  JobHeaderListNotifier() : super([]);

  Future<void> fetchJobHeaderList(String plant,  {bool initial = false}) async {
    try {
      bool jobIdExists = false;
      List<JOB_HEADER> jobHeaders = await DbHelper.getJobHeaderList(plant,initial:initial);

      jobHeaders.sort((a, b) {
        DateTime dateA = convertToDateTime(a.start_date.toString());
        DateTime dateB = convertToDateTime(b.start_date.toString());
        return dateA.compareTo(dateB);
      });

      state = jobHeaders;
    } catch (e) {
      Logger.logError(
          'JobHeaderListNotifier', 'fetchJobHeaderList', e.toString());
    }
  }

  List<RoundsPieChartData> fetchRoundsPieChartData() {
    int completed = 0;
    int accepted = 0;
    int rejected = 0;
    int assigned = 0;
    int open = 0;

    List<JOB_HEADER> jobHeaders = state;

    for (var header in jobHeaders) {
      final status = header.status;

      if (status == Constants.JOB_STATE_NOCO) {
        completed++;
      } else if (status == "ASSIGNED") {
        assigned++;
      } else if (status == "CREATED") {
        open++;
      } else if (status == "ACCEPTED") {
        accepted++;
      } else if (status == "REJECTED") {
        rejected++;
      } else {
        open++;
      }
    }

    return [
      RoundsPieChartData(
        title: 'Completed',
        value: completed.toDouble(),
        color: AppColors.completedChartColor,
      ),
      RoundsPieChartData(
        title: 'Assigned',
        value: assigned.toDouble(),
        color: AppColors.assignedChartColor,
      ),
      RoundsPieChartData(
        title: 'Accepted',
        value: accepted.toDouble(),
        color: AppColors.acceptedChartColor,
      ),
      RoundsPieChartData(
        title: 'Rejected',
        value: rejected.toDouble(),
        color: AppColors.rejectedChartColor,
      ),
      RoundsPieChartData(
        title: 'Open',
        value: open.toDouble(),
        color: AppColors.openChartColor,
      ),
    ];
  }
}

final jobHeaderProvider =
    StateNotifierProvider<JobHeaderNotifier, JOB_HEADER>((ref) {
  return JobHeaderNotifier();
});

class JobHeaderNotifier extends StateNotifier<JOB_HEADER> {
  JobHeaderNotifier() : super(JOB_HEADER(job_id: 0));

  Future<void> getJobHeader({String? jobId, JOB_HEADER? data}) async {
    try {
      if (jobId != null) {
        JOB_HEADER? jobHeader = await DbHelper.getJobHeaderById(jobId);
        if (jobHeader != null) {
          state = jobHeader;
        } else {
          state = JOB_HEADER(job_id: 0);
        }
      } else if (data != null) {
        state = data;
      } else {
        state = JOB_HEADER(job_id: 0);
      }
    } catch (e) {
      Logger.logError('JobHeaderNotifier', 'getJobHeader', e.toString());
    }
  }
}

final insertJobHeaderProvider =
    StateNotifierProvider<InsertJobHeaderNotifier, JOB_HEADER>((ref) {
  return InsertJobHeaderNotifier();
});

class InsertJobHeaderNotifier extends StateNotifier<JOB_HEADER> {
  InsertJobHeaderNotifier() : super(JOB_HEADER(job_id: 0));

  Future<void> insertJobHeader(JOB_HEADER header) async {
    try {
      await AppDatabaseManager()
          .insert(DBInputEntity(JOB_HEADER.TABLE_NAME, header.toJson()));
      state = header;
    } catch (e) {
      Logger.logError('JobHeaderNotifier', 'getJobHeader', e.toString());
    }
  }
}

final jobDescriptionProvider =
    StateNotifierProvider<JobDescriptionNotifier, String>((ref) {
  return JobDescriptionNotifier();
});

class JobDescriptionNotifier extends StateNotifier<String> {
  JobDescriptionNotifier() : super('');

  void getJobDescription(String data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError(
          'JobDescriptionNotifier', 'getJobDescription', e.toString());
    }
  }

  void clearJobDescription() {
    state = '';
  }
}

final jobStartOnProvider =
    StateNotifierProvider<JobStartOnNotifier, int>((ref) {
  return JobStartOnNotifier();
});

class JobStartOnNotifier extends StateNotifier<int> {
  JobStartOnNotifier() : super(0);

  void getJobStartOn(int data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError('JobStartOnNotifier', 'getJobStartOn', e.toString());
    }
  }

  void clearStartDate() {
    state = 0;
  }
}

final jobEndOnProvider = StateNotifierProvider<JobEndOnNotifier, int>((ref) {
  return JobEndOnNotifier();
});

class JobEndOnNotifier extends StateNotifier<int> {
  JobEndOnNotifier() : super(0);

  void getJobEndOn(int data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError('JobEndOnNotifier', 'getJobEndOn', e.toString());
    }
  }

  void clearEndDate() {
    state = 0;
  }
}

final jobEstimationTimeProvider =
    StateNotifierProvider<JobEstimationTimeNotifier, int>((ref) {
  return JobEstimationTimeNotifier();
});

class JobEstimationTimeNotifier extends StateNotifier<int> {
  JobEstimationTimeNotifier() : super(0);

  void getJobEstimationTime(int data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError(
          'JobEstimationTimeNotifier', 'getJobEstimationTime', e.toString());
    }
  }
}

final jobActualTimeProvider =
    StateNotifierProvider<JobActualTimeNotifier, int>((ref) {
  return JobActualTimeNotifier();
});

class JobActualTimeNotifier extends StateNotifier<int> {
  JobActualTimeNotifier() : super(0);

  void getJobActualTime(int data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError(
          'JobActualTimeNotifier', 'getJobActualTime', e.toString());
    }
  }
}

final jobAssignedToProvider =
    StateNotifierProvider<JobAssignedToNotifier, String>((ref) {
  return JobAssignedToNotifier();
});

class JobAssignedToNotifier extends StateNotifier<String> {
  JobAssignedToNotifier() : super('');

  void getJobAssignedTo(String data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError('JobCreatedOnNotifier', 'getJobAssignedOn', e.toString());
    }
  }

  void clearAssignedTo() {
    state = '';
  }
}

final jobLongTextProvider =
    StateNotifierProvider<JobLongTextNotifier, String>((ref) {
  return JobLongTextNotifier();
});

class JobLongTextNotifier extends StateNotifier<String> {
  JobLongTextNotifier() : super('');

  void getJobLongText(String data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError('JobLongTextNotifier', 'getJobLongText', e.toString());
    }
  }

  void clearLongText() {
    state = '';
  }
}

final jobPriorityProvider =
    StateNotifierProvider<JobPriorityNotifier, PRIORITY_HEADER>((ref) {
  return JobPriorityNotifier();
});

class JobPriorityNotifier extends StateNotifier<PRIORITY_HEADER> {
  JobPriorityNotifier() : super(PRIORITY_HEADER(priority_code: ''));

  Future<void> getJobPriority(String data) async {
    try {
      PRIORITY_HEADER? priority = await DbHelper.getPriority(data);
      if (priority != null) {
        state = priority;
      }
    } catch (e) {
      Logger.logError('JobPriorityNotifier', 'getJobPriority', e.toString());
    }
  }

  void clearPriority() {
    state = PRIORITY_HEADER(priority_code: '');
  }
}

final jobTypeListProvider =
    StateNotifierProvider<JobTypeListNotifier, List<JOBTYPE_HEADER>>((ref) {
  return JobTypeListNotifier();
});

class JobTypeListNotifier extends StateNotifier<List<JOBTYPE_HEADER>> {
  JobTypeListNotifier() : super([]);

  Future<void> jobTypeList() async {
    try {
      List<JOBTYPE_HEADER> jobTypes = await DbHelper.getJobTypeList();

      state = jobTypes;
    } catch (e) {
      Logger.logError('JobTypeListNotifier', 'jobTypeList', e.toString());
    }
  }

  String fetchjobTypeFromCode(String code) {
    try {
      JOBTYPE_HEADER jobtype_header = state.firstWhere(
          (element) => element.job_type == code,
          orElse: () => JOBTYPE_HEADER(job_type: "", description: ""));
      return jobtype_header.description ?? "";
      // state = jobTypes;
    } catch (e) {
      Logger.logError('JobTypeListNotifier', 'jobTypeList', e.toString());
      return "";
    }
  }
}

final jobTypeProvider =
    StateNotifierProvider<JobTypeNotifier, JOBTYPE_HEADER>((ref) {
  return JobTypeNotifier();
});

class JobTypeNotifier extends StateNotifier<JOBTYPE_HEADER> {
  JobTypeNotifier() : super(JOBTYPE_HEADER(job_type: ''));

  Future<void> getJobType(String data) async {
    try {
      JOBTYPE_HEADER? jobTypes = await DbHelper.getJobType(data);
      if (jobTypes != null) {
        state = jobTypes;
      }
    } catch (e) {
      Logger.logError('JobTypeNotifier', 'getJobType', e.toString());
    }
  }

  void clearJobType() {
    state = JOBTYPE_HEADER(job_type: '');
  }
}

final getJobDocumentHeaderProvider =
    StateNotifierProvider<JobDocumentHeaderNotifier, List<DOCUMENT_HEADER>>(
        (ref) {
  return JobDocumentHeaderNotifier();
});

class JobDocumentHeaderNotifier extends StateNotifier<List<DOCUMENT_HEADER>> {
  JobDocumentHeaderNotifier() : super([]);

  Future<void> getJobDocumentsHeaders(String jobId) async {
    try {
      List<DOCUMENT_HEADER> headers = [];
      List<JOB_DOCUMENT> documents =
          await DbHelper.getJobDocumentByJobId(jobId);
      if (documents.isNotEmpty) {
        for (JOB_DOCUMENT doc in documents) {
          DOCUMENT_HEADER? attachment =
              await DbHelper.getDocumentHeadersByDocsId(doc.doc_id.toString());
          if (attachment != null) {
            headers.add(attachment);
          }
        }
        state = headers;
      }
    } catch (e) {
      Logger.logError(
          'JobDocumentHeaderNotifier', 'getJobDocumentsHeaders', e.toString());
    }
  }
}

final getJobActionProvider =
    StateNotifierProvider<JobActionNotifier, JOB_ACTION>((ref) {
  return JobActionNotifier();
});

class JobActionNotifier extends StateNotifier<JOB_ACTION> {
  JobActionNotifier() : super(JOB_ACTION(job_id: 0));

  Future<void> getJobAction(String jobId) async {
    try {
      JOB_ACTION? action = await DbHelper.getJobActionByJobId(jobId);
      if (action != null) {
        state = action;
      }
    } catch (e) {
      Logger.logError('JobActionNotifier', 'getJobAction', e.toString());
    }
  }
}

final getJobDocumentProvider =
    StateNotifierProvider<JobDocumentNotifier, List<DOCUMENT_HEADER>>((ref) {
  return JobDocumentNotifier();
});

class JobDocumentNotifier extends StateNotifier<List<DOCUMENT_HEADER>> {
  JobDocumentNotifier() : super([]);

  Future<void> getJobDocuments(String jobId) async {
    try {
      List<DOCUMENT_HEADER> attachments = [];
      List<JOB_DOCUMENT> headers = await DbHelper.getJobDocumentByJobId(jobId);
      if (headers.isNotEmpty) {
        for (JOB_DOCUMENT doc in headers) {
          DOCUMENT_HEADER? attachment =
              await DbHelper.getDocumentHeadersByDocsId(doc.doc_id.toString());
          if (attachment != null) {
            attachments.add(attachment);
          }
        }
        state = attachments;
      } else {
        state = [];
      }
    } catch (e) {
      Logger.logError('JobDocumentNotifier', 'getJobDocuments', e.toString());
    }
  }
}

final pickListHeaderListProvider =
    StateNotifierProvider<PickListHeaderNotifier, List<PICKLIST_HEADER>>((ref) {
  return PickListHeaderNotifier();
});

class PickListHeaderNotifier extends StateNotifier<List<PICKLIST_HEADER>> {
  PickListHeaderNotifier() : super([]);

  Future<void> getPickListHeaderList() async {
    try {
      List<PICKLIST_HEADER> pickListHeader =
          await DbHelper.getPickListHeaderList();

      state = pickListHeader;
    } catch (e) {
      Logger.logError('JobTypeNotifier', 'getJobType', e.toString());
    }
  }

  void clearPickListHeaderList() {
    state = [];
  }
}

final pickListCodeListProvider =
    StateNotifierProvider<PickListCodeNotifier, List<PICKLIST_CODE>>((ref) {
  return PickListCodeNotifier();
});

class PickListCodeNotifier extends StateNotifier<List<PICKLIST_CODE>> {
  PickListCodeNotifier() : super([]);

  Future<void> getPickListCodeList() async {
    try {
      List<PICKLIST_CODE> pickListCode = await DbHelper.getPickListCodeList();

      state = pickListCode;
    } catch (e) {
      Logger.logError(
          'PickListCodeNotifier', 'getPickListCodeList', e.toString());
    }
  }

  void clearPickListCodeList() {
    state = [];
  }
}

final pickListCodeByIdListProvider =
    StateNotifierProvider<PickListCodeByIdNotifier, List<PICKLIST_CODE>>((ref) {
  return PickListCodeByIdNotifier();
});

class PickListCodeByIdNotifier extends StateNotifier<List<PICKLIST_CODE>> {
  PickListCodeByIdNotifier() : super([]);

  Future<void> getPickListCodeListById(String id) async {
    try {
      List<PICKLIST_CODE> pickListCode =
          await DbHelper.getPickListCodeListById(id);

      state = pickListCode;
    } catch (e) {
      Logger.logError(
          'PickListCodeByIdNotifier', 'getPickListCodeListById', e.toString());
    }
  }

  void clearPickListCodeList() {
    state = [];
  }
}
