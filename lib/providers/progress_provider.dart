import 'package:flutter/foundation.dart';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

final progressProvider = ChangeNotifierProvider((ref) => ProgressProvider());

class ProgressProvider with ChangeNotifier {
  bool _tech = true;
  bool _material = false;
  bool _rounds = false;
  bool _forms = false;
  String status = '';
  double get progress => getProgressPercentage();
  int _inboxCount = 0;
  int _outboxCount = 0;
  int get inboxCount => _inboxCount;
  int get outboxCount => _outboxCount;

  int _pendingAttachments = 0;

  int get pendingAttachments => _pendingAttachments;

  set pendingAttachments(int value) {
    _pendingAttachments = value;
    notifyListeners();
  }

  Future<void> setInboxCount() async {
    _inboxCount = await SettingsHelper().getInboxCount();
    notifyListeners();
  }

  Future<void> setPendingAttachmentCount() async {
    _pendingAttachments = await SettingsHelper().getAttachmentCount();
    notifyListeners();
  }

  addInboxCount(data) {
    // type == received (inboxCount count will increase)
    // type == inbox (inboxCount count will decrease)
    if (data[EventSyncStatusFieldType] == EventSyncStatusTypeInbox ||
        data[EventSyncStatusFieldType] == EventSyncStatusTypeReceived ||
        data[EventSyncStatusFieldType] ==
            EventSyncStatusTypeInboxProcessingComplete) {
      _inboxCount = data[EventSyncStatusFieldInboxCount];
    }
    notifyListeners();
  }

// Calculate progress as a double between 0.0 and 1.0
  double getProgressPercentage() {
    int completedTasks = 0;
    if (_tech) completedTasks++;
    if (_material) completedTasks++;
    if (_rounds) completedTasks++;
    if (_forms) completedTasks++;

    // Calculate the percentage as a double between 0.0 and 1.0
    if (completedTasks == 0) {
      return 0.0;
    } else {
      return completedTasks / _inboxCount;
    }
  }

  // Setters for updating progress flags
  void setTechCompleted() {
    _tech = true;
    notifyListeners();
  }

  void setMaterialCompleted() {
    _material = true;
    notifyListeners();
  }

  void setRoundsCompleted() {
    _rounds = true;
    notifyListeners();
  }

  void setFormsCompleted() {
    _forms = true;
    notifyListeners();
  }
}
