import 'package:flutter/foundation.dart';
import 'package:rounds/providers/isolateToken/mobile.dart'
    if (dart.library.html) 'package:rounds/providers/isolateToken/web.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:isolate';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:logger/logger.dart';

final serverConnectionProvider =
    AsyncNotifierProvider<ServerConnectionNotifier, String>(
        ServerConnectionNotifier.new);

class ServerConnectionNotifier extends AsyncNotifier<String> {
  static const String _notConnectedMsg = "Not Connected";
  Isolate? _isolate;

  @override
  Future<String> build() async {
    // Default state
    return "";
  }

  void setUrl(String val) {
    if (_isolate != null) {
      debugPrint('Isolate is already running');
      return;
    }
    Logger.logInfo("ServerConnectionNotifier", 'setUrl',
        "Before start ServerConnection isolate");
    if (!kIsWeb) {
      _startIsolate(val + "/API/v3/ping");
    }
    Logger.logInfo("ServerConnectionNotifier", 'setUrl',
        "after start ServerConnection isolate");
  }

  void _startIsolate(String pingUrl) async {
    ReceivePort receivePort = ReceivePort();
    RootIsolateToken rootIsolateToken = RootIsolateToken.instance!;
    _isolate = await Isolate.spawn(
      _statusCheckIsolate,
      _IsolateOption(
        sendPort: receivePort.sendPort,
        url: pingUrl,
        token: rootIsolateToken,
      ),
    );

    receivePort.listen((message) {
      if (message is String) {
        state = AsyncValue.data(message);
      }
    });
  }

  static void _statusCheckIsolate(_IsolateOption isolateOption) async {
    backgroundIsolateToken(isolateOption.token);

    Timer.periodic(Duration(seconds: 10), (timer) async {
      String status = await _updateConnectionStatus(isolateOption.url);
      isolateOption.sendPort.send(status);
    });
  }

  static Future<String> _updateConnectionStatus(String urlString) async {
    var connectivityResult = await checkConnection();
    if (!connectivityResult) {
      return _notConnectedMsg;
    }
    var url = Uri.parse(urlString);
    try {
      final http.Response response =
          await http.get(url).timeout(Duration(seconds: 5));
      return response.statusCode == 200 ? "Connected" : _notConnectedMsg;
    } on TimeoutException {
      return _notConnectedMsg;
    } catch (e) {
      return _notConnectedMsg;
    }
  }

  static Future<bool> checkConnection() async {
    try {
      var connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      await Logger.logError(
          "ConnectivityManager", "checkConnection", e.toString());
      return false;
    }
  }
}

class _IsolateOption {
  final String url;
  final SendPort sendPort;
  final RootIsolateToken token;

  _IsolateOption({
    required this.url,
    required this.sendPort,
    required this.token,
  });
}
