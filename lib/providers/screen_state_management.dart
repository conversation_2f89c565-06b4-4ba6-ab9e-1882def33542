import 'package:shared_preferences/shared_preferences.dart';

class ScreenStateManager {
  static const String keyLastScreen = "last_screen";

  static Future<void> saveLastScreen(String screen) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(keyLastScreen, screen);
  }

  static Future<String?> getLastScreen() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(keyLastScreen);
  }
}

class UrlStateManager {
  static const String url = "url";

  static Future<void> saveUrl(String screen) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(url, screen);
  }

  static Future<String?> getUrl() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(url);
  }
}