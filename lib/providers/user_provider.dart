import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';

import '../be/ROLE_HEADER.dart';

final userProvider =
    StateNotifierProvider<UserHeaderNotifier, USER_HEADER?>((ref) {
  return UserHeaderNotifier();
});

class UserHeaderNotifier extends StateNotifier<USER_HEADER?> {
  UserHeaderNotifier() : super(null);
  Future<void> getUser() async {
    try {
      final data = await DbHelper.getUser();
      state = data;
    } catch (e) {
      Logger.logError('UserHeaderNotifier', 'getUser', e.toString());
    }
  }
}

final roleProvider =
    StateNotifierProvider<RoleHeaderNotifier, ROLE_HEADER?>((ref) {
  return RoleHeaderNotifier();
});

class RoleHeaderNotifier extends StateNotifier<ROLE_HEADER?> {
  RoleHeaderNotifier() : super(null);
  Future<void> getRole() async {
    try {
      final data = await DbHelper.getRole();
      state = data;
    } catch (e) {
      Logger.logError('RoleHeaderNotifier', 'getRole', e.toString());
    }
  }
}

final usersListProvider =
    StateNotifierProvider<UsersListNotifier, List<USER_HEADER>>((ref) {
  return UsersListNotifier();
});

class UsersListNotifier extends StateNotifier<List<USER_HEADER>> {
  UsersListNotifier() : super([]);

  Future<void> getUsers(
      {required String plantId, required List<String> plantSec}) async {
    try {
      final data = await DbHelper.getUsersData(plantId, plantSec);
      // final data = await DbHelper.getUsersData(plantId, plantSec);
      state = data;
    } catch (e) {
      Logger.logError('UsersListNotifier', 'getUsers', e.toString());
    }
  }

  Future<void> filter(String searchString,
      {required String plantId, required List<String> plantSec}) async {
    try {
      RegExp data = RegExp(searchString.toLowerCase());
      final userList = await DbHelper.getUsersData(plantId, plantSec);
      // final userList = await DbHelper.getUsersData(plantId, plantSec);
      List<USER_HEADER> filteredList = userList
          .where((element) =>
              data.hasMatch(element.first_name!.toLowerCase()) ||
              data.hasMatch(element.last_name!.toLowerCase()) ||
              data.hasMatch(element.email!.toLowerCase()) ||
              data.hasMatch(element.role!.toLowerCase()))
          .toList();

      state = filteredList;
    } catch (e) {
      Logger.logError('UsersListNotifier', 'filter', e.toString());
    }
  }
}
