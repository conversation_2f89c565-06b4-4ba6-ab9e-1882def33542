import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

final fabVisibilityProvider =
    StateNotifierProvider<FabVisibilityProvider, bool>(
  (ref) => FabVisibilityProvider(),
);

class FabVisibilityProvider extends StateNotifier<bool> {
  FabVisibilityProvider() : super(false);

  String? _context;
  static late SharedPreferences _prefs;

  String? get contextValue => _context;
  bool get isFabVisible => state;

  static Future<void> initializeSharedPrefs() async {
    _prefs = await SharedPreferences.getInstance();
  }

  Future<void> updateContext(String? context) async {
    _context = context;
    state = context != null && context.isNotEmpty;
    await updateSharedPrefsContextMessage(context);
  }

  Future<void> updateSharedPrefsContextMessage(String? contextMessage) async {
    _prefs.setString("contextMessage", contextMessage ?? "");
  }
}
