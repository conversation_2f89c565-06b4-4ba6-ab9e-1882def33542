import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';
import 'package:logger/Logger.dart';
import 'package:intl/intl.dart';

// Riverpod provider to manage the selected date
final selectedDateProvider = StateProvider<String>(
    (ref) => DateFormat('yyyyMMdd').format(DateTime.now()));

final currentDateProvider = StateProvider<String>((ref) {
  DateTime now = DateTime.now();
  return DateFormat('yyyyMMdd').format(now); // Store as date
});

final currentTimeProvider = StateProvider<String>((ref) {
  DateTime now = DateTime.now();
  return DateFormat('HHmmss').format(now); // Store as time
});

// final selectedDateProvider = StateNotifierProvider<DateNotifier, DateTime>((ref) {
//   return DateNotifier();
// });
//
// class DateNotifier extends StateNotifier<DateTime> {
//   DateNotifier() : super(DateTime.now());
//   void getDate(DateTime date) async {
//     try {
//       state = date;
//     } catch (e) {
//       Logger.logError('DateNotifier', 'getDate', e.toString());
//     }
//   }
// }
