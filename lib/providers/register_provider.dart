import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';

final registerDomainProvider =
    StateNotifierProvider<RegisterDomainNotifier, String>((ref) {
  return RegisterDomainNotifier();
});

class RegisterDomainNotifier extends StateNotifier<String> {
  RegisterDomainNotifier() : super('');

  void getRegisterDomain(String data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError(
          'RegisterDomainNotifier', 'getRegisterDomain', e.toString());
    }
  }

  void resetRegisterDomain() {
    state = '';
  }

  void clearRegisterDomain() {
    state = '';
  }
}

final registerFullNameProvider =
    StateNotifierProvider<RegisterFullNameNotifier, String>((ref) {
  return RegisterFullNameNotifier();
});

class RegisterFullNameNotifier extends StateNotifier<String> {
  RegisterFullNameNotifier() : super('');

  void getRegisterFullName(String data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError(
          'RegisterFullNameNotifier', 'getRegisterFullName', e.toString());
    }
  }

  void resetRegisterFullName() {
    state = '';
  }

  void clearRegisterFullName() {
    state = '';
  }
}

final registerLastFullNameProvider =
    StateNotifierProvider<RegisterLastFullNameNotifier, String>((ref) {
  return RegisterLastFullNameNotifier();
});

class RegisterLastFullNameNotifier extends StateNotifier<String> {
  RegisterLastFullNameNotifier() : super('');

  void getRegisterLastFullName(String data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError(
          'RegisterLastFullNameNotifier', 'getRegisterLastFullName', e.toString());
    }
  }

  void resetRegisterLastFullName() {
    state = '';
  }

  void clearRegisterLastFullName() {
    state = '';
  }
}

final registerEmailProvider =
    StateNotifierProvider<RegisterEmailNotifier, String>((ref) {
  return RegisterEmailNotifier();
});

class RegisterEmailNotifier extends StateNotifier<String> {
  RegisterEmailNotifier() : super('');

  void getRegisterEmail(String data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError(
          'RegisterEmailNotifier', 'getRegisterEmail', e.toString());
    }
  }

  void resetRegisterEmail() {
    state = '';
  }

  void clearRegisterEmail() {
    state = '';
  }
}

final registerPasswordProvider =
    StateNotifierProvider<RegisterPasswordNotifier, String>((ref) {
  return RegisterPasswordNotifier();
});

class RegisterPasswordNotifier extends StateNotifier<String> {
  RegisterPasswordNotifier() : super('');

  void getRegisterPassword(String data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError(
          'RegisterPasswordNotifier', 'getRegisterPassword', e.toString());
    }
  }

  void resetRegisterPassword() {
    state = '';
  }

  void clearRegisterPassword() {
    state = '';
  }
}

final registerConfirmPasswordProvider =
    StateNotifierProvider<RegisterConfirmPasswordNotifier, String>((ref) {
  return RegisterConfirmPasswordNotifier();
});

class RegisterConfirmPasswordNotifier extends StateNotifier<String> {
  RegisterConfirmPasswordNotifier() : super('');

  void getRegisterConfirmPassword(String data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError('RegisterConfirmPasswordNotifier',
          'getRegisterConfirmPassword', e.toString());
    }
  }

  void resetRegisterConfirmPassword() {
    state = '';
  }

  void clearRegisterConfirmPassword() {
    state = '';
  }
}

final registerPhoneProvider =
    StateNotifierProvider<RegisterPhoneNotifier, String>((ref) {
  return RegisterPhoneNotifier();
});

class RegisterPhoneNotifier extends StateNotifier<String> {
  RegisterPhoneNotifier() : super('');

  void getRegisterPhone(String data) {
    try {
      state = data;
    } catch (e) {
      Logger.logError(
          'RegisterPhoneNotifier', 'getRegisterPhone', e.toString());
    }
  }

  void resetRegisterPhone() {
    state = '';
  }

  void clearRegisterPhone() {
    state = '';
  }
}
