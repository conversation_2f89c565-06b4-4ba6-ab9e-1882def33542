import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../utils/app_colors.dart';

// final cardColorProvider = ChangeNotifierProvider((ref) => CardColorProviderr());

class CardColorProviderr extends ChangeNotifier {
  Color scaffoldColor = AppColors.scaffoldBackgroundGrey;
  Color cardColor = AppColors.white;
  Color cardBorderColor = AppColors.cardBorderGrey;

  getScaffoldColor(Color color) async {
    scaffoldColor = color;
    notifyListeners();
  }

  getCardColor(Color color) async {
    cardColor = color;
    notifyListeners();
  }

  getCardBorderColor(Color color) async {
    cardBorderColor = color;
    notifyListeners();
  }
}

final homeCardColorProvider = ChangeNotifierProvider((ref) => HomeCardColorProviderr());

class HomeCardColorProviderr extends ChangeNotifier {
  Color scaffoldColor = AppColors.scaffoldBackgroundGrey;
  Color cardColor = AppColors.white;
  Color cardBorderColor = AppColors.cardBorderGrey;

  getScaffoldColor(Color color) async {
    scaffoldColor = color;
    notifyListeners();
  }

  getCardColor(Color color) async {
    cardColor = color;
    notifyListeners();
  }

  getCardBorderColor(Color color) async {
    cardBorderColor = color;
    notifyListeners();
  }
}
