import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/ASSET_HEADER.dart';
import 'package:rounds/be/KPI_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';

final kpiHeaderProvider =
    StateNotifierProvider<KpiHeaderNotifier, List<KPI_HEADER>>((ref) {
  return KpiHeaderNotifier();
});

class KpiHeaderNotifier extends StateNotifier<List<KPI_HEADER>> {
  List<KPI_HEADER> filteredData = [];
  KpiHeaderNotifier() : super([]);

  Future<void> getKPIHeaderList(String plantId) async {
    try {
      List<KPI_HEADER> data = [];

      data = await DbHelper.getKPIHeaderList(plantId);
      state = data;
    } catch (e) {
      Logger.logError('KpiHeaderNotifier', 'getKPIHeaderList', e.toString());
    }
  }

  Future<void> filter(String value, String plantId) async {
    try {
      List<KPI_HEADER> data = [];
      data = await DbHelper.getKPIHeaderList(plantId);
      final regex = RegExp(value.replaceAll(" ", "").toLowerCase());
      filteredData = data.where((element) {
        return regex.hasMatch(element.kpi_id!
                .toString()
                .toLowerCase()) || // Replace `name` with your actual field
            regex.hasMatch(element.kpi_type!.toLowerCase()) ||
            regex.hasMatch(element.description!.toLowerCase());
      }).toList();
      state = filteredData;
    } catch (e) {
      Logger.logError('KpiHeaderNotifier', 'filter', e.toString());
    }
  }
}

final kpiIdHeaderProvider =
    StateNotifierProvider<KpiIdHeaderNotifier, KPI_HEADER>((ref) {
  return KpiIdHeaderNotifier();
});

class KpiIdHeaderNotifier extends StateNotifier<KPI_HEADER> {
  KpiIdHeaderNotifier() : super(KPI_HEADER(kpi_id: 0));

  Future<void> getKPIHeaderByKpiId(String plantId, int kpiId) async {
    try {
      KPI_HEADER? data;

      data = await DbHelper.getKPIHeaderByKpiId(
          plantId: plantId, kpiId: kpiId.toString());

      if (data != null) {
        state = data;
      } else {
        state = KPI_HEADER(kpi_id: 0);
      }
    } catch (e) {
      Logger.logError('KpiHeaderNotifier', 'getKPIHeaderByKpiId', e.toString());
    }
  }

  void resetKPIHeaderByKpiId() {
    try {
      state = KPI_HEADER(kpi_id: 0);
    } catch (e) {
      Logger.logError(
          'KpiHeaderNotifier', 'resetKPIHeaderByKpiId', e.toString());
    }
  }
}
