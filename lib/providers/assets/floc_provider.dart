import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/KPI_HEADER.dart';
import 'package:rounds/be/LOCATION_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';

final flocHeaderProvider =
    StateNotifierProvider<FLocHeaderNotifier, List<LOCATION_HEADER>>((ref) {
  return FLocHeaderNotifier();
});

class FLocHeaderNotifier extends StateNotifier<List<LOCATION_HEADER>> {
  FLocHeaderNotifier() : super([]);

  Future<void> getLocHeaderList(String plantId) async {
    try {
      List<LOCATION_HEADER> data = [];
      data = await DbHelper.getLocationHeaderList(plantId);
      state = data;
    } catch (e) {
      Logger.logError('FLocHeaderNotifier', 'getFlocHeaderList', e.toString());
    }
  }

  Future<void> filter(String value, String plantId) async {
    try {
      List<LOCATION_HEADER> data = [];
      data = await DbHelper.getLocationHeaderList(plantId);
      final regex = RegExp(value.toLowerCase());
      List<LOCATION_HEADER> filteredData = data.where((element) {
        return regex.hasMatch(element.location_id!
                .toLowerCase()) || // Replace `name` with your actual field
            regex.hasMatch(element.description!.toLowerCase()) ||
            regex.hasMatch(element.created_by!.toLowerCase()) ||
            regex.hasMatch(element.category!.toLowerCase());
      }).toList();
      state = filteredData;
    } catch (e) {
      Logger.logError('FLocHeaderNotifier', 'filter', e.toString());
    }
  }

  LOCATION_HEADER? findLocationHeadById(String id) {
    try {
      LOCATION_HEADER? found = state.firstWhereOrNull(
        (element) => element.location_id == id,
      );
      return found;
    } catch (e) {
      Logger.logError(
          'FLocHeaderNotifier', 'findLocationHeadById', e.toString());
    }
  }
}
