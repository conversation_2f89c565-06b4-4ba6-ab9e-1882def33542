import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/ASSET_HEADER.dart';
import 'package:rounds/helpers/db_helper.dart';

final assetHeaderProvider =
    StateNotifierProvider<AssetHeaderTitleNotifier, List<ASSET_HEADER>>((ref) {
  return AssetHeaderTitleNotifier();
});

class AssetHeaderTitleNotifier extends StateNotifier<List<ASSET_HEADER>> {
  AssetHeaderTitleNotifier() : super([]);

  Future<void> getAssetHeaderList(String plantId) async {
    try {
      List<ASSET_HEADER> data = [];

      data = await DbHelper.getAssetHeaderList(plantId);
      state = data;
    } catch (e) {
      Logger.logError(
          'AssetHeaderTitleNotifier', 'getAssetHeaderList', e.toString());
    }
  }

  Future<void> filter(String value, String plantId) async {
    try {
      List<ASSET_HEADER> data = [];
      data = await DbHelper.getAssetHeaderList(plantId);
      final regex = RegExp(value.toLowerCase());
      List<ASSET_HEADER> filteredData = data.where((element) {
        return regex.hasMatch((element.category ?? "")
                .toLowerCase()) || // Replace `name` with your actual field
            regex.hasMatch(element.description!.toLowerCase()) ||
            regex.hasMatch(element.asset_no!.toString().toLowerCase());
      }).toList();
      state = filteredData;
    } catch (e) {
      Logger.logError('AssetHeaderTitleNotifier', 'filter', e.toString());
    }
  }

  ASSET_HEADER? findAssetHeaderById(int id) {
    try {
      ASSET_HEADER? found = state.firstWhereOrNull(
        (element) => element.asset_no == id,
      );
      return found;
    } catch (e) {
      Logger.logError(
          'AssetHeaderTitleNotifier', 'findAssetHeaderById', e.toString());
    }
    return null;
  }
}

final singleAssetProvider =
    StateNotifierProvider<AssetTitleNotifier, ASSET_HEADER?>((ref) {
  return AssetTitleNotifier();
});

class AssetTitleNotifier extends StateNotifier<ASSET_HEADER?> {
  AssetTitleNotifier() : super(null);

  Future<void> getAssetHeader(int assetNo) async {
    try {
      ASSET_HEADER data = await DbHelper.getAssetByAssetno(assetNo);
      state = data;
    } catch (e) {
      Logger.logError(
          'AssetHeaderTitleNotifier', 'getAssetHeaderList', e.toString());
    }
  }
}
