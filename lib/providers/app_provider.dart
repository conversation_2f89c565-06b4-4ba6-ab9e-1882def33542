import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

final appProvider = ChangeNotifierProvider((ref) => AppProvider());

class AppProvider extends ChangeNotifier {
  String _userId = "";

  Future<String> getUserId() async {
    final userId = await SettingsHelper().getUserName();
    _userId = userId;
    notifyListeners(); // Notify listeners of the change
    return userId;
  }
}
