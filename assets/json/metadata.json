{"name": "ROUNDS", "description": "Rounds", "version": "1.0", "ABCINDICATOR": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "ABCINDICATOR_HEADER": {"description": "", "className": "com.unvired.rounds.be.ABCINDICATOR_HEADER", "header": true, "field": [{"name": "ABC_INDICATOR", "description": "", "isGid": true, "length": "1", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "APP_SETTING": {"description": "App Setting", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "APP_SETTING_HEADER": {"className": "com.unvired.rounds.be.APP_SETTING_HEADER", "header": true, "field": [{"name": "PROP_NAME", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "PROP_VALUE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "ASSET": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "ASSET_HEADER": {"description": "", "className": "com.unvired.rounds.be.ASSET_HEADER", "header": true, "field": [{"name": "ASSET_NO", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "PLANT_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT_SEC_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_ID", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_SOURCE", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "CATEGORY", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARENT_LOC_ID", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARENT_ASSET_NO", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TECHNICAL_ID", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "MANUFACTURER", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "ABC_INDICATOR", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LATITUDE", "description": "", "isGid": false, "length": "9", "mandatory": false, "sqlType": "REAL"}, {"name": "LONGITUDE", "description": "", "isGid": false, "length": "9", "mandatory": false, "sqlType": "REAL"}, {"name": "CREATED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHANGED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHANGED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "ASSET_DOCUMENT": {"className": "com.unvired.rounds.be.ASSET_DOCUMENT", "field": [{"name": "ASSET_NO", "description": "Asset_No is like the Unique Id of an Asset", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "DOC_ID", "description": "Unique Id of an Documnet or file", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "ASSET_CATEGORY": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "ASSET_CATEGORY_HEADER": {"description": "", "className": "com.unvired.rounds.be.ASSET_CATEGORY_HEADER", "header": true, "field": [{"name": "CATEGORY_CODE", "description": "", "isGid": true, "length": "10", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "CALENDAR": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CALENDAR_HEADER": {"description": "", "className": "com.unvired.rounds.be.CALENDAR_HEADER", "header": true, "field": [{"name": "CALENDAR_ID", "description": "", "isGid": true, "length": "2", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "45", "mandatory": false, "sqlType": "TEXT"}, {"name": "SUNDAY", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "MONDAY", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "TUESDAY", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "WEDNESDAY", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "THURSDAY", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "FRIDAY", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SATURDAY", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "CALENDAR_HOLIDAY": {"className": "com.unvired.rounds.be.CALENDAR_HOLIDAY", "field": [{"name": "CALENDAR_ID", "description": "", "isGid": true, "length": "2", "mandatory": true, "sqlType": "TEXT"}, {"name": "YEAR", "description": "", "isGid": true, "length": "10", "mandatory": true, "sqlType": "INTEGER"}, {"name": "DATE", "description": "Date of an calendar", "isGid": true, "length": "10", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}]}}, "CILT_EXEC": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CILT_EXEC_HEADER": {"description": "", "className": "com.unvired.rounds.be.CILT_EXEC_HEADER", "header": true, "field": [{"name": "CILT_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "PLAN_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "SCHED_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "PLANT_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT_SEC_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "SHIFT", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "STATUS", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRIORITY", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "START_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "START_AT", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "END_AT", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "ASSIGNED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSIGNED_TO", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSIGNED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "ACCEPTED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "COMPLETED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMPLETED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "REASON", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "SKIP_COMMENTS", "description": "", "isGid": false, "length": "65535", "mandatory": false, "sqlType": "TEXT"}, {"name": "DELAY_COMMENTS", "description": "", "isGid": false, "length": "65535", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMMENTS", "description": "", "isGid": false, "length": "65535", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "CILT_EXEC_SEC": {"className": "com.unvired.rounds.be.CILT_EXEC_SEC", "field": [{"name": "CILT_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "SECTION_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "REASON", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "SKIP_COMMENTS", "description": "", "isGid": false, "length": "65535", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "CILT_EXEC_TASK": {"className": "com.unvired.rounds.be.CILT_EXEC_TASK", "field": [{"name": "CILT_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "CILT_TASK_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "TASK_NO", "description": "", "isGid": false, "length": "10", "mandatory": true, "sqlType": "INTEGER"}, {"name": "SECTION_ID", "description": "", "isGid": false, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "TASK_ID", "description": "", "isGid": false, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "STATUS", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMPLETED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMPLETED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "LATITUDE", "description": "", "isGid": false, "length": "9", "mandatory": false, "sqlType": "REAL"}, {"name": "LONGITUDE", "description": "", "isGid": false, "length": "9", "mandatory": false, "sqlType": "REAL"}, {"name": "REASON", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "SKIP_COMMENTS", "description": "", "isGid": false, "length": "65535", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMMENTS", "description": "", "isGid": false, "length": "65535", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "CILT_EXEC_ACTION": {"className": "com.unvired.rounds.be.CILT_EXEC_ACTION", "field": [{"name": "CILT_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "USER_ACTION", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "CONTEXT1", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONTEXT2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONTEXT3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "CILT_EXEC_DOC": {"className": "com.unvired.rounds.be.CILT_EXEC_DOC", "field": [{"name": "CILT_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "DOC_ID", "description": "Unique Id of an Documnet or file", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "CILT_TASK_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "CILT_PLAN": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CILT_PLAN_HEADER": {"description": "", "className": "com.unvired.rounds.be.CILT_PLAN_HEADER", "header": true, "field": [{"name": "PLAN_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "PLANT_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT_SEC_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "TITLE", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "REF_OBJECT", "description": "L - Location A - Asset", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCATION_ID", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_NO", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "MACHINE_PART", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "STANDARD", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "METHOD", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "TOOL", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXEC_TIME", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHANGED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHANGED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "STATUS", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "CILT_SECTION": {"className": "com.unvired.rounds.be.CILT_SECTION", "field": [{"name": "SECTION_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "PLAN_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "TITLE", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "SEQ_NO", "description": "", "isGid": false, "length": "3", "mandatory": false, "sqlType": "INTEGER"}, {"name": "LOCATION_ID", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_NO", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CILT_CODE", "description": "", "isGid": false, "length": "3", "mandatory": false, "sqlType": "INTEGER"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "CILT_TASK": {"className": "com.unvired.rounds.be.CILT_TASK", "field": [{"name": "PLAN_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "TASK_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "SECTION_ID", "description": "Unique Id of an Section", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "SEQ_NO", "description": "", "isGid": false, "length": "3", "mandatory": true, "sqlType": "INTEGER"}, {"name": "CILT_CODE", "description": "", "isGid": false, "length": "3", "mandatory": false, "sqlType": "INTEGER"}, {"name": "LOCATION_ID", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_NO", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "SYS_COND", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACTIVITY", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "MACHINE_PART", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "STANDARD", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "METHOD", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TOOL", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "DURATION", "description": "How much duration required to execute the task ", "isGid": false, "length": "4", "mandatory": false, "sqlType": "REAL"}, {"name": "UOM", "description": "", "isGid": false, "length": "6", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "CILT_PLAN_DOC": {"description": "", "className": "com.unvired.rounds.be.CILT_PLAN_DOC", "field": [{"name": "PLAN_ID", "description": "Unique Id of an CILT plan", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "DOC_ID", "description": "Unique Id of an Documnet or file", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "CILT_TASK_DOC": {"className": "com.unvired.rounds.be.CILT_TASK_DOC", "field": [{"name": "PLAN_ID", "description": "Unique Id of an CILT plan", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "TASK_ID", "description": "Unique Id of an Task ", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "SECTION_ID", "description": "Unique Id of an Section", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "DOC_ID", "description": "Unique Id of an Documnet or file", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "CILT_SCHED": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CILT_SCHED_HEADER": {"description": "", "className": "com.unvired.rounds.be.CILT_SCHED_HEADER", "header": true, "field": [{"name": "SCHED_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "PLAN_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "PRIORITY", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "SCHEDULE", "description": "", "isGid": false, "length": "16777215", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSGN_TYPE", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSGN_VALUE", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "COUNTRY": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "COUNTRY_HEADER": {"description": "", "className": "com.unvired.rounds.be.COUNTRY_HEADER", "header": true, "field": [{"name": "COUNTRY_CODE", "description": "", "isGid": true, "length": "2", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "45", "mandatory": false, "sqlType": "TEXT"}]}}, "DOCUMENT": {"description": "", "attachments": true, "onConflict": "SERVER_WINS", "save": true, "DOCUMENT_HEADER": {"description": "", "className": "com.unvired.rounds.be.DOCUMENT_HEADER", "header": true, "field": [{"name": "DOC_ID", "description": "", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "FOLDER_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TITLE", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "DOC_TYPE", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "SOURCE", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_DOC_ID", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "THUMBNAIL", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHANGED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHANGED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "DOCUMENT_ATTACHMENT": {"description": "Attachment", "className": "com.unvired.rounds.be.DOCUMENT_ATTACHMENT", "attachment": true, "field": [{"name": "UID", "description": "UID", "isGid": true, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "File Name", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Mime Type", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Download URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "External or Internal URL", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL_REQUIRES_AUTH", "description": "External URL Requires Authentication", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "description": "Path to the file on the device", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "NO_CACHE", "description": "Do not cache", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SERVER_TIMESTAMP", "description": "Server timestamp", "isGid": false, "length": "20", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TAG1", "description": "Tag 1", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG2", "description": "Tag 2", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG3", "description": "Tag 3", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG4", "description": "Tag 4", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG5", "description": "Tag 5", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_STATUS", "description": "Status", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AUTO_DOWNLOAD", "description": "Auto Download Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARAM", "description": "Name of the param", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message from User", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "FAILURE_MODE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "FAILURE_MODE_HEADER": {"description": "", "className": "com.unvired.rounds.be.FAILURE_MODE_HEADER", "header": true, "field": [{"name": "FAILURE_CODE", "description": "", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "Description of Failure ", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "FAULT": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "FAULT_HEADER": {"description": "", "className": "com.unvired.rounds.be.FAULT_HEADER", "header": true, "field": [{"name": "FAULT_ID", "description": "Unique Id of an Fault", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "FAULT_TYPE", "description": "Fault Type ", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "FAILURE_MODE", "description": "", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRIORITY", "description": "Priority 1=Highest , 2=High , 3=Medium , 4= Low , 5=Lowest", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "Detailed Description of Fault ", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCATION_ID", "description": "Funcational location Id", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_NO", "description": "Equipment Number ", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "STATUS", "description": "Status of Fault  =  Open , Inprogress , Close ", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSP_TASK_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CILT_TASK_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "REPORTED_BY", "description": "Reported By = Person Name or Number who Reported ", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "REPORTED_ON", "description": "Fault Reported Date", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "REQ_START", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "REQ_END", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "MALF_START", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "MALF_END", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "BREAKDOWN", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "JOB_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "EXTERNAL_ID", "description": "ERP Fault Id ", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_SOURCE", "description": "ERP Source", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHANGED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHANGED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "DETAILS", "description": "Detailed Description ", "isGid": false, "length": "2147483647", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "FAULT_ACTION": {"className": "com.unvired.rounds.be.FAULT_ACTION", "field": [{"name": "FAULT_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "USER_ACTION", "isGid": false, "length": "0", "mandatory": true, "sqlType": "TEXT"}]}, "FAULT_DOCUMENT": {"className": "com.unvired.rounds.be.FAULT_DOCUMENT", "field": [{"name": "FAULT_ID", "description": "Unique Id of an Fault", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "DOC_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "FAULT_TYPE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "FAULT_TYPE_HEADER": {"description": "", "className": "com.unvired.rounds.be.FAULT_TYPE_HEADER", "header": true, "field": [{"name": "FAULT_CODE", "description": "", "isGid": true, "length": "2", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "Desxription Of Fault", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "FOLDER": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "FOLDER_HEADER": {"description": "", "className": "com.unvired.rounds.be.FOLDER_HEADER", "header": true, "field": [{"name": "FOLDER_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "PARENT_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "NAME", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHANGED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHANGED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "INSPECTION_PLAN": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INSPECTION_PLAN_HEADER": {"description": "", "className": "com.unvired.rounds.be.INSPECTION_PLAN_HEADER", "header": true, "field": [{"name": "PLAN_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "PLAN_TYPE", "description": "", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT_SEC_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "TITLE", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "REF_OBJECT", "description": "L - Location A - Asset", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCATION_ID", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_NO", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CREATED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHANGED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHANGED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "STATUS", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "INSPECTION_SECTION": {"className": "com.unvired.rounds.be.INSPECTION_SECTION", "field": [{"name": "SECTION_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "PLAN_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "TITLE", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "SEQ_NO", "description": "", "isGid": false, "length": "3", "mandatory": false, "sqlType": "INTEGER"}, {"name": "LOCATION_ID", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_NO", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "INSPECTION_TASK": {"className": "com.unvired.rounds.be.INSPECTION_TASK", "field": [{"name": "PLAN_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "TASK_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "SECTION_ID", "description": "Unique Id of an Section", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "SEQ_NO", "description": "", "isGid": false, "length": "3", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TASK_TYPE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "TITLE", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCATION_ID", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_NO", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "SYS_COND", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "MANDATORY", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PICKLIST_ID", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "LENGTH", "description": "", "isGid": false, "length": "3", "mandatory": false, "sqlType": "INTEGER"}, {"name": "ACCURACY", "description": "", "isGid": false, "length": "3", "mandatory": false, "sqlType": "INTEGER"}, {"name": "UOM", "description": "", "isGid": false, "length": "6", "mandatory": false, "sqlType": "TEXT"}, {"name": "UPPER_LIMIT", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "REAL"}, {"name": "LOWER_LIMIT", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "REAL"}, {"name": "STANDARD_VALUE", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "REAL"}, {"name": "UPPER_LIMIT_MSG", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOWER_LIMIT_MSG", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "KPI_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "DEPENDENT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "DEP_TASK_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "DEP_COND_CODE", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "DEP_COND_VAL", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "INSPECTION_PLAN_DOC": {"description": "", "className": "com.unvired.rounds.be.INSPECTION_PLAN_DOC", "field": [{"name": "PLAN_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "DOC_ID", "description": "Unique Id of an Documnet or file", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}]}, "INSPECTION_TASK_DOC": {"description": "", "className": "com.unvired.rounds.be.INSPECTION_TASK_DOC", "field": [{"name": "PLAN_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "TASK_ID", "description": "Unique Id of an Task in a inspection plan", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "SECTION_ID", "description": "Unique Id of an section in an inspection plan", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "DOC_ID", "description": "Unique Id of an Documnet or file", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}]}}, "INSP_EXEC": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INSP_EXEC_HEADER": {"description": "", "className": "com.unvired.rounds.be.INSP_EXEC_HEADER", "header": true, "field": [{"name": "INSP_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "PLAN_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "PLAN_TYPE", "description": "", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "SCHED_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "PLANT_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT_SEC_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "SHIFT", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "STATUS", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRIORITY", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "START_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "START_AT", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "END_AT", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "ASSIGNED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSIGNED_TO", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSIGNED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "ACCEPTED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "COMPLETED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMPLETED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "REASON", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "SKIP_COMMENTS", "description": "", "isGid": false, "length": "65535", "mandatory": false, "sqlType": "TEXT"}, {"name": "DELAY_COMMENTS", "description": "", "isGid": false, "length": "65535", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMMENTS", "description": "", "isGid": false, "length": "65535", "mandatory": false, "sqlType": "TEXT"}, {"name": "WORK_DURATION", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "INSP_EXEC_SEC": {"className": "com.unvired.rounds.be.INSP_EXEC_SEC", "field": [{"name": "INSP_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "SECTION_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "REASON", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "SKIP_COMMENTS", "description": "", "isGid": false, "length": "65535", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "INSP_EXEC_TASK": {"className": "com.unvired.rounds.be.INSP_EXEC_TASK", "field": [{"name": "INSP_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "INSP_TASK_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "TASK_NO", "description": "", "isGid": true, "length": "10", "mandatory": true, "sqlType": "INTEGER"}, {"name": "DEP_INSP_TASK_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "SECTION_ID", "description": "", "isGid": false, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "TASK_ID", "description": "", "isGid": false, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "STATUS", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMPLETED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMPLETED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "LATITUDE", "description": "", "isGid": false, "length": "9", "mandatory": false, "sqlType": "REAL"}, {"name": "LONGITUDE", "description": "", "isGid": false, "length": "9", "mandatory": false, "sqlType": "REAL"}, {"name": "NUM_VALUE", "description": "", "isGid": false, "length": "13", "mandatory": false, "sqlType": "REAL"}, {"name": "STR_VALUE", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "BLOB_VALUE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "REASON", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "SKIP_COMMENTS", "description": "", "isGid": false, "length": "65535", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMMENTS", "description": "", "isGid": false, "length": "65535", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_SKIPPED", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_IRRELEVANT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_DISCREPANT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "INSP_EXEC_DOC": {"className": "com.unvired.rounds.be.INSP_EXEC_DOC", "field": [{"name": "INSP_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "DOC_ID", "description": "", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "INSP_TASK_ID", "description": "Unique Id of an Task ", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "INSP_EXEC_ACTION": {"className": "com.unvired.rounds.be.INSP_EXEC_ACTION", "field": [{"name": "INSP_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "USER_ACTION", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "CONTEXT1", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONTEXT2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONTEXT3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INSP_PLAN_TYPE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INSP_PLAN_TYPE_HEADER": {"description": "", "className": "com.unvired.rounds.be.INSP_PLAN_TYPE_HEADER", "header": true, "field": [{"name": "PLAN_TYPE", "description": "", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "INSP_SCHED": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INSP_SCHED_HEADER": {"description": "", "className": "com.unvired.rounds.be.INSP_SCHED_HEADER", "header": true, "field": [{"name": "SCHED_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "PLAN_ID", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "PRIORITY", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "SCHEDULE", "description": "", "isGid": false, "length": "16777215", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSGN_TYPE", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSGN_VALUE", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "JOB": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "JOB_HEADER": {"description": "", "className": "com.unvired.rounds.be.JOB_HEADER", "header": true, "field": [{"name": "JOB_ID", "description": "Unique Id of an Job", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "JOB_TYPE", "description": "", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRIORITY", "description": "Priority of an Job 1=Highest , 2=High , 3=Medium , 4=Low , 5=Lowest", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "Description of an Job", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "FAULT_ID", "description": "Associated Fault Id ", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "PLANT_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCATION_ID", "description": "Associated Funcational Location Id", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_NO", "description": "Associated Equipment Number ", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "STATUS", "description": "Status of an Job Open , Inprogress , Closed ", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_BY", "description": "Job Created By User Name/ID", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_ON", "description": "Job Created Date", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHANGED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHANGED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "ASSIGNED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSIGNED_TO", "description": "Job Assigned to ", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSIGNED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "START_DATE", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "END_DATE", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "COMPLETED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "EXTERNAL_ID", "description": "ERP Reference Number ", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_SOURCE", "description": "ERP Source ", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "DETAILS", "description": "Detailed Description ", "isGid": false, "length": "2147483647", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "JOB_DOCUMENT": {"className": "com.unvired.rounds.be.JOB_DOCUMENT", "field": [{"name": "JOB_ID", "description": "Unique Id of an Job", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "DOC_ID", "description": "Id of an Document", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "JOB_ACTION": {"className": "com.unvired.rounds.be.JOB_ACTION", "field": [{"name": "JOB_ID", "description": "Unique Id of an Job", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "USER_ACTION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "JOBTYPE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "JOBTYPE_HEADER": {"className": "com.unvired.rounds.be.JOBTYPE_HEADER", "header": true, "field": [{"name": "JOB_TYPE", "description": "", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "KPI": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "KPI_HEADER": {"description": "", "className": "com.unvired.rounds.be.KPI_HEADER", "header": true, "field": [{"name": "KPI_ID", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "PLANT_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "POSITION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "REF_OBJECT", "description": "L - Location A - Asset", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCATION_ID", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_NO", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "EXTERNAL_ID", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "SOURCE", "description": "' ', SAP", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "KPI_TYPE", "description": "1 - Qualitative 2 - Quantitative 3 - Counter", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PICKLIST_ID", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "LENGTH", "description": "", "isGid": false, "length": "3", "mandatory": false, "sqlType": "INTEGER"}, {"name": "ACCURACY", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}, {"name": "UOM", "description": "", "isGid": false, "length": "6", "mandatory": false, "sqlType": "TEXT"}, {"name": "UPPER_LIMIT", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "REAL"}, {"name": "STANDARD_VALUE", "description": "", "isGid": false, "length": "13", "mandatory": false, "sqlType": "REAL"}, {"name": "LOWER_LIMIT", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "REAL"}, {"name": "UPPER_LIMIT_MSG", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOWER_LIMIT_MSG", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "OVERFLOW", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "REAL"}, {"name": "START_COUNTER", "description": "", "isGid": false, "length": "13", "mandatory": false, "sqlType": "REAL"}, {"name": "CNT_STEP_ALERT", "description": "", "isGid": false, "length": "13", "mandatory": false, "sqlType": "REAL"}, {"name": "CREATED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHANGED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHANGED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "LOCATION": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "LOCATION_HEADER": {"description": "", "className": "com.unvired.rounds.be.LOCATION_HEADER", "header": true, "field": [{"name": "LOCATION_ID", "description": "", "isGid": true, "length": "50", "mandatory": true, "sqlType": "TEXT"}, {"name": "PLANT_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT_SEC_ID", "description": "", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_ID", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_SOURCE", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "CATEGORY", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARENT_LOC_ID", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "TECHNICAL_ID", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "MANUFACTURER", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "ABC_INDICATOR", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LATITUDE", "description": "", "isGid": false, "length": "9", "mandatory": false, "sqlType": "REAL"}, {"name": "LONGITUDE", "description": "", "isGid": false, "length": "9", "mandatory": false, "sqlType": "REAL"}, {"name": "CREATED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHANGED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHANGED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "LOCATION_DOCUMENT": {"description": "", "className": "com.unvired.rounds.be.LOCATION_DOCUMENT", "field": [{"name": "LOCATION_ID", "description": "Unique Id of an functional location ", "isGid": true, "length": "50", "mandatory": true, "sqlType": "TEXT"}, {"name": "DOC_ID", "description": "Unique Id of an Documnet or file", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "LOCATION_CATEGORY": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "LOCATION_CATEGORY_HEADER": {"description": "", "className": "com.unvired.rounds.be.LOCATION_CATEGORY_HEADER", "header": true, "field": [{"name": "CATEGORY_CODE", "description": "", "isGid": true, "length": "10", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "45", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "PICKLIST": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "PICKLIST_HEADER": {"description": "", "className": "com.unvired.rounds.be.PICKLIST_HEADER", "header": true, "field": [{"name": "PICKLIST_ID", "description": "", "isGid": true, "length": "10", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "PICKLIST_CODE": {"className": "com.unvired.rounds.be.PICKLIST_CODE", "field": [{"name": "PICKLIST_ID", "description": "", "isGid": true, "length": "10", "mandatory": true, "sqlType": "TEXT"}, {"name": "CODE", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACCCEPTABLE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "COLOR", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "PLANT": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "PLANT_HEADER": {"description": "", "className": "com.unvired.rounds.be.PLANT_HEADER", "header": true, "field": [{"name": "PLANT_ID", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "PLANT_NAME", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "ADDRESS", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "CITY", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "STATE", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "PINCODE", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "COUNTRY", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "LATITUDE", "description": "", "isGid": false, "length": "9", "mandatory": false, "sqlType": "REAL"}, {"name": "LONGITUDE", "description": "", "isGid": false, "length": "9", "mandatory": false, "sqlType": "REAL"}, {"name": "STRUCTURE", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CALENDAR_ID", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "PLANT_SECTION": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "PLANT_SECTION_HEADER": {"description": "", "className": "com.unvired.rounds.be.PLANT_SECTION_HEADER", "header": true, "field": [{"name": "PLANT_ID", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "SECTION_ID", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}]}}, "PRIORITY": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "PRIORITY_HEADER": {"description": "", "className": "com.unvired.rounds.be.PRIORITY_HEADER", "header": true, "field": [{"name": "PRIORITY_CODE", "description": "1,2,3,4,5", "isGid": true, "length": "2", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "1=Highest,2=High,3=Medium,4=Low,5=Lowest", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "Status of an Priority Code 1=Active/Created or 0=Inactive/Deleted ", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "ROLE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "ROLE_HEADER": {"description": "", "className": "com.unvired.rounds.be.ROLE_HEADER", "header": true, "field": [{"name": "ROLE_ID", "description": "", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}, {"name": "CONFIG_MGMT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "USER_MGMT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_MGMT", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANNING", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SCHEDULING", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "REPORTING", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "DMS", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CILT", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}, {"name": "INSPECTION", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}, {"name": "FAULT", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TASK", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "SHIFT": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "SHIFT_HEADER": {"description": "", "className": "com.unvired.rounds.be.SHIFT_HEADER", "header": true, "field": [{"name": "PLANT_ID", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "SHIFT_CODE", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "SHIFT_NAME", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "START_TIME", "description": "Start time of an shift", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "END_TIME", "description": "End time of an shift", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHANGED_BY", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHANGED_ON", "description": "", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "SKIP_REASON": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "SKIP_REASON_HEADER": {"description": "", "className": "com.unvired.rounds.be.SKIP_REASON_HEADER", "header": true, "field": [{"name": "REASON", "description": "", "isGid": true, "length": "2", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "CATEGORY", "description": "000 - Header, Section, Task", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "STATE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "STATE_HEADER": {"description": "", "className": "com.unvired.rounds.be.STATE_HEADER", "header": true, "field": [{"name": "COUNTRY_CODE", "description": "", "isGid": true, "length": "2", "mandatory": true, "sqlType": "TEXT"}, {"name": "STATE_CODE", "description": "", "isGid": true, "length": "2", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}]}}, "SYSTEM_CONDITION": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "SYSTEM_CONDITION_HEADER": {"description": "", "className": "com.unvired.rounds.be.SYSTEM_CONDITION_HEADER", "header": true, "field": [{"name": "DOMAIN", "description": "", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "SYS_COND", "description": "", "isGid": true, "length": "2", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "COLOR", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "UNIT": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "UNIT_HEADER": {"description": "", "className": "com.unvired.rounds.be.UNIT_HEADER", "header": true, "field": [{"name": "UOM", "description": "", "isGid": true, "length": "6", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "DIMENSION", "description": "", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "USER": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "USER_HEADER": {"description": "", "className": "com.unvired.rounds.be.USER_HEADER", "header": true, "field": [{"name": "USER_ID", "description": "", "isGid": true, "length": "50", "mandatory": true, "sqlType": "TEXT"}, {"name": "FIRST_NAME", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_NAME", "description": "", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "PHONE", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMAIL", "description": "", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ROLE", "description": "", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "LANGUAGE", "description": "", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "THUMBNAIL", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INACTIVE", "description": "", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "USER_PLANT": {"className": "com.unvired.rounds.be.USER_PLANT", "field": [{"name": "USER_ID", "description": "", "isGid": true, "length": "50", "mandatory": true, "sqlType": "TEXT"}, {"name": "PLANT_ID", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}]}, "USER_PLANT_SECTION": {"className": "com.unvired.rounds.be.USER_PLANT_SECTION", "field": [{"name": "USER_ID", "description": "", "isGid": true, "length": "50", "mandatory": true, "sqlType": "TEXT"}, {"name": "PLANT_ID", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}, {"name": "PLANT_SEC_ID", "description": "", "isGid": true, "length": "5", "mandatory": true, "sqlType": "TEXT"}]}}, "USER_GROUP": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "USER_GROUP_HEADER": {"description": "", "className": "com.unvired.rounds.be.USER_GROUP_HEADER", "header": true, "field": [{"name": "GROUP_ID", "description": "Unique Id of an Group", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "Description of an Group", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT_ID", "description": "Associated Plant", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}]}, "USER_GROUP_MEMBER": {"className": "com.unvired.rounds.be.USER_GROUP_MEMBER", "field": [{"name": "GROUP_ID", "description": "Unique Group Id ", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}, {"name": "USER_ID", "description": "List of User id in specific Group", "isGid": true, "length": "50", "mandatory": true, "sqlType": "TEXT"}]}}, "Index": []}