<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        canvas.drawing,
        canvas.drawingBuffer {
            position: absolute;
            left: 0;
            top: 0;
        }

        html,
        body {
            margin: 0;
            padding: 0;
            height: 200%;
            overflow: hidden;
        }

        #reader {
            width: 100%;
            height: 18.8.....%;
        }
    </style>
    <script src="html5-qrcode.min.js"></script>
</head>

<body>
    <div id="reader"></div>
    <script>
        const formatsToSupport = [
            Html5QrcodeSupportedFormats.QR_CODE,
            Html5QrcodeSupportedFormats.AZTEC,
            Html5QrcodeSupportedFormats.CODABAR,
            Html5QrcodeSupportedFormats.CODE_39,
            Html5QrcodeSupportedFormats.CODE_93,
            Html5QrcodeSupportedFormats.CODE_128,
            Html5QrcodeSupportedFormats.DATA_MATRIX,
            Html5QrcodeSupportedFormats.MAXICODE,
            Html5QrcodeSupportedFormats.ITF,
            Html5QrcodeSupportedFormats.EAN_13,
            Html5QrcodeSupportedFormats.EAN_8,
            Html5QrcodeSupportedFormats.PDF_417,
            Html5QrcodeSupportedFormats.RSS_14,
            Html5QrcodeSupportedFormats.RSS_EXPANDED,
            Html5QrcodeSupportedFormats.UPC_A,
            Html5QrcodeSupportedFormats.UPC_E
        ];

        const html5QrCode = new Html5Qrcode("reader");

        const qrCodeSuccessCallback = (decodedText, decodedResult) => {

            if (
                typeof window.chrome !== "undefined" &&
                typeof window.chrome.webview !== "undefined"
            ) {
                var param = {
                    "methodName": "successCallback",
                    "data": decodedText
                };
                window.chrome.webview.postMessage(param);
            }
        };

        const config = {
            fps: 10,
            qrbox: { width: 250, height: 150 },
            formatsToSupport: formatsToSupport
        };

        html5QrCode.start(
            { facingMode: "environment" },
            config,
            qrCodeSuccessCallback
        ).catch(err => {
            console.error("Failed to start scanner:", err);
            document.body.innerHTML += "<div style='color:red;'>Error: " + err + "</div>";
        });

        // Listen for "close" message from Flutter (optional)
        if (
            typeof window.chrome !== "undefined" &&
            typeof window.chrome.webview !== "undefined"
        ) {
            window.chrome.webview.addEventListener('message', function (e) {
                try {
                    if (typeof e.data === "string") {
                        let data = JSON.parse(e.data);
                        if (data.event === "close") {
                            html5QrCode.stop();
                        }
                    }
                } catch (ex) {
                    // Not a JSON object, ignore
                }
            });
        }
    </script>
</body>

</html>