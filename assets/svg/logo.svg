<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        fill: #fff;
      }

      .cls-3 {
        fill: url(#linear-gradient-4);
      }

      .cls-4 {
        fill: url(#linear-gradient-3);
      }

      .cls-5 {
        fill: url(#linear-gradient-5);
      }

      .cls-6 {
        fill: url(#linear-gradient-8);
      }

      .cls-7 {
        fill: url(#linear-gradient-7);
      }

      .cls-8 {
        fill: url(#linear-gradient-6);
      }

      .cls-9 {
        fill: url(#linear-gradient);
      }
    </style>
    <linearGradient id="linear-gradient" x1="177.57" y1="116.21" x2="324.16" y2="116.21" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1b84cd"/>
      <stop offset=".26" stop-color="#1e75c0"/>
      <stop offset=".75" stop-color="#264ea1"/>
      <stop offset="1" stop-color="#2b388f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="194.9" y1="102.23" x2="230.91" y2="102.23" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-3" x1="219.77" y1="186.23" x2="309.98" y2="186.23" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-4" x1="259.12" y1="326.47" x2="392.95" y2="326.47" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-5" x1="172.91" y1="258.41" x2="226.26" y2="258.41" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-6" x1="118.88" y1="368.26" x2="259.63" y2="368.26" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-7" x1="222.96" y1="91.26" x2="277.2" y2="91.26" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-8" x1="123.23" y1="422.08" x2="177.18" y2="422.08" xlink:href="#linear-gradient"/>
  </defs>
  <g>
    <path class="cls-9" d="M176.38,155.77c7.42,2.1,15.56,2.56,23.21,2.88,22.41.94,45-1.9,66.93-6.35,19.83-4.02,39.3-9.63,58.42-16.2,1.86-.64,3.96-2.6,3.67-4.82-2.41-18.28-7-40.1-22.74-51.84-5.42-4.04-12.39-6.63-19.21-5.62-6.34.94-3.65,10.58,2.66,9.64-1.75.26-1.13.02-.09.1.47.04.96.03,1.43.05,1.45.06-.01-.05.55.05,3.77.71,5.92,1.59,9.11,4.03,12.63,9.66,16.35,28.87,18.29,43.58l3.67-4.82c-29.23,10.05-59.48,18.06-90.31,21.1-9.62.95-19.31,1.43-28.98,1.2-3.37-.08-6.73-.24-10.09-.52-1.41-.12-2.81-.25-4.22-.41q-1.72-.2-2.63-.33c-2.35-.35-4.74-.71-7.03-1.36-6.2-1.76-8.85,7.89-2.66,9.64h0Z"/>
    <path class="cls-1" d="M229.49,67.78c-7.81,1.49-15.43,6.33-21.17,11.62-14.49,13.37-18.64,33.59-17.77,52.57.29,6.41,10.3,6.44,10,0-.77-16.74,2.36-34.37,15.4-46,4.92-4.38,10.19-7.39,16.19-8.54,6.31-1.2,3.64-10.84-2.66-9.64h0Z"/>
    <path class="cls-4" d="M300.3,147.15c2.5,19.01,9.11,41.23-3.68,57.94-8.82,11.51-24.08,17.24-38.28,14.14-14.52-3.18-26.07-14.51-29.38-29.02-1.98-8.65-2.53-17.76-3.68-26.56-.83-6.29-10.84-6.37-10,0,1.16,8.82,2.03,17.73,3.51,26.5,2.82,16.76,14.82,30.79,30.59,36.82,33.26,12.71,68.81-15.84,64.74-50.84-1.13-9.67-2.54-19.32-3.81-28.98-.83-6.29-10.84-6.37-10,0h0Z"/>
    <path class="cls-3" d="M316.13,233.18c17.65,7.69,35.64,14.86,53.04,23.11,12.26,5.81,15.56,17.5,15.88,29.89.28,11.13.57,22.25.85,33.38.52,20.29,1.67,40.68,1.54,60.99-.13,19.35-14.73,37.88-34.92,38.34-7.38.17-14.86-.61-22.22-.96-23.72-1.12-47.45-2.24-71.17-3.37-6.43-.3-6.42,9.7,0,10,21.99,1.04,43.99,2.08,65.98,3.12,8.21.39,16.42.9,24.64,1.17,18.91.6,35.94-10.93,43.43-28.16,4.46-10.25,4.27-20.62,3.99-31.44-.34-13.1-.67-26.2-1.01-39.3s-.67-26.2-1.01-39.3c-.27-10.59-.36-20.9-6.5-30.2-7.86-11.92-21.73-15.96-34.17-21.38-11.11-4.84-22.22-9.68-33.32-14.52-5.84-2.55-10.94,6.07-5.05,8.63h0Z"/>
    <path class="cls-5" d="M223.48,227.93c-10.89,6.29-21.78,12.57-32.67,18.86-13.73,7.92-17.3,22.79-22.25,36.74-2.16,6.08,7.5,8.69,9.64,2.66,1.9-5.36,3.71-10.77,5.71-16.09,2.72-7.25,6.95-11.79,13.4-15.52,10.4-6.01,20.81-12.01,31.21-18.02,5.57-3.22.54-11.86-5.05-8.63h0Z"/>
    <path class="cls-8" d="M166.82,434.91l-3.33.06,4.82,3.67c-12.81-41.82-25.63-83.64-38.44-125.47-1.83-5.98-3.67-11.97-5.5-17.95l-4.82,6.33h108.77l-4.82-3.67c9,35.92,18,71.84,27,107.76,1.27,5.08,2.54,10.15,3.81,15.23,1.56,6.24,11.21,3.6,9.64-2.66-9-35.92-18-71.84-27-107.76-1.27-5.08-2.54-10.15-3.81-15.23-.53-2.12-2.66-3.67-4.82-3.67h-108.77c-3.17,0-5.78,3.21-4.82,6.33,12.81,41.82,25.63,83.64,38.44,125.47,1.83,5.98,3.67,11.97,5.5,17.95.64,2.08,2.59,3.71,4.82,3.67l3.33-.06c6.43-.12,6.45-10.12,0-10h0Z"/>
    <path class="cls-7" d="M278.39,54.29c-21.78-6.24-42.53,8.6-51.09,27.94-5.94,13.41-8.04,28.09-8.83,42.63-.35,6.43,9.65,6.41,10,0,1-18.35,4.04-40.5,18.44-53.62,6.97-6.36,15.39-8.62,24.06-8.06.53.03,2.63.26,1.93.17-.81-.11.93.16,1.29.23.52.1,1.04.21,1.55.35,6.2,1.78,8.84-7.87,2.66-9.64h0Z"/>
  </g>
  <g>
    <rect class="cls-2" x="128.32" y="389.24" width="43.77" height="65.65" rx="21.88" ry="21.88" transform="translate(-163.1 101.85) rotate(-24.78)"/>
    <path class="cls-6" d="M161.44,447.56c-5.4,2.49-12.16,1.45-16.83-2.07-3.36-2.54-5.03-6.07-6.73-9.77-3.06-6.62-8.14-14.19-9.04-21.56-1.63-13.39,12.93-23.18,24.75-16.9,7.44,3.95,10.28,14.08,13.6,21.27,1.69,3.65,3.88,7.39,4.37,11.45.93,7.66-3.44,14.23-10.12,17.58-5.76,2.88-.7,11.51,5.05,8.63,14.77-7.39,18.09-23.76,11.56-37.9-4.65-10.07-8.37-22.86-18.29-29.01-14.86-9.22-34.87-1.45-39.91,15.19-3.76,12.42,3.72,23.99,8.74,34.87,2.78,6.01,5.78,11.39,11.51,15.18,7.87,5.2,17.92,5.59,26.39,1.67,5.84-2.69.76-11.32-5.05-8.63Z"/>
  </g>
</svg>