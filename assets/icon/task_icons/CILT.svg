<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1080 1080">

  <!-- Apply fill colors and other styles directly in each element -->

  <!-- First path with cls-6 color replaced by fill attribute -->
  <path d="M55.74,508.62v26.08h478.96V55.74h-26.08c-243,15.51-437.36,209.88-452.88,452.88Z" fill="#0f9ed5"/>

  <!-- Second path with cls-4 color replaced by fill attribute -->
  <path d="M1025.26,539.85c-.08-267.93-217.31-485.11-485.26-485.11v485.11h485.26Z" fill="#60167f"/>

  <!-- Third path with cls-1 color replaced by fill attribute -->
  <path d="M54.74,540c0,266.44,214.74,482.72,480.59,485.23v-485.39H54.74c0,.05,0,.1,0,.15Z" fill="#196b24"/>

  <!-- Fourth path with cls-3 color replaced by fill attribute -->
  <path d="M540,1025.26c268,0,485.26-217.26,485.26-485.26h-485.26v485.26Z" fill="#e97132"/>

  <!-- Lines with cls-5 style replaced by stroke and stroke-width attributes -->
  <line x1="540" y1="54.74" x2="540" y2="1024.95" stroke="#fff" stroke-miterlimit="10" stroke-width="6"/>
  <line x1="1080" y1="540" x2="-9.35" y2="540" stroke="#fff" stroke-miterlimit="10" stroke-width="6"/>

  <!-- Text elements with cls-2 color and font styles replaced by fill attribute -->
  <text transform="translate(275.84 398.61)" font-family="Montserrat-Regular, Montserrat" font-size="200" fill="#fff">
    <tspan x="0" y="0">C</tspan>
  </text>
  <text transform="translate(684.15 398.61)" font-family="Montserrat-Regular, Montserrat" font-size="200" fill="#fff">
    <tspan x="0" y="0">I</tspan>
  </text>
  <text transform="translate(290.34 806.22)" font-family="Montserrat-Regular, Montserrat" font-size="200" fill="#fff">
    <tspan x="0" y="0">T</tspan>
  </text>
  <text transform="translate(655.45 806.22)" font-family="Montserrat-Regular, Montserrat" font-size="200" fill="#fff">
    <tspan x="0" y="0">L</tspan>
  </text>

</svg>
