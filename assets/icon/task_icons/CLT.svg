<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1080 1080">

  <!-- First path with cls-3 color replaced by fill attribute -->
  <path d="M98.04,776.14c78.09,163.34,244.86,276.18,437.98,276.18s345.34-103.24,427.3-255.16l-427.34-230.03-437.94,209.01Z" fill="#e97132"/>

  <!-- Second path with cls-1 color replaced by fill attribute -->
  <path d="M963.32,797.16c36.93-68.46,57.89-146.79,57.89-230.03,0-267.97-217.23-485.19-485.19-485.19v479.89l427.3,235.33Z" fill="#196b24"/>

  <!-- Third path with cls-5 color replaced by fill attribute -->
  <path d="M533.94,565.51l2.04-483.58c-267.95.02-485.15,217.24-485.15,485.19,0,73.95,16.55,144.04,46.14,206.77l436.97-208.38Z" fill="#0f9ed5"/>

  <!-- Lines with cls-4 style replaced by stroke and stroke-width attributes -->
  <line x1="536.02" y1="82.02" x2="536.02" y2="566.97" stroke="#fff" stroke-miterlimit="10" stroke-width="6"/>
  <line x1="963.32" y1="797.16" x2="536.02" y2="566.97" stroke="#fff" stroke-miterlimit="10" stroke-width="6"/>
  <line x1="98.04" y1="776.14" x2="536.34" y2="567.13" stroke="#fff" stroke-miterlimit="10" stroke-width="6"/>

  <!-- Text elements with cls-2 color and font styles replaced by fill attribute -->
  <text transform="translate(296.14 462.65)" font-family="Montserrat-Regular, Montserrat" font-size="200" fill="#fff">
    <tspan x="0" y="0">C</tspan>
  </text>
  <text transform="translate(635.92 462.65)" font-family="Montserrat-Regular, Montserrat" font-size="200" fill="#fff">
    <tspan x="0" y="0">T</tspan>
  </text>
  <text transform="translate(481.1 844.98)" font-family="Montserrat-Regular, Montserrat" font-size="200" fill="#fff">
    <tspan x="0" y="0">L</tspan>
  </text>

</svg>
