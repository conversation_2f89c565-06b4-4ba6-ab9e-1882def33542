<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1080 1080">
  <defs>
    <style>
      .cls-1 {
      fill: #FFFFFF; /* Ensuring text color is white */
      font-family: Montserrat, sans-serif;
      font-size: 200px;
      text-anchor: middle; /* Centers text horizontally */
      dominant-baseline: middle; /* Centers text vertically */
      }

      .cls-2 {
      fill: #0f9ed5; /* Ensuring circle remains blue */
      }
    </style>
  </defs>

  <!-- Circle -->
  <circle class="cls-2" cx="540" cy="540" r="489.17"/>

  <!-- Centered Text -->
  <text class="cls-1" x="540" y="540">C</text>
</svg>

    <!--<?xml version="1.0" encoding="UTF-8"?>
    <svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1080 1080">
      <defs>
        <style>
          .cls-1 {
          fill: #FFFFFF; /* Ensuring text color is explicitly white */
          font-family: Montserrat, sans-serif;
          font-size: 200px;
          }

          .cls-2 {
          fill: #0f9ed5; /* Ensuring circle remains blue */
          }
        </style>
      </defs>
      &lt;!&ndash; Circle with explicit fill to ensure correct color &ndash;&gt;
      <circle class="cls-2" cx="538.01" cy="540" r="489.17" fill="#0f9ed5"/>

      &lt;!&ndash; Text with explicit color to prevent override issues &ndash;&gt;
      <text class="cls-1" x="468" y="590" fill="#FFFFFF">C</text>
    </svg>-->
