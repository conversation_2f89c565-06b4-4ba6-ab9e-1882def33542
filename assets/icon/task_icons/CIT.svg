<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1080 1080">

  <!-- Apply fill colors and other styles directly in each element -->

  <!-- First path with cls-1 color replaced by fill attribute -->
  <path d="M100.03,774.23c78.09,163.34,244.86,276.18,437.98,276.18s345.34-103.24,427.3-255.16l-427.34-230.03-437.94,209.01Z" fill="#196b24"/>

  <!-- Second path with cls-3 color replaced by fill attribute -->
  <path d="M965.31,795.24c36.93-68.46,57.89-146.79,57.89-230.03,0-267.97-217.23-485.19-485.19-485.19v479.89l427.3,235.33Z" fill="#60167f"/>

  <!-- Third path with cls-5 color replaced by fill attribute -->
  <path d="M535.93,563.6l2.04-483.58c-267.95.02-485.15,217.24-485.15,485.19,0,73.95,16.55,144.04,46.14,206.77l436.97-208.38Z" fill="#0f9ed5"/>

  <!-- Lines with cls-4 style replaced by stroke and stroke-width attributes -->
  <line x1="538.01" y1="80.11" x2="538.01" y2="565.06" stroke="#fff" stroke-miterlimit="10" stroke-width="6"/>
  <line x1="965.31" y1="795.24" x2="538.01" y2="565.06" stroke="#fff" stroke-miterlimit="10" stroke-width="6"/>
  <line x1="100.03" y1="774.23" x2="538.33" y2="565.22" stroke="#fff" stroke-miterlimit="10" stroke-width="6"/>

  <!-- Text elements with cls-2 color and font styles replaced by fill attribute -->
  <text transform="translate(284.1 468.15)" font-family="Montserrat-Regular, Montserrat" font-size="200" fill="#fff">
    <tspan x="0" y="0">C</tspan>
  </text>
  <text transform="translate(665.18 468.15)" font-family="Montserrat-Regular, Montserrat" font-size="200" fill="#fff">
    <tspan x="0" y="0">I</tspan>
  </text>
  <text transform="translate(482.6 943.64)" font-family="Montserrat-Regular, Montserrat" font-size="200" fill="#fff">
    <tspan x="0" y="0">T</tspan>
  </text>

</svg>
