<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1080 1080">

  <!-- Apply fill colors directly in each element -->

  <!-- First path with cls-2 color replaced by fill attribute -->
  <path d="M1030.39,549.04c0-270.28-219.11-489.39-489.39-489.39v978.79c270.28,0,489.39-219.11,489.39-489.39Z" fill="#60167f"/>

  <!-- Second path with cls-4 color replaced by fill attribute -->
  <path d="M51.6,549.04c0,270.28,219.11,489.39,489.39,489.39V59.65c-270.28,0-489.39,219.11-489.39,489.39Z" fill="#0f9ed5"/>

  <!-- Line with cls-3 style replaced by stroke and stroke-width attributes -->
  <line x1="541" y1="63.94" x2="541" y2="1034.15" stroke="#fff" stroke-miterlimit="10" stroke-width="6"/>

  <!-- Text elements with cls-1 color and font styles replaced by fill attribute -->
  <text transform="translate(234.04 587.82)" font-family="Montserrat-Regular, Montserrat" font-size="200" fill="#fff">
    <tspan x="0" y="0">C</tspan>
  </text>
  <text transform="translate(691.55 587.82)" font-family="Montserrat-Regular, Montserrat" font-size="200" fill="#fff">
    <tspan x="0" y="0">I</tspan>
  </text>

</svg>
