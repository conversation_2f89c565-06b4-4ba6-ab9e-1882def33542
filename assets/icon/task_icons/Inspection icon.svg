<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1080 1080">
  <defs>
    <style>
      .cls-1, .cls-2, .cls-3 {
        stroke: #000;
        stroke-miterlimit: 10;
      }

      .cls-1, .cls-3 {
        stroke-width: 12.46px;
      }

      .cls-2 {
        stroke-width: 11.92px;
      }

      .cls-3 {
        fill: #fff;
      }
    </style>
  </defs>
  <circle class="cls-1" cx="766.27" cy="335" r="244.96"/>
  <rect class="cls-2" x="193.99" y="541.78" width="117.89" height="422.13" transform="translate(685.4 86.72) rotate(51.56)"/>
  <rect class="cls-1" x="393.06" y="520.94" width="203.48" height="79.93" transform="translate(-241.46 429.14) rotate(-38.44)"/>
  <circle class="cls-3" cx="766.27" cy="335" r="208.91"/>
</svg>