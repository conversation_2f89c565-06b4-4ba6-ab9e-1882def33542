<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1080 1080">
  <!-- Apply fill colors directly in each element -->

  <!-- First path with cls-3 color replaced by fill attribute -->
  <path d="M100.03,749.01c78.09,163.34,244.86,276.18,437.98,276.18s345.34-103.24,427.3-255.16l-427.34-230.03-437.94,209.01Z" fill="#e97132"/>

  <!-- Second path with cls-4 color replaced by fill attribute -->
  <path d="M965.31,770.03c36.93-68.46,57.89-146.79,57.89-230.03,0-267.97-217.23-485.19-485.19-485.19v479.89l427.3,235.33Z" fill="#60167f"/>

  <!-- Third path with cls-1 color replaced by fill attribute -->
  <path d="M535.93,538.39l2.04-483.58c-267.95.02-485.15,217.24-485.15,485.19,0,73.95,16.55,144.04,46.14,206.77l436.97-208.38Z" fill="#196b24"/>

  <!-- Lines with cls-5 style replaced by stroke and stroke-width attributes -->
  <line x1="538.01" y1="54.89" x2="538.01" y2="539.85" stroke="#fff" stroke-miterlimit="10" stroke-width="6"/>
  <line x1="965.31" y1="770.03" x2="538.01" y2="539.85" stroke="#fff" stroke-miterlimit="10" stroke-width="6"/>
  <line x1="100.03" y1="749.01" x2="538.33" y2="540" stroke="#fff" stroke-miterlimit="10" stroke-width="6"/>

  <!-- Text elements with cls-2 color replaced by fill attribute -->
  <text transform="translate(697.45 475)" font-family="Montserrat-Regular, Montserrat" font-size="200" fill="#fff">
    <tspan x="0" y="0">I</tspan>
  </text>
  <text transform="translate(248.76 475)" font-family="Montserrat-Regular, Montserrat" font-size="200" fill="#fff">
    <tspan x="0" y="0">T</tspan>
  </text>
  <text transform="translate(481.1 830.42)" font-family="Montserrat-Regular, Montserrat" font-size="200" fill="#fff">
    <tspan x="0" y="0">L</tspan>
  </text>
</svg>
