import 'package:flutter_test/flutter_test.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import '../lib/be/FAULT_HEADER.dart';
import '../lib/utils/fault_status_helper.dart';
import '../lib/utils/constants.dart';

void main() {
  group('FaultStatusHelper Tests', () {
    
    // Test helper to create a fault with specific status and sync status
    FAULT_HEADER createTestFault({
      String? status,
      SyncStatus syncStatus = SyncStatus.none,
      String? jobId,
    }) {
      final fault = FAULT_HEADER();
      fault.status = status;
      fault.syncStatus = syncStatus;
      fault.job_id = jobId;
      fault.fault_id = 'TEST_FAULT_001';
      fault.fault_no = 'F001';
      return fault;
    }

    group('Open Fault Tests (CREATED status)', () {
      test('should identify Open fault correctly', () {
        final openFault = createTestFault(status: Constants.FAULT_STATE_OSNO);
        
        expect(FaultStatusHelper.isFaultOpen(openFault), isTrue);
        expect(FaultStatusHelper.isFaultReadOnly(openFault), isFalse);
        expect(FaultStatusHelper.isEditAllowed(openFault), isTrue);
      });

      test('should show all action buttons for Open fault', () {
        final openFault = createTestFault(status: Constants.FAULT_STATE_OSNO);
        
        expect(FaultStatusHelper.shouldShowCreateJobButton(openFault), isTrue);
        expect(FaultStatusHelper.shouldShowCompleteButton(openFault), isTrue);
        expect(FaultStatusHelper.shouldShowEditButton(openFault), isTrue);
        expect(FaultStatusHelper.shouldShowActionButtons(openFault), isTrue);
      });

      test('should not show Create Job button if job already assigned', () {
        final openFaultWithJob = createTestFault(
          status: Constants.FAULT_STATE_OSNO,
          jobId: 'JOB_123'
        );
        
        expect(FaultStatusHelper.shouldShowCreateJobButton(openFaultWithJob), isFalse);
        expect(FaultStatusHelper.shouldShowCompleteButton(openFaultWithJob), isTrue);
        expect(FaultStatusHelper.shouldShowEditButton(openFaultWithJob), isTrue);
      });

      test('should handle null status as Open', () {
        final nullStatusFault = createTestFault(status: null);
        
        expect(FaultStatusHelper.isFaultOpen(nullStatusFault), isTrue);
        expect(FaultStatusHelper.isEditAllowed(nullStatusFault), isTrue);
      });
    });

    group('Assigned Fault Tests (ASSIGNED status)', () {
      test('should identify Assigned fault correctly', () {
        final assignedFault = createTestFault(status: Constants.FAULT_STATE_ORAS);
        
        expect(FaultStatusHelper.isFaultAssigned(assignedFault), isTrue);
        expect(FaultStatusHelper.isFaultOpen(assignedFault), isFalse);
        expect(FaultStatusHelper.isFaultReadOnly(assignedFault), isTrue);
        expect(FaultStatusHelper.isEditAllowed(assignedFault), isFalse);
      });

      test('should hide all action buttons for Assigned fault', () {
        final assignedFault = createTestFault(status: Constants.FAULT_STATE_ORAS);
        
        expect(FaultStatusHelper.shouldShowCreateJobButton(assignedFault), isFalse);
        expect(FaultStatusHelper.shouldShowCompleteButton(assignedFault), isFalse);
        expect(FaultStatusHelper.shouldShowEditButton(assignedFault), isFalse);
        expect(FaultStatusHelper.shouldShowActionButtons(assignedFault), isFalse);
      });
    });

    group('Completed Fault Tests (COMPLETED status)', () {
      test('should identify Completed fault correctly', () {
        final completedFault = createTestFault(status: Constants.FAULT_STATE_NOCO);
        
        expect(FaultStatusHelper.isFaultCompleted(completedFault), isTrue);
        expect(FaultStatusHelper.isFaultOpen(completedFault), isFalse);
        expect(FaultStatusHelper.isFaultReadOnly(completedFault), isTrue);
        expect(FaultStatusHelper.isEditAllowed(completedFault), isFalse);
      });

      test('should hide all action buttons for Completed fault', () {
        final completedFault = createTestFault(status: Constants.FAULT_STATE_NOCO);
        
        expect(FaultStatusHelper.shouldShowCreateJobButton(completedFault), isFalse);
        expect(FaultStatusHelper.shouldShowCompleteButton(completedFault), isFalse);
        expect(FaultStatusHelper.shouldShowEditButton(completedFault), isFalse);
        expect(FaultStatusHelper.shouldShowActionButtons(completedFault), isFalse);
      });
    });

    group('Sync Status Tests', () {
      test('should disable editing for queued sync status', () {
        final queuedFault = createTestFault(
          status: Constants.FAULT_STATE_OSNO,
          syncStatus: SyncStatus.queued
        );
        
        expect(FaultStatusHelper.isEditAllowed(queuedFault), isFalse);
        expect(FaultStatusHelper.shouldShowActionButtons(queuedFault), isFalse);
      });

      test('should disable editing for sent sync status', () {
        final sentFault = createTestFault(
          status: Constants.FAULT_STATE_OSNO,
          syncStatus: SyncStatus.sent
        );
        
        expect(FaultStatusHelper.isEditAllowed(sentFault), isFalse);
        expect(FaultStatusHelper.shouldShowActionButtons(sentFault), isFalse);
      });

      test('should allow editing for error sync status', () {
        final errorFault = createTestFault(
          status: Constants.FAULT_STATE_OSNO,
          syncStatus: SyncStatus.error
        );
        
        expect(FaultStatusHelper.isEditAllowed(errorFault), isTrue);
        expect(FaultStatusHelper.shouldShowActionButtons(errorFault), isTrue);
      });
    });

    group('Status Description Tests', () {
      test('should return correct descriptions for each status', () {
        final openFault = createTestFault(status: Constants.FAULT_STATE_OSNO);
        final assignedFault = createTestFault(status: Constants.FAULT_STATE_ORAS);
        final completedFault = createTestFault(status: Constants.FAULT_STATE_NOCO);
        
        expect(FaultStatusHelper.getStatusDescription(openFault), 
               contains('Open - Can be edited'));
        expect(FaultStatusHelper.getStatusDescription(assignedFault), 
               contains('Job Assigned - Read-only'));
        expect(FaultStatusHelper.getStatusDescription(completedFault), 
               contains('Completed - Read-only'));
      });

      test('should return correct read-only reasons', () {
        final assignedFault = createTestFault(status: Constants.FAULT_STATE_ORAS);
        final completedFault = createTestFault(status: Constants.FAULT_STATE_NOCO);
        final queuedFault = createTestFault(
          status: Constants.FAULT_STATE_OSNO,
          syncStatus: SyncStatus.queued
        );
        
        expect(FaultStatusHelper.getReadOnlyReason(assignedFault), 
               contains('job assigned'));
        expect(FaultStatusHelper.getReadOnlyReason(completedFault), 
               contains('completed'));
        expect(FaultStatusHelper.getReadOnlyReason(queuedFault), 
               contains('queued for sync'));
      });
    });
  });
}
