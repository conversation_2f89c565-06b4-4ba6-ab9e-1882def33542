
const { exec } = require('child_process');
const fs = require('fs')

fs.readFile('BuildNo.txt', (err, data) => {
    if (err) throw err;

    console.log('Pushing Tag on root...')
    exec(`git tag -a ${data}-Windows -m "Jenkins: Updated Build number ${data}-Windows"`)
    exec(`git push origin ${data}-Windows`)
    console.log('Done. Pushing Tag on src...')

    // Push the tag for src folder
    exec(`git tag -a ${data}-Windows -m "Jenkins: Updated Build number ${data}-Windows"`, {
        cwd: 'src'
    })
    exec(`git push origin ${data}-Windows`, {
        cwd: 'src'
    })
    console.log('Done')

    return 0
})
