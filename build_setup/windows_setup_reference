step 1:
Open your Flutter project in VS Code or Android Studio & execute the build command in the terminal.
flutter build windows --release
It will create the .exe file along with some .dll files & the data inside project folder containing assets in the following location:
 build\windows\x64\runner\Release
step 2:
Copy windows supporting files i shared and place it alongside the other .dll files in the project build release folder.
step 3:
Launch Inno Setup and create a new script using script wizard by clicking OK.
if you don't have a inno setup download and install. below link i shared download link
https://jrsoftware.org/isdl.php
 Inno Setup reference link below i added
https://medium.com/@fluttergems/packaging-and-distributing-flutter-desktop-apps-the-missing-guide-part-2-windows-0b468d5e9e70

