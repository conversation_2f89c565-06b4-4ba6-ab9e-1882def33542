; Script generated by the Inno Setup Script Wizard.
; SEE THE DOCUMENTATION FOR DETAILS ON CREATING INNO SETUP SCRIPT FILES!

#define MyAppName "Unvired ROUNDS"
#define MyAppVersion "@VERSION@"
#define MyAppPublisher "Unvired, Inc."
#define MyAppExeName "rounds.exe"
#define MyAppAssocName "Api_dashfile"
#define MyAppAssocExt ".dash"
#define MyAppAssocKey StringChange(MyAppAssocName, " ", "") + MyAppAssocExt

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
; (To generate a new GUID, click Tools | Generate GUID inside the IDE.)
AppId={{EE989C5B-D171-4F47-8404-065EFA40BA83}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName}
AppPublisher={#MyAppPublisher}
DefaultDirName={autopf}\{#MyAppName}
; "ArchitecturesAllowed=x64compatible" specifies that Setup cannot run
; on anything but x64 and Windows 11 on Arm.
ArchitecturesAllowed=x64compatible
; "ArchitecturesInstallIn64BitMode=x64compatible" requests that the
; install be done in "64-bit mode" on x64 or Windows 11 on Arm,
; meaning it should use the native 64-bit Program Files directory and
; the 64-bit view of the registry.
ArchitecturesInstallIn64BitMode=x64compatible
ChangesAssociations=yes
DisableProgramGroupPage=yes
; Uncomment the following line to run in non administrative install mode (install for current user only.)
;PrivilegesRequired=lowest
OutputDir=rounds_windows_x86_64
OutputBaseFilename=rounds
Compression=lzma
SolidCompression=yes
WizardStyle=modern

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
    
[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "{#MyAppExeName}"; DestDir: "{app}"; Flags: ignoreversion
Source: "file_selector_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "connectivity_plus_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "connectivity_plus_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "firebase_core_plugin.lib"; DestDir: "{app}"; Flags: ignoreversion
Source: "flutter_windows.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "libcrypto-1_1-x64.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "libssl-1_1-x64.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "msvcp140.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "msvcp140_1.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "msvcp140_2.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "pdfium.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "pdfx_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "permission_handler_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "sqlcipher.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "sqlcipher_flutter_libs_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "sqlite3.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "vcruntime140.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "vcruntime140_1.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "webview_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "WebView2Loader.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "camera_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "desktop_webview_window_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "flutter_tts_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "geolocator_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "video_player_win_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "data\*"; DestDir: "{app}\data"; Flags: ignoreversion recursesubdirs createallsubdirs
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Registry]
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocExt}\OpenWithProgids"; ValueType: string; ValueName: "{#MyAppAssocKey}"; ValueData: ""; Flags: uninsdeletevalue
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}"; ValueType: string; ValueName: ""; ValueData: "{#MyAppAssocName}"; Flags: uninsdeletekey
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\{#MyAppExeName},0"
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\{#MyAppExeName}"" ""%1"""
Root: HKA; Subkey: "Software\Classes\Applications\{#MyAppExeName}\SupportedTypes"; ValueType: string; ValueName: ".myp"; ValueData: ""

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent