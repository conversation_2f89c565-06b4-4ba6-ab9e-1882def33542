<!DOCTYPE html>
<html>
<head>
  <script src="https://www.gstatic.com/firebasejs/8.4.1/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.4.1/firebase-messaging.js"></script>
  <script src="assets/packages/libphonenumber_plugin/js/libphonenumber.js"></script>
  <script src="assets/packages/libphonenumber_plugin/js/stringbuffer.js"></script>
  <link rel="stylesheet" type="text/css" href="styles.css">
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <meta name="camera" content="allow">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="rounds">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>rounds</title>
  <link rel="manifest" href="manifest.json">

  <!-- Required by flutter_web_qrcode_scanner -->
  <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>


  <script>
    // The value below is injected by flutter build, do not touch.
    const serviceWorkerVersion = null;
  </script>
  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js" defer></script>
  <script>
    const firebaseConfig = {
      apiKey: "AIzaSyDMMWMXRvJxUX6_AjXJuOKcJTnXdQSnQKU",
      appId: "1:704334877465:web:4f9fe8b5b91f32e4586b88",
      messagingSenderId: "704334877465",
      projectId: "rounds-62bfa",
      authDomain: "rounds-62bfa.firebaseapp.com",
      storageBucket: "rounds-62bfa.firebasestorage.app",
      measurementId: "G-NVM75E3PNX"
    };
    firebase.initializeApp(firebaseConfig);
  </script>
</head>
<body>
  <script src="https://cdn.jsdelivr.net/npm/pdfjs-dist@2.12.313/build/pdf.js" type="text/javascript"></script>
  <script type="text/javascript">
    pdfjsLib.GlobalWorkerOptions.workerSrc = "https://cdn.jsdelivr.net/npm/pdfjs-dist@2.12.313/build/pdf.worker.min.js";
    pdfRenderOptions = {
      cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.12.313/cmaps/',
      cMapPacked: true,
    }
  </script>

  <script type="module" src="assets/js/qr_scanner.js"></script>

  <div class="logo-container">
    <div class="circle-logo">
      <img src="assets/rounds_animate_logo.gif" alt="Logo" />
    </div>
    <div class="loading-bar">
      <div class="progress"></div>
    </div>
    </div>
  <!-- This script installs service_worker.js to provide PWA functionality to
         application. For more information, see:
         https://developers.google.com/web/fundamentals/primers/service-workers -->
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('flutter-first-frame', function () {
        navigator.serviceWorker.register('flutter_service_worker.js');
      });
    }
  </script>
  <script defer src="sql-wasm.js"></script>
  <script>
    function loadGoogleMaps(apiKey) {
      let existingScript = document.querySelector("script[src*='maps.googleapis.com']");
      if (existingScript) {
        existingScript.remove();
      }

      let script = document.createElement("script");
      script.src = apiKey
        ? `https://maps.googleapis.com/maps/api/js?key=${apiKey}&callback=initMap`
        : "https://maps.googleapis.com/maps/api/js?sensor=false&callback=initMap";

      script.defer = true;
      script.async = true;
      document.head.appendChild(script);
    }

    // Load Maps on Page Load
    document.addEventListener("DOMContentLoaded", function () {
      loadGoogleMaps(localStorage.getItem("google_maps_api_key") || "");
    });
  </script>
  <script>
    window.addEventListener('load', function(ev) {

      const progressBar = document.querySelector('.progress');
      let progress = 0;
      const interval = setInterval(() => {
        if (progress < 90) {
          progress += 1;
          progressBar.style.width = progress + '%';
        }
      }, 50); // Adjust speed by changing the interval

      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            appRunner.runApp();
          });
        }
      });
    });
  </script>

<script src="flutter_bootstrap.js" async>
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', function () {
      navigator.serviceWorker.register('firebase-messaging-sw.js', {
        scope: '/firebase-cloud-messaging-push-scope',
      });
    });
  }
</script>
</body>
</html>
