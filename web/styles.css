.center {
    margin: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-right: -50%;
    transform: translate(-50%, -50%)
}   

/* Center the entire logo container */
.logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
}

/* Circular frame for the logo */
.circle-logo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    margin-bottom: 20px;
}

/* Ensures the image fills the circle */
.circle-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Loading bar container */
.loading-bar {
    width: 200px;
    height: 10px;
    background-color: #e0e0e0;
    border-radius: 5px;
    overflow: hidden; 
}

/* Animated progress bar */
.progress {
    width: 0%;
    height: 100%;
    background-color: #3F51B5;
    transition: width 0.2s ease;
    border-radius: 5px; 
}

@keyframes loading {
    0% {
        width: 0%;
    }

    50% {
        width: 100%;
    }

    100% {
        width: 0%;
    }
}