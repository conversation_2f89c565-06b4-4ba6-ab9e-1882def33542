importScripts("https://www.gstatic.com/firebasejs/9.19.1/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/9.19.1/firebase-messaging-compat.js");

firebase.initializeApp({
    apiKey: "AIzaSyDMMWMXRvJxUX6_AjXJuOKcJTnXdQSnQKU",
    authDomain: "rounds-62bfa.firebaseapp.com",
    databaseURL: "https://rounds-62bfa-default-rtdb.firebaseio.com",
    projectId: "rounds-62bfa",
    storageBucket: "rounds-62bfa.firebasestorage.app",
    messagingSenderId: "704334877465",
    appId: "1:704334877465:web:4f9fe8b5b91f32e4586b88",
    measurementId: "G-NVM75E3PNX"
});

const messaging = firebase.messaging();
const channel = new BroadcastChannel('sw-messages');

// Handle background messages
messaging.onBackgroundMessage(async (payload) => {
  console.log('Received background message:', payload);
  
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: '/icons/Icon-192.png',
    badge: '/icons/Icon-192.png',
    data: payload.data,
    click_action: payload.notification.click_action,
  };

  channel.postMessage(payload);

  console.log("post message to channel");

  // Save to IndexedDB only if notificationContext is valid
  const data = payload.data;
  var notificationContext = getNotificationContext(data);
  if (notificationContext) {
    notificationContext = { messageId: payload.messageId, data: notificationContext };
    saveNotificationToIndexedDB(notificationContext);
    self.registration.showNotification(notificationTitle, notificationOptions);
  } else {
    console.log('No valid notificationContext found, not saving or showing notification.');
  }
});

function saveNotificationToIndexedDB(data) {
  if (!data.messageId) {
    console.error('Missing messageId in notification data');
    return;
  }

  const request = indexedDB.open('rounds', 1);

  request.onupgradeneeded = (event) => {
    const db = event.target.result;
    if (!db.objectStoreNames.contains('notifications')) {
      db.createObjectStore('notifications', { keyPath: 'messageId' }); // ✅ matches Dart
    }
  };

  request.onsuccess = (event) => {
    const db = event.target.result;
    const transaction = db.transaction('notifications', 'readwrite');
    const store = transaction.objectStore('notifications');

    store.put(data); // ✅ allows upsert (insert or replace)
  };

  request.onerror = (event) => {
    console.error('Error opening IndexedDB:', event.target.error);
  };
}

function getNotificationContext(message) {
  try {
    // Check if "data" exists in the message
    if (!message || !message.data) return null;

    let notificationData;

    // If data is a JSON string, parse it
    if (typeof message.data === 'string') {
      notificationData = JSON.parse(message.data);
    } else {
      notificationData = message.data;
    }

    // Get "NotificationContext"
    const contextRaw = notificationData.NotificationContext;

    if (!contextRaw || (typeof contextRaw === 'string' && contextRaw.trim() === '')) {
      return null;
    }

    // Parse NotificationContext
    return typeof contextRaw === 'string' ? JSON.parse(contextRaw) : contextRaw;

  } catch (e) {
    console.error("Error decoding NotificationContext:", e);
    return null;
  }
}

