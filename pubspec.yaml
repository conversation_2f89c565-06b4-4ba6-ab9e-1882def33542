name: rounds
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=3.2.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  unvired_sdk:
    #    path: D:\projects\unvired_sdk
    git:
      url: https://jenkinsci:<EMAIL>:8443/flutter/unvired_sdk.git
      ref: isolate_logger

  logger:
    git:
      url: https://jenkinsci:<EMAIL>:8443/flutter/flutter_logger_package.git
      ref: flutter_version_update
    # ref: 0.0.38
    # path: ../flutter_logger_package

  unvired_settings:
    #    path: D:\projects\settings
    git:
      url: https://jenkinsci:<EMAIL>:8443/flutter/settings.git
      ref: stenil

  #  ai_ml_support:
  #    git:
  #      url: https://jenkinsci:<EMAIL>/flutter/unvired_ai_ml_support.git
  #      ref: main
  # path: ../unvired_ai_ml_support

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  provider:
  screenshot:
  cupertino_icons: ^1.0.2
  flutter_riverpod: ^2.5.1
  hexcolor: ^3.0.1
  path_provider: ^2.1.2
  go_router: ^14.2.1
  flutter_svg: ^2.0.9
  freezed:
  freezed_annotation: ^2.4.4
  flutter_otp_text_field: ^1.1.3+2
  change_app_package_name: ^1.3.0
  shared_preferences: ^2.0.12
  percent_indicator: ^4.2.2
  lottie: ^1.3.0
  flutter_screenutil: ^5.9.0
  fl_chart: ^0.68.0
  carousel_slider: ^5.0.0
  flutter_native_splash: ^2.3.10
  syncfusion_flutter_sliders: ^20.0.0
  syncfusion_flutter_core: ^20.0.0
  geolocator: 11.0.0
  open_file_plus:
  html: ^0.15.4
  intl:
  intl_phone_number_input: ^0.7.4
  flutter_staggered_grid_view:
  camera_windows:

  flutter_localizations:
    sdk: flutter
  http: ^1.2.0
  google_fonts: ^4.0.4
  easy_date_timeline: ^1.0.3
  shimmer: ^3.0.0
  flutter_slidable: ^2.0.0
  collection: 1.18.0
  file_picker: ^7.0.0
  video_player: ^2.6.3
  encrypt: ^5.0.3
  multi_select_flutter: ^4.1.3
  permission_handler: ^11.2.0
  # flutter_pdfview: ^1.4.0
  pdfx:
  image_picker:
  camera:
  grouped_list:
  syncfusion_flutter_calendar:
  connectivity_plus:
  auto_size_text: ^3.0.0
  signature: ^5.4.0
  qr_code_scanner: ^1.0.1
  flutter_web_qrcode_scanner: ^1.1.1
  uuid: ^4.2.2
  package_info_plus:
  flutter_local_notifications: ^9.9.1
  #  webviewx: ^0.2.1
  #  webview_flutter: ^2.0.13
  webviewx_plus: ^0.5.1
  url_launcher:

  #  mobile_scanner: ^3.5.5
  flutter_html: ^3.0.0-beta.2
  mime:
  idb_shim:
  js: ^0.6.7
  # simple_barcode_scanner: ^0.1.1  # Removed due to namespace compatibility issues
  #  mobile_scanner: ^3.5.4
  #  universal_html: ^2.0.9
  webview_windows:
  animated_custom_dropdown: ^3.1.1

dependency_overrides:
  intl: ^0.18.0
  flutter_local_notifications: ^9.1.1
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0
  flutter_launcher_icons: ^0.13.1

  win32: 5.0.6
  build_runner: ^2.0.6
  msix: ^3.16.12

  # For information on the generic Dart part of this file, see the
  # following page: https://dart.dev/tools/pub/pubspec

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/icon.png"
  min_sdk_android: 21

  # android min sdk min:16, default 21
  # web:
  #   generate: true
  #   image_path: "path/to/image.png"
  #   background_color: "#hexcode"
  #   theme_color: "#hexcode"
  # windows:
  #   generate: true
  #   image_path: "path/to/image.png"
  #   icon_size: 48 # min:48, max:256, default: 48
  # macos:
  #   generate: true
  #   image_path: "path/to/image.png"

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true

  # flutter_intl:
  #   enabled:true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/images/
    - assets/json/
    - assets/json/lottie/
    - assets/json/country_list.json
    - assets/icons/broom.svg
    - assets/icons/eye.svg
    - assets/icons/oil-can.svg
    - assets/icons/tools.svg
    - assets/icons/kpi.png
    - assets/icon/
    - assets/icon/setting.png
    - assets/icon/setting_grey.png
    - assets/icon/setting.svg
    - assets/icons/
    - assets/icon/task_icons/
    - assets/icon/task_icons/accept.png
    - assets/icon/task_icons/reject.png
    - assets/icon/task_icons/C.svg
    - assets/icon/task_icons/C_icon.svg
    - assets/icon/task_icons/CI.svg
    - assets/icon/task_icons/CL.svg
    - assets/icon/task_icons/CILT.svg
    - assets/icon/task_icons/CLT.svg
    - assets/icon/task_icons/CT.svg
    - assets/icon/task_icons/IT.svg
    - assets/icon/task_icons/L.svg
    - assets/icon/task_icons/IL.svg
    - assets/icon/task_icons/ILT.svg
    - assets/icon/task_icons/LT.svg
    - assets/icon/task_icons/T.svg
    - assets/icon/task_icons/T_icon.svg
    - assets/icon/task_icons/LT_icon.svg
    - assets/icon/task_icons/inspection.svg
    - assets/icon/task_icons/info.png
    - assets/images/empty_profile.png
    - assets/svg/
    - assets/gif/
    - assets/gif/no_data_gif.gif
    - assets/inspection_task_icons/
    - web/assets/
    - assets/barcode/barcode.html
    - assets/barcode/html5-qrcode.min.js
    - assets/lottie/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
  # flutter gen-l10n
  # flutter pub run build_runner build

  #flutter_native_splash:
  #  color: "#FABC3F"

flutter_native_splash:
  color: "#FFFFFF"
  image: assets/gif/rounds_animate_logo.gif
  icon_background_color: "#FFFFFF"

  android_12:
    image: assets/gif/rounds_animate_logo.gif
    icon_background_color: "#FFFFFF"
  fullscreen: true
  web: false

msix_config:
  display_name: easyrounds
  publisher_display_name: Unvired
  identity_name: com.unvired.rounds
  publisher: CN=Unvired
  msix_version: *******
  logo_path: windows/runner/resources/app_icon.ico
  capabilities: internetClient, location, microphone, webcam
  certificate_path: windows\certificate\rounds_Certificate.pfx
  certificate_password: unvired123*
